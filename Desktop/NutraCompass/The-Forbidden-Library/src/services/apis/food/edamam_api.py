import requests
import logging
from typing import Dict, Any
from src.services.interfaces.food_api_interface import FoodAPIInterface
from src.config import EDAMAM_APP_ID, EDAMAM_APP_KEY, EDAMAM_PARSER_BASE_URL, EDAMAM_NUTRIENTS_BASE_URL

logger = logging.getLogger("EdamamAPI")
logging.basicConfig(level=logging.INFO)

class EdamamAPI(FoodAPIInterface):
    """
    Implementation of the FoodAPIInterface for the Edamam Food Database API.
    """
    def __init__(self):
        self.API_ID = EDAMAM_APP_ID
        self.API_KEY = EDAMAM_APP_KEY
        self.PARSER_BASE_URL = EDAMAM_PARSER_BASE_URL
        self.NUTRIENTS_BASE_URL = EDAMAM_NUTRIENTS_BASE_URL

    def search_food(self, search_term: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search Edamam Food Database API for foods matching the search term, limiting the number of items.
        
        Args:
            search_term (str): The term to search for.
            limit (int, optional): Maximum number of items to fetch. Defaults to 10.
        
        Returns:
            Dict[str, Any]: Processed response data.
        """
        url = f"{self.PARSER_BASE_URL}?ingr={search_term}&from=0&to={limit}&app_id={self.API_ID}&app_key={self.API_KEY}"
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
            logger.info("Edamam search_food API call successful.")
            processed_data = []
            if data.get("hints"):
                for hint in data["hints"]:
                    food = hint.get("food", {})
                    measures = hint.get("measures", [])
                    if food and measures:
                        processed_data.append({
                            "foodId": food.get("foodId"),
                            "foodLabel": food.get("label"),
                            "foodCategory": food.get("category"),
                            "foodBrand": food.get("brand"),
                            "numberOfServings": 1,
                            "activeMeasure": measures[0],
                            "measures": measures,
                            "defaultNutrients": {
                                "ENERC_KCAL": food.get("nutrients", {}).get("ENERC_KCAL")
                            }
                        })
                processed_data = list({item["foodId"]: item for item in processed_data}.values())
            return {"results": processed_data}
        except Exception as e:
            logger.error(f"Error in search_food: {e}")
            return {}

    def search_food_by_barcode(self, barcode: str) -> Dict[str, Any]:
        url = f"{self.PARSER_BASE_URL}?upc={barcode}&app_id={self.API_ID}&app_key={self.API_KEY}"
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
            logger.info("Edamam search_food_by_barcode API call successful.")
            processed_data = []
            if data.get("hints"):
                for hint in data["hints"]:
                    food = hint.get("food", {})
                    measures = hint.get("measures", [])
                    if food and measures:
                        processed_data.append({
                            "foodId": food.get("foodId"),
                            "foodLabel": food.get("label"),
                            "foodCategory": food.get("category"),
                            "foodBrand": food.get("brand"),
                            "numberOfServings": 1,
                            "activeMeasure": measures[0],
                            "measures": measures,
                            "defaultNutrients": {
                                "ENERC_KCAL": food.get("nutrients", {}).get("ENERC_KCAL")
                            }
                        })
                processed_data = list({item["foodId"]: item for item in processed_data}.values())
            return {"results": processed_data}
        except Exception as e:
            logger.error(f"Error in search_food_by_barcode: {e}")
            return {}

    def get_nutrients(self, ingredients: Dict[str, Any]) -> Dict[str, Any]:
        url = f"{self.NUTRIENTS_BASE_URL}?app_id={self.API_ID}&app_key={self.API_KEY}"
        try:
            response = requests.post(url, json=ingredients, headers={"Content-Type": "application/json"})
            response.raise_for_status()
            logger.info("Edamam get_nutrients API call successful.")
            return response.json()
        except Exception as e:
            logger.error(f"Error in get_nutrients: {e}")
            return {}
