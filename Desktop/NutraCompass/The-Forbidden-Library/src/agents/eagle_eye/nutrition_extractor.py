import cv2
import numpy as np
import streamlit as st
from pathlib import Path
import sys
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import urllib3
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
import os
from PIL import Image
import tempfile
import requests
import base64
import io

# Set up logger first
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# Optional imports - gracefully handle missing dependencies
try:
    from src.agents.eagle_eye.main import EagleEye
    EAGLE_EYE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"EagleEye not available: {e}")
    EagleEye = None
    EAGLE_EYE_AVAILABLE = False

try:
    from src.services.external_api_integrator import ExternalAPIIntegrator
    API_INTEGRATOR_AVAILABLE = True
except ImportError as e:
    logger.warning(f"External API Integrator not available: {e}")
    ExternalAPIIntegrator = None
    API_INTEGRATOR_AVAILABLE = False

class NutritionExtractor:
    """
    Camera-based nutrition extractor using EagleEye for food segmentation,
    OpenRouter GPT-4.1-nano for food detection and analysis,
    and Edamam API for nutrition data retrieval.
    """

    def __init__(self):
        self.eagle_eye = None
        self.api_integrator = None
        self.openrouter_api_key = None
        self.session_history = []
        self.http_session = None

        # Load environment variables
        self._load_env_config()

        # Initialize components
        self._initialize_components()
        
        # Setup HTTP session
        self._setup_http_session()

    def _load_env_config(self):
        """Load configuration from .env.development"""
        try:
            # Try multiple possible env file locations
            env_paths = [
                Path(".env.development"),
                Path("env.development"),
                Path("../.env.development"),
                Path("../../.env.development"),
                Path("../../../.env.development"),
                Path("../../../../.env.development")
            ]

            env_loaded = False
            for env_path in env_paths:
                if env_path.exists():
                    logger.info(f"Loading environment from: {env_path}")
                    with open(env_path, 'r') as f:
                        for line in f:
                            if '=' in line and not line.strip().startswith('#'):
                                key, value = line.strip().split('=', 1)
                                os.environ[key] = value.strip('"\'')
                    env_loaded = True
                    break

            # Set up API keys from environment
            self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
            self.edamam_app_id = os.getenv('EDAMAM_APP_ID')
            self.edamam_app_key = os.getenv('EDAMAM_APP_KEY')

            if env_loaded:
                logger.info("Environment configuration loaded successfully")
            else:
                logger.warning("No .env.development file found, using system environment variables")

        except Exception as e:
            logger.warning(f"Failed to load environment configuration: {e}")
            self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
            self.edamam_app_id = os.getenv('EDAMAM_APP_ID')
            self.edamam_app_key = os.getenv('EDAMAM_APP_KEY')

    def _initialize_components(self):
        """Initialize food recognition components"""
        try:
            # Initialize API integrator if available
            if API_INTEGRATOR_AVAILABLE and ExternalAPIIntegrator:
                self.api_integrator = ExternalAPIIntegrator()
                logger.info("External API Integrator initialized")
            else:
                self.api_integrator = None
                logger.warning("External API Integrator not available - nutrition enhancement disabled")

            # Initialize EagleEye if available
            if EAGLE_EYE_AVAILABLE and EagleEye and self.api_integrator:
                self.eagle_eye = EagleEye(
                    model_path=os.getenv("EAGLE_EYE_MODEL_PATH", "src/data/eagle_eye/FoodSeg103/pretrained/iter_80000.pth"),
                    api_integrator=self.api_integrator
                )
                logger.info("EagleEye initialized")
            else:
                self.eagle_eye = None
                logger.warning("EagleEye not available - food segmentation disabled")

            if not self.openrouter_api_key:
                logger.warning("OpenRouter API key not found. GPT vision and enhancement will be disabled.")
            else:
                logger.info("OpenRouter API key configured")

            logger.info("Component initialization completed")

        except Exception as e:
            logger.error(f"Component initialization failed: {e}")
            self.eagle_eye = None
            self.api_integrator = None

    def _setup_http_session(self):
        """Setup HTTP session with retry logic and connection pooling."""
        try:
            self.http_session = requests.Session()
            
            # Configure retry strategy
            retry_strategy = Retry(
                total=3,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "POST"],
                backoff_factor=1
            )
            
            # Configure HTTP adapter with retry and connection pooling
            adapter = HTTPAdapter(
                max_retries=retry_strategy,
                pool_connections=10,
                pool_maxsize=20
            )
            
            self.http_session.mount("http://", adapter)
            self.http_session.mount("https://", adapter)
            
            # Set timeout defaults
            self.http_session.timeout = 30
            
            logger.info("HTTP session configured with retry logic and connection pooling")
            
        except Exception as e:
            logger.warning(f"Failed to setup HTTP session: {e}")
            self.http_session = requests  # Fallback to regular requests

    def extract_nutrition(self, image_bytes: bytes) -> Dict[str, Any]:
        """
        Extract nutrition information from an image using EagleEye segmentation,
        OpenRouter GPT 4.1 nano for food label detection and nutrition analysis,
        and Edamam API for nutrition data retrieval.

        Args:
            image_bytes (bytes): Image data in bytes.

        Returns:
            dict: Nutrition information with AI vision and enhancement.
        """
        try:
            # Step 1: Segment food regions with EagleEye (optional, for visual feedback)
            segmentation_result = None
            if self.eagle_eye:
                try:
                    segmentation_result = self.eagle_eye._segment_food(np.array(Image.open(io.BytesIO(image_bytes))))
                except Exception as e:
                    logger.warning(f"EagleEye segmentation failed: {e}")

            # Step 2: Use OpenRouter GPT 4.1 nano for comprehensive food analysis
            gpt_analysis = self._detect_food_labels_with_gpt(image_bytes)
            if "error" in gpt_analysis:
                logger.error(f"GPT vision analysis error: {gpt_analysis['error']}")
                return {"error": "GPT vision analysis failed", "details": gpt_analysis}

            # Step 3: Extract primary nutrition data from GPT analysis
            primary_nutrition = self._extract_gpt_nutrition(gpt_analysis)

            # Step 4: Optionally enhance with Edamam API for additional validation
            enhanced_nutrition = {}
            if self.api_integrator and gpt_analysis.get("labels"):
                for label in gpt_analysis.get("labels", []):
                    try:
                        nutrition_info = self.api_integrator.search_food("edamam", label, limit=1)
                        if nutrition_info.get("results"):
                            enhanced_nutrition[label] = self._parse_nutrition_info(nutrition_info["results"][0])
                    except Exception as e:
                        logger.warning(f"Edamam API lookup failed for {label}: {e}")

            # Step 5: Generate expert analysis and recommendations
            expert_analysis = self._generate_expert_analysis(primary_nutrition, enhanced_nutrition)

            return {
                "success": True,
                "segmentation_mask": segmentation_result.tolist() if segmentation_result is not None else None,
                "gpt_analysis": gpt_analysis,
                "primary_nutrition": primary_nutrition,
                "enhanced_nutrition": enhanced_nutrition,
                "expert_analysis": expert_analysis,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Nutrition extraction failed: {str(e)}")
            return {"error": "Nutrition extraction failed", "details": str(e)}

    def _detect_food_labels_with_gpt(self, image_bytes: bytes) -> Dict[str, Any]:
        """
        Use OpenRouter GPT-4.1-nano model to detect food labels from image bytes.

        Args:
            image_bytes (bytes): Image data in bytes.

        Returns:
            dict: Detected food labels or error.
        """
        if not self.openrouter_api_key:
            return {"error": "OpenRouter API key not configured"}

        # Convert image bytes to base64 string for vision
        image_b64 = base64.b64encode(image_bytes).decode("utf-8")
        image_url = f"data:image/jpeg;base64,{image_b64}"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.openrouter_api_key}"
        }

        payload = {
            "model": "openai/gpt-4.1-nano-2025-04-14",
            "messages": [
                {
                    "role": "system",
                    "content": """You are a certified nutritionist and food analysis expert. Your task is to analyze food images with precision and provide accurate nutritional estimates. Always respond with valid JSON only."""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """Analyze this food image and provide detailed nutritional information.

REQUIREMENTS:
1. Identify ALL visible food items with specific names (e.g., "grilled chicken breast", "steamed broccoli", "brown rice")
2. Estimate portion sizes in common units (cups, ounces, pieces)
3. Calculate nutritional values per portion for each item
4. Provide total meal nutritional summary

RESPONSE FORMAT (JSON only):
{
  "labels": ["food_item_1", "food_item_2"],
  "portions": {
    "food_item_1": "estimated_portion_size",
    "food_item_2": "estimated_portion_size"
  },
  "individual_nutrition": {
    "food_item_1": {
      "calories": number,
      "protein_g": number,
      "carbs_g": number,
      "fat_g": number,
      "fiber_g": number,
      "sugar_g": number
    }
  },
  "total_nutrition": {
    "calories": total_number,
    "protein_g": total_number,
    "carbs_g": total_number,
    "fat_g": total_number,
    "fiber_g": total_number,
    "sugar_g": total_number
  },
  "confidence_level": "high/medium/low"
}

Be conservative with estimates and indicate confidence level based on image clarity and food visibility."""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.1,  # Lower temperature for more consistent results
            "max_tokens": 1200   # Increased for more detailed response
        }

        try:
            # Use configured session or fallback to requests
            session = self.http_session if self.http_session else requests
            
            response = session.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            response.raise_for_status()
            data = response.json()
            gpt_reply = data["choices"][0]["message"]["content"]

            # Extract JSON from response
            try:
                # Try to parse as direct JSON
                labels_json = json.loads(gpt_reply)
            except json.JSONDecodeError:
                # Try to extract JSON from markdown code blocks
                import re
                json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', gpt_reply, re.DOTALL)
                if json_match:
                    labels_json = json.loads(json_match.group(1))
                else:
                    # Fallback: try to find JSON-like structure
                    json_match = re.search(r'{.*?"labels".*?\[.*?\].*?}', gpt_reply, re.DOTALL)
                    if json_match:
                        labels_json = json.loads(json_match.group(0))
                    else:
                        return {"error": "Could not parse JSON from GPT response", "raw_response": gpt_reply}

            return labels_json

        except Exception as e:
            logger.error(f"OpenRouter GPT vision request failed: {str(e)}")
            return {"error": "OpenRouter GPT vision request failed", "details": str(e)}

    def _extract_gpt_nutrition(self, gpt_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract and standardize nutrition data from GPT analysis response.

        Args:
            gpt_analysis (dict): GPT analysis response containing nutrition data

        Returns:
            dict: Standardized nutrition information
        """
        try:
            # Extract total nutrition if available
            total_nutrition = gpt_analysis.get("total_nutrition", {})
            individual_nutrition = gpt_analysis.get("individual_nutrition", {})
            portions = gpt_analysis.get("portions", {})
            confidence = gpt_analysis.get("confidence_level", "medium")

            return {
                "total_calories": total_nutrition.get("calories", 0),
                "total_protein": total_nutrition.get("protein_g", 0),
                "total_carbs": total_nutrition.get("carbs_g", 0),
                "total_fat": total_nutrition.get("fat_g", 0),
                "total_fiber": total_nutrition.get("fiber_g", 0),
                "total_sugar": total_nutrition.get("sugar_g", 0),
                "individual_items": individual_nutrition,
                "portions": portions,
                "confidence_level": confidence,
                "food_items": gpt_analysis.get("labels", [])
            }
        except Exception as e:
            logger.error(f"Failed to extract GPT nutrition data: {e}")
            return {
                "total_calories": 0,
                "total_protein": 0,
                "total_carbs": 0,
                "total_fat": 0,
                "confidence_level": "low",
                "error": "Failed to parse GPT nutrition data"
            }

    def _generate_expert_analysis(self, primary_nutrition: Dict[str, Any], enhanced_nutrition: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate expert nutritional analysis and recommendations.

        Args:
            primary_nutrition (dict): Primary nutrition data from GPT
            enhanced_nutrition (dict): Enhanced data from Edamam API

        Returns:
            dict: Expert analysis and recommendations
        """
        if not self.openrouter_api_key:
            return {"error": "OpenRouter API key not configured"}

        # Build comprehensive analysis prompt
        analysis_prompt = self._build_expert_analysis_prompt(primary_nutrition, enhanced_nutrition)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.openrouter_api_key}"
        }

        payload = {
            "model": "openai/gpt-4.1-nano-2025-04-14",
            "messages": [
                {
                    "role": "system",
                    "content": """You are a certified nutritionist. Provide brief, practical nutritional guidance in 150 words or less."""
                },
                {"role": "user", "content": analysis_prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 300
        }

        try:
            # Use configured session or fallback to requests
            session = self.http_session if self.http_session else requests
            
            response = session.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            response.raise_for_status()
            data = response.json()
            expert_response = data["choices"][0]["message"]["content"]

            return {
                "analysis": expert_response,
                "timestamp": datetime.now().isoformat(),
                "nutritional_balance": self._assess_nutritional_balance(primary_nutrition),
                "health_score": self._calculate_health_score(primary_nutrition)
            }
        except Exception as e:
            logger.error(f"Expert analysis generation failed: {str(e)}")
            return {"error": "Expert analysis generation failed", "details": str(e)}

    def _build_expert_analysis_prompt(self, primary_nutrition: Dict[str, Any], enhanced_nutrition: Dict[str, Any]) -> str:
        """Build expert analysis prompt with nutrition data."""

        total_calories = primary_nutrition.get("total_calories", 0)
        total_protein = primary_nutrition.get("total_protein", 0)
        total_carbs = primary_nutrition.get("total_carbs", 0)
        total_fat = primary_nutrition.get("total_fat", 0)
        confidence = primary_nutrition.get("confidence_level", "medium")
        food_items = primary_nutrition.get("food_items", [])

        prompt = f"""
NUTRITIONAL ANALYSIS REQUEST

MEAL: {', '.join(food_items)} ({confidence} confidence)
NUTRITION: {total_calories} cal, {total_protein}g protein, {total_carbs}g carbs, {total_fat}g fat

Provide a BRIEF analysis (max 150 words) covering:
1. **Balance Assessment** - Quick macro ratio evaluation
2. **Health Rating** - Score (1-10) with 1-2 key benefits/concerns
3. **Quick Recommendations** - 2-3 actionable suggestions

Keep it concise and practical. Focus on the most important insights only.
"""
        return prompt

    def _assess_nutritional_balance(self, nutrition_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the nutritional balance of the meal."""
        total_calories = nutrition_data.get("total_calories", 0)
        total_protein = nutrition_data.get("total_protein", 0)
        total_carbs = nutrition_data.get("total_carbs", 0)
        total_fat = nutrition_data.get("total_fat", 0)

        if total_calories == 0:
            return {"balance": "unknown", "reason": "No caloric data available"}

        # Calculate macronutrient percentages
        protein_calories = total_protein * 4
        carb_calories = total_carbs * 4
        fat_calories = total_fat * 9

        total_macro_calories = protein_calories + carb_calories + fat_calories

        if total_macro_calories == 0:
            return {"balance": "unknown", "reason": "No macronutrient data available"}

        protein_pct = (protein_calories / total_macro_calories) * 100
        carb_pct = (carb_calories / total_macro_calories) * 100
        fat_pct = (fat_calories / total_macro_calories) * 100

        # Assess balance based on general healthy ranges
        balance_score = "good"
        issues = []

        if protein_pct < 10:
            issues.append("Low protein content")
            balance_score = "needs_improvement"
        elif protein_pct > 35:
            issues.append("Very high protein content")

        if carb_pct < 20:
            issues.append("Very low carbohydrate content")
        elif carb_pct > 65:
            issues.append("High carbohydrate content")
            balance_score = "needs_improvement"

        if fat_pct < 15:
            issues.append("Low fat content")
        elif fat_pct > 40:
            issues.append("High fat content")
            balance_score = "needs_improvement"

        return {
            "balance": balance_score,
            "protein_percentage": round(protein_pct, 1),
            "carb_percentage": round(carb_pct, 1),
            "fat_percentage": round(fat_pct, 1),
            "issues": issues
        }

    def _calculate_health_score(self, nutrition_data: Dict[str, Any]) -> int:
        """Calculate a simple health score (1-10) based on nutritional content."""
        score = 5  # Start with neutral score

        total_calories = nutrition_data.get("total_calories", 0)
        total_protein = nutrition_data.get("total_protein", 0)
        total_fiber = nutrition_data.get("total_fiber", 0)
        total_sugar = nutrition_data.get("total_sugar", 0)

        # Adjust score based on various factors
        if total_protein >= 20:  # Good protein content
            score += 1
        elif total_protein >= 15:
            score += 0.5

        if total_fiber >= 5:  # Good fiber content
            score += 1
        elif total_fiber >= 3:
            score += 0.5

        if total_sugar <= 10:  # Low sugar content
            score += 1
        elif total_sugar <= 20:
            score += 0.5
        else:
            score -= 1  # High sugar penalty

        # Calorie considerations
        if 300 <= total_calories <= 800:  # Reasonable meal size
            score += 0.5
        elif total_calories > 1000:  # Very high calorie meal
            score -= 1

        return max(1, min(10, int(score)))

    def _parse_nutrition_info(self, api_data: dict) -> dict:
        """
        Parse and standardize nutrition info from Edamam API response.

        Args:
            api_data (dict): Raw API data.

        Returns:
            dict: Parsed nutrition info.
        """
        return {
            "calories": api_data.get("energy", 0),
            "protein": api_data.get("protein", 0),
            "carbs": api_data.get("carbohydrates", 0),
            "fat": api_data.get("fat", 0),
            "density": api_data.get("density", 1000)
        }

    def _enhance_with_gpt(self, nutrition_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use OpenRouter GPT-4.1-nano model to enhance or analyze nutrition data.

        Args:
            nutrition_data (dict): Nutrition data from Edamam API.

        Returns:
            dict: Enhanced nutrition analysis from GPT.
        """
        if not self.openrouter_api_key:
            return {"error": "OpenRouter API key not configured"}

        prompt = self._build_prompt(nutrition_data)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.openrouter_api_key}"
        }

        payload = {
            "model": "openai/gpt-4.1-nano-2025-04-14",
            "messages": [
                {
                    "role": "system",
                    "content": "You are a certified nutritionist and health expert. Provide detailed, accurate nutrition analysis and health insights."
                },
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 800
        }

        try:
            # Use configured session or fallback to requests
            session = self.http_session if self.http_session else requests
            
            response = session.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            response.raise_for_status()
            data = response.json()
            gpt_reply = data["choices"][0]["message"]["content"]

            # Store in session history
            self.session_history.append({
                "timestamp": datetime.now().isoformat(),
                "analysis": gpt_reply,
                "nutrition_data": nutrition_data
            })

            return {"gpt_response": gpt_reply, "analysis_timestamp": datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"OpenRouter GPT request failed: {str(e)}")
            return {"error": "OpenRouter GPT request failed", "details": str(e)}

    def _build_prompt(self, nutrition_data: Dict[str, Any]) -> str:
        """
        Build a comprehensive prompt string for GPT based on nutrition data.

        Args:
            nutrition_data (dict): Nutrition data from Edamam API.

        Returns:
            str: Detailed prompt string.
        """
        if not nutrition_data or "error" in nutrition_data:
            return "No valid nutrition data available to analyze."

        prompt_lines = [
            "As a certified nutritionist, analyze the following nutrition data and provide comprehensive insights:",
            "",
            "NUTRITION DATA:"
        ]

        total_calories = 0
        total_protein = 0
        total_carbs = 0
        total_fat = 0

        for food, data in nutrition_data.items():
            if data.get("status") != "no_data_available":
                prompt_lines.append(f"• {food.title()}:")
                calories = data.get('calories', 0)
                protein = data.get('protein', 0)
                carbs = data.get('carbs', 0)
                fat = data.get('fat', 0)

                prompt_lines.append(f"  - Calories: {calories}")
                prompt_lines.append(f"  - Protein: {protein}g")
                prompt_lines.append(f"  - Carbohydrates: {carbs}g")
                prompt_lines.append(f"  - Fat: {fat}g")

                total_calories += calories
                total_protein += protein
                total_carbs += carbs
                total_fat += fat
            else:
                prompt_lines.append(f"• {food.title()}: No nutrition data available")

        prompt_lines.extend([
            "",
            f"TOTAL MEAL SUMMARY:",
            f"Total Calories: {total_calories}",
            f"Total Protein: {total_protein}g",
            f"Total Carbohydrates: {total_carbs}g",
            f"Total Fat: {total_fat}g",
            "",
            "Please provide:",
            "1. Nutritional balance assessment",
            "2. Health benefits and concerns",
            "3. Recommendations for improvement",
            "4. Portion size evaluation",
            "5. Dietary considerations (allergies, restrictions)",
            "6. Overall meal rating (1-10) with explanation"
        ])

        return "\n".join(prompt_lines)

    def capture_and_analyze(self, image_source=None) -> Dict:
        """Capture image and analyze nutrition content"""
        try:
            if image_source is None:
                return {"success": False, "error": "No image source provided"}
            elif isinstance(image_source, bytes):
                # Use provided image bytes
                image_bytes = image_source
            elif isinstance(image_source, str):
                # Load from file path
                with open(image_source, "rb") as f:
                    image_bytes = f.read()
            elif hasattr(image_source, 'read'):
                # File-like object (e.g., Streamlit uploaded file)
                image_bytes = image_source.read()
            else:
                return {"success": False, "error": "Unsupported image source type"}

            # Analyze nutrition
            result = self.extract_nutrition(image_bytes)

            if "error" in result:
                return {"success": False, "error": result["error"], "details": result.get("details")}

            return {
                "success": True,
                "analysis": result,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Capture and analyze failed: {str(e)}")
            return {"success": False, "error": "Analysis failed", "details": str(e)}


def run_streamlit_app():
    """
    Run the Streamlit web interface for nutrition extraction.
    """
    import streamlit as st

    # Page configuration
    st.set_page_config(
        page_title="NutraCompass - AI Nutrition Analyzer",
        page_icon="🍎",
        layout="wide"
    )

    # Header
    st.title("🍎 NutraCompass - AI Nutrition Analyzer")
    st.markdown("*Powered by EagleEye AI, OpenRouter GPT-4.1-nano, and Edamam Nutrition Database*")

    # Initialize session state
    if 'extractor' not in st.session_state:
        with st.spinner("Initializing AI components..."):
            st.session_state.extractor = NutritionExtractor()

    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configuration")

        # Check component status
        extractor = st.session_state.extractor

        st.subheader("Component Status")
        if extractor.eagle_eye:
            st.success("✅ EagleEye AI - Ready")
        else:
            st.error("❌ EagleEye AI - Failed")

        if extractor.openrouter_api_key:
            st.success("✅ OpenRouter GPT-4.1-nano - Ready")
        else:
            st.warning("⚠️ OpenRouter API - Not configured")

        if extractor.api_integrator:
            st.success("✅ Edamam Nutrition API - Ready")
        else:
            st.error("❌ Nutrition API - Failed")

    # Main interface
    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("📸 Upload Food Image")

        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a food image...",
            type=["jpg", "jpeg", "png", "bmp", "gif"],
            help="Upload a clear image of food for AI analysis"
        )

        # Camera input option
        camera_input = st.camera_input("Or take a photo with your camera")

        # Use either uploaded file or camera input
        image_source = uploaded_file if uploaded_file is not None else camera_input

        if image_source is not None:
            # Display the image
            st.image(image_source, caption="Food Image", use_column_width=True)

            # Analysis button
            if st.button("🔍 Analyze Nutrition", type="primary", use_container_width=True):
                with st.spinner("🤖 AI is analyzing your food... This may take 30-60 seconds."):
                    try:
                        # Reset image to beginning of file before reading
                        image_source.seek(0)
                        image_bytes = image_source.read()

                        # Analyze using extract_nutrition method
                        result = st.session_state.extractor.extract_nutrition(image_bytes)

                        if "error" in result:
                            st.error(f"❌ Analysis failed: {result['error']}")
                            if "details" in result:
                                with st.expander("Error Details"):
                                    st.write(result["details"])
                        else:
                            st.session_state.analysis_result = result
                            st.success("✅ Analysis complete!")
                            st.rerun()

                    except Exception as e:
                        st.error(f"❌ An unexpected error occurred: {str(e)}")
                        logger.error(f"Streamlit app error: {str(e)}")
        else:
            st.info("👆 Please upload a food image or take a photo to get started!")

            # Instructions
            st.markdown("""
            ### 📋 How it works:
            1. **Upload** a clear, well-lit image of food
            2. **AI Vision** detects and identifies food items
            3. **Nutrition Database** retrieves detailed nutrition facts
            4. **Expert Analysis** provides personalized insights

            ### 📝 Tips for best results:
            - Use good lighting and clear focus
            - Include the entire food item in frame
            - Avoid cluttered backgrounds
            - Multiple food items are supported

            ### 🔧 Supported formats:
            JPG, JPEG, PNG, BMP, GIF
            """)

    with col2:
        st.subheader("📊 Analysis Results")

        if 'analysis_result' in st.session_state:
            result = st.session_state.analysis_result

            # Check if analysis was successful
            if not result.get("success", False):
                st.error(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
                if "details" in result:
                    with st.expander("Error Details"):
                        st.write(result["details"])
                return

            # Show detected food items
            primary_nutrition = result.get("primary_nutrition", {})
            food_items = primary_nutrition.get("food_items", [])
            confidence = primary_nutrition.get("confidence_level", "medium")

            if food_items:
                st.markdown("#### 🏷️ Detected Food Items")
                st.markdown(f"*Confidence Level: {confidence.title()}*")

                # Display as badges
                cols = st.columns(min(len(food_items), 3))
                for i, item in enumerate(food_items):
                    with cols[i % 3]:
                        st.markdown(f"<div style='background-color: #e1f5fe; padding: 8px; border-radius: 20px; text-align: center; margin: 4px; color: black;'><b>{item.title()}</b></div>", unsafe_allow_html=True)

            # Show primary nutrition data
            if primary_nutrition:
                st.markdown("#### 📊 Nutrition Information")

                # Display totals prominently
                st.markdown("**🍽️ Total Meal Summary:**")
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Total Calories", f"{primary_nutrition.get('total_calories', 0):.0f}")
                with col2:
                    st.metric("Total Protein", f"{primary_nutrition.get('total_protein', 0):.1f}g")
                with col3:
                    st.metric("Total Carbs", f"{primary_nutrition.get('total_carbs', 0):.1f}g")
                with col4:
                    st.metric("Total Fat", f"{primary_nutrition.get('total_fat', 0):.1f}g")

                # Additional nutrients if available
                if primary_nutrition.get('total_fiber', 0) > 0 or primary_nutrition.get('total_sugar', 0) > 0:
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Total Fiber", f"{primary_nutrition.get('total_fiber', 0):.1f}g")
                    with col2:
                        st.metric("Total Sugar", f"{primary_nutrition.get('total_sugar', 0):.1f}g")

                st.markdown("---")

                # Individual food items breakdown
                individual_items = primary_nutrition.get("individual_items", {})
                portions = primary_nutrition.get("portions", {})

                if individual_items:
                    st.markdown("**📋 Individual Food Items:**")
                    for food_item, nutrition in individual_items.items():
                        portion = portions.get(food_item, "Unknown portion")
                        with st.expander(f"🥗 {food_item.title()} ({portion})"):
                            col1, col2, col3, col4 = st.columns(4)
                            with col1:
                                st.metric("Calories", f"{nutrition.get('calories', 0):.0f}")
                            with col2:
                                st.metric("Protein", f"{nutrition.get('protein_g', 0):.1f}g")
                            with col3:
                                st.metric("Carbs", f"{nutrition.get('carbs_g', 0):.1f}g")
                            with col4:
                                st.metric("Fat", f"{nutrition.get('fat_g', 0):.1f}g")

                            if nutrition.get('fiber_g', 0) > 0 or nutrition.get('sugar_g', 0) > 0:
                                col1, col2 = st.columns(2)
                                with col1:
                                    st.metric("Fiber", f"{nutrition.get('fiber_g', 0):.1f}g")
                                with col2:
                                    st.metric("Sugar", f"{nutrition.get('sugar_g', 0):.1f}g")

            # Show expert analysis
            expert_analysis = result.get("expert_analysis", {})
            if expert_analysis and "analysis" in expert_analysis:
                st.markdown("#### 🤖 AI Nutrition Expert Analysis")
                # Display analysis in a more compact format
                analysis_text = expert_analysis["analysis"]
                # Limit display to first 500 characters if too long
                if len(analysis_text) > 500:
                    st.markdown(analysis_text[:500] + "...")
                    with st.expander("View Full Analysis"):
                        st.markdown(analysis_text)
                else:
                    st.markdown(analysis_text)

                # Show nutritional balance assessment
                balance_info = expert_analysis.get("nutritional_balance", {})
                if balance_info:
                    st.markdown("#### ⚖️ Nutritional Balance")
                    balance_score = balance_info.get("balance", "unknown")

                    if balance_score == "good":
                        st.success("✅ Good nutritional balance")
                    elif balance_score == "needs_improvement":
                        st.warning("⚠️ Nutritional balance could be improved")
                    else:
                        st.info("ℹ️ Nutritional balance assessment unavailable")

                    # Show macronutrient breakdown
                    if "protein_percentage" in balance_info:
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Protein %", f"{balance_info['protein_percentage']:.1f}%")
                        with col2:
                            st.metric("Carbs %", f"{balance_info['carb_percentage']:.1f}%")
                        with col3:
                            st.metric("Fat %", f"{balance_info['fat_percentage']:.1f}%")

                    # Show issues if any
                    issues = balance_info.get("issues", [])
                    if issues:
                        st.markdown("**⚠️ Areas for improvement:**")
                        for issue in issues:
                            st.markdown(f"• {issue}")

                # Show health score
                health_score = expert_analysis.get("health_score", 0)
                if health_score > 0:
                    st.markdown("#### 🏥 Health Score")
                    score_color = "green" if health_score >= 7 else "orange" if health_score >= 5 else "red"
                    st.markdown(f"<div style='font-size: 24px; color: {score_color}; font-weight: bold;'>{health_score}/10</div>", unsafe_allow_html=True)

                if "timestamp" in expert_analysis:
                    st.caption(f"Analysis completed: {expert_analysis['timestamp']}")

            # Raw data in expander
            with st.expander("🔍 View Technical Details"):
                st.json(result)

        else:
            st.info("🔄 Upload and analyze an image to see results here.")

            # Placeholder content
            st.markdown("""
            ### 🎯 What you'll get:

            **🏷️ Food Detection**
            - AI-powered food item identification
            - Multiple food recognition in single image

            **📊 Nutrition Facts**
            - Calories, protein, carbs, fat content
            - Individual and total meal breakdown
            - Accurate portion-based calculations

            **🤖 Expert Analysis**
            - Nutritional balance assessment
            - Health benefits and concerns
            - Personalized recommendations
            - Meal quality rating
            """)


if __name__ == "__main__":
    # Check if running with Streamlit by checking if streamlit is already imported
    if 'streamlit' in sys.modules or any('streamlit' in arg for arg in sys.argv):
        # Running with Streamlit
        run_streamlit_app()
    else:
        # Running from command line
        print("Usage:")
        print("  For Streamlit app: streamlit run nutrition_extractor.py")
        print("  For command line: python nutrition_extractor.py <image_path>")

        # If image path is provided, run command line analysis
        if len(sys.argv) > 1:
            image_path = sys.argv[1]
            with open(image_path, "rb") as f:
                image_bytes = f.read()

            extractor = NutritionExtractor()
            result = extractor.extract_nutrition(image_bytes)
            print(json.dumps(result, indent=2))
