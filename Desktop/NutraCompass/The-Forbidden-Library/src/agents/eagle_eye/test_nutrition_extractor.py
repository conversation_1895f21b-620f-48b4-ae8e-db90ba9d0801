#!/usr/bin/env python3
"""
Test script for the enhanced nutrition extractor.
This script tests the basic functionality without requiring all dependencies.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_environment_loading():
    """Test environment configuration loading."""
    print("🔧 Testing environment configuration...")
    
    try:
        from src.agents.eagle_eye.nutrition_extractor import NutritionExtractor
        
        # Create extractor instance
        extractor = NutritionExtractor()
        
        # Check if API keys are loaded
        if extractor.openrouter_api_key:
            print("✅ OpenRouter API key loaded successfully")
        else:
            print("⚠️ OpenRouter API key not found")
            
        if extractor.edamam_app_id and extractor.edamam_app_key:
            print("✅ Edamam API credentials loaded successfully")
        else:
            print("⚠️ Edamam API credentials not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Environment loading failed: {e}")
        return False

def test_gpt_analysis_parsing():
    """Test GPT analysis response parsing."""
    print("\n🧠 Testing GPT analysis parsing...")
    
    try:
        from src.agents.eagle_eye.nutrition_extractor import NutritionExtractor
        
        extractor = NutritionExtractor()
        
        # Mock GPT response
        mock_gpt_response = {
            "labels": ["grilled chicken breast", "steamed broccoli", "brown rice"],
            "portions": {
                "grilled chicken breast": "4 oz",
                "steamed broccoli": "1 cup",
                "brown rice": "0.5 cup"
            },
            "individual_nutrition": {
                "grilled chicken breast": {
                    "calories": 185,
                    "protein_g": 35,
                    "carbs_g": 0,
                    "fat_g": 4,
                    "fiber_g": 0,
                    "sugar_g": 0
                },
                "steamed broccoli": {
                    "calories": 25,
                    "protein_g": 3,
                    "carbs_g": 5,
                    "fat_g": 0,
                    "fiber_g": 3,
                    "sugar_g": 2
                },
                "brown rice": {
                    "calories": 110,
                    "protein_g": 2,
                    "carbs_g": 22,
                    "fat_g": 1,
                    "fiber_g": 2,
                    "sugar_g": 0
                }
            },
            "total_nutrition": {
                "calories": 320,
                "protein_g": 40,
                "carbs_g": 27,
                "fat_g": 5,
                "fiber_g": 5,
                "sugar_g": 2
            },
            "confidence_level": "high"
        }
        
        # Test parsing
        parsed_nutrition = extractor._extract_gpt_nutrition(mock_gpt_response)
        
        print(f"✅ Parsed nutrition data:")
        print(f"   Total Calories: {parsed_nutrition['total_calories']}")
        print(f"   Total Protein: {parsed_nutrition['total_protein']}g")
        print(f"   Total Carbs: {parsed_nutrition['total_carbs']}g")
        print(f"   Total Fat: {parsed_nutrition['total_fat']}g")
        print(f"   Confidence: {parsed_nutrition['confidence_level']}")
        print(f"   Food Items: {', '.join(parsed_nutrition['food_items'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ GPT analysis parsing failed: {e}")
        return False

def test_nutritional_balance_assessment():
    """Test nutritional balance assessment."""
    print("\n⚖️ Testing nutritional balance assessment...")
    
    try:
        from src.agents.eagle_eye.nutrition_extractor import NutritionExtractor
        
        extractor = NutritionExtractor()
        
        # Test data
        test_nutrition = {
            "total_calories": 320,
            "total_protein": 40,
            "total_carbs": 27,
            "total_fat": 5
        }
        
        balance_assessment = extractor._assess_nutritional_balance(test_nutrition)
        
        print(f"✅ Balance assessment:")
        print(f"   Overall Balance: {balance_assessment['balance']}")
        print(f"   Protein: {balance_assessment['protein_percentage']}%")
        print(f"   Carbs: {balance_assessment['carb_percentage']}%")
        print(f"   Fat: {balance_assessment['fat_percentage']}%")
        
        if balance_assessment['issues']:
            print(f"   Issues: {', '.join(balance_assessment['issues'])}")
        else:
            print("   No issues identified")
            
        return True
        
    except Exception as e:
        print(f"❌ Nutritional balance assessment failed: {e}")
        return False

def test_health_score_calculation():
    """Test health score calculation."""
    print("\n🏥 Testing health score calculation...")
    
    try:
        from src.agents.eagle_eye.nutrition_extractor import NutritionExtractor
        
        extractor = NutritionExtractor()
        
        # Test data
        test_nutrition = {
            "total_calories": 320,
            "total_protein": 40,
            "total_carbs": 27,
            "total_fat": 5,
            "total_fiber": 5,
            "total_sugar": 2
        }
        
        health_score = extractor._calculate_health_score(test_nutrition)
        
        print(f"✅ Health score: {health_score}/10")
        
        return True
        
    except Exception as e:
        print(f"❌ Health score calculation failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Enhanced Nutrition Extractor")
    print("=" * 50)
    
    tests = [
        test_environment_loading,
        test_gpt_analysis_parsing,
        test_nutritional_balance_assessment,
        test_health_score_calculation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The nutrition extractor is ready to use.")
        print("\n🚀 To run the Streamlit app:")
        print("   cd The-Forbidden-Library")
        print("   streamlit run src/agents/eagle_eye/nutrition_extractor.py")
    else:
        print("⚠️ Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    main()
