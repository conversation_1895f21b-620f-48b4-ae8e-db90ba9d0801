# src/config.py
from dotenv import load_dotenv
import os
import pathlib

# Compute the project root
project_root = pathlib.Path(__file__).parent.parent

# Environment configuration
env = os.environ.get("ENVIRONMENT", "development")
env_file = project_root / f".env.{env}"
env_file_no_dot = project_root / f"env.{env}"

# Load environment variables without debug output
if env_file.exists():
    load_dotenv(dotenv_path=str(env_file), verbose=False)
elif env_file_no_dot.exists():
    load_dotenv(dotenv_path=str(env_file_no_dot), verbose=False)
else:
    # Fallback to default .env file if exists
    default_env = project_root / ".env"
    if default_env.exists():
        load_dotenv(dotenv_path=str(default_env), verbose=False)

# API configurations (with defaults for optional ones)
EDAMAM_APP_ID = os.environ.get("EDAMAM_APP_ID", "")
EDAMAM_APP_KEY = os.environ.get("EDAMAM_APP_KEY", "")
EDAMAM_PARSER_BASE_URL = os.environ.get(
    "EDAMAM_PARSER_BASE_URL",
    "https://api.edamam.com/api/food-database/v2/parser"
)
EDAMAM_NUTRIENTS_BASE_URL = os.environ.get(
    "EDAMAM_NUTRIENTS_BASE_URL",
    "https://api.edamam.com/api/food-database/v2/nutrients"
)

# Security-sensitive configurations (with defaults)
FIREBASE_ADMIN_CREDENTIALS_JSON = os.environ.get("FIREBASE_ADMIN_CREDENTIALS_JSON", "")
CLOUD_VISION_CREDENTIALS_JSON = os.environ.get("CLOUD_VISION_CREDENTIALS_JSON", "")
ANTHROPIC_API_KEY = os.environ.get("ANTHROPIC_API_KEY", "")