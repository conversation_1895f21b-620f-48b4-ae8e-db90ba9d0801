const isWindows =
  process.env.IS_WINDOWS === "true" || process.platform === "win32";

module.exports = {
  adminConfig:
    process.env.NUTRA_ADMIN_JSON || process.env.FIREBASE_ADMIN_CREDENTIALS_JSON,
  storageBucket:
    process.env.NUTRA_STORAGE_BUCKET || process.env.FIREBASE_STORAGE_BUCKET,
  // Edamam Food DB Config
  edamamConfig: {
    APP_ID: process.env.EDAMAM_APP_ID,
    APP_KEY: process.env.EDAMAM_APP_KEY,
    PARSER_BASE_URL: "https://api.edamam.com/api/food-database/v2/parser",
    NUTRIENTS_BASE_URL: "https://api.edamam.com/api/food-database/v2/nutrients",
  },
  // USDA FoodData Central Config
  usdaConfig: {
    USDA_API_KEY: process.env.USDA_API_KEY,
    USDA_BASE_URL: "https://api.nal.usda.gov/fdc/v1",
  },
  redisConfig: {
    URL:
      isWindows && process.env.REDIS_URL_WINDOWS
        ? process.env.REDIS_URL_WINDOWS
        : process.env.REDIS_URL,
    RATE_LIMIT: {
      GLOBAL_WINDOW: parseInt(process.env.REDIS_RATE_WINDOW_GLOBAL) || 15,
      GLOBAL_MAX: parseInt(process.env.REDIS_RATE_MAX_GLOBAL) || 150,
      AUTH_WINDOW: parseInt(process.env.REDIS_AUTH_WINDOW) || 5,
      AUTH_MAX: parseInt(process.env.REDIS_AUTH_MAX) || 30,
    },
    CACHE: {
      DB_NUMBER: 1, // Different DB for caching
      TLS: process.env.NODE_ENV === "production",
      CONNECTION_TIMEOUT: 10000,
      POOL_SIZE:
        process.env.REDIS_POOL_SIZE ||
        (process.env.NODE_ENV === "production" ? 10 : 3),
      // Cost-optimized eviction policy
      MAX_MEMORY: process.env.REDIS_MAX_MEM || "256mb",
      MEMORY_POLICY: "allkeys-lru",
      // Connection recycling
      IDLE_TIMEOUT: 30000,
      CACHE_HMAC_SECRET: process.env.CACHE_HMAC_SECRET,
      ALLOWED_FLUSH_PATTERNS: [
        "foodLogEntries:*:*", // Explicit format requirement
      ],
      TTL: {
        // Analytics
        ANALYTICS: {
          MEMORY: {
            MAX_ITEMS: 1, // Only need latest analytics
            TTL: process.env.NODE_ENV === "development" ? 300 : 1800, // 5m/30m
          },
          REDIS: process.env.NODE_ENV === "development" ? 300 : 1800, // 5m/30m
        },
        // Food Diary
        FOOD_DIARY: {
          MEMORY: {
            TTL: process.env.NODE_ENV === "development" ? 300 : 7200, // 5m/2h (1:12 ratio)
            MAX_ITEMS: 1000,
          },
          REDIS: process.env.NODE_ENV === "development" ? 600 : 86400, // 10m/24h
        },

        // Food Diary Version
        FOOD_DIARY_VERSION: {
          MEMORY: {
            TTL: process.env.NODE_ENV === "development" ? 60 : 300, // 1m/5m
            MAX_ITEMS: 500,
          },
          REDIS: process.env.NODE_ENV === "development" ? 300 : 3600, // 5m/1h
        },

        // Food Search Query
        FOOD_SEARCH_QUERY: {
          MEMORY: {
            TTL: process.env.NODE_ENV === "development" ? 300 : 600, // 5m/10m (1:12 ratio)
            MAX_ITEMS: 500,
          },
          REDIS: process.env.NODE_ENV === "development" ? 600 : 7200, // 10m/2h
        },

        // Food Search Barcode
        FOOD_SEARCH_BARCODE: {
          MEMORY: {
            TTL: process.env.NODE_ENV === "development" ? 300 : 3600, // 5m/1h (1:12 ratio)
            MAX_ITEMS: 200,
          },
          REDIS: process.env.NODE_ENV === "development" ? 3600 : 259200, // 1h/3d
        },

        // Food Search Nutrient
        FOOD_SEARCH_NUTRIENT: {
          MEMORY: {
            TTL: process.env.NODE_ENV === "development" ? 150 : 1800, // 2.5m/30m (1:12 ratio)
            MAX_ITEMS: 300,
          },
          REDIS: process.env.NODE_ENV === "development" ? 1800 : 86400, // 30m/24h
        },
      },
    },
  },
  notionConfig: {
    NOTION_API_KEY: process.env.NOTION_API_KEY,
    NOTION_FEEDBACK_DB_ID: process.env.NOTION_FEEDBACK_DB_ID,
  },
};
