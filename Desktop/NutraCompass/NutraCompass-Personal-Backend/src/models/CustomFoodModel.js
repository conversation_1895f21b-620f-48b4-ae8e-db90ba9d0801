const Joi = require("joi");

// Allows any key/value pairs (replicates { strict: false } in Mongoose)
const NutrientSchema = Joi.object().unknown(true);

const MeasureSchema = Joi.object({
  label: Joi.string().required(),
  uri: Joi.string().required(),
  weight: Joi.number().default(1), // Default to 1 if not provided
});

const CustomFoodSchema = Joi.object({
  name: Joi.string().required(),
  foodId: Joi.string().optional(),
  brand: Joi.string().default("User Submitted"),
  foodCategory: Joi.string().default("Generic foods"),
  creatorId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/) // Validate MongoDB ObjectId format
    .required(),
  nutrients: NutrientSchema.required(),
  measures: Joi.array().items(MeasureSchema).default([]),
  activeMeasure: MeasureSchema.required(),
  date: Joi.string().optional(), // No format restriction unless specified
});

module.exports = {
  CustomFoodSchema,
  // Optional: Export nested schemas if needed elsewhere
  MeasureSchema,
  NutrientSchema,
};
