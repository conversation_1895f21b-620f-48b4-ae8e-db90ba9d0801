const FoodSearchService = require("../services/FoodSearchService.js");
const logger = require("../utils/logger.js");

class FoodSearchController {
  static async handleSearchQuery(req, res) {
    try {
      const { query, userId, page = 1 } = req.query;
      const pageNumber = Math.max(1, parseInt(page, 10)) || 1;

      // Validate required parameters
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "User ID required",
        });
      }

      if (!query?.trim()) {
        return res.status(400).json({
          success: false,
          message: "Search query required",
        });
      }

      const { data, pagination } = await FoodSearchService.searchFoodItems(
        query,
        userId,
        pageNumber
      );

      res.status(200).json({
        success: true,
        data,
        pagination,
      });
    } catch (error) {
      logger.error("Search error:", error);
      res.status(500).json({
        success: false,
        message:
          process.env.NODE_ENV === "production"
            ? "Search failed"
            : error.message,
      });
    }
  }

  static async handleBarcodeSearch(req, res) {
    try {
      const { code } = req.params;
      const { userId } = req.query;

      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "User ID required",
        });
      }

      if (!code?.match(/^\d+$/)) {
        return res.status(400).json({
          success: false,
          message: "Valid numeric barcode required",
        });
      }

      const results = await FoodSearchService.searchByBarcode(code, userId);

      res.status(200).json({
        success: true,
        data: results.data, // Return the array of food items
        $metadata: results.$metadata, // Preserve metadata if needed
      });
    } catch (error) {
      logger.error("Barcode error:", error);
      res.status(500).json({
        success: false,
        message: "Barcode scan failed",
      });
    }
  }

  static async handleServingSizeUpdate(req, res) {
    try {
      const { userId, foodItem, newServing } = req.body;

      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "User ID required",
        });
      }

      if (!foodItem?.foodId || !newServing?.uri) {
        return res.status(400).json({
          success: false,
          message: "Valid food item and serving size required",
        });
      }

      const updated = await FoodSearchService.updateServingSize(
        foodItem,
        newServing,
        userId
      );

      res.status(200).json({
        success: true,
        data: updated,
      });
    } catch (error) {
      logger.error("Serving update error:", error);
      res.status(500).json({
        success: false,
        message: "Serving update failed",
      });
    }
  }
}

module.exports = FoodSearchController;
