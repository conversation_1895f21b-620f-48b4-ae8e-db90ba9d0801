// NutritionalProgramController.js
const NutritionalProgramService = require("../services/NutritionalProgramService.js");
const { BasicProgramSchema } = require("../models/NutritionalProgramModels.js");
const logger = require("../utils/logger.js");

class NutritionalProgramController {
  /**
   * @desc Endpoint to calculate the daily nutritional goals based on user's body metrics, goal, and activity level.
   *
   * @param {Object} req - The HTTP request object.
   * @param {Object} req.body - The request body containing user data.
   * @param {string} req.body.sex - The biological sex of the user ("Male" or "Female").
   * @param {number|string} req.body.heightFeet - Height in feet.
   * @param {number|string} req.body.heightInches - Height in inches.
   * @param {number|string} req.body.weight - Current body weight in pounds.
   * @param {number|string} req.body.age - Age of the user.
   * @param {string} req.body.activityLevel - The activity level of the user.
   * @param {string} req.body.goal - The user's goal (e.g., "Maintain", "Lose", "Build").
   * @returns {Object} A response containing the calculated daily nutritional goals.
   */
  static async calculateDailyNutritionalGoals(req, res) {
    try {
      const {
        sex,
        heightFeet,
        heightInches,
        weight,
        age,
        activityLevel,
        goal,
      } = req.body;

      // ======= Enhanced Logging for Debugging =======
      logger.debug("Received Request to Calculate Daily Nutritional Goals:");
      logger.debug(`sex: ${sex} (Type: ${typeof sex})`);
      logger.debug(`heightFeet: ${heightFeet} (Type: ${typeof heightFeet})`);
      logger.debug(
        `heightInches: ${heightInches} (Type: ${typeof heightInches})`
      );
      logger.debug(`weight: ${weight} (Type: ${typeof weight})`);
      logger.debug(`age: ${age} (Type: ${typeof age})`);
      logger.debug(
        `activityLevel: ${activityLevel} (Type: ${typeof activityLevel})`
      );
      logger.debug(`goal: ${goal} (Type: ${typeof goal})`);
      // ================================================

      // Validate required fields (Removed type checks)
      if (
        !sex ||
        !heightFeet ||
        !heightInches ||
        !weight ||
        !age ||
        !activityLevel ||
        !goal
      ) {
        logger.error(
          "Validation Failed: Missing input fields for calculateDailyNutritionalGoals"
        );
        return res.status(400).json({
          success: false,
          message: "Missing input fields.",
        });
      }

      const nutritionalGoals =
        await NutritionalProgramService.calculateDailyNutritionalGoals(
          sex,
          heightFeet,
          heightInches,
          weight,
          age,
          activityLevel,
          goal
        );

      res
        .status(200)
        .json({ success: true, nutritionalGoals: nutritionalGoals });
    } catch (error) {
      logger.error("Error calculating nutritional goals: ", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * @desc Endpoint to generate a complete nutritional program based on `programType`.
   *       Uses Joi to validate the request body for each program type before calling the service.
   *
   * @param {Object} req - The Express request object
   * @param {Object} req.body - The request body containing user data
   * @returns {Object} Returns an HTTP response with either the generated program (success)
   *                   or an error message (failure).
   */
  static async generateNutritionalProgram(req, res) {
    try {
      const { programType } = req.body;
      if (!programType) {
        return res.status(400).json({
          success: false,
          message: "programType is required.",
        });
      }

      // Switch on programType to pick the right schema or placeholder
      switch (programType) {
        case "Basic": {
          // Validate against the BasicProgramSchema
          const { error, value } = BasicProgramSchema.validate(req.body, {
            abortEarly: false,
            convert: true, // This allows numeric strings to become numbers
          });

          if (error) {
            // Gather all error messages into one string
            const messages = error.details.map((d) => d.message).join(", ");
            logger.error("Validation failed for Basic program:", messages);
            return res.status(400).json({ success: false, message: messages });
          }

          // Now `value.startDate` and `value.endDate` are guaranteed strings in "YYYY-MM-DD" format.
          // Convert them to actual Date objects so the service can do date arithmetic and storage.
          const startDateObj = new Date(value.startDate + "T00:00:00Z");
          const endDateObj = new Date(value.endDate + "T00:00:00Z");

          // Check if they are valid dates
          if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
            return res.status(400).json({
              success: false,
              message: "Invalid startDate or endDate. Must be YYYY-MM-DD.",
            });
          }

          // If valid, call the service with fully validated and converted data
          const program =
            await NutritionalProgramService.generateNutritionalProgram({
              ...value, // all the other fields (sex, weight, etc.)
              startDate: startDateObj,
              endDate: endDateObj,
            });

          return res.status(200).json({ success: true, program });
        }

        case "AI Meal Plans":
          return res.status(501).json({
            success: false,
            message: "AI Meal Plans not yet implemented.",
          });

        case "Voice-Assisted Creation":
          return res.status(501).json({
            success: false,
            message: "Voice-Assisted not yet implemented.",
          });

        case "Food Tracker":
          return res.status(501).json({
            success: false,
            message: "Food Tracker not yet implemented.",
          });

        default:
          return res.status(400).json({
            success: false,
            message: `Unsupported programType: ${programType}`,
          });
      }
    } catch (error) {
      logger.error("Error generating program:", error.message);
      return res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * @desc Handles the activation of a user's nutritional program.
   * This method calls the service layer to save the provided program in the 'activeNutritionalPrograms' collection.
   *
   * @param {Object} req - The HTTP request object.
   * @param {string} req.body.userId - The user ID who is activating the program.
   * @param {Object} req.body.program - The nutritional program details to be activated.
   * @param {string} req.body.program.programName - The desired name for the nutritional program.
   * @returns {Object} A response indicating whether the activation was successful.
   */
  static async activateNutritionalProgram(req, res) {
    try {
      const { userId, program } = req.body;

      // Validate required fields
      if (!userId || !program) {
        return res.status(400).json({
          success: false,
          message: "Missing userId or program details.",
        });
      }

      // Validate that program includes programName
      if (!program.programName || program.programName.trim() === "") {
        return res.status(400).json({
          success: false,
          message: "Program name is required for activation.",
        });
      }

      // Call service to activate the program
      const activatedProgram = await NutritionalProgramService.activateProgram(
        userId,
        program
      );
      res.status(200).json({
        success: true,
        message: "Program activated",
        activatedProgram,
      });
    } catch (error) {
      logger.error("Error activating program:", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * @desc Handles the deactivation of a user's nutritional program.
   * This method moves the program from 'activeNutritionalPrograms' to 'inactiveNutritionalPrograms'.
   *
   * @param {Object} req - The HTTP request object.
   * @param {string} req.body.userId - The user ID who is deactivating the program.
   * @param {string} req.body.programId - The ID of the program to be deactivated.
   * @returns {Object} A response indicating whether the deactivation was successful.
   */
  static async deactivateNutritionalProgram(req, res) {
    try {
      const { userId, programId } = req.body;

      // Validate required fields
      if (!userId || !programId) {
        return res.status(400).json({
          success: false,
          message: "Missing userId or programId.",
        });
      }

      // Call service to deactivate the program
      const deactivatedProgram =
        await NutritionalProgramService.deactivateProgram(userId, programId);
      res.status(200).json({
        success: true,
        message: "Program deactivated",
        deactivatedProgram,
      });
    } catch (error) {
      logger.error("Error deactivating program:", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * Fetch the active nutritional program for a user.
   *
   * @param {Object} req - Express request object containing userId as a query param.
   * @param {Object} res - Express response object to return the active program.
   */
  static async getActiveProgram(req, res) {
    try {
      const { userId } = req.query;

      // Validate userId
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "Missing userId in query parameters.",
        });
      }

      const activeProgram = await NutritionalProgramService.getActiveProgram(
        userId
      );
      res.status(200).json(activeProgram);
    } catch (error) {
      logger.error("Error fetching active program:", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * Fetch all inactive programs for a user.
   *
   * @param {Object} req - Express request object containing userId as a query param.
   * @param {Object} res - Express response object to return the inactive programs.
   */
  static async getInactivePrograms(req, res) {
    try {
      const { userId } = req.query;

      // Validate userId
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "Missing userId in query parameters.",
        });
      }

      const inactivePrograms =
        await NutritionalProgramService.getInactivePrograms(userId);
      res.status(200).json(inactivePrograms);
    } catch (error) {
      logger.error("Error fetching inactive programs:", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * Handles the deletion of a single nutritional program.
   *
   * @param {Object} req - Express request object.
   * @param {Object} res - Express response object.
   */
  static async deleteNutritionalProgram(req, res) {
    const { userId, programId } = req.params;

    if (!userId || !programId) {
      return res
        .status(400)
        .json({ error: "User ID and Program ID are required." });
    }

    try {
      await NutritionalProgramService.deleteNutritionalProgram(
        userId,
        programId
      );
      return res
        .status(200)
        .json({ message: "Nutritional program deleted successfully." });
    } catch (error) {
      logger.error(
        "Error in deleteNutritionalProgram controller:",
        error.message
      );
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Handles the batch deletion of nutritional programs.
   *
   * @param {Object} req - Express request object.
   * @param {Object} res - Express response object.
   */
  static async deleteNutritionalProgramsBatch(req, res) {
    const { userId } = req.params;
    const { programIds } = req.body;

    if (!userId || !Array.isArray(programIds) || programIds.length === 0) {
      return res.status(400).json({
        error: "User ID and a non-empty array of Program IDs are required.",
      });
    }

    try {
      await NutritionalProgramService.deleteNutritionalProgramsBatch(
        userId,
        programIds
      );
      return res
        .status(200)
        .json({ message: "Nutritional programs deleted successfully." });
    } catch (error) {
      logger.error(
        "Error in deleteNutritionalProgramsBatch controller:",
        error.message
      );
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * @desc Logs the user's weight for a specific checkpoint.
   *
   * @param {Object} req - The HTTP request object.
   * @param {string} req.params.checkpointId - The ID of the checkpoint.
   * @param {string} req.body.userId - The ID of the user.
   * @param {number|string} req.body.userLoggedWeight - The user's logged weight for the checkpoint.
   * @returns {Object} A response indicating success or failure.
   */
  static async logCheckpointWeight(req, res) {
    try {
      const { checkpointId } = req.params;
      const { userId, userLoggedWeight } = req.body;

      // Validate required fields
      if (!userId || !checkpointId || userLoggedWeight === undefined) {
        return res.status(400).json({
          success: false,
          message:
            "Missing userId, checkpointId, or userLoggedWeight in request body.",
        });
      }

      // Convert userLoggedWeight to number if it's a string
      const parsedUserLoggedWeight = Number(userLoggedWeight);
      if (isNaN(parsedUserLoggedWeight)) {
        return res.status(400).json({
          success: false,
          message: "userLoggedWeight must be a number.",
        });
      }

      // Call service method to log the weight
      const updatedCheckpoint =
        await NutritionalProgramService.logCheckpointWeight(
          userId,
          checkpointId,
          parsedUserLoggedWeight
        );

      res.status(200).json({ success: true, checkpoint: updatedCheckpoint });
    } catch (error) {
      logger.error("Error logging checkpoint weight:", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * @route POST /v1/active-nutritional-program/checkpoint/:checkpointId/upload-progress-picture
   * @desc Uploads a progress picture for a specific checkpoint.
   * @access Authenticated
   *
   * @param {Object} req - The HTTP request object.
   * @param {string} req.params.checkpointId - The ID of the checkpoint.
   * @param {string} req.body.userId - The ID of the user.
   * @param {File} req.file - The image file uploaded.
   * @returns {Object} A response indicating success or failure, along with the URL of the uploaded picture.
   */
  static async uploadProgressPicture(req, res) {
    try {
      const { checkpointId } = req.params;
      const { userId } = req.body; // Assuming userId is passed in the body

      // Validate required fields
      if (!userId || !checkpointId) {
        return res.status(400).json({
          success: false,
          message: "Missing userId or checkpointId.",
        });
      }

      if (isNaN(checkpointId)) {
        return res.status(400).json({
          success: false,
          message: "Invalid checkpoint ID format",
        });
      }

      if (!req.file) {
        return res
          .status(400)
          .json({ success: false, message: "No image file provided." });
      }

      // Call the service to handle the file upload
      const progressPictureUrl =
        await NutritionalProgramService.uploadProgressPicture(
          userId,
          checkpointId,
          req.file
        );

      res.status(200).json({
        success: true,
        message: "Progress picture uploaded successfully",
        progressPictureUrl,
      });
    } catch (error) {
      logger.error("Error uploading progress picture:", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * @route DELETE /v1/active-nutritional-program/checkpoint/:checkpointId/delete-progress-picture
   * @desc Deletes a progress picture for a specific checkpoint.
   * @access Authenticated
   *
   * @param {Object} req - The HTTP request object.
   * @param {string} req.params.checkpointId - The ID of the checkpoint.
   * @param {string} req.body.userId - The ID of the user.
   * @returns {Object} A response indicating success or failure.
   */
  static async deleteProgressPicture(req, res) {
    try {
      const { checkpointId } = req.params;
      const { userId } = req.body; // Assuming userId is passed in the body

      // Validate required fields
      if (!userId || !checkpointId) {
        return res.status(400).json({
          success: false,
          message: "Missing userId or checkpointId.",
        });
      }

      // Call the service to delete the picture
      await NutritionalProgramService.deleteProgressPicture(
        userId,
        checkpointId
      );

      res.status(200).json({
        success: true,
        message: "Progress picture deleted successfully",
      });
    } catch (error) {
      logger.error("Error deleting progress picture:", error.message);
      res.status(500).json({ success: false, message: error.message });
    }
  }
}

module.exports = NutritionalProgramController;
