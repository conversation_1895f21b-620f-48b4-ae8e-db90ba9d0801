const CacheService = require("../services/CacheService.js");
const logger = require("../utils/logger.js");

class CacheController {
  static getCacheService() {
    if (!CacheController._service) {
      CacheController._service = require("../services/CacheService").instance;
    }
    return CacheController._service;
  }

  static async handleDeleteCacheEntry(req, res) {
    try {
      const { key } = req.params;

      if (!key || typeof key !== "string") {
        return res.status(400).json({
          success: false,
          message: "Invalid cache key format",
        });
      }

      const { tiersDeleted } = await CacheService.instance.delete(key);

      if (tiersDeleted === 0) {
        return res.status(404).json({
          success: false,
          message: "Cache entry not found in any tier",
          data: { key },
        });
      }

      res.status(200).json({
        success: true,
        message: "Cache entry deleted from tier(s)",
        data: {
          key,
          tiersDeleted,
        },
      });
    } catch (error) {
      logger.error(`Cache deletion error for ${req.params.key}:`, error);
      res.status(500).json({
        success: false,
        message: "Cache deletion failed",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  static async handleFlushUserCache(req, res) {
    try {
      const { userId } = req.params;
      if (!userId || typeof userId !== "string") {
        return res.status(400).json({
          success: false,
          message: "Invalid user ID format",
        });
      }

      const result = await CacheService.instance.flushUserData(userId);

      res.status(200).json({
        success: true,
        message: `Flushed cache for user ${userId}`,
        data: {
          userId,
          flushedCount: result.totalFlushed,
        },
      });
    } catch (error) {
      logger.error(`User cache flush error: ${error}`);
      res.status(500).json({
        success: false,
        message: "User cache flush failed",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  static async handleFlushCache(req, res) {
    try {
      const { pattern = "*" } = req.query;
      const result = await CacheService.instance.flushPattern(pattern);

      res.status(200).json({
        success: true,
        message: `Successfully flushed cache entries`,
        data: {
          pattern,
          flushedCount: result.totalFlushed,
        },
      });
    } catch (error) {
      logger.error("Cache flush error:", error);
      res.status(500).json({
        success: false,
        message: "Cache flush operation failed",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  static async handleGetCacheMetrics(req, res) {
    try {
      const metrics = CacheService.instance.getPerformanceSummary();

      res.status(200).json({
        success: true,
        message: "Cache metrics retrieved successfully",
        data: metrics,
      });
    } catch (error) {
      logger.error("Cache metrics error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve cache metrics",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }

  static async handleGetCacheHealth(req, res) {
    try {
      const healthStatus = await CacheService.instance.healthCheck();

      res.status(200).json({
        success: true,
        message: "Cache health check completed",
        data: healthStatus,
      });
    } catch (error) {
      logger.error("Cache health check error:", error);
      res.status(500).json({
        success: false,
        message: "Cache health check failed",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }
}

module.exports = CacheController;
