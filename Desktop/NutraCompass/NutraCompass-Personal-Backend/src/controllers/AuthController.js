const AuthService = require("../services/AuthService");
const TokenService = require("../services/TokenService");
const logger = require("../utils/logger");

class AuthController {
  // ================================
  // OAuth 2.0 Endpoints
  // ================================

  static async handleTokenRequest(req, res) {
    try {
      const { grant_type, username, password, refresh_token } = req.body;

      if (grant_type === "password") {
        // Password grant
        const result = await AuthService.authenticateUser(username, password);
        if (!result)
          return res.status(401).json({ error: "Invalid credentials" });
        res.json({
          access_token: result.access_token,
          refresh_token: result.refresh_token,
          expires_in: result.expires_in,
          token_type: "Bearer",
        });
      } else if (grant_type === "refresh_token") {
        // Refresh token grant
        const newTokens = await AuthService.refreshTokens(refresh_token);
        res.json({
          access_token: newTokens.access_token,
          refresh_token: newTokens.refresh_token,
          expires_in: newTokens.expires_in,
          token_type: "Bearer",
        });
      } else {
        res.status(400).json({ error: "Unsupported grant type" });
      }
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  static async handleTokenRevocation(req, res) {
    try {
      const { token, token_type_hint } = req.body;
      await AuthService.revokeToken(token, token_type_hint);
      res.json({ success: true });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  static async handleTokenIntrospection(req, res) {
    try {
      const { token, token_type_hint } = req.body;
      const result = await TokenService.introspectToken(token, token_type_hint);
      res.json(result);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  // ================================
  // User Management Endpoints
  // ================================

  static async registerUser(req, res) {
    try {
      const result = await AuthService.register(req.body);
      res.json({
        userId: result.userId,
        access_token: result.access_token,
        refresh_token: result.refresh_token,
        expires_in: result.expires_in,
      });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  static async authenticateUser(req, res) {
    try {
      const { email, password } = req.body;
      const result = await AuthService.authenticateUser(email, password);
      if (!result)
        return res.status(401).json({ error: "Invalid credentials" });
      res.json({
        userId: result.userId,
        access_token: result.access_token,
        refresh_token: result.refresh_token,
        expires_in: result.expires_in,
      });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  static async verifyUserSession(req, res) {
    try {
      const { idToken } = req.body;
      const decoded = await AuthService.verifyToken(idToken);
      res.json(decoded);
    } catch (error) {
      res.status(401).json({ error: "Invalid token" });
    }
  }

  // Google login endpoint
  static async googleLogin(req, res) {
    try {
      const { idToken } = req.body;
      const result = await AuthService.authenticateWithGoogle(idToken);
      res.json({
        userId: result.userId,
        access_token: result.access_token,
        refresh_token: result.refresh_token,
        expires_in: result.expires_in,
      });
    } catch (error) {
      res.status(401).json({ error: "Google authentication failed" });
    }
  }

  // Apple login endpoint
  static async appleLogin(req, res) {
    try {
      const { idToken, nonce, profile } = req.body;
      const result = await AuthService.authenticateWithApple(idToken, nonce, {
        firstName: profile?.firstName || "",
        lastName: profile?.lastName || "",
      });
      res.json({
        userId: result.userId,
        access_token: result.access_token,
        refresh_token: result.refresh_token,
        expires_in: result.expires_in,
      });
    } catch (error) {
      res.status(401).json({ error: "Apple authentication failed" });
    }
  }

  // ===================================
  // Subscription Management Endpoints
  // ===================================

  static async upgradeRole(req, res) {
    try {
      const { userId } = req.params;
      const { newRole } = req.body;

      await AuthService.upgradeUserRole(userId, newRole);
      res.json({ success: true });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  static async purchaseFeature(req, res) {
    try {
      const { userId } = req.params;
      const { featureId } = req.body;

      await AuthService.addPremiumFeature(userId, featureId);
      res.json({ success: true });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  static async checkFeatureAccess(req, res) {
    try {
      const { userId, featureId } = req.params;

      const hasAccess = await AuthService.checkFeatureAccess(userId, featureId);
      res.json({ hasAccess });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  /**
   * Handles password reset or change requests by generating a password reset link.
   * Sends the reset link to the user's email address.
   *
   * @param {Object} req - The HTTP request object containing the user's email.
   * @param {Object} res - The HTTP response object to send back a message.
   */
  static async resetPassword(req, res) {
    try {
      const { email, firstName } = req.body;
      if (!email) return res.status(400).json({ message: "Email is required" });

      await AuthService.resetPassword(email, firstName);
      res.status(200).json({
        message: "Password reset email sent successfully.",
      });
    } catch (error) {
      logger.error("Password reset failed:", error);
      res.status(500).json({
        message: "Failed to process password reset.",
        error: error.message,
      });
    }
  }

  /**
   * Handles the deletion of a user's account and all associated data.
   * This method ensures that both authentication records and Firestore data are deleted.
   *
   * @param {Object} req - The HTTP request object, expected to contain `userId` in the body.
   * @param {Object} res - The HTTP response object to return a success or error message.
   */
  static async deleteAccount(req, res) {
    try {
      const { userId, firstName } = req.body;
      if (!userId)
        return res.status(400).json({ message: "User ID is required" });

      await AuthService.deleteUserAccount(userId, firstName);
      res.status(200).json({ message: "Account deleted successfully." });
    } catch (error) {
      logger.error("Account deletion failed:", error);
      res.status(500).json({
        message: "Failed to delete account.",
        error: error.message,
      });
    }
  }

  /**
   * Checks if an email already exists in Firebase Authentication.
   *
   * @param {Object} req - The HTTP request object containing the email in the body.
   * @param {Object} res - The HTTP response object used to send back the existence status.
   */
  static async checkEmailExists(req, res) {
    try {
      const { email } = req.body;
      if (!email) return res.status(400).json({ error: "Email is required." });

      const exists = await AuthService.checkEmailExists(email);
      res.status(200).json({ exists });
    } catch (error) {
      logger.error("Email check failed:", error);
      res.status(500).json({ error: "Failed to check email existence." });
    }
  }

  /**
   * Sends a verification code to the user's email.
   * This method generates a one-time code and emails it to the provided address.
   *
   * @param {Object} req - The HTTP request object containing the user's email.
   * @param {Object} res - The HTTP response object used to send success or error messages.
   */
  static async sendVerificationCode(req, res) {
    try {
      const { email, firstName } = req.body;
      if (!email)
        return res.status(400).json({ message: "Email is required." });

      await AuthService.sendVerificationCode(email, firstName);
      res.status(200).json({ message: "Verification code sent successfully." });
    } catch (error) {
      logger.error("Verification code send failed:", error);
      res.status(500).json({
        message: "Failed to send verification code.",
        error: error.message,
      });
    }
  }

  /**
   * Verifies the code entered by the user to confirm their email.
   * Validates the provided code against the generated code in the system.
   *
   * @param {Object} req - The HTTP request object containing the email and code.
   * @param {Object} res - The HTTP response object used to send success or error messages.
   */
  static async verifyCode(req, res) {
    try {
      const { email, code } = req.body;
      if (!email || !code) {
        return res
          .status(400)
          .json({ message: "Both email and code are required." });
      }

      const isVerified = await AuthService.verifyCode(email, code);
      if (isVerified) {
        res.status(200).json({ message: "Verification successful." });
      } else {
        res.status(400).json({ message: "Invalid verification code." });
      }
    } catch (error) {
      logger.error("Code verification failed:", error);
      res.status(500).json({
        message: "Failed to verify code.",
        error: error.message,
      });
    }
  }

  /**
   * Resends a verification code to the user's email.
   * Generates a new one-time code and emails it to the user.
   *
   * @param {Object} req - The HTTP request object containing the user's email.
   * @param {Object} res - The HTTP response object used to send success or error messages.
   */
  static async resendCode(req, res) {
    try {
      const { email, firstName } = req.body;
      if (!email)
        return res.status(400).json({ message: "Email is required." });

      await AuthService.sendVerificationCode(email, firstName);
      res
        .status(200)
        .json({ message: "Verification code resent successfully." });
    } catch (error) {
      logger.error("Code resend failed:", error);
      res.status(500).json({
        message: "Failed to resend verification code.",
        error: error.message,
      });
    }
  }
}

module.exports = AuthController;
