const AnalyticsService = require("../services/AnalyticsService")
const logger = require("../utils/logger");

class AnalyticsController {
  /**
   * Gets comprehensive analytics data
   */
  static async getAnalytics(req, res) {
    try {
      const analytics = await AnalyticsService.getComprehensiveAnalytics();
      res.status(200).json(analytics);
    } catch (error) {
      logger.error("Error getting analytics:", error);
      res.status(500).json({ error: "Failed to get analytics data" });
    }
  }

  /**
   * Gets just the user count
   */
  static async getUserCount(req, res) {
    try {
      const userCount = await AnalyticsService.getUserCount();
      res.status(200).json({ count: userCount });
    } catch (error) {
      logger.error("Error getting user count:", error);
      res.status(500).json({ error: "Failed to get user count" });
    }
  }
}

module.exports = AnalyticsController;