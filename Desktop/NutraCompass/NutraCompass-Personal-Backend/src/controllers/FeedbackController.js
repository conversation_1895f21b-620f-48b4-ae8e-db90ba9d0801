const FeedbackService = require("../services/FeedbackService");
const logger = require("../utils/logger");

class FeedbackController {
  /**
   * Submits feedback to Notion
   */
  static async submitFeedback(req, res) {
    try {
      const { userId, name, email, message, timestamp } = req.body;

      // Validate input
      if (!userId || !name || !email || !message || !timestamp) {
        return res.status(400).json({ error: "Missing required fields" });
      }
      if (message.length > 500) {
        return res
          .status(400)
          .json({ error: "Message exceeds 500 characters" });
      }

      await FeedbackService.sendToNotion({
        userId,
        name,
        email,
        message,
        timestamp,
      });

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error("Feedback submission error:", error);
      res.status(500).json({
        error: error.message || "Failed to submit feedback",
      });
    }
  }
}

module.exports = FeedbackController;
