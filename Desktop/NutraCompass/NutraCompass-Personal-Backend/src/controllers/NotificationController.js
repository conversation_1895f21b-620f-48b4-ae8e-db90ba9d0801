const NotificationService = require("../services/NotificationService.js");
const logger = require("../utils/logger.js");

/**
 * Notification Settings Controller
 * Responsibilities:
 * 1. API Gateway: Handle HTTP request/response lifecycle
 * 2. Input Validation: Protect downstream services from malformed data
 * 3. Response Standardization: Enforce consistent API response formats
 * 4. Error Handling: Centralize error processing and logging
 *
 * Design Principles:
 * - Single Responsibility: Each method handles one specific task
 * - Separation of Concerns: No business logic resides here
 * - Fail Fast: Validate inputs at entry points
 * - Auditability: Detailed error logging with context
 */
class NotificationController {
  // =============================================
  // Core Notification Configuration Endpoints
  // =============================================

  /**
   * Full Settings Update Endpoint
   * Strategy:
   * - Accept atomic settings updates with optional push token refresh
   * - Maintain backward compatibility during migration period
   * - Allow phased client updates without breaking changes
   *
   * @param {Object} req - Express request {params: {userId}, body: {settings, expoPushToken?}}
   * @param {Object} res - Express response
   */
  static async updateNotificationSettings(req, res) {
    const { userId } = NotificationController._validateRequestParams(req);
    const { settings, expoPushToken } = NotificationController._sanitizeInput(
      req.body
    );

    try {
      NotificationController._validateSettingsStructure(settings);

      // Add version validation using your existing pattern
      if (typeof settings._version !== "number" || settings._version < 0) {
        throw new ValidationError("Version must be a positive integer", {
          receivedType: typeof settings._version,
          receivedValue: settings._version,
        });
      }

      const result = await NotificationService.updateNotificationSettings(
        userId,
        settings,
        expoPushToken // Optional token handling
      );

      NotificationController._sendSuccess(res, {
        message: "Notification settings updated successfully",
        scheduledNotifications: result,
        location: NotificationController._buildResourceLocation(userId),
      });
    } catch (error) {
      NotificationController._handleError(
        res,
        error,
        "update notification settings"
      );
    }
  }

  // =============================================
  // Device Registration Endpoints
  // =============================================

  /**
   * Push Token Registration Endpoint
   * Security Considerations:
   * - Rate limiting should be implemented at the route level
   * - Token validation prevents poisoned push notification queues
   *
   * @param {Object} req - Express request {params: {userId}, body: {expoPushToken}}
   */
  static async registerPushToken(req, res) {
    const { userId } = NotificationController._validateRequestParams(req);
    const { expoPushToken } = NotificationController._sanitizeInput(req.body);

    try {
      NotificationController._validatePushToken(expoPushToken);

      await NotificationService.storePushToken(userId, expoPushToken);

      NotificationController._sendSuccess(res, {
        message: "Push token registered successfully",
        location: NotificationController._buildTokenLocation(userId),
      });
    } catch (error) {
      NotificationController._handleError(res, error, "register push token");
    }
  }

  // =============================================
  // State Retrieval Endpoints
  // =============================================

  /**
   * Settings Retrieval Endpoint
   * Cache Strategy:
   * - Client-side caching via ETag
   * - Server-side cache invalidated on updates
   *
   * @param {Object} req - Express request {params: {userId}}
   */
  static async getNotificationSettings(req, res) {
    const { userId } = NotificationController._validateRequestParams(req);

    // 1. Extract and validate version parameter
    const rawVersion = req.query.versionCheck;
    let clientVersion;

    if (typeof rawVersion !== "undefined") {
      clientVersion = Number(rawVersion);

      if (!Number.isInteger(clientVersion)) {
        throw new ValidationError("versionCheck must be an integer", {
          receivedValue: rawVersion,
          example: 5,
        });
      }
    }

    try {
      // 2. Get settings with version check
      const settings = await NotificationService.getNotificationSettings(
        userId,
        {
          versionCheck: clientVersion,
        }
      );

      // 3. Handle missing settings
      if (!settings) {
        return NotificationController._sendNotFound(
          res,
          `Notification settings for user ${userId}`,
          NotificationController._buildResourceLocation(userId)
        );
      }

      // 4. Version comparison logic
      if (typeof clientVersion === "number") {
        if (settings._version <= clientVersion) {
          return res.status(304).end(); // Not Modified
        }
      }

      // 5. Return updated settings
      NotificationController._sendSuccess(res, {
        message: "Settings retrieved successfully",
        settings: NotificationController._sanitizeInput(settings),
        location: NotificationController._buildResourceLocation(userId),
      });
    } catch (error) {
      NotificationController._handleError(
        res,
        error,
        "retrieve notification settings"
      );
    }
  }

  // =============================================
  // Notification Lifecycle Endpoints
  // =============================================

  /**
   * Meal Reminder Cancellation Endpoint
   * Idempotency:
   * - Safe to retry - non-existent notifications are ignored
   * - Partial success supported with detailed response
   *
   * @param {Object} req - Express request {params: {userId}, body: {mealIds}}
   */
  static async cancelMealReminders(req, res) {
    const { userId } = NotificationController._validateRequestParams(req);
    const { mealIds } = NotificationController._sanitizeInput(req.body);

    try {
      NotificationController._validateCancellationParameters(mealIds);

      const result = await NotificationService.cancelMealReminders(
        userId,
        mealIds
      );

      NotificationController._sendSuccess(res, {
        ...result,
        location: NotificationController._buildResourceLocation(userId),
      });
    } catch (error) {
      NotificationController._handleError(res, error, "cancel meal reminders");
    }
  }

  // =============================================
  // Validation & Sanitization Layer
  // =============================================

  /**
   * Core Parameter Validation
   * Security: Basic protection against malformed requests
   */
  static _validateRequestParams(req) {
    // Only validate user ID format
    if (!/^[a-zA-Z0-9_-]{20,}$/.test(req.params.userId)) {
      throw new ValidationError("Invalid user ID format", {
        statusCode: 400,
        received: req.params.userId,
      });
    }

    return { userId: req.params.userId };
  }

  /**
   * Settings Structure Validation
   * Schema Enforcement: Ensures valid transformation map for service layer
   */
  static _validateSettingsStructure(settings) {
    if (!settings || typeof settings !== "object" || Array.isArray(settings)) {
      throw new ValidationError(
        "Invalid settings format: Expected non-array object",
        { receivedType: typeof settings }
      );
    }
  }

  /**
   * Push Token Validation
   * Format Checking: Basic pattern matching for Expo tokens
   */
  static _validatePushToken(token) {
    if (!token || typeof token !== "string") {
      throw new ValidationError("Invalid Expo push token format", {
        expectedType: "string",
        receivedType: typeof token,
      });
    }
  }

  /**
   * Cancellation Parameter Validation
   * Business Rules: Enforces minimum viable request format
   */
  static _validateCancellationParameters(mealIds) {
    const MAX_MEAL_IDS = 100;
    if (
      !Array.isArray(mealIds) ||
      mealIds.length > MAX_MEAL_IDS ||
      mealIds.length === 0
    ) {
      throw new ValidationError(
        `Maximum ${MAX_MEAL_IDS} meals per request and meal IDs array length must be greater than 0`
      );
    }
  }

  // =============================================
  // Response Handling Layer
  // =============================================

  /**
   * Success Response Handler
   * Standardization: Enforces consistent success response format
   */
  static _sendSuccess(res, data, statusCode = 200) {
    res.status(statusCode).json({
      success: true,
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Not Found Handler
   * Discoverability: Provides resource location hint for valid resources
   */
  static _sendNotFound(res, resourceName, location) {
    res.status(404).json({
      success: false,
      message: `${resourceName} not found`,
      location,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Centralized Error Handler
   * Observability: Captures full error context for diagnostics
   * Security: Obfuscates internal details in production
   */
  static _handleError(res, error, context) {
    const errorId = `ERR-${Date.now()}-${Math.random()
      .toString(36)
      .slice(2, 6)}`;

    // Server-side logging (unchanged from your original)
    logger.error(`[${errorId}] ${context} error:`, {
      message: error.message,
      stack: error.stack,
      details: error.details,
    });

    // Client response structure (maintains original fields)
    const statusCode = error.statusCode || 500;
    const response = {
      success: false,
      message: error.message, // Preserve original message
      errorId,
      timestamp: new Date().toISOString(),
    };

    // Add only SECURITY CRITICAL changes
    if (process.env.NODE_ENV === "production") {
      if (statusCode === 500) {
        response.message = "An unexpected error occurred";
      }
      // Remove stack traces in production
      delete response.stack;
    } else {
      // Development enhancements (non-breaking)
      response.debug = {
        type: error.name,
        stack: error.stack,
        ...(error.details && { details: error.details }),
      };
    }

    res.status(statusCode).json(response);
  }

  // =============================================
  // Utility Methods
  // =============================================

  /**
   * Input Sanitization
   * Security: Basic XSS prevention through serialization
   * Performance: Lightweight sanitization for high throughput
   */
  static _sanitizeInput(input) {
    // Simple deep clone without HTML processing
    return JSON.parse(JSON.stringify(input));
  }

  /**
   * Resource Location Builder
   * REST Compliance: Enables HATEOAS-style discoverability
   */
  static _buildResourceLocation(userId) {
    return `/users/${userId}/notifications/settings`;
  }

  /**
   * Token Location Builder
   * API Discoverability: Guides clients to related endpoints
   */
  static _buildTokenLocation(userId) {
    return `/users/${userId}/push-tokens`;
  }
}

// =============================================
// Domain-Specific Error Classes
// =============================================

/**
 * Validation Error Structure
 * Enables precise error handling and client feedback
 */
class ValidationError extends Error {
  constructor(message, details = {}) {
    super(message);
    this.name = "ValidationError";
    this.statusCode = 400;
    this.details = details;
  }
}

module.exports = NotificationController;
