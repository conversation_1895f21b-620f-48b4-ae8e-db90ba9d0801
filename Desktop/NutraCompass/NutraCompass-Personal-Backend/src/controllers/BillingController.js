// controllers/BillingController.js
const SubscriptionService = require("../services/SubscriptionService");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const logger = require("../utils/logger");

class BillingController {
  static async createSubscription(req, res) {
    try {
      const { userId } = req.user;
      const { priceId, paymentMethodId } = req.body;

      // Validate input
      if (!priceId || !paymentMethodId) {
        return res.status(400).json({ error: "Missing required parameters" });
      }

      // Create Stripe customer if needed
      const userDoc = await admin.firestore().doc(`users/${userId}`).get();
      const userData = userDoc.data();
      let customerId = userData.account?.stripeCustomerId;

      if (!customerId) {
        const customer = await stripe.customers.create({
          email: userData.profile.email,
          name: `${userData.profile.firstName} ${userData.profile.lastName}`,
          payment_method: paymentMethodId,
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });

        customerId = customer.id;
        await admin.firestore().doc(`users/${userId}`).update({
          "account.stripeCustomerId": customerId,
        });
      }

      // Create subscription
      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        expand: ["latest_invoice.payment_intent"],
      });

      res.json({
        id: subscription.id,
        status: subscription.status,
        latestInvoice: subscription.latest_invoice,
      });
    } catch (error) {
      logger.error("Subscription creation failed:", error);
      res.status(400).json({ error: error.message });
    }
  }

  static async updatePaymentMethod(req, res) {
    try {
      const { userId } = req.user;
      const { paymentMethodId } = req.body;

      // Validate input
      if (!paymentMethodId) {
        return res.status(400).json({ error: "Payment method ID required" });
      }

      const userDoc = await admin.firestore().doc(`users/${userId}`).get();
      const customerId = userDoc.data()?.account?.stripeCustomerId;

      if (!customerId) {
        return res.status(400).json({ error: "Customer not found" });
      }

      // Attach payment method to customer
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      // Set as default payment method
      await stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

      res.json({ success: true });
    } catch (error) {
      logger.error("Payment method update failed:", error);
      res.status(400).json({ error: error.message });
    }
  }

  static async handleWebhook(req, res) {
    const sig = req.headers["stripe-signature"];
    let event;

    try {
      event = stripe.webhooks.constructEvent(
        req.rawBody,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      logger.error("Webhook verification failed:", err);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
      switch (event.type) {
        case "invoice.payment_succeeded":
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case "customer.subscription.updated":
          await this.handleSubscriptionUpdate(event.data.object);
          break;
        case "invoice.payment_failed":
          await this.handlePaymentFailed(event.data.object);
          break;
      }

      res.json({ received: true });
    } catch (error) {
      logger.error("Webhook handler failed:", error);
      res.status(500).json({ error: "Webhook processing failed" });
    }
  }

  static async handlePaymentSucceeded(invoice) {
    const subscriptionId = invoice.subscription;
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const userId = subscription.metadata.userId;

    if (!userId) return;

    // Update subscription in Firestore
    await SubscriptionService.updateSubscriptionStatus(
      userId,
      subscriptionId,
      "active"
    );
  }

  static async handleSubscriptionUpdate(subscription) {
    const userId = subscription.metadata.userId;
    if (!userId) return;

    if (subscription.status === "active") {
      await SubscriptionService.updateSubscriptionStatus(
        userId,
        subscription.id,
        "active"
      );
    } else if (subscription.status === "past_due") {
      await SubscriptionService.updateSubscriptionStatus(
        userId,
        subscription.id,
        "past_due"
      );
    }
  }

  static async handlePaymentFailed(invoice) {
    const subscriptionId = invoice.subscription;
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const userId = subscription.metadata.userId;

    if (!userId) return;

    // Update subscription status
    await SubscriptionService.updateSubscriptionStatus(
      userId,
      subscriptionId,
      "payment_failed"
    );
  }
}

module.exports = BillingController;
