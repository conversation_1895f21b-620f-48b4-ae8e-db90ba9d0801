const admin = require("../config/firebase-admin");
const db = admin.firestore();
const logger = require("../utils/logger.js");

class FriendManagementService {
  constructor() {
    this.db = db; // Utilizing Firestore database
  }

  /**
   * Fetches a list of recommended users, excluding the requester and anyone with whom they have a pending friend request,
   * existing friendship, or a block relationship.
   * This method fetches users in batches, filters out exclusions, and continues fetching until the desired number of recommendations is met.
   *
   * @param {string} requesterId - ID of the user requesting recommendations.
   * @param {number} limit - Maximum number of recommended users to return.
   * @returns {Promise<Array>} An array of user objects, excluding the requester and their connected users, ensuring user privacy and preferences are respected.
   */
  async getRecommendedUsers(requesterId, limit = 50) {
    const exclusions = new Set([requesterId]);
    const results = [];

    // Fetch exclusions from specified subcollections
    const exclusionRefs = [
      "friends",
      "outgoingRequests",
      "incomingRequests",
      // "blocked",
    ];
    await Promise.all(
      exclusionRefs.map((ref) =>
        this.db
          .collection(`users/${requesterId}/${ref}`)
          .get()
          .then((snapshot) => snapshot.forEach((doc) => exclusions.add(doc.id)))
      )
    );

    // Fetch users who have blocked the requester
    // try {
    //   const blockedByOthersSnapshot = await this.db
    //     .collectionGroup("blocked")
    //     .where("blockedUserId", "==", requesterId)
    //     .get();
    //   blockedByOthersSnapshot.forEach((doc) => {
    //     exclusions.add(doc.ref.parent.parent.id); // Assuming the structure /users/{userId}/blocked/{blockedUserId}
    //   });
    // } catch (error) {
    //   logger.error("Error querying blocked users by others:", error);
    // }

    // Fetch users excluding the ones in exclusions
    try {
      while (results.length < limit) {
        const querySnapshot = await this.db
          .collection("users")
          .where(
            admin.firestore.FieldPath.documentId(),
            "not-in",
            Array.from(exclusions)
          )
          .limit(50)
          .get();
        querySnapshot.docs.forEach((doc) => {
          if (results.length < limit) {
            results.push({
              id: doc.id,
              profile: {
                firstName: doc.data().profile.firstName,
                lastName: doc.data().profile.lastName,
                userName: doc.data().profile.userName,
                userNameLower: doc.data().profile.userNameLower,
                pictureUrl: doc.data().profile.pictureUrl || "",
              },
            });
          }
        });

        if (querySnapshot.empty || querySnapshot.size < 50) break;
      }
    } catch (error) {
      logger.error("Error fetching recommended users:", error);
    }

    return results;
  }

  /**
   * Searches users based on a given text query, excluding the requester and anyone with whom they have a pending friend request, existing friendship, or a block relationship.
   * Continuously fetches and filters user batches based on search criteria and exclusions until enough results are found or the user pool is exhausted.
   *
   * @param {string} requesterId - ID of the user making the request.
   * @param {string} searchQuery - Text query for user search.
   * @returns {Promise<Array>} An array of user objects that match the search query, excluding the requester and connected users.
   */
  async searchUsers(requesterId, searchQuery) {
    const exclusions = new Set([requesterId]);
    const results = [];
    const normalizedQuery = searchQuery.trim().toLowerCase();

    const exclusionRefs = [
      "friends",
      "outgoingRequests",
      "incomingRequests",
      // "blocked",
    ];
    await Promise.all(
      exclusionRefs.map((ref) =>
        this.db
          .collection(`users/${requesterId}/${ref}`)
          .get()
          .then((snapshot) => snapshot.forEach((doc) => exclusions.add(doc.id)))
      )
    );

    // Include users who have blocked the requester
    // try {
    //   const blockedByOthersSnapshot = await this.db
    //     .collectionGroup("blocked")
    //     .where("blockedUserId", "==", requesterId)
    //     .get();
    //   blockedByOthersSnapshot.forEach((doc) => {
    //     exclusions.add(doc.ref.parent.parent.id);
    //   });
    // } catch (error) {
    //   logger.error("Error querying users who blocked the requester:", error);
    // }

    try {
      while (results.length < 50) {
        const querySnapshot = await this.db
          .collection("users")
          .where("profile.userNameLower", ">=", normalizedQuery)
          .where(
            "profile.userNameLower",
            "<",
            normalizedQuery.replace(/.$/, (c) =>
              String.fromCharCode(c.charCodeAt(0) + 1)
            )
          )
          .where(
            admin.firestore.FieldPath.documentId(),
            "not-in",
            Array.from(exclusions)
          )
          .limit(100)
          .get();

        querySnapshot.docs.forEach((doc) => {
          if (!exclusions.has(doc.id) && results.length < 50) {
            results.push({
              id: doc.id,
              profile: {
                firstName: doc.data().profile.firstName,
                lastName: doc.data().profile.lastName,
                userName: doc.data().profile.userName,
                userNameLower: doc.data().profile.userNameLower,
                pictureUrl: doc.data().profile.pictureUrl || "",
              },
            });
          }
        });

        if (querySnapshot.empty || querySnapshot.size < 100) break;
      }
    } catch (error) {
      logger.error("Error fetching search results:", error);
    }

    return results;
  }

  /**
   * Sends a friend request from one user to another and return the newly created request document.
   * @param {string} senderId - ID of the user sending the friend request.
   * @param {string} receiverId - ID of the user receiving the friend request.
   * @returns {Promise<Object>} Confirmation of the friend request sent.
   */
  async sendFriendRequest(senderId, receiverId) {
    try {
      const senderRef = this.db.doc(`users/${senderId}`);
      const receiverRef = this.db.doc(`users/${receiverId}`);

      const [senderSnap, receiverSnap] = await Promise.all([
        senderRef.get(),
        receiverRef.get(),
      ]);

      if (!senderSnap.exists || !receiverSnap.exists) {
        throw new Error("One or both users do not exist.");
      }

      const senderData = senderSnap.data();
      const receiverData = receiverSnap.data();

      const senderProfile = {
        firstName: senderData.profile.firstName,
        lastName: senderData.profile.lastName,
        userName: senderData.profile.userName,
        userNameLower: senderData.profile.userNameLower,
        pictureUrl: senderData.profile.pictureUrl || "",
      };

      const receiverProfile = {
        firstName: receiverData.profile.firstName,
        lastName: receiverData.profile.lastName,
        userName: receiverData.profile.userName,
        userNameLower: receiverData.profile.userNameLower,
        pictureUrl: receiverData.profile.pictureUrl || "",
      };

      const senderOutgoingRef = this.db.doc(
        `users/${senderId}/outgoingRequests/${receiverId}`
      );
      const receiverIncomingRef = this.db.doc(
        `users/${receiverId}/incomingRequests/${senderId}`
      );

      await this.db.runTransaction(async (transaction) => {
        transaction.set(senderOutgoingRef, {
          id: receiverId,
          status: "pending",
          receiverProfile, // Including the receiver's profile information
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
        transaction.set(receiverIncomingRef, {
          id: senderId,
          status: "pending",
          senderProfile, // Including the sender's profile information
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
      });

      // Retrieve the newly created request details
      const senderRequestSnap = await senderOutgoingRef.get();
      const requestDetails = senderRequestSnap.data();

      return {
        success: true,
        message: "Friend request sent successfully.",
        requestDetails, // Return the details of the newly created request
      };
    } catch (error) {
      logger.error("Error sending friend request:", error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Cancels a friend request sent by a user, removing the corresponding documents from the database.
   * @param {string} senderId - ID of the user who sent the friend request.
   * @param {string} receiverId - ID of the user to whom the friend request was sent.
   * @returns {Promise<Object>} Confirmation of the friend request cancellation.
   */
  async cancelFriendRequest(senderId, receiverId) {
    try {
      const outgoingRequestRef = this.db.doc(
        `users/${senderId}/outgoingRequests/${receiverId}`
      );
      const incomingRequestRef = this.db.doc(
        `users/${receiverId}/incomingRequests/${senderId}`
      );

      const receiverProfileRef = this.db.doc(`users/${receiverId}`);
      const receiverSnap = await receiverProfileRef.get();

      if (!receiverSnap.exists) {
        throw new Error("Receiver does not exist.");
      }

      const receiverProfile = {
        id: receiverId,
        profile: {
          firstName: receiverSnap.data().profile.firstName,
          lastName: receiverSnap.data().profile.lastName,
          userName: receiverSnap.data().profile.userName,
          userNameLower: receiverSnap.data().profile.userNameLower,
          pictureUrl: receiverSnap.data().profile.pictureUrl || "",
        },
      };

      await this.db.runTransaction(async (transaction) => {
        transaction.delete(outgoingRequestRef);
        transaction.delete(incomingRequestRef);
      });

      return {
        success: true,
        message: "Friend request cancelled successfully.",
        receiverProfile,
      };
    } catch (error) {
      logger.error("Error cancelling friend request:", error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Accepts a friend request, establishing a friendship between two users returns the newly created friendship details.
   * @param {string} receiverId - ID of the user accepting the friend request.
   * @param {string} senderId - ID of the user whose friend request is being accepted.
   * @returns {Promise<Object>} Confirmation of the friend request acceptance.
   */
  async acceptFriendRequest(receiverId, senderId) {
    try {
      const incomingRequestRef = this.db.doc(
        `users/${receiverId}/incomingRequests/${senderId}`
      );
      const outgoingRequestRef = this.db.doc(
        `users/${senderId}/outgoingRequests/${receiverId}`
      );
      const receiverFriendRef = this.db.doc(
        `users/${receiverId}/friends/${senderId}`
      );
      const senderFriendRef = this.db.doc(
        `users/${senderId}/friends/${receiverId}`
      );

      // Retrieve both users' data to include in friends documents
      const [receiverSnap, senderSnap] = await Promise.all([
        this.db.doc(`users/${receiverId}`).get(),
        this.db.doc(`users/${senderId}`).get(),
      ]);

      if (!receiverSnap.exists || !senderSnap.exists) {
        throw new Error("One or both users do not exist.");
      }

      const receiverData = receiverSnap.data();
      const senderData = senderSnap.data();

      const receiverProfile = {
        firstName: receiverData.profile.firstName,
        lastName: receiverData.profile.lastName,
        userName: receiverData.profile.userName,
        userNameLower: receiverData.profile.userNameLower,
        pictureUrl: receiverData.profile.pictureUrl || "",
      };

      const senderProfile = {
        firstName: senderData.profile.firstName,
        lastName: senderData.profile.lastName,
        userName: senderData.profile.userName,
        userNameLower: senderData.profile.userNameLower,
        pictureUrl: senderData.profile.pictureUrl || "",
      };

      await this.db.runTransaction(async (transaction) => {
        transaction.delete(incomingRequestRef);
        transaction.delete(outgoingRequestRef);
        transaction.set(receiverFriendRef, {
          friendId: senderId,
          friendProfile: senderProfile, // Include sender's profile info
        });
        transaction.set(senderFriendRef, {
          friendId: receiverId,
          friendProfile: receiverProfile, // Include receiver's profile info
        });
      });

      // Retrieve the newly created friendship details
      const receiverFriendshipSnap = await receiverFriendRef.get();
      const senderFriendshipSnap = await senderFriendRef.get();
      const receiverFriendshipDetails = receiverFriendshipSnap.data();
      const senderFriendshipDetails = senderFriendshipSnap.data();

      return {
        success: true,
        message: "Friend request accepted successfully.",
        receiverFriendshipDetails,
        senderFriendshipDetails,
      };
    } catch (error) {
      logger.error("Error processing friend request:", error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Rejects a friend request by removing the corresponding documents from the database.
   * @param {string} receiverId - ID of the user rejecting the friend request.
   * @param {string} senderId - ID of the user whose friend request is being rejected.
   * @returns {Promise<Object>} Confirmation of the friend request rejection.
   */
  async rejectFriendRequest(receiverId, senderId) {
    try {
      const incomingRequestRef = this.db.doc(
        `users/${receiverId}/incomingRequests/${senderId}`
      );
      const outgoingRequestRef = this.db.doc(
        `users/${senderId}/outgoingRequests/${receiverId}`
      );

      const senderProfileRef = this.db.doc(`users/${senderId}`);
      const senderSnap = await senderProfileRef.get();

      if (!senderSnap.exists) {
        throw new Error("Sender does not exist.");
      }

      const senderProfile = {
        id: senderId,
        profile: {
          firstName: senderSnap.data().profile.firstName,
          lastName: senderSnap.data().profile.lastName,
          userName: senderSnap.data().profile.userName,
          userNameLower: senderSnap.data().profile.userNameLower,
          pictureUrl: senderSnap.data().profile.pictureUrl || "",
        },
      };

      await this.db.runTransaction(async (transaction) => {
        transaction.delete(incomingRequestRef);
        transaction.delete(outgoingRequestRef);
      });

      return {
        success: true,
        message: "Friend request rejected successfully.",
        senderProfile,
      };
    } catch (error) {
      logger.error("Error rejecting friend request:", error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Retrieves incoming friend requests for a given user.
   * @param {string} receiverId - The ID of the user receiving friend requests.
   * @returns {Promise<Array>} An array of incoming request objects.
   */
  async getIncomingRequests(receiverId) {
    try {
      const incomingRequestsRef = this.db.collection(
        `users/${receiverId}/incomingRequests`
      );
      const snapshot = await incomingRequestsRef.get();
      if (snapshot.empty) {
        logger.debug("No incoming friend requests found.");
        return [];
      }
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      logger.error("Failed to retrieve incoming requests:", error);
      throw new Error("Error fetching incoming friend requests");
    }
  }

  /**
   * Retrieves outgoing friend requests for a given user.
   * @param {string} senderId - The ID of the user who sent the friend requests.
   * @returns {Promise<Array>} An array of outgoing request objects.
   */
  async getOutgoingRequests(senderId) {
    try {
      const outgoingRequestsRef = this.db.collection(
        `users/${senderId}/outgoingRequests`
      );
      const snapshot = await outgoingRequestsRef.get();
      if (snapshot.empty) {
        logger.debug("No outgoing friend requests found.");
        return [];
      }
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      logger.error("Failed to retrieve outgoing requests:", error);
      throw new Error("Error fetching outgoing friend requests");
    }
  }

  /**
   * Removes a friend connection between two users.
   * @param {string} userId - The ID of the user initiating the removal.
   * @param {string} friendId - The ID of the friend to be removed.
   * @returns {Promise<Object>} A confirmation message of the removal.
   */
  async removeFriend(userId, friendId) {
    try {
      const userFriendsRef = this.db.doc(`users/${userId}/friends/${friendId}`);
      const friendFriendsRef = this.db.doc(
        `users/${friendId}/friends/${userId}`
      );
      await db.runTransaction(async (transaction) => {
        transaction.delete(userFriendsRef);
        transaction.delete(friendFriendsRef);
      });
      return { success: true, message: "Friend removed successfully" };
    } catch (error) {
      logger.error("Failed to remove friend connection:", error);
      throw new Error("Error removing friend");
    }
  }

  /**
   * Blocks a user from interacting with another user by storing detailed profile information.
   * @param {string} userId - The ID of the user who is blocking.
   * @param {string} blockedUserId - The ID of the user to be blocked.
   * @returns {Promise<Object>} A confirmation message of the block, including blocked user details.
   */
  async blockUser(userId, blockedUserId) {
    const userRef = this.db.doc(`users/${blockedUserId}`);
    const blockRef = this.db.doc(`users/${userId}/blocked/${blockedUserId}`);

    try {
      const userSnap = await userRef.get();
      if (!userSnap.exists) {
        logger.error("User to be blocked does not exist:", blockedUserId);
        throw new Error("User to be blocked does not exist.");
      }

      const userData = userSnap.data();
      const blockedUserProfile = {
        id: blockedUserId,
        profile: {
          firstName: userData.profile.firstName,
          lastName: userData.profile.lastName,
          userName: userData.profile.userName,
          userNameLower: userData.profile.userNameLower,
          pictureUrl: userData.profile.pictureUrl || "",
        },
      };

      await blockRef.set(blockedUserProfile);

      return {
        success: true,
        message: "User blocked successfully",
        blockedUser: blockedUserProfile,
      };
    } catch (error) {
      logger.error("Failed to block user:", userId, blockedUserId, error);
      throw new Error(`Failed to block user: ${error.message}`);
    }
  }

  /**
   * Unblocks a user, allowing interactions again.
   * @param {string} userId - The ID of the user who is unblocking.
   * @param {string} blockedUserId - The ID of the user to be unblocked.
   * @returns {Promise<Object>} A confirmation message of the unblock.
   */
  async unblockUser(userId, blockedUserId) {
    try {
      const blockRef = this.db.doc(`users/${userId}/blocked/${blockedUserId}`);
      await blockRef.delete();
      return { success: true, message: "User unblocked successfully" };
    } catch (error) {
      logger.error("Failed to unblock user:", error);
      throw new Error("Error unblocking user");
    }
  }

  /**
   * Retrieves a list of blocked users for a given user.
   * @param {string} userId - The ID of the user whose blocked list is being requested.
   * @returns {Promise<Array>} An array of blocked user objects.
   */
  async getBlockedUsersList(userId) {
    try {
      const blockedRef = this.db.collection(`users/${userId}/blocked`);
      const snapshot = await blockedRef.get();
      if (snapshot.empty) {
        logger.debug("No blocked users found.");
        return [];
      }
      return snapshot.docs.map((doc) => ({
        blockedUserId: doc.id, // Assuming the ID of the blocked user is stored as the document ID
        ...doc.data(), // Additional data about the block if stored
      }));
    } catch (error) {
      logger.error("Failed to retrieve blocked users list:", error);
      throw new Error("Error fetching blocked users list");
    }
  }

  /**
   * Retrieves a list of friends for a given user.
   * @param {string} userId - The ID of the user whose friends list is being requested.
   * @returns {Promise<Array>} An array of friend objects.
   */
  async getFriendsList(userId) {
    try {
      const friendsRef = this.db.collection(`users/${userId}/friends`);
      const snapshot = await friendsRef.get();
      if (snapshot.empty) {
        logger.debug("No friends found.");
        return [];
      }
      return snapshot.docs.map((doc) => ({ friendId: doc.id, ...doc.data() }));
    } catch (error) {
      logger.error("Failed to retrieve friends list:", error);
      throw new Error("Error fetching friends list");
    }
  }
}

module.exports = new FriendManagementService();
