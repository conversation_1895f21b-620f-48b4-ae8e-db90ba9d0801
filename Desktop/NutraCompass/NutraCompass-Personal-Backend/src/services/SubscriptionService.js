// services/SubscriptionService.js
const admin = require("../config/firebase-admin");
const logger = require("../utils/logger");

class SubscriptionService {
  static async addFeatureSubscription(userId, feature, paymentDetails) {
    const userRef = admin.firestore().doc(`users/${userId}`);
    const featureKey = feature.id.toLowerCase().replace(/\s+/g, "-");

    return admin.firestore().runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      if (!userDoc.exists) throw new Error("User not found");

      const userData = userDoc.data();

      // Validate feature
      if (!feature.id || !feature.name || typeof feature.price !== "number") {
        throw new Error("Invalid feature data");
      }

      // Create new entitlement
      const newEntitlement = {
        access: true,
        expiry: feature.expiryDate || null,
        usageLimit: feature.usageLimit || null,
        usageCount: 0,
        lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
      };

      // Create subscription record
      const newSubscription = {
        id: this.generateSubscriptionId(),
        featureId: feature.id,
        featureName: feature.name,
        featureKey,
        price: feature.price,
        startDate: admin.firestore.FieldValue.serverTimestamp(),
        expiryDate: feature.expiryDate || null,
        status: "active",
        paymentMethod: paymentDetails.method,
        transactionId: paymentDetails.transactionId,
        metadata: {
          createdBy: "direct-purchase",
          source: "in-app",
        },
      };

      // Update user document
      transaction.update(userRef, {
        [`account.subscriptionFeatures.${featureKey}`]: true,
        [`account.entitlements.${featureKey}`]: newEntitlement,
        subscriptions: admin.firestore.FieldValue.arrayUnion(newSubscription),
      });

      logger.info("Feature purchased", {
        userId,
        feature: feature.id,
        price: feature.price,
        paymentMethod: paymentDetails.method,
      });

      return newSubscription;
    });
  }

  static async updateSubscriptionStatus(userId, subscriptionId, status) {
    const userRef = admin.firestore().doc(`users/${userId}`);

    return admin.firestore().runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      if (!userDoc.exists) throw new Error("User not found");

      const subscriptions = userDoc.data().subscriptions || [];
      const updatedSubscriptions = subscriptions.map((sub) => {
        if (sub.id === subscriptionId) {
          return {
            ...sub,
            status,
            lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
          };
        }
        return sub;
      });

      transaction.update(userRef, { subscriptions: updatedSubscriptions });
    });
  }

  static async checkFeatureAccess(userId, featureId) {
    const featureKey = featureId.toLowerCase().replace(/\s+/g, "-");
    const userDoc = await admin.firestore().doc(`users/${userId}`).get();

    if (!userDoc.exists) return false;
    const userData = userDoc.data();

    const entitlement = userData.account?.entitlements?.[featureKey];
    if (!entitlement || !entitlement.access) return false;

    // Check expiry
    if (entitlement.expiry && new Date() > entitlement.expiry.toDate()) {
      return false;
    }

    // Check usage limits
    if (
      entitlement.usageLimit &&
      entitlement.usageCount >= entitlement.usageLimit
    ) {
      return false;
    }

    return true;
  }

  static async recordFeatureUsage(userId, featureId) {
    const featureKey = featureId.toLowerCase().replace(/\s+/g, "-");
    const userRef = admin.firestore().doc(`users/${userId}`);

    return admin.firestore().runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      if (!userDoc.exists) return;

      const userData = userDoc.data();
      const entitlement = userData.account?.entitlements?.[featureKey];

      if (!entitlement) return;

      const updatedEntitlements = {
        ...userData.account.entitlements,
        [featureKey]: {
          ...entitlement,
          usageCount: (entitlement.usageCount || 0) + 1,
          lastUsed: admin.firestore.FieldValue.serverTimestamp(),
        },
      };

      transaction.update(userRef, {
        "account.entitlements": updatedEntitlements,
      });
    });
  }

  static generateSubscriptionId() {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

module.exports = SubscriptionService;
