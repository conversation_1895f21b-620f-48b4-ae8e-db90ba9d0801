const admin = require("../config/firebase-admin");
const db = admin.firestore();
const Expo = require("expo-server-sdk");
const expo = new Expo.Expo();
const {
  MEAL_NOTIFICATION_TEMPLATES,
  STEP_GOAL_TEMPLATES,
} = require("../models/notificationTemplates");
const { parseLocalTime, TimezoneError } = require("../utils/timezoneHelpers");
const logger = require("../utils/logger.js");

// Lock configuration
const LOCK_TIMEOUT_MS = 60000; // Increased to 60s
const LOCK_RETRY_INTERVAL = 1000; // 1s base interval
const MAX_RETRY_ATTEMPTS = 8;

class ConcurrentModificationError extends Error {
  constructor(resource, currentVersion) {
    super(`${resource} modified concurrently`);
    this.name = "ConcurrentModificationError";
    this.currentVersion = currentVersion;
    this.code = 409; // Conflict status code
  }
}

class ValidationError extends Error {
  constructor(message, details) {
    super(message);
    this.name = "ValidationError";
    this.statusCode = 400;
    this.details = details;
  }
}

class NotificationService {
  static instance = null;

  constructor() {
    if (!NotificationService.instance) {
      NotificationService.instance = this;
      this.db = db;
    }
    return NotificationService.instance;
  }

  // ====================================================================
  // Distributed Locking Subsystem
  // ====================================================================

  async _acquireLock(userId) {
    const lockRef = this.db.collection("operation_locks").doc(userId);
    let attempts = 0;

    logger.debug(`Lock acquisition started for ${userId}`, {
      region: "us-central1",
      timestamp: new Date().toISOString(),
    });

    while (attempts < MAX_RETRY_ATTEMPTS) {
      try {
        await this.db.runTransaction(async (t) => {
          const doc = await t.get(lockRef);
          if (doc.exists) {
            const lockData = doc.data();
            // Use server timestamp for comparison
            const now = admin.firestore.Timestamp.now();
            if (lockData.expiresAt > now) {
              throw new Error("Resource contention - lock active");
            }
          }
          // Set expiration using server-relative time
          t.set(lockRef, {
            acquiredAt: admin.firestore.FieldValue.serverTimestamp(),
            expiresAt: admin.firestore.Timestamp.fromMillis(
              admin.firestore.Timestamp.now().toMillis() + LOCK_TIMEOUT_MS
            ),
          });
        });
        return;
      } catch (error) {
        // Exponential backoff with jitter
        const delay =
          LOCK_RETRY_INTERVAL * Math.pow(2, attempts) + Math.random() * 100;
        await new Promise((resolve) => setTimeout(resolve, delay));
        attempts++;
      }
    }
    logger.error(`Final lock failure for ${userId}`, {
      attempts,
      lastRetry: new Date().toISOString(),
      region: "us-central1",
    });
    throw new Error(
      `Lock acquisition failed for ${userId} after ${attempts} retries`
    );
  }

  async _releaseLock(userId) {
    const lockRef = this.db.collection("operation_locks").doc(userId);
    await lockRef
      .delete()
      .catch((error) =>
        logger.error(`Lock cleanup failed for ${userId}:`, error)
      );
  }

  async _withLock(userId, operation) {
    try {
      await this._acquireLock(userId);
      return await operation();
    } finally {
      await this._releaseLock(userId).catch((error) =>
        logger.error("Non-critical lock release failure:", error)
      );
    }
  }

  // ====================================================================
  // Core Business Logic
  // ====================================================================

  async getNotificationSettings(userId, options = {}) {
    return this._withLock(userId, async () => {
      if (
        typeof options.versionCheck !== "undefined" &&
        typeof options.versionCheck !== "number"
      ) {
        logger.warn("Invalid versionCheck type", {
          userId,
          receivedType: typeof options.versionCheck,
        });
        delete options.versionCheck;
      }

      try {
        const settingsRef = this.db
          .collection("users")
          .doc(userId)
          .collection("notifications")
          .doc("settings");

        const doc = await settingsRef.get();

        if (!doc.exists) {
          return null;
        }

        const rawData = doc.data();

        // Version-only response optimization
        if (rawData._version && options?.versionCheck) {
          return { _version: rawData._version };
        }

        // Convert Firestore timestamps to ISO strings
        const processSettings = (settings) => ({
          ...settings,
          mealReminders: Object.fromEntries(
            Object.entries(settings.mealReminders || {}).map(
              ([id, reminder]) => [
                id,
                {
                  ...reminder,
                  time: reminder.time,
                  _createdAt: this._convertToDate(
                    reminder._createdAt
                  )?.toISOString(),
                  _updatedAt: this._convertToDate(
                    reminder._updatedAt
                  )?.toISOString(),
                },
              ]
            )
          ),
          stepGoal: {
            ...settings.stepGoal,
            time: settings.stepGoal?.time,
          },
          _version: settings._version || 0,
        });

        return processSettings(rawData);
      } catch (error) {
        logger.error("Failed to fetch notification settings:", {
          userId,
          error: error.message,
        });
        throw new Error("Failed to retrieve notification settings");
      }
    });
  }

  async updateNotificationSettings(
    uid,
    settings,
    expoPushToken,
    expectedVersion
  ) {
    return this._withLock(uid, async () => {
      const userRef = this.db.collection("users").doc(uid);
      const settingsRef = userRef.collection("notifications").doc("settings");

      await this.db.runTransaction(async (t) => {
        const doc = await t.get(settingsRef);
        const currentData = doc.exists ? doc.data() : {};
        const currentVersion = currentData._version || 0;

        // Version validation logic
        if (
          typeof expectedVersion === "number" &&
          currentVersion !== expectedVersion
        ) {
          throw new ConcurrentModificationError(
            "notification-settings",
            currentVersion
          );
        }

        if (expoPushToken) {
          t.update(userRef, {
            pushTokens: admin.firestore.FieldValue.arrayUnion(expoPushToken),
          });
        }

        t.set(
          settingsRef,
          {
            ...settings,
            _version: admin.firestore.FieldValue.increment(1),
          },
          { merge: true }
        );
      });

      return this.scheduleNotifications(uid);
    });
  }

  async scheduleNotifications(uid) {
    return this._withLock(uid, async () => {
      const [userDoc, settingsDoc] = await Promise.all([
        this.db.collection("users").doc(uid).get(),
        this.db
          .collection("users")
          .doc(uid)
          .collection("notifications")
          .doc("settings")
          .get(),
      ]);

      const userData = userDoc.data() || {};
      const settings = settingsDoc.data() || {};

      // Verify version hasn't changed during processing
      const versionCheck = await this.getNotificationSettings(uid, {
        versionCheck: true,
      });

      if (versionCheck?._version !== settings._version) {
        logger.error("Version mismatch during scheduling:", {
          storedVersion: settings._version,
          fetchedVersion: versionCheck._version,
          userId: uid,
        });
        throw new ConcurrentModificationError(
          "notification-settings",
          versionCheck._version
        );
      }

      const pushTokens = await this._sanitizePushTokens(uid);
      const userName = userData.profile?.firstName || "User";
      const userTimezone = userData.location?.timezone || "America/Los_Angeles";

      // Add timezone validation
      const VALID_TIMEZONES = new Set(Intl.supportedValuesOf("timeZone"));
      if (!VALID_TIMEZONES.has(userTimezone)) {
        logger.error(`Invalid timezone '${userTimezone}' for user ${uid}`);
        throw new Error(
          "Invalid timezone configuration. Please update your profile settings."
        );
      }

      const messages = this._generateNotifications(
        pushTokens,
        uid,
        settings,
        userName,
        userTimezone
      );
      const receipts = await this._deliverNotifications(messages);
      await this._persistNotificationIds(uid, receipts);
      return receipts;
    });
  }

  async cancelMealReminders(userId, mealIds) {
    return this._withLock(userId, async () => {
      const MAX_CLAUSE_SIZE = 10; // Firestore's "IN" limit
      const BATCH_LIMIT = 500; // Firestore batch write limit
      const MAX_PARALLEL_UPDATES = 5; // Parallel operation limiter

      // Phase 1: Batched settings updates with parallel control
      const settingsRef = this.db
        .collection("users")
        .doc(userId)
        .collection("notifications")
        .doc("settings");

      const batchedUpdates = [];
      for (let i = 0; i < mealIds.length; i += BATCH_LIMIT) {
        const batchIds = mealIds.slice(i, i + BATCH_LIMIT);
        batchedUpdates.push(
          settingsRef.update({
            ...batchIds.reduce(
              (acc, mealId) => ({
                ...acc,
                [`mealReminders.${mealId}`]:
                  admin.firestore.FieldValue.delete(),
              }),
              {}
            ),
          })
        );
      }

      // Execute in controlled parallel batches
      while (batchedUpdates.length > 0) {
        const currentBatch = batchedUpdates.splice(0, MAX_PARALLEL_UPDATES);
        await Promise.all(currentBatch);
      }

      // Phase 2: Delete scheduled notifications
      const notificationsCol = this.db.collection(
        `users/${userId}/scheduled_notifications`
      );

      // Split into chunks of 10 for "IN" queries
      const queryChunks = [];
      for (let i = 0; i < mealIds.length; i += MAX_CLAUSE_SIZE) {
        queryChunks.push(mealIds.slice(i, i + MAX_CLAUSE_SIZE));
      }

      // Execute parallel queries
      const querySnapshots = await Promise.all(
        queryChunks.map((chunk) =>
          notificationsCol.where("mealId", "in", chunk).get()
        )
      );

      // Batch deletions (500 per batch)
      let totalDeleted = 0;
      for (const snapshot of querySnapshots) {
        const docs = snapshot.docs;
        for (let i = 0; i < docs.length; i += BATCH_LIMIT) {
          const batch = this.db.batch();
          docs.slice(i, i + BATCH_LIMIT).forEach((doc) => {
            batch.delete(doc.ref);
          });
          await batch.commit();
          totalDeleted += docs.slice(i, i + BATCH_LIMIT).length;
        }
      }

      await Promise.all(batchedUpdates);
      return { success: true, canceled: totalDeleted };
    });
  }

  async rescheduleUserNotifications(userId) {
    return this._withLock(userId, async () => {
      // Clear local notification records
      await this._clearNotificationRecords(userId);

      // Clean up expired receipts (24h+ old)
      await this._cleanupNotificationRecords(userId);

      // Regenerate notifications with current templates
      return this.scheduleNotifications(userId);
    });
  }

  // ====================================================================
  // Notification Hygiene
  // ====================================================================

  async _cleanupNotificationRecords(userId) {
    const receiptsRef = db.collection(`users/${userId}/notification_receipts`);
    const expiredReceipts = await receiptsRef
      .where("createdAt", "<", new Date(Date.now() - 24 * 60 * 60 * 1000))
      .get();

    const batch = db.batch();
    expiredReceipts.forEach((doc) => batch.delete(doc.ref));
    await batch.commit();
  }

  async _clearNotificationRecords(userId) {
    const activeRef = this.db
      .collection("users")
      .doc(userId)
      .collection("scheduled_notifications")
      .doc("active");
    await activeRef.delete();
  }

  // ====================================================================
  // Notification Pipeline
  // ====================================================================

  _generateNotifications(tokens, uid, settings, userName, userTimezone) {
    const messages = [];
    if (settings.mealReminders) {
      Object.entries(settings.mealReminders).forEach(([mealId, config]) => {
        if (config.enabled && config.time) {
          const template = this._getVersionedTemplate(
            settings._meta?.mealTemplateIndex,
            MEAL_NOTIFICATION_TEMPLATES
          );
          messages.push(
            ...this._buildNotificationBatch(tokens, {
              template,
              uid,
              time: config.time,
              userName,
              mealName: config.mealName,
              mealId,
              timezone: userTimezone,
            })
          );
        }
      });
    }
    if (settings.stepGoal?.enabled) {
      const template = this._getVersionedTemplate(
        settings._meta?.stepTemplateIndex,
        STEP_GOAL_TEMPLATES
      );
      messages.push(
        ...this._buildNotificationBatch(tokens, {
          template,
          uid,
          time: settings.stepGoal.time,
          userName,
          timezone: userTimezone,
        })
      );
    }
    return messages;
  }

  _buildNotificationBatch(tokens, config) {
    const {
      template,
      uid,
      time, // Expected format "HH:mm"
      userName,
      mealName = null,
      mealId = null,
      timezone = "America/Los_Angeles",
    } = config;

    try {
      // Convert local time to UTC components
      const { utcHours, utcMinutes } = parseLocalTime(time, timezone);

      // Validation for time format
      if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time)) {
        logger.error("Invalid time format:", { userId: uid, time });
        return [];
      }

      return tokens.map((token) => ({
        to: token,
        title: template.title.replace(/{mealName}/g, mealName || ""),
        body: template.body.replace(/{userName}/g, userName),
        trigger: {
          hour: utcHours,
          minute: utcMinutes,
          repeats: true,
          timezone,
        },
        metadata: {
          userId: uid,
          scheduledAt: new Date().toISOString(),
          notificationType: mealId ? "meal" : "stepGoal",
          timezone,
        },
      }));
    } catch (error) {
      if (error instanceof TimezoneError) {
        logger.error("Invalid time configuration:", {
          userId: uid,
          time,
          timezone,
          error: error.message,
        });
        return [];
      }
      throw error;
    }
  }

  // ====================================================================
  // Utilities
  // ====================================================================

  async _deliverNotifications(messages) {
    if (!messages.length) return [];

    const chunks = expo.chunkPushNotifications(messages);
    const receipts = [];

    for (const chunk of chunks) {
      try {
        const chunkReceipts = await expo.sendPushNotificationsAsync(chunk);
        receipts.push(...chunkReceipts);
      } catch (error) {
        logger.error("Notification delivery failed:", {
          error: error.message,
          chunkSize: chunk.length,
          code: error.code, // Expo-specific error code
          userId: chunk[0]?.metadata?.userId, // Track affected user
        });
        throw new Error("Partial notification delivery failure");
      }
    }

    return receipts;
  }

  async _persistNotificationIds(uid, receipts) {
    const receiptsRef = db.collection(`users/${uid}/notification_receipts`);
    const batch = db.batch();
    receipts.forEach((receipt) => {
      if (receipt?.id && typeof receipt.id === "string") {
        const docRef = receiptsRef.doc();
        batch.set(docRef, {
          receiptId: receipt.id,
          status: receipt.status || "unknown",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    });
    await batch.commit();
  }

  async storePushToken(userId, expoPushToken) {
    return this._withLock(userId, async () => {
      // 1. Validate using Expo's native check
      if (!Expo.Expo.isExpoPushToken(expoPushToken)) {
        throw new ValidationError("Invalid Expo push token format", {
          example: "ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]",
        });
      }

      // 2. Reference to pushTokens subcollection
      const pushTokensRef = this.db.collection(
        `users/${userId}/notifications/pushTokens`
      );

      // 3. Check for existing token
      const snapshot = await pushTokensRef
        .where("token", "==", expoPushToken)
        .limit(1)
        .get();

      // 4. Add if not exists
      if (snapshot.empty) {
        await pushTokensRef.add({
          token: expoPushToken,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          status: "active",
        });
      }

      // 5. Trigger rescheduling (using your existing flow)
      await this.rescheduleUserNotifications(userId);
    });
  }

  _sanitizePushTokens(userId) {
    return this._withLock(userId, async () => {
      const snapshot = await this.db
        .collection(`users/${userId}/notifications/pushTokens`)
        .where("status", "==", "active")
        .get();

      return snapshot.docs
        .map((doc) => doc.data().token)
        .filter((token) => Expo.Expo.isExpoPushToken(token));
    });
  }

  _getVersionedTemplate(index, templates) {
    if (templates.length === 0) {
      throw new Error(`No templates available for ${templateType}`);
    }

    const safeIndex =
      typeof index === "number"
        ? index % templates.length
        : Math.floor(Math.random() * templates.length);
    return templates[safeIndex];
  }

  _convertToDate(input) {
    if (!input) return null; // Handle missing dates

    // Firestore Timestamp
    if (typeof input.toDate === "function") return input.toDate();

    // JSON representation { seconds: number, nanoseconds: number }
    if (input.seconds) {
      return new Date(input.seconds * 1000 + (input.nanoseconds || 0) / 1e6);
    }

    // ISO string or millisecond number
    const date = new Date(input);
    return isNaN(date) ? null : date;
  }
}

module.exports = new NotificationService();
