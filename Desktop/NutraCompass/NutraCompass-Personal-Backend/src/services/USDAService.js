const Configs = require("../../configs.js");
const HttpService = require("../utils/HttpService.js");
const CacheService = require("./CacheService.js");
const NutrientProcessor = require("../utils/NutrientProcessor.js");
const crypto = require("crypto");
const { USDAHelper } = require("../utils/foodIdHelper.js");
const logger = require("../utils/logger.js");
const { USDA_API_KEY, USDA_BASE_URL } = Configs.usdaConfig;

class USDAService {
  constructor() {
    if (!USDA_API_KEY) {
      throw new Error("USDA API key missing!");
    }

    this.baseSearchParams = {
      api_key: USDA_API_KEY,
      pageSize: 5,
    };

    // Define cache service types
    this.CACHE_TYPES = {
      SEARCH: "FOOD_SEARCH_QUERY",
      BARCODE: "FOOD_SEARCH_BARCODE",
      NUTRIENT: "FOOD_SEARCH_NUTRIENT",
    };
  }

  async searchByQuery(query, userId, page = 1, pageSize = 5) {
    const normalizedQuery = this.normalizeQuery(query);
    const pageNumber = Math.max(1, parseInt(page, 10)) || 1;

    // cache key
    const baseCacheKey = `usda:search:user:${userId}:query:${normalizedQuery}:ps${pageSize}`;
    let rawResults = [];

    const { data: cachedBase } = await CacheService.instance.get(
      baseCacheKey,
      this.CACHE_TYPES.SEARCH
    );

    if (!cachedBase) {
      const url = new URL(`${USDA_BASE_URL}/foods/search`);
      Object.entries({
        ...this.baseSearchParams,
        query,
      }).forEach(([k, v]) => url.searchParams.append(k, v));

      try {
        const response = await HttpService.safeFetch(url.toString());
        // Extract only data array from response
        rawResults = this.processSearchResponse(response).data || [];

        await CacheService.instance.set(
          baseCacheKey,
          {
            data: rawResults,
            $metadata: {
              source: "usda",
              retrievedAt: new Date().toISOString(),
              pageSize: pageSize, // Track page size
            },
          },
          this.CACHE_TYPES.SEARCH
        );
      } catch (err) {
        logger.error(`[USDA API ERROR] ${err.message}`);
        return {
          data: [],
          pagination: { hasMore: false },
          $metadata: {
            error: err.message,
            source: "usda",
          },
        };
      }
    } else {
      rawResults = cachedBase.data || [];
    }

    if (!Array.isArray(rawResults)) {
      logger.error("Invalid rawResults format, resetting cache");
      await CacheService.instance.delete(baseCacheKey, this.CACHE_TYPES.SEARCH);
      rawResults = [];
    }

    const resultsVersion = crypto
      .createHash("sha1")
      .update(JSON.stringify(rawResults))
      .digest("hex");

    // Updated processed page key with page size
    const processedPageKey = `${baseCacheKey}:v${resultsVersion}:page:${pageNumber}`;

    const { data: cachedPage } = await CacheService.instance.get(
      processedPageKey,
      this.CACHE_TYPES.SEARCH
    );

    if (cachedPage) {
      return {
        ...cachedPage,
        $metadata: {
          ...cachedPage.$metadata,
          cacheStatus: "hit",
          source: "usda",
        },
      };
    }

    // Client-side pagination with dynamic pageSize
    const startIdx = Math.max(0, (pageNumber - 1) * pageSize);
    const endIdx = startIdx + pageSize;
    const paginatedData = rawResults.slice(startIdx, endIdx);

    const resultPayload = {
      data: paginatedData,
      pagination: {
        currentPage: pageNumber,
        totalItems: rawResults.length,
        hasMore: endIdx < rawResults.length,
      },
      $metadata: {
        source: "usda",
        warnings: [],
      },
    };

    if (paginatedData.length > 0) {
      await CacheService.instance.set(
        processedPageKey,
        resultPayload,
        this.CACHE_TYPES.SEARCH
      );
    }

    return {
      ...resultPayload,
      $metadata: {
        ...resultPayload.$metadata,
        cacheStatus: "miss",
      },
    };
  }

  processSearchResponse(response) {
    if (!response?.foods) {
      return {
        data: [],
        pagination: { hasMore: false },
      };
    }

    return {
      data: response.foods.map((item) => {
        // Convert fdcId to string explicitly
        const internalId = `usda:${String(item.fdcId)}`;

        const processedNutrients = NutrientProcessor.processFullNutrientData(
          item.foodNutrients || [],
          "usda"
        );

        const coreNutrients = processedNutrients.core || {};

        return {
          foodId: internalId, // Use string-converted ID
          foodLabel: item.description,
          foodCategory: item.foodCategory || "USDA Food",
          foodBrand: item.brandOwner || null,
          numberOfServings: 1,
          measures: [
            {
              uri: "100g",
              label: "100g",
              weight: 100,
            },
          ],
          activeMeasure: {
            uri: "100g",
            label: "100g",
            weight: 100,
          },
          nutrients: processedNutrients,
          defaultNutrients: {
            calories: coreNutrients.ENERC_KCAL?.quantity || 0,
            protein: coreNutrients.PROCNT?.quantity || 0,
            fat: coreNutrients.FAT?.quantity || 0,
            carbs: coreNutrients.CHOCDF?.quantity || 0,
          },
          $metadata: {
            source: "usda",
            success: true,
            warnings: [],
            fdcId: item.fdcId,
            dataType: item.dataType,
            publicationDate: item.publicationDate,
            // processingId: `usda-${Date.now()}-${item.fdcId}`,
          },
        };
      }),
      pagination: {
        currentPage: response.currentPage,
        totalItems: response.totalHits,
        hasMore:
          response.currentPage <
          Math.ceil(response.totalHits / this.baseSearchParams.pageSize),
      },
    };
  }

  async getNutrients(foodItem, measureURI = "100g", userId) {
    // Validate and convert internal ID
    const internalId = foodItem.foodId;
    const fdcId = USDAHelper.fromInternal(internalId);

    if (!fdcId) {
      logger.error("Invalid USDA food item — missing FDC ID");
      return {
        data: null,
        $metadata: {
          success: false,
          error: "Invalid food ID format",
          source: "usda",
        },
      };
    }

    // Cache key using internal ID for consistency
    const cacheKey = `usda:nutrients:${userId}:${internalId}:${measureURI}`;
    const { data: cached } = await CacheService.instance.get(
      cacheKey,
      this.CACHE_TYPES.NUTRIENT
    );

    if (cached) {
      return {
        ...cached,
        $metadata: {
          ...cached.$metadata,
          cacheStatus: "hit",
          source: "usda",
        },
      };
    }

    const url = new URL(`${USDA_BASE_URL}/food/${fdcId}`);
    url.searchParams.append("api_key", USDA_API_KEY);

    try {
      const response = await HttpService.safeFetch(url.toString());
      const processed = NutrientProcessor.processFullNutrientData(
        response.foodNutrients,
        "usda"
      );
      const hasCoreData = processed.core?.ENERC_KCAL?.quantity > 0;

      await CacheService.instance.set(
        cacheKey,
        {
          data: processed,
          $metadata: {
            success: hasCoreData,
            hasFullData: !!processed.vitamins && !!processed.minerals,
            source: "usda",
          },
        },
        this.CACHE_TYPES.NUTRIENT
      );

      return {
        data: processed,
        $metadata: {
          cacheStatus: "miss",
          success: hasCoreData,
          hasFullData: !!processed.vitamins && !!processed.minerals,
          source: "usda",
        },
      };
    } catch (err) {
      logger.error(`[USDA Nutrient Error] ${err.message}`);
      return {
        data: null,
        $metadata: {
          success: false,
          error: err.message,
          source: "usda",
        },
      };
    }
  }

  normalizeQuery(query) {
    return query.trim().toLowerCase().replace(/\s+/g, "_");
  }
}

module.exports = new USDAService();
