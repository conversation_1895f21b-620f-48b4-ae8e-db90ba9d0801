const admin = require("../config/firebase-admin");
const logger = require("../utils/logger.js");

class UserSettingsService {
  static instance = null;

  constructor() {
    if (!UserSettingsService.instance) {
      UserSettingsService.instance = this;
    }
    return UserSettingsService.instance;
  }

  /**
   * Fetches user settings from Firestore using the user's UID with retry logic
   * to handle Firestore's eventual consistency model.
   * @param {string} uid - User's UID
   * @returns {Promise<Object>} User settings data
   * @throws {Error} If settings cannot be retrieved after max attempts
   */
  async getSettings(uid) {
    const MAX_ATTEMPTS = 5;
    const BASE_DELAY_MS = 300;
    let attempts = 0;

    while (attempts < MAX_ATTEMPTS) {
      try {
        const userSettingsDocRef = admin.firestore().doc(`users/${uid}`);
        const userSettingsDoc = await userSettingsDocRef.get();

        if (userSettingsDoc.exists) {
          //logger.debug(`Settings found for ${uid} on attempt ${attempts + 1}`);
          return userSettingsDoc.data();
        }

        // Only throw on final attempt
        if (attempts === MAX_ATTEMPTS - 1) {
          throw new Error(`Settings not found after ${MAX_ATTEMPTS} attempts`);
        }

        // Exponential backoff with jitter
        const delay =
          BASE_DELAY_MS * Math.pow(2, attempts) + Math.random() * 100;
        logger.debug(`Retry ${attempts + 1} for ${uid}, waiting ${delay}ms`);
        await new Promise((resolve) => setTimeout(resolve, delay));

        attempts++;
      } catch (error) {
        logger.error(`Attempt ${attempts + 1} failed:`, error.message);
        if (attempts === MAX_ATTEMPTS - 1) {
          throw new Error(`Failed to fetch settings: ${error.message}`);
        }
      }
    }

    // Fallback error if all attempts fail
    throw new Error(
      `Settings fetch failed for ${uid} after ${MAX_ATTEMPTS} retries`
    );
  }

  /**
   * Updates user settings in Firestore using the user's UID and the provided settings object.
   * @param {string} uid - User's UID.
   * @param {Object} settings - New settings to be saved.
   * @returns {Promise<void>}
   */
  async updateSettings(uid, settings) {
    try {
      const userSettingsDocRef = admin.firestore().doc(`users/${uid}`);
      await userSettingsDocRef.set(settings, { merge: true });
    } catch (error) {
      logger.error("Settings Update Error: ", error);
      throw new Error("Failed to update settings: " + error.message);
    }
  }

  static getBaseDefaultSettings() {
    return {
      profile: {
        firstName: "",
        lastName: "",
        userName: "",
        email: "",
        birthday: "",
        age: null,
        sex: "",
        bodyWeight: "",
        height: { inches: null, centimeters: null },
        pictureUrl: "",
      },
      location: {
        timezone: "UTC",
        region: "US",
      },
      appAppearance: {
        theme: "Default",
        isDark: true,
      },
      nutritionalGoals: {
        calorieGoal: 2000,
        waterGoal: { amount: 64, unit: "fl oz" },
        macroGoals: {
          carb: { dailyPercentage: 0.4, dailyCalories: 800, dailyGrams: 200 },
          protein: {
            dailyPercentage: 0.3,
            dailyCalories: 600,
            dailyGrams: 150,
          },
          fat: { dailyPercentage: 0.3, dailyCalories: 600, dailyGrams: 67 },
        },
      },
      physicalFitnessGoals: {
        stepsGoal: 10000,
        distanceGoal: 5,
        distanceUnit: "mi",
      },
      account: {
        role: "user", // 'user', 'personal-trainer', 'gym-owner', 'admin'
        status: "active", // 'active', 'suspended', 'deleted'
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        lastPaymentDate: null,
        subscriptionFeatures: {
          foodScanner: false,
          voiceLogging: false,
          // Add more features as needed
        },
        subscriptionExpiry: null,
        entitlements: {
          // Feature-specific access details
          foodScanner: {
            access: false,
            expiry: null,
            usageLimit: 0,
            usageCount: 0,
          },
          voiceLogging: {
            access: false,
            expiry: null,
            usageLimit: 0,
            usageCount: 0,
          },
        },
        paymentMethods: [], // Array of payment methods
        billingAddress: null,
      },
      subscriptions: [], // Array of active subscriptions
    };
  }

  static generateUsername(email, firstName, lastName) {
    // Prioritize user-provided name if available
    if (firstName && lastName) {
      const base = `${firstName}${lastName}`.toLowerCase().replace(/\s+/g, "");
      return `${base}${Math.floor(1000 + Math.random() * 9000)}`;
    }
    return email.split("@")[0];
  }

  static getProviderProfile(user, providerData) {
    const providerId = user.providerData[0]?.providerId;

    if (providerId === "apple.com") {
      return {
        email: providerData.email || user.email || "",
        firstName: providerData.firstName || "",
        lastName: providerData.lastName || "",
      };
    }

    if (providerId === "google.com") {
      const [firstName, ...lastNameParts] = user.displayName?.split(" ") || [];
      return {
        email: user.email || "",
        firstName: firstName || "",
        lastName: lastNameParts.join(" ") || "",
      };
    }

    // Fallback for email/password
    return {
      email: user.email || "",
      firstName: "",
      lastName: "",
    };
  }

  static createUserDocument(user, formSettings = {}, providerData = {}) {
    const baseDefaults = this.getBaseDefaultSettings();
    const providerProfile = this.getProviderProfile(user, providerData);

    // Merge settings with priority: form data > provider data > defaults
    return {
      ...baseDefaults,
      ...formSettings,
      profile: {
        ...baseDefaults.profile,
        ...providerProfile,
        ...formSettings.profile,
        firstName: formSettings.profile?.firstName || providerProfile.firstName,
        lastName: formSettings.profile?.lastName || providerProfile.lastName,
        email: providerProfile.email,
        userName:
          formSettings.profile?.userName ||
          this.generateUsername(
            providerProfile.email,
            providerProfile.firstName,
            providerProfile.lastName
          ),
        userNameLower: (
          formSettings.profile?.userName ||
          this.generateUsername(
            providerProfile.email,
            providerProfile.firstName,
            providerProfile.lastName
          )
        ).toLowerCase(),
        // Preserve form-based metrics
        birthday: formSettings.profile?.birthday,
        age: formSettings.profile?.age,
        sex: formSettings.profile?.sex,
        bodyWeight: formSettings.profile?.bodyWeight,
        height: formSettings.profile?.height,
      },
    };
  }

  static async saveUserDocument(uid, userDocument) {
    try {
      const userDocRef = admin.firestore().doc(`users/${uid}`);
      await userDocRef.set(userDocument);
      logger.info(`User document created for ${uid}`);
      return true;
    } catch (error) {
      logger.error("Document creation error:", error);
      throw error;
    }
  }

  static async ensureUserDocument(user, formSettings = {}, providerData = {}) {
    const userDocRef = admin.firestore().doc(`users/${user.uid}`);
    const userDoc = await userDocRef.get();

    if (!userDoc.exists) {
      const userDocument = this.createUserDocument(
        user,
        formSettings,
        providerData
      );
      await this.saveUserDocument(user.uid, userDocument);
      return true;
    }
    return false;
  }
}

module.exports = new UserSettingsService(); // Automatically create and export a singleton instance
