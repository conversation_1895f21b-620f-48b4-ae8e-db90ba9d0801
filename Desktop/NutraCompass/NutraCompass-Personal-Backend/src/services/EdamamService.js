const Configs = require("../../configs.js");
const HttpService = require("../utils/HttpService.js");
const CacheService = require("./CacheService.js");
const NutrientProcessor = require("../utils/NutrientProcessor.js");
const crypto = require("crypto");
const { EdamamHelper } = require("../utils/foodIdHelper.js");
const logger = require("../utils/logger.js");

const { APP_ID, APP_KEY, PARSER_BASE_URL, NUTRIENTS_BASE_URL } =
  Configs.edamamConfig;

class EdamamService {
  constructor() {
    this.baseSearchParams = {
      app_id: APP_ID,
      app_key: APP_KEY,
    };

    if (!APP_ID || !APP_KEY) {
      throw new Error("Edamam API credentials missing!");
    }

    // Define cache service types
    this.CACHE_TYPES = {
      SEARCH: "FOOD_SEARCH_QUERY",
      BARCODE: "FOOD_SEARCH_BARCODE",
      NUTRIENT: "FOOD_SEARCH_NUTRIENT",
    };
  }

  // Core API Methods
  async searchByQuery(query, userId, page = 1, pageSize = 5) {
    const pageNumber = Math.max(1, parseInt(page, 10)) || 1;
    const normalizedQuery = this.normalizeQuery(query);

    // cache key with page size
    const rawHintsKey = `edm:search:user:${userId}:hints:${normalizedQuery}:ps${pageSize}`;
    let rawHints = [];

    const { data: cachedRawHints } = await CacheService.instance.get(
      rawHintsKey,
      this.CACHE_TYPES.SEARCH
    );

    if (!cachedRawHints) {
      const url = new URL(PARSER_BASE_URL);
      Object.entries({
        ...this.baseSearchParams,
        ingr: encodeURIComponent(query),
      }).forEach(([k, v]) => url.searchParams.append(k, v));

      try {
        const response = await HttpService.safeFetch(url.toString());
        rawHints = this.processRawResponse(response) || [];

        await CacheService.instance.set(
          rawHintsKey,
          {
            data: rawHints,
            $metadata: {
              source: "edm",
              retrievedAt: new Date().toISOString(),
              pageSize: pageSize, // Track page size in metadata
            },
          },
          this.CACHE_TYPES.SEARCH
        );
      } catch (error) {
        logger.error(`API request failed: ${error.message}`);
        return {
          data: [],
          pagination: { hasMore: false },
          $metadata: {
            error: error.message,
            source: "edm",
          },
        };
      }
    } else {
      rawHints = cachedRawHints.data || [];
    }

    if (!Array.isArray(rawHints)) {
      logger.error("Invalid rawHints format, resetting cache");
      await CacheService.instance.delete(rawHintsKey, this.CACHE_TYPES.SEARCH);
      rawHints = [];
    }

    const rawHintsVersion = crypto
      .createHash("sha1")
      .update(JSON.stringify(rawHints))
      .digest("hex");

    // processed page key with page size
    const processedPageKey = `edm:search:user:${userId}:page:${normalizedQuery}:v${rawHintsVersion}:${pageNumber}:ps${pageSize}`;

    const { data: cachedPage } = await CacheService.instance.get(
      processedPageKey,
      this.CACHE_TYPES.SEARCH
    );

    if (cachedPage) {
      return {
        ...cachedPage,
        $metadata: {
          ...cachedPage.$metadata,
          cacheStatus: "hit",
          source: "edm",
        },
      };
    }

    const startIdx = Math.max(0, (pageNumber - 1) * pageSize);
    const endIdx = startIdx + pageSize;
    const batchHints = rawHints.slice(startIdx, endIdx);

    const processedItems = await this.processHints(batchHints, userId);

    const resultPayload = {
      data: processedItems,
      pagination: {
        currentPage: pageNumber,
        totalItems: rawHints.length,
        hasMore: endIdx < rawHints.length,
      },
      $metadata: {
        source: "edm",
        warnings: processedItems.filter((i) => i.$metadata.warnings?.length)
          .length,
      },
    };

    if (processedItems.length > 0) {
      await CacheService.instance.set(
        processedPageKey,
        resultPayload,
        this.CACHE_TYPES.SEARCH
      );
    }

    return {
      ...resultPayload,
      $metadata: {
        ...resultPayload.$metadata,
        cacheStatus: "miss",
      },
    };
  }

  async searchByBarcode(barcode, userId) {
    // Standardized barcode key
    const cacheKey = `edm:barcode:${userId}:${barcode}`;
    const { data: cached } = await CacheService.instance.get(
      cacheKey,
      this.CACHE_TYPES.BARCODE
    );

    if (cached) {
      return {
        ...cached,
        $metadata: {
          ...cached.$metadata,
          cacheStatus: "hit",
          source: "edm",
        },
      };
    }

    const url = new URL(PARSER_BASE_URL);
    Object.entries({
      ...this.baseSearchParams,
      upc: barcode,
    }).forEach(([key, value]) => url.searchParams.append(key, value));

    try {
      const response = await HttpService.safeFetch(url.toString());
      if (!response?.hints) return [];

      const processedItems = await this.processHints(response.hints, userId);

      await CacheService.instance.set(
        cacheKey,
        {
          data: processedItems,
          $metadata: {
            source: "edm",
            retrievedAt: new Date().toISOString(),
          },
        },
        this.CACHE_TYPES.BARCODE
      );

      return {
        data: processedItems,
        $metadata: {
          cacheStatus: "miss",
          source: "edm",
        },
      };
    } catch (error) {
      logger.error(`Barcode search failed for ${barcode}:`, error);
      return {
        data: [],
        $metadata: {
          error: error.message,
          source: "edm",
        },
      };
    }
  }

  async getNutrients(foodItem, measureURI, userId) {
    if (!measureURI || !foodItem.foodId) {
      return {
        data: null,
        $metadata: {
          success: false,
          error: "Missing measure or food ID",
          source: "edm",
        },
      };
    }

    // Convert internal ID to Edamam API ID
    const apiFoodId = EdamamHelper.fromInternal(foodItem.foodId);

    // Standardized nutrient key using internal ID format
    const cacheKey = `edm:nutrients:${userId}:${foodItem.foodId}:${measureURI}`;
    const { data: cached } = await CacheService.instance.get(
      cacheKey,
      this.CACHE_TYPES.NUTRIENT
    );

    if (cached) {
      return {
        ...cached,
        $metadata: {
          ...cached.$metadata,
          cacheStatus: "hit",
          source: "edm",
        },
      };
    }

    const url = new URL(NUTRIENTS_BASE_URL);
    url.searchParams.append("app_id", this.baseSearchParams.app_id);
    url.searchParams.append("app_key", this.baseSearchParams.app_key);

    const payload = {
      ingredients: [
        {
          quantity: 1,
          measureURI: measureURI || foodItem.measures[0]?.uri,
          foodId: apiFoodId, // Use raw API ID for the request
        },
      ],
    };

    try {
      const response = await HttpService.safeFetch(url.toString(), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      logger.debug("[Edamam] Raw nutrient response:", {
        status: response?.status,
        keys: response ? Object.keys(response) : "no-response",
      });

      const processed = NutrientProcessor.processFullNutrientData(
        response,
        "edamam"
      );

      logger.debug("[Edamam] Processed nutrients:", {
        coreKeys: processed.core ? Object.keys(processed.core) : "no-core",
        vitaminKeys: processed.vitamins
          ? Object.keys(processed.vitamins)
          : "no-vitamins",
        mineralKeys: processed.minerals
          ? Object.keys(processed.minerals)
          : "no-minerals",
      });

      const hasCoreData = processed.core?.ENERC_KCAL?.quantity > 0;

      await CacheService.instance.set(
        cacheKey,
        {
          data: processed,
          $metadata: {
            success: hasCoreData,
            hasFullData: !!processed.vitamins && !!processed.minerals,
            source: "edm",
          },
        },
        this.CACHE_TYPES.NUTRIENT
      );

      return {
        data: processed,
        $metadata: {
          cacheStatus: "miss",
          success: hasCoreData,
          hasFullData: !!processed.vitamins && !!processed.minerals,
          source: "edm",
        },
      };
    } catch (error) {
      logger.error(`Nutrient fetch failed: ${error.message}`);

      const fallback = {
        data: this.createFallbackNutrients(foodItem),
        $metadata: {
          success: true,
          hasFullData: false,
          source: "edm",
          isFallback: true,
        },
      };

      await CacheService.instance.set(
        cacheKey,
        fallback,
        this.CACHE_TYPES.NUTRIENT,
        300 // Short TTL for fallbacks
      );

      return fallback;
    }
  }

  async processHints(hints, userId) {
    if (!Array.isArray(hints)) {
      logger.error("Invalid hints format:", typeof hints);
      return [];
    }

    logger.debug(`Processing ${hints.length} hints for user ${userId}`);

    const validHints = hints.filter(
      (hint) =>
        hint?.food?.foodId &&
        Array.isArray(hint.measures) &&
        hint.measures.length > 0
    );

    if (validHints.length === 0) return [];

    const processingStart = Date.now();
    const BATCH_SIZE = 5;
    const batchedHints = [];

    for (let i = 0; i < validHints.length; i += BATCH_SIZE) {
      batchedHints.push(validHints.slice(i, i + BATCH_SIZE));
    }

    const processedItems = [];
    for (const batch of batchedHints) {
      const batchResults = await Promise.allSettled(
        batch.map((hint, index) => this.processHint(hint, userId, index))
      );

      const successfulItems = batchResults
        .filter(
          (result) =>
            result.status === "fulfilled" &&
            result.value?.foodId &&
            result.value.activeMeasure &&
            result.value.$metadata.success
        )
        .map((result) => result.value);

      processedItems.push(...successfulItems);
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    const successCount = processedItems.filter(
      (i) => i.$metadata.success
    ).length;

    logger.debug(
      `Processed ${processedItems.length} items in ${
        Date.now() - processingStart
      }ms`,
      `(Success: ${successCount})`
    );

    return this.deduplicateResults(processedItems);
  }

  async processHint(hint, userId, index) {
    const hintMetadata = {
      // processingId: `${userId}-${Date.now()}-${index}`,
      startTime: Date.now(),
      success: false,
    };

    try {
      // Convert raw API ID to internal format immediately
      const internalFoodId = EdamamHelper.toInternal(hint.food.foodId);

      // Validate measures
      const validMeasures = hint.measures.filter((m) =>
        this.validateMeasure(m)
      );
      if (validMeasures.length === 0) {
        logger.warn(`No valid measures for ${internalFoodId}`);
        return {
          foodId: internalFoodId,
          foodLabel: hint.food.label,
          $metadata: {
            ...hintMetadata,
            success: false,
            warnings: ["No valid measures found"],
            duration: Date.now() - hintMetadata.startTime,
          },
        };
      }

      const activeMeasure = {
        uri: validMeasures[0].uri,
        label: validMeasures[0].label,
        weight: validMeasures[0].weight,
      };

      const nutrients = await this.getNutrients(
        {
          foodId: internalFoodId,
          measures: validMeasures,
          nutrients: hint.food.nutrients,
        },
        activeMeasure.uri,
        userId
      );

      // Only consider successful if we have both nutrients and active measure
      const isSuccess = !!nutrients.data && !!activeMeasure;

      return {
        foodId: internalFoodId,
        foodLabel: hint.food.label,
        foodCategory: hint.food?.category || "Generic Food",
        foodBrand: hint.food?.brand || null,
        numberOfServings: 1,
        measures: validMeasures.map((m) => ({
          uri: m.uri,
          label: m.label,
          weight: m.weight,
        })),
        activeMeasure,
        nutrients: nutrients.data || null,
        defaultNutrients: {
          calories: hint.food.nutrients?.ENERC_KCAL || 0,
          protein: hint.food.nutrients?.PROCNT || 0,
          fat: hint.food.nutrients?.FAT || 0,
          carbs: hint.food.nutrients?.CHOCDF || 0,
        },
        $metadata: {
          ...hintMetadata,
          success: isSuccess,
          source: "edm",
          duration: Date.now() - hintMetadata.startTime,
          warnings: isSuccess ? [] : ["Missing required data"],
          cacheStatus: nutrients.$metadata?.cached ? "hit" : "miss",
        },
      };
    } catch (error) {
      logger.error(`Processing failed for ${hint.food.foodId}:`, error);
      return {
        foodId: EdamamHelper.toInternal(hint.food.foodId),
        foodLabel: hint.food.label,
        $metadata: {
          ...hintMetadata,
          error: error.message,
          duration: Date.now() - hintMetadata.startTime,
          warnings: ["Complete processing failure"],
        },
      };
    }
  }

  // Helper Methods
  processRawResponse(response) {
    const apiHints = [response?.hints, response?.parsed?.flatMap((p) => p.food)]
      .flat()
      .filter(Boolean);

    return this.deduplicateRawHints(
      apiHints.filter(
        (hint) =>
          hint?.food?.foodId &&
          Array.isArray(hint.measures) &&
          hint.measures.length > 0
      )
    );
  }

  deduplicateRawHints(hints) {
    const seen = new Set();
    return hints.filter((hint) => {
      const fullId = hint.food?.foodId;
      return fullId ? !seen.has(fullId) && seen.add(fullId) : false;
    });
  }

  deduplicateResults(items) {
    const uniqueMap = new Map();
    items.forEach((item) => {
      if (item?.foodId) uniqueMap.set(item.foodId, item);
    });
    return Array.from(uniqueMap.values());
  }

  normalizeQuery(query) {
    return query
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9 ]/g, "")
      .replace(/\s+/g, "-");
  }

  validateMeasure(measure) {
    return (
      measure.weight > 0 && measure.uri?.startsWith("http://www.edamam.com/")
    );
  }

  createFallbackNutrients(foodItem) {
    return {
      core: {
        ENERC_KCAL: {
          quantity: foodItem.nutrients?.ENERC_KCAL || 0,
          unit: "kcal",
        },
        PROCNT: { quantity: foodItem.nutrients?.PROCNT || 0, unit: "g" },
        FAT: { quantity: foodItem.nutrients?.FAT || 0, unit: "g" },
        CHOCDF: { quantity: foodItem.nutrients?.CHOCDF || 0, unit: "g" },
      },
      source: "fallback",
    };
  }
}

module.exports = new EdamamService();
