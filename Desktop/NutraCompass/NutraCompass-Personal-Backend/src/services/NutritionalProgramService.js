// NutritionalProgramService.js
const { CloudTasksClient } = require("@google-cloud/tasks");
const client = new CloudTasksClient();
const admin = require("../config/firebase-admin");
const db = admin.firestore();
const bucket = admin.storage().bucket();
const uuid = require("uuid");
const OpenAI = require("openai");
const logger = require("../utils/logger.js");

class NutritionalProgramService {
  constructor() {
    this.db = db;
    this.bucket = bucket;
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      organization: process.env.OPENAI_ORG_ID,
    });
  }

  // ========================================================================
  // AI METHODS
  // ========================================================================

  /**
   * Generates a meal plan based on the user's information and nutritional goals using OpenAI.
   *
   * @param {Object} userDetails - The user's information (e.g., sex, weight, goal, activity level).
   * @param {Object} nutritionalGoals - The calculated nutritional goals (e.g., dailyCalories, carbGrams, proteinGrams, fatGrams).
   * @returns {Promise<string>} The generated meal plan in string format.
   */
  async generateMealPlan(userDetails, nutritionalGoals) {
    try {
      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content:
              "You are a knowledgeable nutritionist creating meal plans based on nutritional goals.",
          },
          {
            role: "user",
            content: `Create a meal plan for a ${userDetails.sex} weighing ${userDetails.weight} lbs, with a goal to ${userDetails.goal}, an activity level of ${userDetails.activityLevel}. The daily nutritional goals are ${nutritionalGoals.dailyCalories} calories, ${nutritionalGoals.carbGrams} grams of carbs, ${nutritionalGoals.proteinGrams} grams of protein, and ${nutritionalGoals.fatGrams} grams of fat.`,
          },
        ],
      });

      return completion.choices[0].message.content;
    } catch (error) {
      logger.error("Error generating meal plan:", error);
      throw new Error("Unable to generate meal plan using OpenAI.");
    }
  }

  /**
   * Generates a workout plan based on the user's information and nutritional goals using OpenAI.
   *
   * @param {Object} userDetails - The user's information (e.g., sex, weight, goal, activity level).
   * @param {Object} nutritionalGoals - The calculated nutritional goals (e.g., dailyCalories, carbGrams, proteinGrams, fatGrams).
   * @returns {Promise<string>} The generated workout plan in string format.
   */
  async generateWorkoutPlan(userDetails, nutritionalGoals) {
    try {
      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content:
              "You are a fitness expert creating workout plans based on user goals and nutritional needs.",
          },
          {
            role: "user",
            content: `Create a workout plan for a ${userDetails.sex} weighing ${userDetails.weight} lbs, with a goal to ${userDetails.goal}, an activity level of ${userDetails.activityLevel}, and a daily intake of ${nutritionalGoals.dailyCalories} calories.`,
          },
        ],
      });

      return completion.choices[0].message.content;
    } catch (error) {
      logger.error("Error generating workout plan:", error);
      throw new Error("Unable to generate workout plan using OpenAI.");
    }
  }

  /**
   * Generates healthy habits based on the user's nutritional goals and lifestyle using OpenAI.
   *
   * @param {Object} userDetails - The parameters used for generating healthy habits (e.g., sex, weight, goal, activity level).
   * @param {Object} nutritionalGoals - The calculated nutritional goals (e.g., dailyCalories, carbGrams, proteinGrams, fatGrams).
   * @returns {Promise<string>} The generated list of healthy habits in string format.
   */
  async generateHealthyHabits(userDetails, nutritionalGoals) {
    try {
      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content:
              "You are a wellness expert generating healthy habits for a user based on their nutritional program and lifestyle.",
          },
          {
            role: "user",
            content: `Generate healthy habits for a ${userDetails.sex} weighing ${userDetails.weight} lbs, with a goal to ${userDetails.goal}, an activity level of ${userDetails.activityLevel}, and a daily intake of ${nutritionalGoals.dailyCalories} calories. The nutritional goals include ${nutritionalGoals.carbGrams} grams of carbs, ${nutritionalGoals.proteinGrams} grams of protein, and ${nutritionalGoals.fatGrams} grams of fat.`,
          },
        ],
      });

      return completion.choices[0].message.content;
    } catch (error) {
      logger.error("Error generating healthy habits:", error);
      throw new Error("Unable to generate healthy habits using OpenAI.");
    }
  }

  /**
   * Generates educational resources related to nutrition and fitness based on user input using OpenAI.
   *
   * @param {Object} userDetails - The user's information (e.g., sex, weight, goal).
   * @returns {Promise<string>} The generated educational resources in string format.
   */
  async generateEducationalResources(userDetails) {
    try {
      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content:
              "You are a health expert providing educational resources for a nutritional program.",
          },
          {
            role: "user",
            content: `Create educational resources for a ${userDetails.sex}, weighing ${userDetails.weight} lbs, who wants to ${userDetails.goal}.`,
          },
        ],
      });

      return completion.choices[0].message.content;
    } catch (error) {
      logger.error("Error generating educational resources:", error);
      throw new Error("Unable to generate educational resources using OpenAI.");
    }
  }

  // ========================================================================
  // CRUD METHODS
  // ========================================================================

  /**
   * High-level method that delegates to a specific generation function based on programType.
   *
   * @param {Object} params - All parameters from req.body (incl. programType).
   * @returns {Promise<Object>} The complete generated program.
   */
  async generateNutritionalProgram(params) {
    const { programType } = params;

    switch (programType) {
      case "Basic":
        return this.generateBasicProgram(params);

      case "AI Meal Plans":
        return this.generateAIMealPlanProgram(params);

      case "Voice-Assisted Creation":
        return this.generateVoiceAssistedProgram(params);

      case "Food Tracker":
        return this.generateFoodTrackerProgram(params);

      default:
        throw new Error(`Unsupported program type: ${programType}`);
    }
  }

  /**
   * Generates a "Basic" nutritional program
   */
  async generateBasicProgram(params) {
    // Because of Joi, we know these fields exist and are valid types:
    const {
      sex,
      heightFeet, // now guaranteed to be a number
      heightInches, // guaranteed to be a number
      weight, // guaranteed to be a number
      age, // guaranteed to be a number
      activityLevel,
      goal,
      goalWeight, // guaranteed to be a number
      startDate, // guaranteed to be a Date object
      endDate, // guaranteed to be a Date object
    } = params;

    // Example domain check: endDate must be after startDate
    const weeksUntilGoal = this.calculateWeeksDifference(startDate, endDate);
    if (weeksUntilGoal <= 0) {
      throw new Error("End date must be after start date.");
    }

    // Calculate weight changes, checkpoints, etc.
    const parsedCurrentWeight = weight;
    const parsedGoalWeight = goalWeight;
    const weightChangePerWeek =
      (parsedGoalWeight - parsedCurrentWeight) / weeksUntilGoal;
    const biweeklyWeightChange = weightChangePerWeek * 2;

    // Generate biweekly checkpoints
    const checkpoints = Array.from(
      { length: Math.ceil(weeksUntilGoal / 2) + 1 },
      (_, weekIndex) => {
        const expectedWeight =
          parsedCurrentWeight + biweeklyWeightChange * weekIndex;
        const dateAtWeek = new Date(startDate); // copy start date
        dateAtWeek.setUTCDate(dateAtWeek.getUTCDate() + weekIndex * 14);

        return {
          week: weekIndex * 2 + 1,
          expectedWeight: expectedWeight.toFixed(1),
          date: dateAtWeek,
          userLoggedWeight: null,
          progressPictureUrl: "",
          feedback: "",
          grade: "",
        };
      }
    );

    // Overwrite first and last checkpoint dates to match exactly
    checkpoints[0].date = new Date(startDate);
    checkpoints[checkpoints.length - 1].date = new Date(endDate);

    // Calculate nutritional goals
    const calculatedGoals = this.calculateDailyNutritionalGoals(
      sex,
      heightFeet,
      heightInches,
      weight,
      age,
      activityLevel,
      goal,
      goalWeight,
      startDate,
      endDate
    );

    const nutritionalGoals = {
      calorieGoal: calculatedGoals.dailyCalories,
      macroGoals: {
        carb: {
          dailyPercentage: calculatedGoals.carbPercentage,
          dailyCalories: Math.round(
            calculatedGoals.dailyCalories * calculatedGoals.carbPercentage
          ),
          dailyGrams: calculatedGoals.carbGrams,
        },
        protein: {
          dailyPercentage: calculatedGoals.proteinPercentage,
          dailyCalories: Math.round(
            calculatedGoals.dailyCalories * calculatedGoals.proteinPercentage
          ),
          dailyGrams: calculatedGoals.proteinGrams,
        },
        fat: {
          dailyPercentage: calculatedGoals.fatPercentage,
          dailyCalories: Math.round(
            calculatedGoals.dailyCalories * calculatedGoals.fatPercentage
          ),
          dailyGrams: calculatedGoals.fatGrams,
        },
      },
    };

    return {
      programType: "Basic",
      startDate,
      endDate,
      startWeight: parsedCurrentWeight,
      goalWeight: parsedGoalWeight,
      checkpoints,
      nutritionalGoals,
      feedback: "",
      grade: "",
    };
  }

  /**
   * Outline for AI Meal Plans (Placeholder)
   */
  async generateAIMealPlanProgram(params) {
    // Future: Validate any extra fields needed for AI meal plans, call AI methods, etc.
    return {
      programType: "AI Meal Plans",
      message: "AI Meal Plan generation logic to be implemented.",
    };
  }

  /**
   * Outline for Voice-Assisted Creation (Placeholder)
   */
  async generateVoiceAssistedProgram(params) {
    // Future: Possibly require transcripts, parse them, etc.
    return {
      programType: "Voice-Assisted Creation",
      message: "Voice-Assisted logic to be implemented.",
    };
  }

  /**
   * Outline for Food Tracker (Placeholder)
   */
  async generateFoodTrackerProgram(params) {
    // Future: Maybe minimal data, focusing on daily macro logging.
    return {
      programType: "Food Tracker",
      message: "Food Tracker logic to be implemented.",
    };
  }

  /**
   * Activates the provided nutritional program by saving it in the 'activeNutritionalPrograms' subcollection
   * within the user's document. Ensures that only one active program exists for a user at a time.
   * The method assigns a new program ID using UUID, sets it as active, and schedules the nutritional goals update for the start date.
   *
   * @param {string} userId - The user ID who is activating the program.
   * @param {Object} program - The nutritional program details to be saved.
   * @returns {Object} The activated program with programId and program details.
   * @throws {Error} Throws an error if the activation fails.
   */
  async activateProgram(userId, program) {
    const programId = uuid.v4();
    const programWithActiveStatus = {
      ...program,
      programId: programId,
      active: true,
      userId,
      activatedAt: new Date(),
    };

    try {
      // Check if the user already has an active nutritional program
      const activeProgramSnapshot = await this.db
        .collection("users")
        .doc(userId)
        .collection("activeNutritionalPrograms")
        .limit(1)
        .get();

      if (!activeProgramSnapshot.empty) {
        const existingProgram = activeProgramSnapshot.docs[0];
        await this.deactivateProgram(userId, existingProgram.id); // Deactivate existing program
      }

      // Save the new program to the activeNutritionalPrograms subcollection
      const activeProgramRef = this.db
        .collection("users")
        .doc(userId)
        .collection("activeNutritionalPrograms")
        .doc(programId);

      await activeProgramRef.set(programWithActiveStatus);

      return { programId, ...programWithActiveStatus };
    } catch (error) {
      logger.error("Error activating program:", error.message);
      throw new Error("Failed to activate the program: " + error.message);
    }
  }

  /**
   * Deactivates a nutritional program by moving it from 'activeNutritionalPrograms' to 'inactiveNutritionalPrograms'.
   *
   * @param {string} userId - The user ID who is deactivating the program.
   * @param {string} programId - The ID of the program to be deactivated.
   * @returns {Object} An object containing the deactivated program's ID.
   * @throws {Error} Throws an error if the deactivation fails.
   */
  async deactivateProgram(userId, programId) {
    try {
      const activeProgramRef = this.db
        .collection("users")
        .doc(userId)
        .collection("activeNutritionalPrograms")
        .doc(programId);

      const generatedProgram = await activeProgramRef.get();
      if (!generatedProgram.exists) throw new Error("Program not found");

      const program = generatedProgram.data();
      const taskName = program.taskName;
      program.active = false;
      program.deactivatedAt = new Date();

      // Move the program to inactiveNutritionalPrograms
      const inactiveProgramRef = this.db
        .collection("users")
        .doc(userId)
        .collection("inactiveNutritionalPrograms")
        .doc(programId);

      await inactiveProgramRef.set(program);
      await activeProgramRef.delete();

      // Delete the scheduled task if it exists
      if (taskName) {
        await client.deleteTask({ name: taskName });
        logger.debug(`Task ${taskName} cancelled for user ${userId}`);
      }

      return { programId };
    } catch (error) {
      logger.error("Error deactivating program:", error.message);
      throw new Error("Failed to deactivate the program: " + error.message);
    }
  }

  /**
   * Fetches the currently active nutritional program for a given user.
   * Queries the activeNutritionalPrograms subcollection within the user document.
   *
   * @param {string} userId - The unique ID of the user for whom the active program is being fetched.
   * @returns {Object|null} The active nutritional program if found, otherwise null.
   * @throws {Error} Throws an error if there is an issue fetching the program from the database.
   */
  async getActiveProgram(userId) {
    try {
      const programSnapshot = await this.db
        .collection("users")
        .doc(userId)
        .collection("activeNutritionalPrograms")
        .limit(1)
        .get();

      if (!programSnapshot.empty) {
        const program = programSnapshot.docs[0].data();
        return { programId: programSnapshot.docs[0].id, ...program };
      }

      return null; // No active program found
    } catch (error) {
      logger.error("Error fetching active program:", error.message);
      throw new Error("Failed to retrieve active nutritional program.");
    }
  }

  /**
   * Fetches all inactive nutritional programs for a given user.
   * Queries the inactiveNutritionalPrograms subcollection within the user document.
   *
   * @param {string} userId - The unique ID of the user for whom inactive programs are being fetched.
   * @returns {Array<Object>} A list of inactive nutritional programs, or an empty array if none are found.
   * @throws {Error} Throws an error if there is an issue fetching the programs from the database.
   */
  async getInactivePrograms(userId) {
    try {
      const programSnapshots = await this.db
        .collection("users")
        .doc(userId)
        .collection("inactiveNutritionalPrograms")
        .get();

      const inactivePrograms = [];
      programSnapshots.forEach((doc) => {
        inactivePrograms.push({ programId: doc.id, ...doc.data() });
      });

      return inactivePrograms;
    } catch (error) {
      logger.error("Error fetching inactive programs:", error.message);
      throw new Error("Failed to retrieve inactive nutritional programs.");
    }
  }

  /**
   * Deletes a single inactive nutritional program and its associated progress pictures.
   *
   * @param {string} userId - The ID of the user.
   * @param {string} programId - The ID of the program to delete.
   * @returns {Promise<void>}
   * @throws {Error} Throws an error if deletion fails.
   */
  async deleteNutritionalProgram(userId, programId) {
    const programRef = this.db
      .collection("users")
      .doc(userId)
      .collection("inactiveNutritionalPrograms")
      .doc(programId);

    try {
      const programDoc = await programRef.get();
      if (!programDoc.exists) {
        throw new Error("Nutritional program not found.");
      }

      const programData = programDoc.data();
      const checkpoints = programData.checkpoints || [];

      // Delete using direct path construction where possible
      const deleteOps = checkpoints.map((checkpoint) => {
        if (!checkpoint.progressPictureUrl) return null;

        try {
          // New path format deletion attempt
          const path = `progressPictures/${userId}/programs/${programId}/checkpoints/${checkpoint.week}/progressPicture_${checkpoint.week}.jpg`;
          return this.deleteImage(path);
        } catch (error) {
          // Fallback to URL-based deletion
          return this.deleteImageByUrl(checkpoint.progressPictureUrl);
        }
      });

      await Promise.all(deleteOps);
      // Delete the program document
      await programRef.delete();
    } catch (error) {
      logger.error("Error deleting nutritional program:", error.message);
      throw new Error("Failed to delete the nutritional program.");
    }
  }

  /**
   * Deletes multiple inactive nutritional programs and their associated progress pictures in a batch.
   *
   * @param {string} userId - The ID of the user.
   * @param {Array<string>} programIds - An array of program IDs to delete.
   * @returns {Promise<void>}
   * @throws {Error} Throws an error if deletion fails.
   */
  async deleteNutritionalProgramsBatch(userId, programIds) {
    if (!Array.isArray(programIds) || programIds.length === 0) {
      throw new Error("Program IDs must be a non-empty array.");
    }

    const batch = this.db.batch();
    const imagePaths = [];

    try {
      // Iterate through each program ID to prepare deletions
      for (const programId of programIds) {
        const programRef = this.db
          .collection("users")
          .doc(userId)
          .collection("inactiveNutritionalPrograms")
          .doc(programId);

        const programDoc = await programRef.get();
        if (!programDoc.exists) {
          logger.warn(`Nutritional program with ID ${programId} not found.`);
          continue; // Skip non-existent programs
        }

        const programData = programDoc.data();
        const checkpoints = programData.checkpoints || [];

        // Collect both path formats for deletion
        checkpoints.forEach((checkpoint) => {
          if (checkpoint.progressPictureUrl) {
            // New path format
            imagePaths.push(
              `progressPictures/${userId}/programs/${programId}/checkpoints/${checkpoint.week}/progressPicture_${checkpoint.week}.jpg`
            );
            // Legacy URL format
            imagePaths.push(checkpoint.progressPictureUrl);
          }
        });

        batch.delete(programRef);
      }

      // Execute all deletions
      await Promise.all([
        batch.commit(),
        Promise.allSettled(
          imagePaths.map((path) =>
            path.includes("progressPictures/")
              ? this.deleteImage(path)
              : this.deleteImageByUrl(path)
          )
        ),
      ]);
    } catch (error) {
      logger.error("Error batch deleting nutritional programs:", error.message);
      throw new Error("Failed to batch delete nutritional programs.");
    }
  }

  // ========================================================================
  // CHECKPOINT METHODS
  // ========================================================================

  /**
   * Logs the user's weight for a specific checkpoint in the active nutritional program and calculates a grade and feedback based on performance.
   *
   * @param {string} userId - The ID of the user.
   * @param {string} checkpointId - The ID of the checkpoint.
   * @param {number} userLoggedWeight - The user's logged weight.
   * @returns {Object} The updated checkpoint with grade and feedback.
   * @throws {Error} Throws an error if logging fails.
   */
  async logCheckpointWeight(userId, checkpointId, userLoggedWeight) {
    try {
      // Get the user's active nutritional program
      const programSnapshot = await this.db
        .collection("users")
        .doc(userId)
        .collection("activeNutritionalPrograms")
        .limit(1)
        .get();

      if (programSnapshot.empty) {
        throw new Error("No active nutritional program found.");
      }

      const programDoc = programSnapshot.docs[0];
      const programData = programDoc.data();

      // Convert checkpointId to a number for proper comparison
      const numericCheckpointId = Number(checkpointId);

      // Find and update the specific checkpoint
      const updatedCheckpoints = programData.checkpoints.map((checkpoint) => {
        if (checkpoint.week === numericCheckpointId) {
          const expectedWeight = parseFloat(checkpoint.expectedWeight);
          const deviation = Math.abs(userLoggedWeight - expectedWeight);

          // Determine the grade based on deviation
          let grade;
          if (deviation <= 1) {
            grade = "A"; // Excellent
          } else if (deviation <= 2) {
            grade = "B"; // Good
          } else if (deviation <= 5) {
            grade = "C"; // Fair
          } else {
            grade = "D"; // Needs Improvement
          }

          // Provide feedback based on grade
          let feedback;
          switch (grade) {
            case "A":
              feedback = "Great job! You’re right on track with your goal.";
              break;
            case "B":
              feedback =
                "Good work, but you might want to make slight adjustments.";
              break;
            case "C":
              feedback =
                "You’re slightly off track. Review your plan and make adjustments.";
              break;
            case "D":
              feedback =
                "You’re far from your goal. Consider revisiting your habits and seek guidance.";
              break;
            default:
              feedback = "";
          }

          return {
            ...checkpoint,
            userLoggedWeight,
            grade,
            feedback,
          };
        }
        return checkpoint;
      });

      // Update the program in the database
      await programDoc.ref.update({ checkpoints: updatedCheckpoints });

      // Return the updated checkpoint
      const updatedCheckpoint = updatedCheckpoints.find(
        (checkpoint) => checkpoint.week === numericCheckpointId
      );

      return updatedCheckpoint;
    } catch (error) {
      logger.error("Error logging checkpoint weight:", error.message);
      throw new Error("Failed to log checkpoint weight: " + error.message);
    }
  }

  // ========================================================================
  // IMAGE MANAGEMENT METHODS
  // ========================================================================

  /**
   * Uploads a progress picture for a specific checkpoint.
   *
   * @param {string} userId - The user's ID.
   * @param {string} checkpointId - The checkpoint's ID.
   * @param {Object} imageFile - The progress picture image file.
   * @returns {Promise<string>} The public URL of the uploaded progress picture.
   * @throws {Error} Throws an error if the upload fails.
   */
  async uploadProgressPicture(userId, checkpointId, imageFile) {
    if (!userId || !checkpointId || !imageFile) {
      throw new Error(
        "Missing required parameters to upload progress picture."
      );
    }

    try {
      // Get the user's active nutritional program document
      const programSnapshot = await this.db
        .collection("users")
        .doc(userId)
        .collection("activeNutritionalPrograms")
        .limit(1)
        .get();

      if (programSnapshot.empty) {
        throw new Error("Active nutritional program not found.");
      }

      const programDoc = programSnapshot.docs[0];
      const programData = programDoc.data();
      const programId = programDoc.id; // Get actual program ID
      const imageName = `progressPicture_${checkpointId}.jpg`;
      const imagePath = `progressPictures/${userId}/programs/${programId}/checkpoints/${checkpointId}/${imageName}`;

      // Check for existing image
      const file = this.bucket.file(imagePath);
      const [exists] = await file.exists().catch((error) => {
        logger.warn("Existence check failed:", error);
        return [false]; // Assume no existing file if check fails
      });

      // Delete only if exists
      if (exists) {
        await file.delete();
      }

      // Upload new image with cache-busting
      const imageUrl = await this.uploadImage(imageFile, imagePath, userId);

      // Update the specific checkpoint with the new progress picture URL
      const updatedCheckpoints = programData.checkpoints.map((checkpoint) => {
        if (checkpoint.week === Number(checkpointId)) {
          checkpoint.progressPictureUrl = imageUrl;
        }
        return checkpoint;
      });

      await programDoc.ref.update({ checkpoints: updatedCheckpoints });

      return imageUrl;
    } catch (error) {
      logger.error("Failed to upload progress picture:", error);
      throw new Error("Error uploading progress picture.");
    }
  }

  /**
   * Deletes a progress picture for a specific checkpoint.
   *
   * @param {string} userId - The user's ID.
   * @param {string} checkpointId - The checkpoint's ID.
   * @returns {Promise<void>}
   * @throws {Error} Throws an error if the deletion fails.
   */
  async deleteProgressPicture(userId, checkpointId) {
    if (!userId || !checkpointId) {
      throw new Error("Missing required parameters");
    }

    try {
      const programSnapshot = await this.db
        .collection("users")
        .doc(userId)
        .collection("activeNutritionalPrograms")
        .limit(1)
        .get();

      if (programSnapshot.empty) throw new Error("Program not found");

      const programDoc = programSnapshot.docs[0];
      const programId = programDoc.id;
      const imageName = `progressPicture_${checkpointId}.jpg`;
      const imagePath = `progressPictures/${userId}/programs/${programId}/checkpoints/${checkpointId}/${imageName}`;

      // Delete from storage
      await this.deleteImage(imagePath);

      // Update Firestore
      const updatedCheckpoints = programDoc
        .data()
        .checkpoints.map((cp) =>
          cp.week === Number(checkpointId)
            ? { ...cp, progressPictureUrl: "" }
            : cp
        );

      await programDoc.ref.update({ checkpoints: updatedCheckpoints });
    } catch (error) {
      logger.error("Delete failed:", error);
      throw error.code === 404
        ? new Error("Image not found")
        : new Error("Deletion failed");
    }
  }

  /**
   * Deletes an image from Google Cloud Storage given its public URL.
   *
   * @param {string} imageUrl - The public URL of the image to delete.
   * @returns {Promise<void>}
   * @throws {Error} Throws an error if deletion fails.
   */
  async deleteImageByUrl(imageUrl) {
    try {
      const url = new URL(imageUrl);
      // Remove query parameters and decode URI components
      const cleanPath = decodeURIComponent(url.pathname.split("?")[0]);
      // Extract path after bucket name
      const filePath = cleanPath.replace(`/${this.bucket.name}/`, "");

      await this.bucket.file(filePath).delete();
    } catch (error) {
      if (error.code === 404) {
        logger.warn("Image not found, continuing:", imageUrl);
        return;
      }
      logger.error("Delete image by URL failed:", error);
      throw error;
    }
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  /**
   * Uploads an image file to Google Cloud Storage and returns the public URL.
   *
   * @param {Object} imageFile - The image file to upload.
   * @param {string} path - The storage path within the bucket.
   * @param {string} userId - The user id.
   * @returns {Promise<string>} A promise that resolves with the public URL of the uploaded image.
   * @throws {Error} Throws an error if the upload fails.
   */
  async uploadImage(imageFile, path, userId) {
    const file = this.bucket.file(path);

    // Upload as public file
    await file.save(imageFile.buffer, {
      metadata: {
        contentType: "image/jpeg",
        cacheControl: "public, max-age=31536000",
      },
      public: true, // Make file publicly accessible
    });

    // Encode each path segment separately
    const encodedPath = path
      .split("/")
      .map((segment) => encodeURIComponent(segment))
      .join("/");

    // Return public URL
    return `https://storage.googleapis.com/${this.bucket.name}/${encodedPath}`;
  }

  /**
   * Deletes an image from Google Cloud Storage.
   *
   * @param {string} imagePath - The path to the image in Google Cloud Storage.
   * @returns {Promise<void>}
   * @throws {Error} Throws an error if the deletion fails.
   */
  async deleteImage(imagePath) {
    try {
      // Remove any URL parameters and decode URI
      const cleanPath = decodeURIComponent(imagePath.split("?")[0]);
      const file = this.bucket.file(cleanPath);

      const [exists] = await file.exists();
      if (exists) {
        await file.delete();
        return true;
      }
      return false;
    } catch (error) {
      if (error.code === 404) {
        logger.warn("Image not found:", imagePath);
        return false;
      }
      throw error;
    }
  }

  /**
   * Helper method to calculate total weeks between two dates
   */
  calculateWeeksDifference(start, end) {
    // start and end are Date objects
    const diffMs = end - start;
    return Math.ceil(diffMs / (1000 * 60 * 60 * 24 * 7));
  }

  /**
   * Calculates the daily nutritional goals for the user based on their body metrics, goal, and activity level.
   *
   * @param {string} sex - The biological sex of the user ("Male" or "Female").
   * @param {number} heightFeet - Height in feet.
   * @param {number} heightInches - Height in inches.
   * @param {number} weight - Current body weight in pounds.
   * @param {number} age - Age of the user.
   * @param {string} activityLevel - Activity level of the user.
   * @param {string} goal - The user's goal (e.g., "Maintain", "Lose", "Build").
   * @param {number} goalWeight - The user's target weight in pounds.
   * @param {string} startDate - The start date of the program.
   * @param {string} endDate - The end date of the program.
   * @returns {Object} An object containing the daily nutritional goals (calories, macronutrient breakdown).
   */
  calculateDailyNutritionalGoals(
    sex,
    heightFeet,
    heightInches,
    weight,
    age,
    activityLevel,
    goal,
    goalWeight,
    startDate,
    endDate
  ) {
    // Convert height to centimeters and weight to kilograms
    const totalHeightInCm = heightFeet * 30.48 + heightInches * 2.54;
    const weightInKg = weight * 0.453592;

    // Calculate Basal Metabolic Rate (BMR)
    let bmr;
    if (sex === "Male") {
      bmr = 10 * weightInKg + 6.25 * totalHeightInCm - 5 * age + 5;
    } else {
      bmr = 10 * weightInKg + 6.25 * totalHeightInCm - 5 * age - 161;
    }

    // Activity level multipliers
    const activityMultipliers = {
      None: 1,
      "Sedentary (BMR x 0.2)": 1.2,
      "Lightly Active (BMR x 0.375)": 1.375,
      "Moderately Active (BMR x 0.5)": 1.5,
      "Very Active (BMR x 0.9)": 1.9,
    };
    const activityMultiplier = activityMultipliers[activityLevel];
    const maintenanceCalories = Math.floor(bmr * activityMultiplier);

    let dailyCalories = null;

    // Handle goalWeight and endDate for calorie calculation
    if (goalWeight && startDate && endDate) {
      const initialDate = new Date(startDate);
      const targetDate = new Date(endDate);

      const timeDifferenceInDays = Math.floor(
        (targetDate - initialDate) / (1000 * 60 * 60 * 24)
      );
      const goalWeightInKg = goalWeight * 0.453592;
      const weightDifference = goalWeightInKg - weightInKg;

      logger.debug("Weight Difference (kg):", weightDifference);
      logger.debug("Time Difference (days):", timeDifferenceInDays);

      // 1 kg of weight change ~ 7700 calories
      const totalCaloriesForWeightChange = weightDifference * 7700;
      logger.debug(
        "Total Calories for Weight Change:",
        totalCaloriesForWeightChange
      );

      if (timeDifferenceInDays > 0) {
        const totalCaloriesToConsume =
          maintenanceCalories * timeDifferenceInDays +
          totalCaloriesForWeightChange;
        dailyCalories = totalCaloriesToConsume / timeDifferenceInDays;
      } else {
        logger.error("Invalid time range for the program.");
      }
    } else {
      // Default approach for Maintain, Lose, Build if dates/goalWeight not provided
      if (goal === "Maintain") {
        dailyCalories = maintenanceCalories;
      } else if (goal === "Lose") {
        dailyCalories = maintenanceCalories - 500;
      } else if (goal === "Build") {
        dailyCalories = maintenanceCalories + 500;
      }
    }

    logger.debug("Calculated Daily Calories:", dailyCalories);

    if (dailyCalories === null) {
      logger.error("Daily calories calculation resulted in null.");
    }

    // Macronutrient breakdown
    const carbRatio = 0.4;
    const proteinRatio = 0.3;
    const fatRatio = 0.3;

    const carbCalories = dailyCalories * carbRatio;
    const proteinCalories = dailyCalories * proteinRatio;
    const fatCalories = dailyCalories * fatRatio;

    const carbGrams = Math.round(carbCalories / 4);
    const proteinGrams = Math.round(proteinCalories / 4);
    const fatGrams = Math.round(fatCalories / 9);

    return {
      dailyCalories: Math.round(dailyCalories),
      carbPercentage: carbRatio,
      proteinPercentage: proteinRatio,
      fatPercentage: fatRatio,
      carbGrams,
      proteinGrams,
      fatGrams,
    };
  }
}

module.exports = new NutritionalProgramService(); // Export as a singleton instance
