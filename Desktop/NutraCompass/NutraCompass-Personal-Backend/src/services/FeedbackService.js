const { Client } = require("@notionhq/client");
const logger = require("../utils/logger");
const Configs = require("../../configs.js");

const notion = new Client({ auth: Configs.notionConfig.NOTION_API_KEY });
const NOTION_DB_ID = Configs.notionConfig.NOTION_FEEDBACK_DB_ID;

class FeedbackService {
  /**
   * Sends feedback data to Notion database
   * @param {Object} feedbackData - Feedback data to store
   * @param {string} feedbackData.userId - User ID
   * @param {string} feedbackData.name - name
   * @param {string} feedbackData.email - User email
   * @param {string} feedbackData.message - Feedback message
   * @param {string} feedbackData.timestamp - ISO timestamp
   */
  static async sendToNotion(feedbackData) {
    try {
      await notion.pages.create({
        parent: { database_id: NOTION_DB_ID },
        properties: {
          "User ID": {
            title: [{ text: { content: feedbackData.userId } }],
          },
          Name: {
            rich_text: [{ text: { content: feedbackData.name } }],
          },
          Email: {
            email: feedbackData.email,
          },
          Message: {
            rich_text: [{ text: { content: feedbackData.message } }],
          },
          Date: {
            date: { start: feedbackData.timestamp },
          },
          Status: {
            select: { name: "New" },
          },
        },
      });
      logger.info(`Feedback submitted to Notion by ${feedbackData.email}`);
    } catch (error) {
      logger.error("Notion API error:", error);
      throw new Error("Failed to save to Notion database");
    }
  }
}

module.exports = FeedbackService;
