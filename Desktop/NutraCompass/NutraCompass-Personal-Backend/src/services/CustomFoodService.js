const CacheService = require("./CacheService.js");
const NutrientProcessor = require("../utils/NutrientProcessor.js");
const crypto = require("crypto");
const FoodModel = require("../models/CustomFoodModel.js");
const { CustomHelper } = require("../utils/foodIdHelper.js");
const logger = require("../utils/logger.js");

class CustomFoodService {
  constructor() {
    // Define cache service types
    this.CACHE_TYPES = {
      SEARCH: "FOOD_SEARCH_QUERY",
      BARCODE: "FOOD_SEARCH_BARCODE",
      NUTRIENT: "FOOD_SEARCH_NUTRIENT",
    };
    this.DEFAULT_PAGE_SIZE = 5;
  }

  async searchByQuery(query, userId, page = 1) {
    const normalizedQuery = this._normalizeQuery(query);
    const pageNumber = Math.max(1, parseInt(page, 10)) || 1;

    // Base cache key for raw results
    const baseCacheKey = `cust:search:user:${userId}:query:${normalizedQuery}`;
    let rawResults = [];
    const { data: cachedBase } = await CacheService.instance.get(
      baseCacheKey,
      this.CACHE_TYPES.SEARCH
    );

    if (!cachedBase) {
      rawResults = await FoodModel.find({
        name: { $regex: new RegExp(normalizedQuery, "i") },
      });

      await CacheService.instance.set(
        baseCacheKey,
        {
          data: rawResults,
          $metadata: {
            source: "cust",
            retrievedAt: new Date().toISOString(),
          },
        },
        this.CACHE_TYPES.SEARCH
      );
    } else {
      rawResults = cachedBase.data || [];
    }

    // Generate version hash of raw results
    const resultsVersion = crypto
      .createHash("sha1")
      .update(JSON.stringify(rawResults))
      .digest("hex");

    // Processed page key with versioning
    const processedPageKey = `${baseCacheKey}:v${resultsVersion}:page:${pageNumber}`;
    const { data: cachedPage } = await CacheService.instance.get(
      processedPageKey,
      this.CACHE_TYPES.SEARCH
    );

    if (cachedPage) {
      return {
        ...cachedPage,
        $metadata: {
          ...cachedPage.$metadata,
          cacheStatus: "hit",
          source: "cust",
        },
      };
    }

    // Client-side pagination (aligned with other services)
    const startIdx = Math.max(0, (pageNumber - 1) * this.DEFAULT_PAGE_SIZE);
    const endIdx = startIdx + this.DEFAULT_PAGE_SIZE;
    const paginatedData = rawResults.slice(startIdx, endIdx);

    const formatted = paginatedData.map((item) => ({
      foodId: CustomHelper.toInternal(item._id.toString()),
      name: item.name,
      brand: item.brand || "User Submitted",
      nutrients: item.nutrients,
      measures: item.measures || [{ label: "100g", uri: "100g" }],
      activeMeasure: item.activeMeasure || { label: "100g", uri: "100g" },
      source: "cust",
      $metadata: {
        creatorId: item.creatorId,
        createdAt: item.createdAt,
      },
    }));

    await CacheService.instance.set(
      processedPageKey,
      {
        data: formatted,
        pagination: {
          currentPage: pageNumber,
          totalItems: rawResults.length,
          hasMore: endIdx < rawResults.length,
        },
        $metadata: {
          source: "cust",
        },
      },
      this.CACHE_TYPES.SEARCH
    );

    return {
      data: formatted,
      pagination: {
        currentPage: pageNumber,
        totalItems: rawResults.length,
        hasMore: endIdx < rawResults.length,
      },
      $metadata: {
        cacheStatus: "miss",
        source: "cust",
      },
    };
  }

  async getNutrients(foodItem, measureURI, userId) {
    if (!CustomHelper.validate(foodItem?.foodId)) {
      return {
        data: null,
        $metadata: {
          success: false,
          error: "Invalid custom food ID format",
          source: "cust",
        },
      };
    }

    const dbId = CustomHelper.fromInternal(foodItem.foodId);
    const cacheKey = `cust:nutrients:${userId}:${dbId}:${measureURI}`;
    const { data: cached } = await CacheService.instance.get(
      cacheKey,
      this.CACHE_TYPES.NUTRIENT
    );

    if (cached) {
      return {
        ...cached,
        $metadata: {
          ...cached.$metadata,
          cacheStatus: "hit",
          source: "cust",
        },
      };
    }

    try {
      const dbItem = await FoodModel.findById(dbId);
      if (!dbItem) throw new Error("Custom food not found");

      const normalized = NutrientProcessor.processFullNutrientData(
        dbItem.nutrients,
        "cust"
      );

      await CacheService.instance.set(
        cacheKey,
        {
          data: normalized,
          $metadata: {
            success: true,
            source: "cust",
          },
        },
        this.CACHE_TYPES.NUTRIENT
      );

      return {
        data: normalized,
        $metadata: {
          cacheStatus: "miss",
          success: true,
          source: "cust",
          retrievedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error(`[Custom Food Error] ${error.message}`);
      return {
        data: null,
        $metadata: {
          cacheStatus: "error",
          success: false,
          error: error.message,
          source: "cust",
        },
      };
    }
  }

  async createCustomFood({
    name,
    brand = "User Submitted",
    nutrients,
    creatorId,
    measures = [{ label: "100g", uri: "100g" }],
    activeMeasure = { label: "100g", uri: "100g" },
  }) {
    if (!name || !nutrients || !creatorId) {
      throw new Error("Missing required fields for custom food creation");
    }

    try {
      const newEntry = await FoodModel.create({
        name,
        brand,
        nutrients,
        creatorId,
        measures,
        activeMeasure,
      });

      // Invalidate relevant cache entries
      await CacheService.instance.flushPattern(`cust:search:*:${creatorId}:*`);

      return {
        foodId: CustomHelper.toInternal(newEntry._id.toString()),
        name: newEntry.name,
        nutrients: newEntry.nutrients,
        source: "cust",
        $metadata: {
          creatorId: newEntry.creatorId,
          createdAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error("[Custom Food Creation Error]", error);
      throw new Error("Failed to create custom food entry");
    }
  }

  _normalizeQuery(query) {
    return query.trim().toLowerCase().replace(/\s+/g, "_");
  }
}

module.exports = new CustomFoodService();
