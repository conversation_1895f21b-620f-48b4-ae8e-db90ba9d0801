const admin = require("../config/firebase-admin");
const { sendEmailWithTemplate } = require("../coms/EmailService");
const logger = require("../utils/logger");
const CacheService = require("./CacheService");
const TokenService = require("./TokenService");
const UserSettingsService = require("./UserSettingsService");

class AuthService {
  static instance = null;

  constructor() {
    if (!AuthService.instance) {
      AuthService.instance = this;
    }
    return AuthService.instance;
  }

  // ================================
  // User Registration
  // ================================

  async register({ email, password, settings = {}, role = "user" }) {
    try {
      // Validate role
      const validRoles = ["user", "personal-trainer", "gym-owner", "admin"];
      if (!validRoles.includes(role)) {
        throw new Error(`Invalid role: ${role}`);
      }

      // Create Firebase user
      const userRecord = await admin.auth().createUser({ email, password });

      // Set custom claims for role-based access
      const customClaims = {
        "https://nutracompass.io/roles": role,
      };
      await admin.auth().setCustomUserClaims(userRecord.uid, customClaims);

      // Create user document with default settings and role
      const userDocument = UserSettingsService.createUserDocument(userRecord, {
        ...settings,
        account: {
          ...(settings.account || {}),
          role,
        },
      });

      // Save to Firestore
      await UserSettingsService.saveUserDocument(userRecord.uid, userDocument);

      // Generate OAuth tokens
      const tokens = await TokenService.generateTokens({
        userId: userRecord.uid,
        clientId: "nutracompass-app",
        role,
      });

      // Send welcome email
      const userName = settings?.profile?.firstName || "User";
      await sendEmailWithTemplate(
        "nutracompass-personal-user-registration",
        email,
        "Welcome to NutraCompass!",
        [{ name: "userName", content: userName }]
      );

      return {
        userId: userRecord.uid,
        ...tokens,
      };
    } catch (error) {
      logger.error("Registration Error: ", error);
      throw new Error("Registration failed: " + error.message);
    }
  }

  // ================================
  // User Authentication
  // ================================

  async authenticateUser(email, password) {
    try {
      // Rate limiting for authentication attempts
      const rateLimitKey = `auth_attempt:${email}`;
      const attempts =
        (await CacheService.instance.get(rateLimitKey, "AUTH")) || 0;

      if (attempts >= 5) {
        throw new Error("Too many attempts. Try again later.");
      }

      // Authenticate with Firebase
      const userCredential = await admin
        .auth()
        .signInWithEmailAndPassword(email, password);

      // Reset rate limit on successful auth
      await CacheService.instance.delete(rateLimitKey, "AUTH");

      return this._generateAuthResponse(userCredential.user);
    } catch (error) {
      // Handle rate limit errors
      if (error.message.includes("Too many attempts")) {
        throw error;
      }

      // Increment failed attempt count
      const rateLimitKey = `auth_attempt:${email}`;
      const attempts =
        (await CacheService.instance.get(rateLimitKey, "AUTH")) || 0;
      await CacheService.instance.set(
        rateLimitKey,
        attempts + 1,
        "AUTH",
        { ttl: 300 } // 5-minute lockout
      );

      // Handle Firebase errors
      if (
        error.code === "auth/wrong-password" ||
        error.code === "auth/user-not-found"
      ) {
        return null;
      }

      logger.error("Authentication error:", error);
      throw new Error("Authentication service unavailable");
    }
  }

  // ================================
  // Social Authentication
  // ================================

  async authenticateWithGoogle(idToken) {
    try {
      // Verify Google token
      const credential = admin.auth.GoogleAuthProvider.credential(idToken);
      const userCredential = await admin
        .auth()
        .signInWithCredential(credential);

      const user = userCredential.user;

      // Create user document if new user
      if (userCredential.additionalUserInfo?.isNewUser) {
        const providerData = {
          email: user.email,
          firstName: user.displayName?.split(" ")[0] || "",
          lastName: user.displayName?.split(" ").slice(1).join(" ") || "",
        };

        const userDocument = UserSettingsService.createUserDocument(
          user,
          {},
          providerData
        );

        await UserSettingsService.saveUserDocument(user.uid, userDocument);
      }

      return this._generateAuthResponse(user);
    } catch (error) {
      logger.error("Google authentication error:", error);
      throw new Error("Google authentication failed");
    }
  }

  async authenticateWithApple(idToken, nonce, providerData = {}) {
    try {
      // Verify Apple token
      const provider = new admin.auth.OAuthProvider("apple.com");
      const credential = provider.credential({ idToken, rawNonce: nonce });
      const userCredential = await admin
        .auth()
        .signInWithCredential(credential);

      const user = userCredential.user;

      // Create user document if new user
      if (userCredential.additionalUserInfo?.isNewUser) {
        const userDocument = UserSettingsService.createUserDocument(
          user,
          {},
          providerData
        );

        await UserSettingsService.saveUserDocument(user.uid, userDocument);
      }

      return this._generateAuthResponse(user);
    } catch (error) {
      logger.error("Apple authentication error:", error);
      throw new Error("Apple authentication failed");
    }
  }

  // ================================
  // Token Management
  // ================================

  async refreshTokens(refreshToken) {
    try {
      const newTokens = await TokenService.refreshTokens(
        refreshToken,
        "nutracompass-app"
      );
      return newTokens;
    } catch (error) {
      logger.error("Token refresh error:", error);
      throw new Error("Invalid refresh token");
    }
  }

  async revokeToken(token, tokenTypeHint = "access_token") {
    try {
      return await TokenService.revokeToken(token, tokenTypeHint);
    } catch (error) {
      logger.error("Token revocation error:", error);
      throw new Error("Token revocation failed");
    }
  }

  // ================================
  // Helper Methods
  // ================================

  async _generateAuthResponse(firebaseUser) {
    // Get custom claims
    const claims = await admin.auth().getUser(firebaseUser.uid);
    const role =
      claims.customClaims?.["https://nutracompass.io/roles"] || "user";

    // Generate OAuth tokens
    const tokens = await TokenService.generateTokens({
      userId: firebaseUser.uid,
      clientId: "nutracompass-app",
      role,
    });

    return {
      userId: firebaseUser.uid,
      email: firebaseUser.email,
      role,
      ...tokens,
    };
  }

  async verifyToken(idToken) {
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      if (!decodedToken || !decodedToken.uid) throw new Error("Invalid token");
      return {
        uid: decodedToken.uid,
        email: decodedToken.email,
        email_verified: decodedToken.email_verified || false,
        role: decodedToken["https://nutracompass.io/roles"] || "user",
      };
    } catch (error) {
      logger.error("Token Verification Error: ", error);
      throw new Error("Failed to verify token: " + error.message);
    }
  }

  async checkEmailExists(email) {
    try {
      const userRecord = await admin.auth().getUserByEmail(email);
      return !!userRecord;
    } catch (error) {
      if (error.code === "auth/user-not-found") {
        return false;
      }
      logger.error("Error checking email existence:", error);
      throw new Error("Failed to check email existence.");
    }
  }

  async sendPasswordResetEmail(email) {
    try {
      // Generate Firebase reset link
      const link = await admin.auth().generatePasswordResetLink(email, {
        url: process.env.PASSWORD_RESET_URL,
        handleCodeInApp: true,
      });

      // Get user first name from Firestore
      const user = await admin.auth().getUserByEmail(email);
      const userDoc = await admin.firestore().doc(`users/${user.uid}`).get();
      const firstName = userDoc.data()?.profile?.firstName || "User";

      // Send email
      await sendEmailWithTemplate(
        "nutracompass-password-reset",
        email,
        "Reset Your NutraCompass Password",
        [
          { name: "userName", content: firstName },
          { name: "resetLink", content: link },
        ]
      );
    } catch (error) {
      logger.error("Password reset email error:", error);
      throw new Error("Failed to send reset email");
    }
  }

  // ================================
  // Subscription Management
  // ================================

  async upgradeUserRole(userId, newRole) {
    try {
      // Validate role
      const validRoles = ["user", "personal-trainer", "gym-owner", "admin"];
      if (!validRoles.includes(newRole)) {
        throw new Error(`Invalid role: ${newRole}`);
      }

      // Update Firebase custom claims
      await admin.auth().setCustomUserClaims(userId, {
        "https://nutracompass.io/roles": newRole,
      });

      // Update Firestore document
      const userRef = admin.firestore().doc(`users/${userId}`);
      await userRef.update({
        "account.role": newRole,
        "account.lastUpdated": admin.firestore.FieldValue.serverTimestamp(),
      });

      return true;
    } catch (error) {
      logger.error("Role upgrade error:", error);
      throw new Error("Failed to upgrade user role");
    }
  }

  async addPremiumFeature(userId, featureId) {
    try {
      const userRef = admin.firestore().doc(`users/${userId}`);

      // Default feature configuration
      const featureConfig = {
        access: true,
        expiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        usageLimit: 100,
        usageCount: 0,
      };

      // Update user document
      await userRef.update({
        [`account.subscriptionFeatures.${featureId}`]: true,
        [`account.entitlements.${featureId}`]: featureConfig,
        [`subscriptions`]: admin.firestore.FieldValue.arrayUnion({
          featureId,
          startDate: admin.firestore.FieldValue.serverTimestamp(),
          expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          status: "active",
        }),
      });

      return true;
    } catch (error) {
      logger.error("Feature addition error:", error);
      throw new Error("Failed to add premium feature");
    }
  }

  async checkFeatureAccess(userId, featureId) {
    try {
      const userDoc = await admin.firestore().doc(`users/${userId}`).get();
      if (!userDoc.exists) return false;

      const userData = userDoc.data();
      const entitlement = userData.account?.entitlements?.[featureId];

      if (!entitlement || !entitlement.access) return false;

      // Check expiry
      if (entitlement.expiry && new Date() > entitlement.expiry.toDate()) {
        return false;
      }

      // Check usage limits
      if (
        entitlement.usageLimit &&
        entitlement.usageCount >= entitlement.usageLimit
      ) {
        return false;
      }

      return true;
    } catch (error) {
      logger.error("Feature access check error:", error);
      return false;
    }
  }

  // ================================
  // Verification Code Management
  // ================================

  /**
   * Sends a verification code to the specified email.
   * Generates a unique one-time code and emails it to the user.
   *
   * @param {string} email - The user's email address.
   * @param {string} firstName - The user's first name.
   */
  async sendVerificationCode(email, firstName) {
    try {
      const code = this.generateCode(); // Generate a unique 6-digit code
      const userName = firstName || (await this.getUserNameByEmail(email));

      // Cleanup any existing codes before generating a new one
      await this.cleanupExpiredCode(email);
      await this.storeCode(email, code); // Store the code in a database or cache

      await sendEmailWithTemplate(
        "nutracompass-personal-registration-verification-code",
        email,
        "Your Registration Verification Code",
        [
          { name: "userName", content: userName },
          { name: "code", content: code },
        ]
      );
    } catch (error) {
      logger.error("Error sending verification code:", error);
      throw new Error("Failed to send verification code.");
    }
  }

  /**
   * Verifies the code entered by the user.
   * Validates the provided code against the stored code.
   *
   * @param {string} email - The user's email address.
   * @param {string} code - The verification code entered by the user.
   * @returns {boolean} - True if the code is valid, otherwise false.
   */
  async verifyCode(email, code) {
    try {
      const isValid = await this.validateCode(email, code);

      if (!isValid) {
        throw new Error("Invalid or expired verification code.");
      }
      return true;
    } catch (error) {
      logger.error("Error verifying code:", error);
      throw new Error("Failed to verify code.");
    }
  }

  // ================================
  // Password & Email Management
  // ================================

  /**
   * Generates a password reset link and sends it to the user's email address.
   * The reset link allows users to update their passwords securely.
   *
   * @param {string} email - The user's email address.
   * @param {string} firstName - The user's first name.
   */
  async resetPassword(email, firstName) {
    try {
      const resetLink = await this.generateResetPasswordLink(email);
      const userName = firstName || (await this.getUserNameByEmail(email));
      await sendEmailWithTemplate(
        "nutracompass-personal-user-changes-password",
        email,
        "Reset Your Password",
        [
          { name: "userName", content: userName },
          { name: "resetLink", content: resetLink },
        ]
      );
    } catch (error) {
      logger.error("Error generating password reset link:", error);
      throw new Error("Failed to generate password reset link.");
    }
  }

  async generateResetPasswordLink(email) {
    // Get environment-safe URL
    const resetUrl = this.getResetPasswordUrl();

    return admin.auth().generatePasswordResetLink(email, {
      url: resetUrl,
      handleCodeInApp: true,
    });
  }

  getResetPasswordUrl() {
    // Handle missing environment variable
    if (typeof process.env.NODE_ENV === "undefined") {
      throw new Error("NODE_ENV is not defined!");
    }

    // Return URL with protocol validation
    const baseUrl =
      process.env.NODE_ENV === "production"
        ? "https://nutracompass-personal-prod.firebaseapp.com"
        : "https://nutracompass-individual.firebaseapp.com";

    // Ensure valid URL format
    try {
      new URL(baseUrl); // Will throw if invalid
      return baseUrl;
    } catch (e) {
      throw new Error(`Invalid reset URL: ${baseUrl}`);
    }
  }

  // ================================
  // Account Management
  // ================================

  /**
   * Deletes a user account, removing their authentication record, Firestore data, and associated storage files.
   * Sends a confirmation email upon successful deletion.
   *
   * @param {string} userId - The user's unique identifier.
   * @param {string} firstName - The user's first name.
   */
  async deleteUserAccount(userId, firstName) {
    try {
      const db = admin.firestore();
      const bucket = admin.storage().bucket();
      const user = await admin.auth().getUser(userId);
      const userDocRef = db.collection("users").doc(userId);

      // 1. Delete all subcollections first
      const subcollections = await userDocRef.listCollections();
      for (const subcollection of subcollections) {
        await this._deleteEntireCollection(subcollection);
      }

      // 2. Delete main user document
      await userDocRef.delete();

      // 3. Delete storage files
      const folders = ["profilePictures", "customMeals", "progressPictures"];
      for (const folder of folders) {
        const filePrefix = `${folder}/${userId}/`;
        const [files] = await bucket.getFiles({ prefix: filePrefix });

        for (const file of files) {
          await file.delete();
        }
      }

      // 4. Delete authentication record
      await admin.auth().deleteUser(userId);

      // 5. Invalidate analytics cache
      process.emit("userAccountDeleted");

      // 6. Send confirmation email
      const userName = firstName || (await this.getUserNameByEmail(user.email));
      await sendEmailWithTemplate(
        "nutracompass-personal-user-deletes-account",
        user.email,
        "Your Account Has Been Deleted",
        [{ name: "userName", content: userName }]
      );
    } catch (error) {
      logger.error("Error deleting user account:", error);
      throw new Error("Failed to delete user account: " + error.message);
    }
  }

  // ================================
  // Helper Functions
  // ================================

  /**
   * Recursively deletes all documents in a Firestore collection/subcollection
   * using batched writes for safety and efficiency.
   *
   * @param {admin.firestore.CollectionReference} collectionRef - Reference to the collection
   * @param {number} [batchSize=500] - Maximum documents to delete per batch (Firestore limit: 500)
   * @returns {Promise<void>}
   *
   * @example
   * const userRef = db.collection('users').doc('userId');
   * const postsCollection = userRef.collection('posts');
   * await _deleteEntireCollection(postsCollection);
   */
  async _deleteEntireCollection(collectionRef, batchSize = 500) {
    // Query with ordering and batch size limit
    const query = collectionRef.orderBy("__name__").limit(batchSize);

    // Batch deletion loop
    while (true) {
      // Get current batch of documents
      const snapshot = await query.get();

      // Exit condition: no more documents
      if (snapshot.empty) break;

      // Initialize batched write
      const batch = collectionRef.firestore.batch();

      // Queue all documents in current batch for deletion
      snapshot.docs.forEach((doc) => batch.delete(doc.ref));

      // Commit batched deletions
      await batch.commit();
    }
  }

  /**
   * Generates a unique 6-digit verification code.
   * @returns {string} - A 6-digit numeric code.
   */
  generateCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Cleans up expired verification codes for an email
   * @param {string} email - Email to cleanup
   */
  async cleanupExpiredCode(email) {
    const db = admin.firestore();
    const docRef = db.collection("verificationCodes").doc(email);

    try {
      const doc = await docRef.get();
      if (doc.exists) {
        const { expiresAt } = doc.data();
        if (Date.now() > expiresAt) {
          await docRef.delete();
          logger.info(`Cleaned up expired code for ${email}`);
        }
      }
    } catch (error) {
      logger.error("Cleanup error:", error);
    }
  }

  /**
   * Background task to clean up all expired codes
   * This function can be scheduled to run periodically as a cloud function.
   */
  async cleanupAllExpiredCodes() {
    const db = admin.firestore();
    const now = Date.now();

    try {
      const snapshot = await db
        .collection("verificationCodes")
        .where("expiresAt", "<", now)
        .get();

      const batch = db.batch();
      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      logger.info(`Cleaned up ${snapshot.size} expired verification codes`);
    } catch (error) {
      logger.error("Mass cleanup error:", error);
    }
  }

  /**
   * Stores a verification code for a user's email.
   * Typically stores the code in an in-memory cache or database with an expiration time.
   *
   * @param {string} email - The user's email address.
   * @param {string} code - The verification code to store.
   */
  async storeCode(email, code) {
    const db = admin.firestore();
    const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes from now
    await db
      .collection("verificationCodes")
      .doc(email)
      .set({ code, expiresAt });
  }

  /**
   * Validates a verification code against the stored code.
   * Ensures the code matches and has not expired.
   *
   * @param {string} email - The user's email address.
   * @param {string} code - The verification code entered by the user.
   * @returns {boolean} - True if the code is valid, otherwise false.
   */
  async validateCode(email, code) {
    const db = admin.firestore();
    const docRef = db.collection("verificationCodes").doc(email);
    const doc = await docRef.get();

    if (!doc.exists) {
      return false; // No code stored for this email
    }

    const { code: storedCode, expiresAt } = doc.data();
    const now = Date.now();

    // Always delete expired codes regardless of match
    if (now > expiresAt) {
      await docRef.delete(); // CRITICAL: Clean up expired code
      return false;
    }

    if (storedCode !== code) {
      return false; // Code mismatch
    }

    // Delete valid code after successful validation
    await docRef.delete();
    return true;
  }

  /**
   * Helper function to get userName by email.
   * Queries Firestore to retrieve the user's name based on their email address stored in the profile object.
   * @param {string} email - The email address of the user.
   * @returns {Promise<string>} The user's name, or a fallback if not available.
   */
  async getUserNameByEmail(email) {
    logger.debug("Fetching user's first name...");
    try {
      const db = admin.firestore();
      const userSnapshot = await db
        .collection("users")
        .where("profile.email", "==", email)
        .get();

      if (!userSnapshot.empty) {
        const userData = userSnapshot.docs[0].data();
        return userData?.profile?.firstName || "User";
      }

      return "User"; // Fallback if no user data is found
    } catch (error) {
      logger.error("Error fetching user name by email:", error);
      return "User"; // Fallback in case of an error
    }
  }
}

module.exports = new AuthService();
