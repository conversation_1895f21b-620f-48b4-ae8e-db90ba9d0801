const admin = require("../config/firebase-admin");
const db = admin.firestore();
const uuid = require("uuid");
const FoodMenuService = require("./FoodMenuService");
const CacheService = require("./CacheService");
const {
  subMonths,
  addMonths,
  format,
  parseISO,
  isWithinInterval,
} = require("date-fns");
const logger = require("../utils/logger");

function cleanFirestoreObject(obj) {
  const cleaned = { ...obj };
  // Remove Firestore metadata properties
  delete cleaned.$metadata;
  delete cleaned._metadata;
  delete cleaned.__metadata;
  return cleaned;
}

/**
 * Service class for handling the data layer concerning food diary functionalities.
 * This includes operations on food entries, meal sections, and custom meals.
 */
class FoodDiaryService {
  constructor() {
    this.db = db;
    this.pendingRequests = new Map(); // For request deduplication
    this.versionKey = (userId) => `foodLogEntries:${userId}:version`;
    // Define cache service types
    this.CACHE_TYPES = {
      FOOD_DIARY: "FOOD_DIARY",
      FOOD_DIARY_VERSION: "FOOD_DIARY_VERSION",
    };
  }

  async getDataVersion(userId) {
    try {
      // Get version with proper service type
      const result = await CacheService.instance.get(
        this.versionKey(userId),
        this.CACHE_TYPES.FOOD_DIARY_VERSION // This must match your config
      );

      // If no version exists, initialize it
      if (!result.data) {
        return this.bumpDataVersion(userId);
      }

      return result.data;
    } catch (error) {
      logger.error("[Version] Fetch failed, initializing new version:", error);
      // If version check fails completely, start fresh
      return this.bumpDataVersion(userId);
    }
  }

  async bumpDataVersion(userId) {
    const newVersion = Date.now();

    try {
      // Update version
      await CacheService.instance.set(
        `foodDiaryVersion:${userId}`,
        newVersion,
        this.CACHE_TYPES.FOOD_DIARY_VERSION,
        2592000 // 30 days
      );

      // NEW: Clear all food entries for this user
      await CacheService.instance.flushPattern(
        `foodLogEntries:${userId}:*`,
        this.CACHE_TYPES.FOOD_DIARY
      );

      logger.debug(`[Version] Bumped to ${newVersion} for ${userId}`);
      return newVersion;
    } catch (error) {
      logger.error("[Version] Critical update failure:", error);
      throw new Error("Failed to update data version");
    }
  }

  /**
   * Retrieves all custom meal sections for a given user, ensuring the user ID is valid.
   * @param {string} userId - User ID to identify the correct user's meal sections.
   * @returns {Promise<Array>} A list of meal sections or an empty array if none found.
   */
  async getMealSections(userId) {
    if (!userId) {
      throw new Error("Invalid user ID provided.");
    }
    try {
      const collectionRef = this.db.collection(
        `users/${userId}/customMealSections`
      );
      const snapshot = await collectionRef.get();

      // If the collection is empty, create default meal sections
      if (snapshot.empty) {
        const defaultSections = [
          { id: "Meal 1", name: "Breakfast" },
          { id: "Meal 2", name: "Lunch" },
          { id: "Meal 3", name: "Dinner" },
          { id: "Meal 4", name: "" },
          { id: "Meal 5", name: "" },
          { id: "Meal 6", name: "" },
          { id: "Water", name: "Water" },
        ];

        // Use a batch to create all default meal sections
        const batch = this.db.batch();
        defaultSections.forEach((section) => {
          const sectionRef = collectionRef.doc(section.id);
          batch.set(sectionRef, { name: section.name });
        });
        await batch.commit();

        // Return the default sections after creating them
        return defaultSections;
      }

      // Otherwise, return the existing sections
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      logger.error("Failed to fetch meal sections:", error);
      throw new Error("Error retrieving meal sections.");
    }
  }

  /**
   * Updates the names of meal sections for a given user.
   * Checks if each section exists before attempting an update.
   * Uses batch processing to efficiently handle multiple updates.
   * @param {string} userId - User's unique identifier.
   * @param {Array} updates - Array of objects containing meal section IDs and their new names.
   * @returns {Promise<Array>} The updated meal sections.
   */
  async updateMealSectionNames(userId, updates) {
    if (!userId || !updates || !Array.isArray(updates)) {
      throw new Error("Invalid input parameters.");
    }

    const batch = this.db.batch();
    for (const update of updates) {
      const { id, newName } = update;

      const docRef = this.db.doc(`users/${userId}/customMealSections/${id}`);
      const docSnap = await docRef.get();

      if (docSnap.exists) {
        batch.update(docRef, { name: newName });
      } else {
        batch.set(docRef, { name: newName });
      }
    }

    try {
      await batch.commit();
      logger.debug("Batch update committed successfully.");
      return this.getMealSections(userId);
    } catch (error) {
      logger.error("Failed to commit batch update:", error);
      throw new Error("Error updating meal section names.");
    }
  }

  /**
   * Fetches the most recent food history for a specific user.
   * Filters out water entries and standalone quick add entries that are not part of any custom meals.
   * Groups consecutive entries that belong to the same custom meal to avoid inflating the history limit.
   * Uses Promise.all for concurrent fetching of entries and custom meals for efficiency.
   * @param {string} userId - User's unique identifier.
   * @returns {Promise<Array>} A list of filtered and grouped food history items, limiting to the first 20 entries.
   * @throws {Error} If the userId is invalid or the database query fails.
   */
  async fetchUserFoodHistory(userId) {
    if (!userId) {
      throw new Error("Invalid user ID provided.");
    }

    try {
      // Fetch both entries and custom meals concurrently
      const [entriesSnapshot, customMeals] = await Promise.all([
        this.db
          .collection(`users/${userId}/foodLogEntries`)
          .orderBy("timestamp", "desc")
          .get(),
        FoodMenuService.getCustomMeals(userId),
      ]);

      const entries = entriesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Map custom meals by their ID for easy lookup
      const customMealsById = new Map(
        customMeals.map((meal) => [meal.id, meal])
      );

      const combinedHistory = [];
      let i = 0;

      // Traverse the entries and group them appropriately
      while (i < entries.length) {
        const entry = entries[i];

        // 1. Skip water entries
        if (entry.foodCategory === "Water") {
          i++;
          continue;
        }

        // 2. Handle new meal-header entries
        if (entry.type === "meal-header") {
          const savedMeal = customMealsById.get(entry.mealId);
          if (savedMeal) {
            // Add the saved meal without timestamp
            const { timestamp, ...mealWithoutTimestamp } = savedMeal;
            combinedHistory.push(mealWithoutTimestamp);

            // Skip all meal-items associated with this header
            while (
              i < entries.length &&
              entries[i].type === "meal-header" &&
              entries[i].id === entry.id
            ) {
              i++;
            }
            continue;
          }
        }

        // 3. Handle old custom meal items
        if (entry.inACustomMeal?.isPartOfMeal && entry.inACustomMeal.mealId) {
          const mealId = entry.inACustomMeal.mealId;
          const savedMeal = customMealsById.get(mealId);

          if (savedMeal) {
            // Add the saved meal without timestamp
            const { timestamp, ...mealWithoutTimestamp } = savedMeal;
            combinedHistory.push(mealWithoutTimestamp);

            // Skip all entries that are part of this meal
            while (
              i < entries.length &&
              entries[i].inACustomMeal?.mealId === mealId
            ) {
              i++;
            }
            continue;
          }
        }

        // 4. Skip standalone Quick Add entries
        if (
          entry.foodCategory === "Quick Add" &&
          !entry.inACustomMeal?.isPartOfMeal
        ) {
          i++;
          continue;
        }

        // 5. Add all other entries to history
        combinedHistory.push(entry);
        i++;
      }

      // Return the first 20 items from the combined history
      return combinedHistory.slice(0, 20);
    } catch (error) {
      logger.error("Failed to fetch food history:", error);
      throw new Error("Error retrieving food history.");
    }
  }

  /**
   * Fetches entries within 6 months of selectedDate
   * Implements version-aware range-based caching
   */
  async getFoodEntries(userId, selectedDate, clientVersion) {
    try {
      logger.debug("[FoodDiary] Fetching entries for", {
        userId,
        selectedDate,
        clientVersion,
      });

      // Validate date format and validity
      this._validateDate(selectedDate);
      const range = this._getDateRange(selectedDate);
      const currentVersion = await this.getDataVersion(userId);

      // NEW: Client version validation
      if (clientVersion && clientVersion === currentVersion) {
        logger.debug("[FoodDiary] Client version current:", currentVersion);
        return {
          statusCode: 304,
          source: "client",
          version: currentVersion,
        };
      }

      // NEW: Simplified cache key (without version)
      const cacheKey = `foodLogEntries:${userId}:${range.start}:${range.end}`;

      // Check cache with the current version
      const cachedResponse = await CacheService.instance.get(
        cacheKey,
        this.CACHE_TYPES.FOOD_DIARY
      );

      // NEW: Validate cache version
      if (cachedResponse?.data?.version === currentVersion) {
        logger.debug("[FoodDiary] Versioned cache hit", {
          key: cacheKey,
          version: currentVersion,
        });

        if (!this._validateCacheStructure(cachedResponse.data)) {
          logger.warn("[FoodDiary] Invalid cache structure, purging");
          await CacheService.instance.delete(
            cacheKey,
            this.CACHE_TYPES.FOOD_DIARY
          );
        } else {
          return {
            data: this._safeClone(cachedResponse.data.data),
            source: cachedResponse.source,
            range: range,
            version: currentVersion,
          };
        }
      }

      logger.debug("[FoodDiary] Cache miss or version mismatch", {
        cacheVersion: cachedResponse?.data?.version,
        currentVersion,
      });

      // Fetch from database
      const query = this.db
        .collection(`users/${userId}/foodLogEntries`)
        .where("date", ">=", range.start)
        .where("date", "<=", range.end);

      const snapshot = await query.get();
      const filteredDocs = snapshot.docs.filter((doc) => {
        const docDate = doc.get("date");
        return docDate >= range.start && docDate <= range.end;
      });

      // Process results
      const entries = this._groupByDateAndMeal(filteredDocs) || {};

      // NEW: Cache entry with version metadata
      const cacheEntry = {
        version: currentVersion, // Store version with cached data
        generatedAt: new Date().toISOString(),
        data: entries,
      };

      await CacheService.instance.set(
        cacheKey,
        cacheEntry,
        this.CACHE_TYPES.FOOD_DIARY, // Use the defined cache type
        86400 // 24 hours
      );

      return {
        data: this._safeClone(entries),
        source: "database",
        range: range,
        version: currentVersion,
      };
    } catch (error) {
      logger.error("[FoodDiary] Critical failure", error);
      return {
        error: "Food entry retrieval failed",
        statusCode: 500,
        details: error.message,
      };
    }
  }

  // Add helper method to prevent undefined data
  _safeClone(data) {
    try {
      return JSON.parse(JSON.stringify(data || {}));
    } catch (error) {
      logger.warn("[FoodDiary] Clone failed, returning empty", error);
      return {};
    }
  }

  // Date validation helper
  _validateDate(date) {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      throw new Error("Invalid date format. Use YYYY-MM-DD");
    }

    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      throw new Error("Invalid date: Not a valid calendar date");
    }

    if (dateObj.toISOString().split("T")[0] !== date) {
      throw new Error("Invalid date: Non-existent date");
    }
  }

  _validateCacheStructure(cachedEntry) {
    try {
      // Validate top-level structure
      const isValidTopLevel =
        cachedEntry &&
        typeof cachedEntry === "object" &&
        typeof cachedEntry.version === "number" &&
        typeof cachedEntry.generatedAt === "string" &&
        typeof cachedEntry.data === "object" &&
        cachedEntry.data !== null;

      if (!isValidTopLevel) return false;

      // Handle empty data case
      if (Object.keys(cachedEntry.data).length === 0) {
        return true; // Empty data is valid
      }

      // Validate nested structure
      return Object.values(cachedEntry.data).every((dateEntry) => {
        return Object.values(dateEntry).every((mealSection) => {
          return (
            Array.isArray(mealSection) &&
            mealSection.every((item) => {
              // Validate entries based on their type
              if (item.type === "meal-header") {
                return (
                  item.mealId &&
                  item.mealName &&
                  typeof item.mealId === "string" &&
                  typeof item.mealName === "string"
                );
              }
              // All other entries must have id and foodLabel
              return (
                item &&
                typeof item === "object" &&
                "id" in item &&
                "foodLabel" in item
              );
            })
          );
        });
      });
    } catch (e) {
      logger.warn("[Cache Validation] Error:", e);
      return false;
    }
  }

  // Helper to calculate 6-month window
  _getDateRange(date) {
    // Handle both string and Date inputs
    const baseDate = typeof date === "string" ? parseISO(date) : date;

    return {
      start: format(subMonths(baseDate, 3), "yyyy-MM-dd"),
      end: format(addMonths(baseDate, 3), "yyyy-MM-dd"),
    };
  }

  // Organize data for frontend
  _groupByDateAndMeal(docs) {
    const mealsMap = new Map();
    const items = [];

    // Separate headers and items
    docs.forEach((doc) => {
      const data = doc.data();
      if (data.type === "meal-header") {
        mealsMap.set(data.id, { ...data, items: [] });
      } else {
        items.push(data);
      }
    });

    // Assign items to their headers
    items.forEach((item) => {
      if (item.mealHeaderId && mealsMap.has(item.mealHeaderId)) {
        mealsMap.get(item.mealHeaderId).items.push(item);
      }
    });

    // Sort items within meals
    mealsMap.forEach((meal) => {
      meal.items.sort((a, b) => a.mealItemIndex - b.mealItemIndex);
    });

    // Convert to date-based grouping
    return Array.from(mealsMap.values())
      .concat(items)
      .reduce((acc, entry) => {
        const { date, mealType } = entry;
        acc[date] = acc[date] || {};
        acc[date][mealType] = acc[date][mealType] || [];

        // For meal headers, push the header and its items
        if (entry.type === "meal-header") {
          acc[date][mealType].push(entry); // Push the header
          // Push all items belonging to this header
          entry.items.forEach((item) => {
            acc[date][mealType].push(item);
          });
        }
        // For standalone items (no mealHeaderId) or orphaned items
        else if (!entry.mealHeaderId || !mealsMap.has(entry.mealHeaderId)) {
          acc[date][mealType].push(entry);
        }

        return acc;
      }, {});
  }

  /**
   * Saves a complete meal to the user's food log, creating new food entry documents.
   * Each item in the meal has a unique document associated with it.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealType - The type of meal (e.g., breakfast, lunch).
   * @param {string} selectedDate - The date on which the meal is logged.
   * @param {Array} mealItems - Array of food items making up the meal.
   * @param {string} mealId - Unique identifier for the meal
   * @param {string} mealName - Name of the meal
   * @param {string} [mealImage] - Optional image URL for the meal
   * @returns {Promise<Object>} Confirmation message and details of the new entries.
   */
  async saveMeal(
    userId,
    mealType,
    selectedDate,
    mealItems,
    mealId,
    mealName,
    mealImage
  ) {
    // Enhanced error message with missing parameters
    const missingParams = [];
    if (!userId) missingParams.push("userId");
    if (!mealType) missingParams.push("mealType");
    if (!selectedDate) missingParams.push("selectedDate");
    if (!mealItems || mealItems.length === 0) missingParams.push("mealItems");
    if (!mealId) missingParams.push("mealId");
    if (!mealName) missingParams.push("mealName");

    if (missingParams.length > 0) {
      throw new Error(
        `Missing required parameters for saving meal: ${missingParams.join(
          ", "
        )}`
      );
    }

    try {
      const collectionRef = this.db.collection(
        `users/${userId}/foodLogEntries`
      );
      const batch = this.db.batch();
      const entryItems = [];
      const mealHeaderId = uuid.v4();

      // Create meal header
      const mealHeaderEntry = cleanFirestoreObject({
        id: mealHeaderId,
        type: "meal-header",
        mealId,
        mealName,
        mealImageUrl: mealImage || "",
        mealType,
        date: selectedDate,
        clientTimestamp: new Date().toISOString(),
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });

      batch.set(collectionRef.doc(mealHeaderId), mealHeaderEntry);
      entryItems.push(mealHeaderEntry);

      // Create meal items
      mealItems.forEach((item, index) => {
        const uniqueId = uuid.v4();
        let newEntry = cleanFirestoreObject({
          id: uniqueId,
          type: "meal-item",
          mealHeaderId,
          mealItemIndex: index,
          foodLabel: item.foodLabel,
          nutrients: item.nutrients || {},
          numberOfServings: item.numberOfServings || 1,
          mealType,
          date: selectedDate,
          clientTimestamp: new Date().toISOString(),
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });

        if (item.foodCategory !== "Quick Add") {
          newEntry = cleanFirestoreObject({
            ...newEntry,
            foodId: item.foodId,
            foodCategory: item.foodCategory || "",
            foodBrand: item.foodBrand || "",
            activeMeasure: item.activeMeasure,
            measures: item.measures || [],
          });
        }

        batch.set(collectionRef.doc(uniqueId), newEntry);
        entryItems.push(newEntry);
      });

      await batch.commit();
      const newVersion = await this.bumpDataVersion(userId);

      return {
        message: "Meal saved successfully",
        version: newVersion,
        entryItems: entryItems || [],
      };
    } catch (error) {
      logger.error("Failed to save meal:", error);
      throw new Error("Error saving meal to food log: " + error.message);
    }
  }

  /**
   * Adds or updates a single food entry in the user's food log based on the presence of an entry ID.
   * If an entry ID is provided, it attempts to update the existing entry. If no entry ID is found,
   * or the provided ID does not correspond to an existing document, it treats the operation as a new entry.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealType - The type of meal (e.g., breakfast, lunch).
   * @param {string} entryId - The Firestore document ID of the food entry to update.
   * @param {Object} updatedEntry - The new data for the food entry.
   * @param {string} selectedDate - The date of the food entry.
   * @param {string} [originalHeaderId] - (Optional) The ID of the original meal header if this entry is being moved from a group.
   * @param {string} [originalDate] - (Optional) The original date of the meal header.
   * @param {string} [originalMealType] - (Optional) The original meal type of the meal header.
   * @returns {Promise<Object>} Result of the update operation or the addition of a new entry.
   */
  async saveOrUpdateSingleFoodItemToFoodLog(
    userId,
    mealType,
    entryId,
    updatedEntry,
    selectedDate,
    originalHeaderId = null,
    originalDate = null,
    originalMealType = null
  ) {
    const db = this.db;
    const batch = db.batch();
    let deletedHeaderId = null;

    // Prepare the entry document
    const entryRef = entryId
      ? db.collection(`users/${userId}/foodLogEntries`).doc(entryId)
      : db.collection(`users/${userId}/foodLogEntries`).doc();

    const finalEntry = cleanFirestoreObject({
      ...updatedEntry,
      id: entryId || entryRef.id,
      mealType,
      date: selectedDate,
      clientTimestamp: new Date().toISOString(),
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    if (entryId) {
      batch.update(entryRef, finalEntry);
    } else {
      batch.set(entryRef, finalEntry);
    }

    // Check if we need to delete abandoned header
    if (originalHeaderId && originalDate && originalMealType) {
      const headerRef = db
        .collection(`users/${userId}/foodLogEntries`)
        .doc(originalHeaderId);

      // Check if header exists
      const headerDoc = await headerRef.get();
      if (headerDoc.exists) {
        // Count remaining items in original group EXCLUDING the current entry
        const itemsQuery = db
          .collection(`users/${userId}/foodLogEntries`)
          .where("mealHeaderId", "==", originalHeaderId)
          .where("date", "==", originalDate)
          .where("mealType", "==", originalMealType);

        const itemsSnapshot = await itemsQuery.get();

        // Filter out the current entry that's being moved
        const remainingItems = itemsSnapshot.docs.filter(
          (doc) => doc.id !== entryId
        );

        // If no items remain, delete the header
        if (remainingItems.length === 0) {
          batch.delete(headerRef);
          deletedHeaderId = originalHeaderId;
        }
      }
    }

    // Execute all operations
    await batch.commit();
    const newVersion = await this.bumpDataVersion(userId);

    return {
      message: entryId
        ? "Entry updated successfully"
        : "Entry created successfully",
      version: newVersion,
      entry: finalEntry,
      deletedHeaderId,
    };
  }

  /**
   * Adds a new food entry for a user, checking for all necessary parameters.
   * @param {string} userId - User's unique identifier.
   * @param {string} mealType - The type of meal for the food entry.
   * @param {Object} foodEntry - The food entry data.
   * @returns {Promise<void>}
   */
  async addFoodEntry(userId, mealType, foodEntry, selectedDate) {
    const collectionRef = this.db.collection(`users/${userId}/foodLogEntries`);
    const uniqueId = uuid.v4();

    const newEntry = cleanFirestoreObject({
      id: uniqueId,
      type: "food",
      mealHeaderId: null,
      mealItemIndex: null,
      foodId: foodEntry.foodId,
      foodLabel: foodEntry.foodLabel,
      foodCategory: foodEntry.foodCategory || "",
      foodBrand: foodEntry.foodBrand || "",
      numberOfServings: foodEntry.numberOfServings,
      activeMeasure: foodEntry.activeMeasure,
      measures: foodEntry.measures || [],
      nutrients: foodEntry.nutrients,
      mealType: mealType,
      date: selectedDate,
      clientTimestamp: new Date().toISOString(),
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    try {
      await collectionRef.doc(uniqueId).set(newEntry);

      // After successful write
      // await CacheService.instance.flushPattern(`foodLogEntries:${userId}:*`);
      const newVersion = await this.bumpDataVersion(userId);

      return {
        message: "Food log entry added successfully",
        version: newVersion,
        entry: newEntry,
      };
    } catch (error) {
      logger.error("Error saving food log entry:", error);
      throw new Error("Failed to save food log entry.");
    }
  }

  /**
   * Updates a specific food entry, ensuring all parameters are valid and the entry exists.
   * @param {string} userId - User's unique identifier.
   * @param {string} entryId - The specific entry's ID to update.
   * @param {Object} foodEntry - Updated data for the food entry.
   * @returns {Promise<void>}
   */
  async updateFoodEntry(userId, entryId, updatedEntry) {
    const docRef = this.db
      .collection(`users/${userId}/foodLogEntries`)
      .doc(entryId);
    try {
      const docSnapshot = await docRef.get();
      if (docSnapshot.exists) {
        // Clean entry and add timestamps
        const finalEntry = cleanFirestoreObject({
          ...updatedEntry,
          clientTimestamp: new Date().toISOString(),
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });

        await docRef.set(finalEntry, { merge: true });

        // After successful write
        // await CacheService.instance.flushPattern(`foodLogEntries:${userId}:*`);
        const newVersion = await this.bumpDataVersion(userId);

        return {
          message: "Food log entry updated successfully",
          version: newVersion,
          entry: updatedEntry,
        };
      } else {
        throw new Error("No such document!");
      }
    } catch (error) {
      logger.error("Error updating food log entry:", error);
      throw new Error("Failed to update food log entry.");
    }
  }

  /**
   * Deletes a specific food entry, and deletes the meal header if all its items are gone.
   * @param {string} userId - User's unique identifier.
   * @param {string} entryId - The specific entry's ID to delete.
   * @returns {Promise<void>}
   */
  async deleteFoodEntry(userId, entryId) {
    if (!userId || !entryId) {
      throw new Error("Missing required parameters to delete food entry.");
    }
    try {
      const docRef = this.db.doc(`users/${userId}/foodLogEntries/${entryId}`);
      const doc = await docRef.get();

      if (!doc.exists) {
        throw new Error("Entry not found");
      }

      const entryData = doc.data();
      const batch = this.db.batch();
      let headerIdToCheck = null;

      // Delete the entry
      batch.delete(docRef);

      // If deleting a meal item, remember its header for cleanup check
      if (entryData.type === "meal-item" && entryData.mealHeaderId) {
        headerIdToCheck = entryData.mealHeaderId;
      }

      // If deleting a meal header, delete all its items
      if (entryData.type === "meal-header") {
        // Query for all meal items with this header id
        const itemsQuery = this.db
          .collection(`users/${userId}/foodLogEntries`)
          .where("mealHeaderId", "==", entryId)
          .where("type", "==", "meal-item");

        const itemsSnapshot = await itemsQuery.get();
        itemsSnapshot.forEach((itemDoc) => {
          batch.delete(itemDoc.ref);
        });
      }

      await batch.commit();

      // Check if header needs to be deleted after deleting a meal item
      if (headerIdToCheck) {
        await this.deleteHeaderIfEmpty(userId, headerIdToCheck);
      }

      // Bump data version after successful write
      const newVersion = await this.bumpDataVersion(userId);

      return {
        message: "Entry deleted successfully",
        version: newVersion,
      };
    } catch (error) {
      logger.error("Failed to delete food entry:", error);
      throw new Error("Error deleting food entry.");
    }
  }

  /**
   * Deletes a meal header if it has no remaining items
   * @param {string} userId - User ID
   * @param {string} headerId - Header ID to check
   */
  async deleteHeaderIfEmpty(userId, headerId) {
    try {
      const headerRef = this.db.doc(
        `users/${userId}/foodLogEntries/${headerId}`
      );
      const itemsQuery = this.db
        .collection(`users/${userId}/foodLogEntries`)
        .where("mealHeaderId", "==", headerId)
        .where("type", "==", "meal-item");

      const [headerDoc, itemsSnapshot] = await Promise.all([
        headerRef.get(),
        itemsQuery.get(),
      ]);

      // Delete header if it exists and has no items
      if (headerDoc.exists && itemsSnapshot.empty) {
        await headerRef.delete();
        logger.info(`Deleted orphaned meal header: ${headerId}`);
      }
    } catch (error) {
      logger.error("Error checking/deleting meal header:", error);
    }
  }

  /**
   * Adds or updates a quick food entry in the user's food log.
   *
   * This function handles the addition of new quick food entries and the updating of existing entries
   * based on the presence of an `id` property in the entryDetails object. It is designed to be robust
   * and flexible, ensuring data integrity and providing clear feedback on the operation performed.
   *
   * @param {string} userId - The user's unique identifier.
   * @param {string} mealType - The type of meal, e.g., breakfast, lunch, etc.
   * @param {Object} entryDetails - Details of the food entry, including nutritional information.
   * @param {string} selectedDate - The date for which the food entry is logged.
   * @returns {Promise<Object>} A promise that resolves to a message and the entry details.
   * @throws {Error} Throws an error if required parameters are missing or if the Firestore operation fails.
   */
  async addQuickFoodEntry(userId, mealType, entryDetails, selectedDate) {
    // Validate required parameters to ensure robustness and avoid runtime errors.
    if (!userId || !mealType || !entryDetails || !selectedDate) {
      throw new Error(
        "Required parameters are missing for adding a quick food entry."
      );
    }

    // Reference to the Firestore collection where food log entries are stored.
    const foodLogEntriesCollectionRef = this.db.collection(
      `users/${userId}/foodLogEntries`
    );

    // Determine if this is an update operation by checking for an existing entry ID.
    const entryId = entryDetails.id ? entryDetails.id : uuid.v4(); // Use existing ID or generate a new one.

    // Construct the entry object to be stored in Firestore.
    const newEntry = {
      id: entryId,
      type: "food",
      mealHeaderId: null,
      mealItemIndex: null,
      foodLabel: entryDetails.foodLabel,
      nutrients: entryDetails.nutrients,
      foodCategory: "Quick Add",
      numberOfServings: entryDetails.numberOfServings || 1, // Default to one serving if not specified.
      mealType: mealType,
      date: selectedDate,
      clientTimestamp: new Date().toISOString(),
      timestamp: admin.firestore.FieldValue.serverTimestamp(), // Ensure time consistency with server time.
    };

    try {
      // Create or update the document in Firestore based on the presence of an entry ID.
      const docRef = foodLogEntriesCollectionRef.doc(entryId);
      if (entryDetails.id) {
        // Update the existing document with the provided entry details.
        await docRef.update(newEntry);

        // After successful write
        // await CacheService.instance.flushPattern(`foodLogEntries:${userId}:*`);

        return {
          message: "Quick food entry updated successfully",
          entry: newEntry,
        };
      } else {
        // Create a new document with the new entry details.
        await docRef.set(newEntry);

        // After successful write
        // await CacheService.instance.flushPattern(`foodLogEntries:${userId}:*`);
        const newVersion = await this.bumpDataVersion(userId);

        return {
          message: "Quick food entry added successfully",
          version: newVersion,
          entry: newEntry,
        };
      }
    } catch (error) {
      // Log and rethrow errors related to Firestore operations to handle them upstream.
      logger.error("Failed to save quick food entry:", error);
      throw new Error("Error saving quick food entry.");
    }
  }

  /**
   * Deletes all entries for a meal section and date, including orphaned headers
   * @param {string} userId - User ID
   * @param {string} mealSectionId - Meal section ID
   * @param {string} date - Date in YYYY-MM-DD format
   */
  async deleteAllEntriesByMealSectionAndDate(userId, mealSectionId, date) {
    if (!userId || !mealSectionId || !date) {
      throw new Error("Missing required parameters for deleting entries.");
    }
    try {
      const collectionRef = this.db.collection(
        `users/${userId}/foodLogEntries`
      );

      // Query for all entries in this meal section/date
      const queryRef = collectionRef
        .where("mealType", "==", mealSectionId)
        .where("date", "==", date);

      const snapshot = await queryRef.get();
      const batch = this.db.batch();

      // Track headers that might become orphaned
      const headersToCheck = new Set();

      snapshot.forEach((doc) => {
        const data = doc.data();
        batch.delete(doc.ref);

        // If deleting a meal item, remember its header
        if (data.type === "meal-item" && data.mealHeaderId) {
          headersToCheck.add(data.mealHeaderId);
        }
      });

      await batch.commit();

      // Check and delete orphaned headers
      await Promise.all(
        [...headersToCheck].map((headerId) =>
          this.deleteHeaderIfEmpty(userId, headerId)
        )
      );

      // Bump data version after successful write
      const newVersion = await this.bumpDataVersion(userId);

      return {
        message: "Entries deleted successfully",
        version: newVersion,
      };
    } catch (error) {
      logger.error("Failed to delete entries:", error);
      throw new Error("Error deleting entries.");
    }
  }

  /**
   * Directly copies entries between meal sections and dates, applying new identifiers and updating timestamps.
   * Ensures that entries are processed within Firestore's batch limit to maintain atomicity of the batch operation.
   *
   * @param {string} userId - User identifier.
   * @param {Array} entries - Array of entries to be copied.
   * @param {string} destinationMealType - The meal type to which the entries are copied.
   * @param {string} destinationDate - The date to which the entries are copied.
   * @returns {Object} - Returns a summary including message, count of entries copied, and new entries data.
   * @throws {Error} - Throws an error if the operation fails.
   */
  async copyEntries(userId, entries, destinationMealType, destinationDate) {
    const collectionRef = this.db.collection(`users/${userId}/foodLogEntries`);
    const batch = this.db.batch();
    let operationCount = 0;
    const batchLimit = 500;
    const updatedEntries = [];
    const newHeaderMap = new Map(); // Maps old header IDs to new header IDs

    // First pass: Create new headers for meal groups
    entries.forEach((entry) => {
      if (entry.type === "meal-header") {
        const newHeaderId = uuid.v4();
        newHeaderMap.set(entry.id, newHeaderId);

        const newHeaderEntry = {
          ...entry,
          id: newHeaderId,
          mealType: destinationMealType,
          date: destinationDate,
          clientTimestamp: new Date().toISOString(),
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        };

        batch.set(collectionRef.doc(newHeaderId), newHeaderEntry);
        updatedEntries.push(newHeaderEntry);
        operationCount++;
      }
    });

    // Second pass: Handle meal items and standalone entries
    entries.forEach((entry) => {
      if (operationCount >= batchLimit) {
        logger.warn(
          "Batch limit reached, remaining entries will be in a new batch"
        );
        return;
      }

      // Skip headers since we already processed them
      if (entry.type === "meal-header") return;

      const uniqueId = uuid.v4();
      let newEntry;

      if (entry.type === "meal-item" && newHeaderMap.has(entry.mealHeaderId)) {
        // Preserve meal item relationship with new header
        newEntry = {
          ...entry,
          id: uniqueId,
          mealHeaderId: newHeaderMap.get(entry.mealHeaderId),
          mealType: destinationMealType,
          date: destinationDate,
          clientTimestamp: new Date().toISOString(),
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        };
      } else {
        // Reset meal properties for standalone entries
        newEntry = {
          ...entry,
          id: uniqueId,
          type: "food",
          mealHeaderId: null,
          mealItemIndex: null,
          mealType: destinationMealType,
          date: destinationDate,
          clientTimestamp: new Date().toISOString(),
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        };
      }

      batch.set(collectionRef.doc(uniqueId), newEntry);
      updatedEntries.push(newEntry);
      operationCount++;
    });

    if (operationCount > 0) {
      await batch.commit();
      logger.debug(`Successfully copied ${operationCount} entries.`);
      const newVersion = await this.bumpDataVersion(userId);

      return {
        message: "Entries copied successfully",
        version: newVersion,
        count: operationCount,
        entries: updatedEntries,
      };
    } else {
      logger.debug("No valid entries to process in the batch.");
      return { message: "No valid entries to process", count: 0 };
    }
  }

  /**
   * Adds or updates a water entry in the user's food log.
   *
   * This function either creates a new water entry or updates an existing one based on the presence
   * of an entryId. It handles both operations within the same logical flow, providing flexibility
   * and ensuring that each entry is uniquely identified and stored correctly.
   *
   * @param {string} userId - The user's unique identifier.
   * @param {number} volume - The volume of water consumed.
   * @param {string} unit - The unit of measurement for the water volume, e.g., 'ml' or 'fl oz'.
   * @param {string} date - The date on which the water was consumed.
   * @param {string} entryId - Optional. The ID of the water entry to update. If not provided, a new entry is created.
   * @returns {Promise<Object>} A promise that resolves to a message and details of the water entry.
   * @throws {Error} Throws an error if required parameters are missing or if the Firestore operation fails.
   */
  async addOrUpdateWaterEntry(userId, volume, unit, date, entryId) {
    if (!userId || !volume || !unit || !date) {
      throw new Error("Missing required parameters.");
    }

    let isNewEntry = !entryId;
    const docRef = this.db
      .collection(`users/${userId}/foodLogEntries`)
      .doc(isNewEntry ? uuid.v4() : entryId);

    const newEntry = {
      id: docRef.id, // This ensures that the ID is correctly set whether new or existing
      type: "food",
      mealHeaderId: null,
      mealItemIndex: null,
      volume,
      unit,
      date,
      foodLabel: "Water",
      foodCategory: "Water",
      mealType: "Water",
      clientTimestamp: new Date().toISOString(),
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    try {
      if (isNewEntry) {
        await docRef.set(newEntry); // Use set for a new entry

        // After successful write
        // await CacheService.instance.flushPattern(`foodLogEntries:${userId}:*`);
        const newVersion = await this.bumpDataVersion(userId);

        return {
          message: "Water entry added successfully",
          version: newVersion,
          entry: newEntry,
        };
      } else {
        await docRef.update(newEntry); // Use update for an existing entry

        // After successful write
        // await CacheService.instance.flushPattern(`foodLogEntries:${userId}:*`);
        const newVersion = await this.bumpDataVersion(userId);

        return {
          message: "Water entry updated successfully",
          version: newVersion,
          entry: newEntry,
        };
      }
    } catch (error) {
      logger.error(
        `Failed to ${isNewEntry ? "add" : "update"} water entry:`,
        error
      );
      throw new Error(
        `Error ${isNewEntry ? "adding" : "updating"} water entry.`
      );
    }
  }

  /**
   * Updates a meal group's section and servings
   * - Updates the meal header with new section and servings
   * - Updates all meal items with new section
   * - Uses batch processing for atomic updates
   *
   * @param {string} userId - User's unique identifier
   * @param {string} mealGroupId - Meal header document ID
   * @param {string} newSection - New meal section ID
   * @param {number} servings - Number of servings (optional)
   * @param {Array} mealItems - Array of meal item IDs to update
   * @returns {Promise<Object>} Updated header and items
   */
  async updateMealGroup(
    userId,
    mealGroupId,
    newSection,
    servings = 1,
    mealItems
  ) {
    // Validate input parameters
    if (!userId || !mealGroupId || !newSection || !Array.isArray(mealItems)) {
      throw new Error("Invalid input parameters");
    }

    const batch = this.db.batch();
    const serverTimestamp = admin.firestore.FieldValue.serverTimestamp();
    const clientTimestamp = new Date().toISOString(); // Generate client timestamp once for consistency
    const updatedItems = [];

    try {
      // 1. Update meal header
      const headerRef = this.db.doc(
        `users/${userId}/foodLogEntries/${mealGroupId}`
      );
      batch.update(headerRef, {
        mealType: newSection,
        numberOfServings: Number(servings) || 1,
        updatedAt: serverTimestamp,
        clientTimestamp, // Add client timestamp
      });

      // 2. Update meal items
      for (const itemId of mealItems) {
        const itemRef = this.db.doc(`users/${userId}/foodLogEntries/${itemId}`);
        batch.update(itemRef, {
          mealType: newSection,
          updatedAt: serverTimestamp,
          clientTimestamp, // Add same client timestamp to all items
        });
        updatedItems.push({ id: itemId, mealType: newSection });
      }

      // Commit batch update
      await batch.commit();
      logger.info(
        `Updated meal group ${mealGroupId} with ${mealItems.length} items`
      );

      return {
        updatedHeader: {
          id: mealGroupId,
          mealType: newSection,
          numberOfServings: Number(servings) || 1,
          clientTimestamp, // Include in response
        },
        updatedItems: updatedItems.map((item) => ({
          ...item,
          clientTimestamp, // Include in each item response
        })),
      };
    } catch (error) {
      logger.error("Failed to update meal group:", {
        userId,
        mealGroupId,
        error: error.message,
      });
      throw new Error("Error updating meal group");
    }
  }
}

module.exports = new FoodDiaryService();
