const admin = require("../config/firebase-admin");
const jwt = require("jsonwebtoken");
const logger = require("../utils/logger");
const CacheService = require("./CacheService");

class TokenService {
  static OAUTH_CONFIG = {
    ACCESS_TOKEN_EXPIRATION:
      process.env.NODE_ENV === "development" ? 86400 : 1800,
    REFRESH_TOKEN_EXPIRATION:
      process.env.NODE_ENV === "development" ? 7776000 : 2592000,
    TOKEN_ISSUER: "nutracompass-auth",
  };

  /**
   * Generates new access and refresh tokens
   * @param {Object} params
   * @param {string} params.userId - The user ID
   * @param {string} params.clientId - The client ID
   * @param {string} [params.scope=""] - Optional scope
   * @param {string} [params.role="user"] - User role for access control
   * @returns {Promise<Object>} Token response object
   */
  static async generateTokens({ userId, clientId, scope = "", role = "user" }) {
    try {
      // Generate access token with role claim
      const accessToken = jwt.sign(
        {
          sub: userId,
          client_id: clientId,
          token_type: "access",
          scope,
          env: process.env.NODE_ENV,
          iss: this.OAUTH_CONFIG.TOKEN_ISSUER,
          iat: Math.floor(Date.now() / 1000),
          exp:
            Math.floor(Date.now() / 1000) +
            this.OAUTH_CONFIG.ACCESS_TOKEN_EXPIRATION,
          role,
        },
        process.env.JWT_SECRET
      );

      // Generate JWT refresh token
      const refreshToken = jwt.sign(
        {
          sub: userId,
          client_id: clientId,
          token_type: "refresh",
          env: process.env.NODE_ENV,
          iss: this.OAUTH_CONFIG.TOKEN_ISSUER,
          iat: Math.floor(Date.now() / 1000),
          exp:
            Math.floor(Date.now() / 1000) +
            this.OAUTH_CONFIG.REFRESH_TOKEN_EXPIRATION,
        },
        process.env.JWT_SECRET
      );

      // Store refresh token in Firestore
      await admin
        .firestore()
        .collection("refresh_tokens")
        .doc(refreshToken)
        .set({
          userId,
          clientId,
          scope,
          role,
          expiresAt: new Date(
            Date.now() + this.OAUTH_CONFIG.REFRESH_TOKEN_EXPIRATION * 1000
          ),
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      return {
        access_token: accessToken,
        token_type: "Bearer",
        expires_in: this.OAUTH_CONFIG.ACCESS_TOKEN_EXPIRATION,
        refresh_token: refreshToken,
        scope,
      };
    } catch (error) {
      logger.error("Token generation failed:", error);
      throw error;
    }
  }

  /**
   * Refreshes access token using valid refresh token
   * @param {string} refreshToken - Valid refresh token
   * @param {string} clientId - Client ID
   * @returns {Promise<Object>} New tokens
   */
  static async refreshTokens(refreshToken, clientId) {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET, {
        issuer: this.OAUTH_CONFIG.TOKEN_ISSUER,
      });

      if (decoded.token_type !== "refresh" || decoded.client_id !== clientId) {
        throw new Error("Invalid refresh token");
      }

      // Check Firestore for token existence
      const tokenDoc = await admin
        .firestore()
        .collection("refresh_tokens")
        .doc(refreshToken)
        .get();

      if (!tokenDoc.exists) throw new Error("Refresh token not found");

      // Delete immediately after verification
      await tokenDoc.ref.delete();

      // Get stored data (including role)
      const { userId, scope, role } = tokenDoc.data();

      // Generate new tokens with stored role
      return this.generateTokens({
        userId,
        clientId,
        scope: scope || "",
        role: role || "user",
      });
    } catch (error) {
      logger.error("Token refresh failed:", error);
      throw error;
    }
  }

  /**
   * Validates an access token with caching
   * @param {string} token - The token to validate
   * @returns {Promise<Object>} Decoded token payload
   */
  static async validateAccessToken(token) {
    const cacheKey = `token:val:${token}`;

    try {
      // Check cache first
      const cached = await CacheService.instance.get(cacheKey, "AUTH");
      if (cached && cached.data) return cached.data;

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET, {
        issuer: this.OAUTH_CONFIG.TOKEN_ISSUER,
      });

      if (decoded.token_type !== "access") {
        throw new Error("Invalid token type");
      }

      // Check blacklist
      const isRevoked = await this.isTokenRevoked(token);
      if (isRevoked) throw new Error("Token revoked");

      // Cache validation result
      await CacheService.instance.set(cacheKey, decoded, "AUTH");
      return decoded;
    } catch (error) {
      // Cache negative result
      await CacheService.instance.set(
        cacheKey,
        { valid: false, error: error.message },
        "AUTH"
      );
      logger.error("Token validation failed:", error);
      throw error;
    }
  }

  /**
   * Introspects a token to check its validity with caching
   * @param {string} token - The token to introspect
   * @param {string} [tokenTypeHint="access_token"] - Token type hint
   * @returns {Promise<Object>} Token introspection result
   */
  static async introspectToken(token, tokenTypeHint = "access_token") {
    const cacheKey = `token:intro:${token}`;

    try {
      // Check cache first
      const cached = await CacheService.instance.get(cacheKey, "AUTH");
      if (cached && cached.data) return cached.data;

      let decoded;
      let active = false;

      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET, {
          issuer: this.OAUTH_CONFIG.TOKEN_ISSUER,
        });

        // Additional checks based on token type
        if (tokenTypeHint === "refresh_token") {
          if (decoded.token_type !== "refresh") {
            throw new Error("Invalid token type");
          }

          // Check Firestore existence
          const tokenDoc = await admin
            .firestore()
            .collection("refresh_tokens")
            .doc(token)
            .get();
          active = tokenDoc.exists;
        } else {
          if (decoded.token_type !== "access") {
            throw new Error("Invalid token type");
          }

          // Check blacklist
          active = !(await this.isTokenRevoked(token));
        }
      } catch (e) {
        // Token is invalid - leave active as false
      }

      const result = {
        active,
        ...(active && decoded
          ? {
              sub: decoded.sub,
              client_id: decoded.client_id,
              token_type: tokenTypeHint,
              exp: decoded.exp,
              iat: decoded.iat,
              scope: decoded.scope || "",
              env: decoded.env,
              role: decoded.role || "user",
            }
          : {}),
      };

      // Cache result
      await CacheService.instance.set(cacheKey, result, "AUTH");
      return result;
    } catch (error) {
      logger.error("Token introspection failed:", error);
      return { active: false };
    }
  }

  /**
   * Revokes a token using CacheService
   * @param {string} token - The token to revoke
   * @param {string} [tokenTypeHint="access_token"] - Token type hint
   * @returns {Promise<Object>} Revocation result
   */
  static async revokeToken(token, tokenTypeHint = "access_token") {
    try {
      if (tokenTypeHint === "refresh_token") {
        // Delete from Firestore
        await admin
          .firestore()
          .collection("refresh_tokens")
          .doc(token)
          .delete();
      } else {
        // Add access token to Redis blacklist via CacheService
        const decoded = jwt.decode(token);
        if (decoded?.exp) {
          const ttl = Math.max(0, decoded.exp - Math.floor(Date.now() / 1000));
          if (ttl > 0) {
            await CacheService.instance.set(
              `token_blacklist:${token}`,
              "1",
              "AUTH",
              ttl
            );
          }
        }
      }

      // Clear cached validations
      await Promise.all([
        CacheService.instance.delete(`token:val:${token}`, "AUTH"),
        CacheService.instance.delete(`token:intro:${token}`, "AUTH"),
      ]);

      return { success: true };
    } catch (error) {
      logger.error("Token revocation failed:", error);
      throw error;
    }
  }

  /**
   * Checks if a token has been revoked
   * @param {string} token - The token to check
   * @returns {Promise<boolean>} True if token is revoked
   */
  static async isTokenRevoked(token) {
    try {
      // Check Redis blacklist via CacheService
      const exists = await CacheService.instance.exists(
        `token_blacklist:${token}`,
        "AUTH"
      );
      return exists;
    } catch (error) {
      logger.error("Revocation check failed:", error);
      return false;
    }
  }

  /**
   * Revokes all tokens for a specific user
   * @param {string} userId - The user ID to revoke tokens for
   * @returns {Promise<Object>} Revocation result
   */
  static async revokeAllUserTokens(userId) {
    try {
      // Revoke all refresh tokens
      const tokensSnapshot = await admin
        .firestore()
        .collection("refresh_tokens")
        .where("userId", "==", userId)
        .get();

      const batch = admin.firestore().batch();
      tokensSnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();

      // Add user to global revocation list
      await CacheService.instance.set(`user_revoked:${userId}`, "1", "AUTH");

      return { success: true, count: tokensSnapshot.size };
    } catch (error) {
      logger.error("Full token revocation failed:", error);
      throw error;
    }
  }
}

module.exports = TokenService;
