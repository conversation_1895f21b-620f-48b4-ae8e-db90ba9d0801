const admin = require("../config/firebase-admin");
const logger = require("../utils/logger");
const Configs = require("../../configs");
const cron = require("node-cron");

// Circuit breaker to prevent cost spikes
const MAX_MONTHLY_AUTH_READS = 50000; // $1.50 max
let authReadsThisMonth = 0;

class AnalyticsService {
  static cache = {
    data: null,
    expiresAt: 0, // Unix timestamp (seconds)
  };

  static get TTL() {
    return Math.min(
      Configs.redisConfig.CACHE.TTL.ANALYTICS.MEMORY.TTL,
      30 // Never exceed 30s cache even if misconfigured
    );
  }

  async getComprehensiveAnalytics() {
    try {
      // 1. Cache check (seconds)
      const now = Math.floor(Date.now() / 1000);
      if (now < this.constructor.cache.expiresAt) {
        return this.constructor.cache.data;
      }

      // 2. Rate limit check
      if (authReadsThisMonth >= MAX_MONTHLY_AUTH_READS) {
        logger.warn("Monthly auth reads limit reached");
        return this.constructor.cache.data || this._getFallbackData();
      }

      // 3. Fetch fresh data
      const result = {
        timestamp: new Date().toISOString(),
        users: await this._getUserAnalytics(),
      };

      // 4. Update cache (seconds)
      this.constructor.cache = {
        data: result,
        expiresAt: now + this.constructor.TTL,
      };

      authReadsThisMonth += 1; // Count auth reads

      return result;
    } catch (error) {
      logger.error("Analytics fetch error:", error);
      return this.constructor.cache.data || this._getFallbackData();
    }
  }

  _getFallbackData() {
    return {
      timestamp: new Date().toISOString(),
      users: { totalCount: 0, newToday: 0 },
    };
  }

  async _getUserAnalytics() {
    try {
      const [totalCount, newToday] = await Promise.all([
        this._getTotalUserCount(),
        this._getTodaysNewUsers(),
      ]);

      return {
        totalCount,
        newToday,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Error getting user analytics:", error);
      throw error;
    }
  }

  async _getTotalUserCount() {
    try {
      let totalCount = 0;
      let nextPageToken;

      do {
        const listUsersResult = await admin
          .auth()
          .listUsers(1000, nextPageToken);
        totalCount += listUsersResult.users.length;
        nextPageToken = listUsersResult.pageToken;
      } while (nextPageToken);

      return totalCount;
    } catch (error) {
      logger.error("Error getting total user count:", error);
      throw error;
    }
  }

  async _getTodaysNewUsers() {
    try {
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);

      const db = admin.firestore();
      const usersRef = db.collection("users");
      const snapshot = await usersRef
        .where("createdAt", ">=", todayStart)
        .count()
        .get();

      return snapshot.data().count;
    } catch (error) {
      logger.error("Error getting today's new users:", error);
      return 0;
    }
  }

  _setupChangeListeners() {
    const db = admin.firestore();
    db.collection("users").onSnapshot(() => {
      // Reset cache on changes
      analyticsCache.lastUpdated = 0;
      logger.debug("User data changed - cache invalidated");
    });
  }
}

// Reset counter monthly
// Runs at midnight on the 1st of every month
cron.schedule("0 0 1 * *", () => {
  authReadsThisMonth = 0;
  logger.info("Monthly analytics counter reset");
});

module.exports = new AnalyticsService();
