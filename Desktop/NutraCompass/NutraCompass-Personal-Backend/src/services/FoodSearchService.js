const EdamamService = require("./EdamamService.js");
const USDAService = require("./USDAService.js");
const CustomFoodService = require("./CustomFoodService.js");
const NutrientProcessor = require("../utils/NutrientProcessor.js");
const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  USDAHelper,
  CustomHelper,
} = require("../utils/foodIdHelper.js");
const logger = require("../utils/logger.js");

/**
 * Central service for coordinating food-related operations across multiple data sources
 * @class FoodSearchService
 */
class FoodSearchService {
  /**
   * Configuration for integrated food data sources
   * @returns {Object} Source configuration with prefix, service reference, and priority
   */
  get SOURCES() {
    return {
      EDAMAM: {
        prefix: EdamamHelper.toInternal(""), // "edm:"
        service: EdamamService,
        priority: 1, // Primary source for general searches
      },
      USDA: {
        prefix: USDAHelper.toInternal(""), // "usda:"
        service: USDAService,
        priority: 2, // Authoritative source for official nutritional data
      },
      CUSTOM: {
        prefix: CustomHelper.toInternal(""), // "cust:"
        service: CustomFoodService,
        priority: 3, // User-generated content fallback
      },
    };
  }

  /**
   * Search for food items across all integrated sources
   * @param {string} searchTerm - Query text or barcode number
   * @param {string} userId - Authenticated user identifier
   * @param {number} [page=1] - Pagination page number
   * @returns {Promise<Object>} Unified search results with metadata
   * @throws {Error} When search parameters are invalid
   */
  async searchFoodItems(searchTerm, userId, page = 1) {
    try {
      if (!searchTerm?.trim() || !userId) {
        throw new Error("Invalid search parameters");
      }

      // Handle barcode searches separately
      if (/^\d+$/.test(searchTerm)) {
        return this.searchByBarcode(searchTerm, userId);
      }

      // Use unified search for text queries
      return this._unifiedSearch(searchTerm, userId, page);
    } catch (error) {
      this._logError("SEARCH_OPERATION", error, { searchTerm, userId });
      throw new Error(`Search failed: ${error.message}`);
    }
  }

  /**
   * Specialized search using product barcode
   * @param {string} barcode - Numeric product identifier
   * @param {string} userId - Authenticated user identifier
   * @returns {Promise<Object>} Barcode-specific search results
   * @throws {Error} For invalid barcode formats
   */
  async searchByBarcode(barcode, userId) {
    try {
      // Validate barcode format
      if (!/^\d+$/.test(barcode)) {
        throw new Error("Invalid barcode format - numeric values only");
      }

      // Edamam maintains best barcode database
      const result = await EdamamService.searchByBarcode(barcode, userId);

      // Check if we got valid results
      if (!result.data || result.data.length === 0) {
        return {
          data: [],
          $metadata: {
            source: "EDAMAM",
            warnings: ["No products found for this barcode"],
          },
        };
      }

      // For barcode searches, we expect only 1 item, so take the first result
      const item = result.data[0];

      // Create and return formatted food item
      return {
        data: [
          {
            foodId: item.foodId,
            foodLabel: item.foodLabel,
            foodCategory: item.foodCategory || "Generic Food",
            foodBrand: item.foodBrand || null,
            numberOfServings: item.numberOfServings || 1,
            measures: item.measures || [],
            activeMeasure: item.activeMeasure,
            nutrients: NutrientProcessor.applyServingSize(
              item.nutrients,
              item.numberOfServings || 1
            ),
            defaultNutrients: item.defaultNutrients,
            $metadata: {
              source: "EDAMAM",
              cached: item.$metadata?.cacheStatus === "hit",
              retrievedAt: new Date().toISOString(),
              warnings: item.$metadata?.warnings || [],
            },
          },
        ],
        $metadata: {
          cacheStatus: result.$metadata?.cacheStatus || "miss",
        },
      };
    } catch (error) {
      this._logError("BARCODE_SEARCH", error, { barcode, userId });
      throw new Error(`Barcode search failed: ${error.message}`);
    }
  }

  /**
   * Update nutritional information for a specific serving size
   * @param {Object} foodItem - Target food item
   * @param {Object} newServing - Selected serving configuration
   * @param {string} userId - Authenticated user identifier
   * @returns {Promise<Object>} Updated nutritional data
   * @throws {Error} For invalid input or source errors
   */
  async updateServingSize(foodItem, newServing, userId) {
    try {
      // Validate critical input parameters
      if (!foodItem?.foodId || !newServing?.uri || !userId) {
        throw new Error("Incomplete update parameters");
      }

      // Extract source information from prefixed food ID
      const { source } = this._parseFoodId(foodItem.foodId);
      const service = this.SOURCES[source]?.service;

      if (!service) {
        throw new Error(`Unsupported food source: ${source}`);
      }

      // Route request to appropriate service with original food ID
      const nutrientResponse = await service.getNutrients(
        foodItem,
        newServing.uri,
        userId
      );

      // critical nutritient response validation
      if (!nutrientResponse || typeof nutrientResponse !== "object") {
        throw new Error(`Invalid nutrient response from ${source} service`);
      }

      return this._formatNutritionResponse(
        foodItem,
        nutrientResponse.data,
        newServing,
        source
      );
    } catch (error) {
      this._logError("SERVING_UPDATE", error, {
        foodId: foodItem?.foodId,
        userId,
      });
      throw new Error(`Update failed: ${error.message}`);
    }
  }

  // --------------------------
  // Internal Helper Methods
  // --------------------------

  /**
   * Unified search with combined pagination
   * @private
   */
  async _unifiedSearch(query, userId, page = 1, pageSize = 5) {
    // USDA commented out - now using only EDAMAM
    // const servicePage = Math.ceil((page * pageSize) / (pageSize * 2));
    // const offset = ((page - 1) * pageSize) % pageSize;

    // Use actual page number for EDAMAM since we're not combining services
    const edamamPage = page;

    // Only call EDAMAM service
    const [edamam] = await Promise.allSettled([
      this._searchSource("EDAMAM", query, userId, edamamPage, pageSize),
      // USDA commented out
      // this._searchSource("USDA", query, userId, servicePage, pageSize),
    ]);

    // Process successful results
    const edamamResults = edamam.status === "fulfilled" ? edamam.value : null;
    // const usdaResults = usda.status === "fulfilled" ? usda.value : null;

    // Extract and combine item arrays
    const edamamItems = edamamResults?.data?.data || [];
    // const usdaItems = usdaResults?.data?.data || [];

    // Use only EDAMAM results
    const combined = edamamItems.slice(0, pageSize);

    // Log each food item
    combined.forEach((item) => {
      const source = item.$metadata?.source || "unknown";
      logger.debug(`[SEARCH ITEM] ${item.foodLabel} (${source})`);
    });

    // Calculate pagination metadata
    const totalEdamam = edamamResults?.data?.pagination?.totalItems || 0;
    // const totalUSDA = usdaResults?.data?.pagination?.totalItems || 0;
    const hasMoreEdamam = edamamResults?.data?.pagination?.hasMore || false;
    // const hasMoreUSDA = usdaResults?.data?.pagination?.hasMore || false;

    return {
      data: combined,
      pagination: {
        currentPage: page,
        totalItems: totalEdamam, // Only EDAMAM total
        hasMore: hasMoreEdamam, // Only EDAMAM hasMore
      },
      $metadata: {
        sources: ["EDAMAM"], // Only EDAMAM source
        cacheStatus: edamamResults?.$metadata?.cacheStatus || "MISS", // Only EDAMAM cache status
        warnings: edamamResults?.warnings || [], // Only EDAMAM warnings
      },
    };
  }

  /**
   * Unified search with combined pagination
   * @private
   */
  // async _unifiedSearch(query, userId, page = 1, pageSize = 5) {
  //   const servicePage = Math.ceil((page * pageSize) / (pageSize * 2));
  //   const offset = ((page - 1) * pageSize) % pageSize;

  //   const [edamam, usda] = await Promise.allSettled([
  //     this._searchSource("EDAMAM", query, userId, servicePage, pageSize),
  //     this._searchSource("USDA", query, userId, servicePage, pageSize),
  //   ]);

  //   // Process successful results
  //   const edamamResults = edamam.status === "fulfilled" ? edamam.value : null;
  //   const usdaResults = usda.status === "fulfilled" ? usda.value : null;

  //   // Extract and combine item arrays
  //   const edamamItems = edamamResults?.data?.data || [];
  //   const usdaItems = usdaResults?.data?.data || [];

  //   // Balance results between services
  //   const combined = [
  //     ...edamamItems.slice(offset, offset + Math.ceil(pageSize / 2)),
  //     ...usdaItems.slice(offset, offset + Math.floor(pageSize / 2)),
  //   ].slice(0, pageSize);

  //   // Log each food item
  //   combined.forEach((item) => {
  //     const source = item.$metadata?.source || "unknown";
  //     logger.debug(`[SEARCH ITEM] ${item.foodLabel} (${source})`);
  //   });

  //   // Calculate pagination metadata
  //   const totalEdamam = edamamResults?.data?.pagination?.totalItems || 0;
  //   const totalUSDA = usdaResults?.data?.pagination?.totalItems || 0;
  //   const hasMoreEdamam = edamamResults?.data?.pagination?.hasMore || false;
  //   const hasMoreUSDA = usdaResults?.data?.pagination?.hasMore || false;

  //   return {
  //     data: combined,
  //     pagination: {
  //       currentPage: page,
  //       totalItems: totalEdamam + totalUSDA,
  //       hasMore: combined.length === pageSize || hasMoreEdamam || hasMoreUSDA,
  //     },
  //     $metadata: {
  //       sources: ["EDAMAM", "USDA"],
  //       cacheStatus: this._getCombinedCacheStatus(edamamResults, usdaResults),
  //       warnings: this._getCombinedWarnings(edamamResults, usdaResults),
  //     },
  //   };
  // }

  /**
   * Execute search against a specific source with page size
   * @private
   */
  async _searchSource(sourceKey, searchTerm, userId, page, pageSize) {
    const source = this.SOURCES[sourceKey];
    const response = await source.service.searchByQuery(
      searchTerm,
      userId,
      page,
      pageSize
    );

    return {
      source: sourceKey,
      data: response,
      $metadata: response.$metadata || {},
    };
  }

  /**
   * Determine combined cache status
   * @private
   */
  _getCombinedCacheStatus(...results) {
    const statuses = results
      .filter((r) => r?.$metadata?.cacheStatus)
      .map((r) => r.$metadata.cacheStatus);

    if (statuses.includes("miss")) return "partial-miss";
    if (statuses.length > 0) return "hit";
    return "miss";
  }

  /**
   * Combine warnings from all sources
   * @private
   */
  _getCombinedWarnings(...results) {
    return results.reduce((warnings, result) => {
      if (result?.$metadata?.warnings) {
        warnings.push(...result.$metadata.warnings);
      }
      return warnings;
    }, []);
  }

  /**
   * Format final nutrition response
   * @private
   */
  _formatNutritionResponse(original, nutrientData, newServing, source) {
    return {
      ...original,
      activeMeasure: newServing,
      nutrients: NutrientProcessor.applyServingSize(
        nutrientData, // Direct nutrient structure
        original.numberOfServings || 1
      ),
      $metadata: {
        source: this.SOURCES[source].prefix.replace(":", ""),
        cached: original.$metadata?.cacheStatus === "hit",
        retrievedAt: new Date().toISOString(),
        warnings: original.$metadata?.warnings || [],
      },
    };
  }

  /**
   * Parse source information from food ID
   * @private
   */
  _parseFoodId(foodId) {
    for (const [sourceKey, config] of Object.entries(this.SOURCES)) {
      if (foodId.startsWith(config.prefix)) {
        return {
          source: sourceKey,
          sourceId: foodId.replace(config.prefix, ""),
        };
      }
    }
    throw new Error(`Unrecognized food ID format: ${foodId}`);
  }

  /**
   * Log search operation metrics
   * @private
   */
  _logSearchOperation(searchTerm, userId, mergedResults, sources) {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        searchTerm,
        userId,
        metrics: {
          sourceCount: sources.length,
          resultCount: mergedResults.data.length,
          cacheHitRate: `${Math.round(
            (mergedResults.$metadata.cacheHits / sources.length) * 100
          )}%`,
          warningCount: mergedResults.$metadata.warnings?.length || 0,
          priorityOrder: sources.map((s) => s.source),
        },
        performance: {
          duration: `${
            mergedResults.$metadata.lastUpdated -
            new Date(mergedResults.$metadata.startedAt)
          }ms`,
          sourcesUsed: mergedResults.$metadata.sources,
        },
        paginationSummary: {
          currentPage: mergedResults.pagination.currentPage,
          hasMore: mergedResults.pagination.hasMore,
          totalItems: `[${mergedResults.pagination.totalItems} items]`, // Obfuscate large number
        },
        // Sample first 3 results for quality checking
        resultSample: mergedResults.data
          .slice(0, 3)
          .map((r) => ({ id: r.foodId, name: r.name, source: r.source })),
      };

      logger.info(
        "[SEARCH AUDIT]",
        JSON.stringify(
          logEntry,
          (key, value) => {
            // Remove verbose metadata fields
            if (["$metadata", "nutrients", "measures"].includes(key))
              return undefined;
            // Truncate long arrays
            if (Array.isArray(value) && value.length > 5)
              return value.slice(0, 5);
            return value;
          },
          2
        )
      );
    } catch (error) {
      logger.error("[SEARCH LOGGING ERROR]", error.message);
    }
  }

  /**
   * Standardized error logging
   * @private
   */
  _logError(context, error, metadata = {}) {
    logger.error(`[${context} Error] ${error.message}`, {
      stack: error.stack,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }
}

module.exports = new FoodSearchService();

// const EdamamService = require("./EdamamService.js");
// const NutrientProcessor = require("../utils/NutrientProcessor.js");

// class FoodSearchService {
//   async searchFoodItems(searchTerm, userId, page = 1) {
//     try {
//       if (!searchTerm?.trim()) {
//         throw new Error("Invalid search term");
//       }

//       if (/^\d+$/.test(searchTerm)) {
//         return this.searchByBarcode(searchTerm, userId);
//       }

//       return EdamamService.searchByQuery(searchTerm, userId, page);
//     } catch (error) {
//       logger.error("[Search Error]", error.message);
//       throw new Error(`Search failed: ${error.message}`);
//     }
//   }

//   async searchByBarcode(barcode, userId) {
//     try {
//       if (!barcode?.match(/^\d+$/)) {
//         throw new Error("Invalid barcode format");
//       }

//       logger.debug(`[Barcode Search] ${barcode} for user ${userId}`);
//       return EdamamService.searchByBarcode(barcode, userId);
//     } catch (error) {
//       logger.error("[Barcode Error]", error.message);
//       throw new Error(`Barcode search failed: ${error.message}`);
//     }
//   }

//   async updateServingSize(foodItem, newServing, userId) {
//     try {
//       if (!foodItem?.foodId || !newServing?.uri || !userId) {
//         throw new Error("Invalid food item, serving size, or user");
//       }

//       logger.debug(`[Serving Update] ${foodItem.foodId} for ${userId}`, {
//         measure: newServing.uri,
//       });

//       // Get fresh nutrients with new serving size
//       const {
//         data: nutrients,
//         source,
//         $metadata,
//       } = await EdamamService.getNutrients(
//         {
//           foodId: foodItem.foodId,
//           measures: foodItem.measures,
//           nutrients: foodItem.nutrients,
//         },
//         newServing.uri,
//         userId
//       );

//       if (!nutrients) {
//         throw new Error("Failed to fetch updated nutrients");
//       }

//       return {
//         ...foodItem,
//         activeMeasure: newServing,
//         nutrients: NutrientProcessor.applyServingSize(
//           nutrients,
//           foodItem.numberOfServings || 1
//         ),
//         $metadata: {
//           ...($metadata || {}),
//           source,
//           cached: $metadata?.cached || false,
//           retrievedAt: new Date().toISOString(),
//         },
//       };
//     } catch (error) {
//       logger.error("[Serving Update Error]", error.message, {
//         foodId: foodItem?.foodId,
//         userId,
//       });
//       throw new Error(`Failed to update serving size: ${error.message}`);
//     }
//   }
// }

// module.exports = new FoodSearchService();
