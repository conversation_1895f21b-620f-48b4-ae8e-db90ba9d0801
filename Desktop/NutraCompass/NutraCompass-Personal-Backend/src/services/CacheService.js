if (process.env.NODE_ENV === "production") {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
}
const { createClient } = require("redis");
const crypto = require("crypto");
const Configs = require("../../configs.js");
const logger = require("../utils/logger.js");

class CacheService {
  static _instance = null;

  constructor() {
    const { CACHE } = Configs.redisConfig;

    if (process.env.NODE_ENV === "production" && !CACHE.CACHE_HMAC_SECRET) {
      throw new Error("CACHE_HMAC_SECRET required in production");
    }

    // Initialize service configurations
    this.serviceConfigs = CACHE.TTL;
    this._validateTTLConfig();

    // Dynamic memory configuration by service type
    this.serviceMemoryLimits = new Map();
    Object.entries(this.serviceConfigs).forEach(([service, config]) => {
      this.serviceMemoryLimits.set(service, {
        maxItems: config.MEMORY.MAX_ITEMS,
        ttl: config.MEMORY.TTL,
      });
    });

    // Global fallbacks (only used if service not found)
    this.defaultMemoryConfig = {
      maxItems: 500, // Conservative default
      ttl: 5000,
    };

    this.redisClient = this._createRedisClient();
    this.memoryCache = new Map();

    this.metrics = {
      hits: { l1: 0, l2: 0 },
      misses: 0,
      errors: 0,
      latencies: [],
      connectionStates: [],
      keyPatterns: new Map(),
      services: {
        FOOD_DIARY: { hits: 0, misses: 0 },
        FOOD_DIARY_VERSION: { hits: 0, misses: 0 },
        FOOD_SEARCH_QUERY: { hits: 0, misses: 0 },
        FOOD_SEARCH_BARCODE: { hits: 0, misses: 0 },
        FOOD_SEARCH_NUTRIENT: { hits: 0, misses: 0 },
      },
    };

    this._configureEventListeners();
    this._startMemoryCleanupJob();
  }

  // Strict configuration validation =========================================
  _validateTTLConfig() {
    Object.entries(this.serviceConfigs).forEach(([service, config]) => {
      if (!config.MEMORY || !config.REDIS) {
        throw new Error(`Service ${service} missing MEMORY/REDIS config`);
      }
      if (
        typeof config.MEMORY.MAX_ITEMS !== "number" ||
        config.MEMORY.MAX_ITEMS <= 0
      ) {
        throw new Error(`Invalid MAX_ITEMS for ${service}`);
      }
    });
  }

  //#region Singleton Initialization ******************************************
  static async initialize() {
    if (!this._instance) {
      this._instance = new CacheService();
      await this._instance.connect();
    }
    return this._instance;
  }

  static get instance() {
    if (!this._instance) {
      throw new Error(
        "CacheService must be initialized with initialize() first"
      );
    }
    return this._instance;
  }
  //#endregion

  //#region Connection Management ********************************************
  async connect() {
    try {
      await this.redisClient.connect();
      await this.redisClient.ping();
      logger.debug("Cache service connected to Redis");
      return this;
    } catch (error) {
      this._handleStartupError(error);
    }
  }

  async disconnect() {
    await this.redisClient.quit();
    logger.debug("Cache service disconnected from Redis");
  }

  _handleStartupError(error) {
    logger.error("Cache service connection failed:", error);
    process.exit(1);
  }

  get isReady() {
    return this.redisClient.isReady;
  }

  //#endregion

  //#region Security & Key Management *****************************************
  _secureCacheKey(rawKey, serviceType) {
    if (typeof rawKey !== "string") {
      throw new Error("Cache keys must be strings");
    }

    const [namespace, ...parts] = rawKey.split(":");

    // 1. Maintain metrics tracking
    this.metrics.keyPatterns.set(
      namespace,
      (this.metrics.keyPatterns.get(namespace) || 0) + 1
    );

    // 2. General validation
    if (!namespace || namespace.length > 50) {
      throw new Error("Invalid cache key structure");
    }

    // 3. Service-specific validation
    if (serviceType?.startsWith("FOOD_SEARCH")) {
      const validPrefixes = ["edm", "usda", "cust"];
      if (!validPrefixes.includes(namespace)) {
        throw new Error(`Invalid search source prefix: ${namespace}`);
      }
    }

    // 4. Security hashing
    const sensitiveData = parts.join(":");
    return `${namespace}:${crypto
      .createHmac("sha256", Configs.redisConfig.CACHE.CACHE_HMAC_SECRET)
      .update(sensitiveData)
      .digest("hex")}`;
  }
  //#endregion

  //#region Core Cache Operations ********************************************
  async get(rawKey, serviceType) {
    this._validateServiceType(serviceType);

    // Get service-specific config
    const { MEMORY, REDIS } = this.serviceConfigs[serviceType];
    const key = this._secureCacheKey(rawKey, serviceType);

    try {
      // Memory cache check with service-specific TTL
      const memEntry = this.memoryCache.get(key);
      if (memEntry?.expires > Date.now()) {
        // Update expiration using service TTL
        this.memoryCache.set(key, {
          ...memEntry,
          expires: Date.now() + MEMORY.TTL,
          lastUsed: Date.now(),
        });

        logger.debug("[CACHE HIT] Memory", {
          key: rawKey,
          serviceType,
          ttl: MEMORY.TTL,
          remainingTTL: memEntry.expires - Date.now(),
        });
        return { data: memEntry.value, source: "memory" };
      }

      // Redis check
      const start = Date.now();
      const redisData = await this.redisClient.get(key);
      const latency = Date.now() - start;

      if (!redisData) {
        logger.debug("[CACHE MISS]", {
          key: rawKey,
          serviceType,
          latency,
        });
        this._updateCacheMetrics("misses", null, serviceType);
        return { data: null, source: null };
      }

      logger.debug("[CACHE HIT] Redis", {
        key: rawKey,
        serviceType,
        latency,
        dataSize: redisData.length,
        ttl: REDIS,
      });

      const parsed = JSON.parse(redisData);

      // Refresh memory cache
      this.memoryCache.set(key, {
        value: parsed,
        expires: Date.now() + MEMORY.TTL,
        lastUsed: Date.now(),
      });

      this._updateCacheMetrics("hits", "l2", serviceType);
      return { data: parsed, source: "redis" };
    } catch (error) {
      logger.error("[CACHE ERROR]", {
        key: rawKey,
        serviceType,
        error: error.message,
      });
      this._updateCacheMetrics("errors", null, serviceType);
      return { data: null, source: "error" };
    }
  }

  /**
   * Sets a value in both cache tiers with optional TTL override
   * @param {string} rawKey - Cache key identifier
   * @param {any} value - Value to cache
   * @param {string} serviceType - Service namespace for TTL config
   * @param {number|null} overrideTtl - Optional Redis TTL override in seconds
   * @returns {Promise<{tiersUpdated: number}>}
   */
  async set(rawKey, value, serviceType, overrideTtl = null) {
    this._validateServiceType(serviceType);

    const { MEMORY, REDIS } = this.serviceConfigs[serviceType];
    const key = this._secureCacheKey(rawKey, serviceType);
    const finalRedisTtl = overrideTtl ?? REDIS;

    try {
      // Calculate dataSize safely
      let dataSize;
      try {
        dataSize = JSON.stringify(value)?.length || 0;
      } catch (error) {
        dataSize = 0;
        logger.error("[CACHE] Data size calculation failed:", error);
      }

      // Set memory cache
      this.memoryCache.set(key, {
        value,
        expires: Date.now() + MEMORY.TTL,
        lastUsed: Date.now(),
      });
      logger.debug("[CACHE SET] Memory", {
        key: rawKey,
        serviceType,
        ttl: MEMORY.TTL,
        dataSize, // Now properly defined
      });

      // Set Redis
      await this.redisClient.setEx(key, finalRedisTtl, JSON.stringify(value));
      logger.debug("[CACHE SET] Redis", {
        key: rawKey,
        serviceType,
        ttl: finalRedisTtl,
        dataSize, // Now properly defined
      });

      return { tiersUpdated: 2 };
    } catch (error) {
      const tiers = this.memoryCache.has(key) ? 1 : 0;
      logger.error("[CACHE SET ERROR]", {
        key: rawKey,
        serviceType,
        error: error.message,
        tiersUpdated: tiers,
      });
      return { tiersUpdated: tiers };
    }
  }

  /**
   * Dual-layer DELETE operation
   * @param {string} keyToDelete - Cache key identifier
   * @param {string} [serviceType] - Optional service type for validation
   * @returns {Promise<{ tiersDeleted: number }>}
   * Number of cache tiers where key was removed
   */
  async delete(keyToDelete, serviceType) {
    try {
      const key = this._secureCacheKey(keyToDelete, serviceType);
      let tiersDeleted = 0;

      // Delete from memory
      if (this.memoryCache.delete(key)) {
        tiersDeleted++;
        logger.debug("[CACHE DELETE] Memory", {
          key: keyToDelete,
          serviceType,
          secureKey: key,
        });
      }

      // Delete from Redis
      const result = await this.redisClient.del(key);
      if (result > 0) {
        tiersDeleted++;
        logger.debug("[CACHE DELETE] Redis", {
          key: keyToDelete,
          serviceType,
          secureKey: key,
        });
      }

      return { tiersDeleted };
    } catch (error) {
      logger.error("[CACHE DELETE ERROR]", {
        key: keyToDelete,
        serviceType,
        error: error.message,
      });
      return { tiersDeleted: 0 };
    }
  }

  _validateServiceType(serviceType) {
    const validServices = Object.keys(this.serviceConfigs);

    if (!serviceType || !validServices.includes(serviceType)) {
      const error = `Invalid serviceType: ${serviceType}. Valid types: ${validServices.join(
        ", "
      )}`;
      logger.error("[SECURITY]", error);
      throw new Error(error);
    }
  }

  //#endregion

  //#region Memory Management *************************************************

  /**
   * Starts periodic memory cleanup job
   * Removes expired entries and enforces size limits
   */
  _startMemoryCleanupJob() {
    setInterval(() => {
      const now = Date.now();
      const serviceCounts = new Map();

      // Phase 1: Remove expired entries
      this.memoryCache.forEach((value, key) => {
        // Get service type from key prefix (e.g. "foodLogEntries:..." -> FOOD_DIARY)
        const service = key.split(":")[0].toUpperCase().replace(/-/g, "_");

        if (value.expires <= now) {
          this.memoryCache.delete(key);
          serviceCounts.set(service, (serviceCounts.get(service) || 0) - 1);
        }
      });

      // Phase 2: Enforce service-specific limits
      this.memoryCache.forEach((_, key) => {
        const service = key.split(":")[0].toUpperCase().replace(/-/g, "_");
        serviceCounts.set(service, (serviceCounts.get(service) || 0) + 1);
      });

      serviceCounts.forEach((count, service) => {
        const config =
          this.serviceMemoryLimits.get(service) || this.defaultMemoryConfig;
        if (count > config.maxItems) {
          const entries = Array.from(this.memoryCache.entries())
            .filter(([k]) => k.startsWith(service.toLowerCase() + ":"))
            .sort((a, b) => a[1].lastUsed - b[1].lastUsed);

          const toRemove = Math.max(entries.length - config.maxItems, 0);
          entries
            .slice(0, toRemove)
            .forEach(([k]) => this.memoryCache.delete(k));
        }
      });
    }, 30000); // Runs every 30 seconds
  }

  //#endregion

  //#region Redis Client Management ********************************************

  /**
   * Creates Redis client with environment-aware configuration
   * Implements progressive connection retry strategy
   */
  _createRedisClient() {
    const redisUrl = new URL(Configs.redisConfig.URL);

    return createClient({
      url: Configs.redisConfig.URL,
      database: Configs.redisConfig.CACHE.DB_NUMBER,
      // Top-level TLS configuration
      tls: {}, // Empty object
      socket: {
        connectTimeout: Configs.redisConfig.CACHE.CONNECTION_TIMEOUT,
        keepAlive: Configs.redisConfig.CACHE.IDLE_TIMEOUT,
        reconnectStrategy: (retries) => {
          this.metrics.connectionStates.push({
            timestamp: Date.now(),
            event: "reconnect_attempt",
            attempt: retries,
          });
          return Math.min(retries * 500, 5000);
        },
      },
      isolationPoolOptions: {
        min: 1,
        max: Configs.redisConfig.CACHE.POOL_SIZE,
      },
      disableOfflineQueue: true,
      pingInterval: 25000,
    });
  }

  /**
   * Configures Redis client event listeners
   * with enhanced diagnostic logging
   */
  _configureEventListeners() {
    this.redisClient.on("connecting", () => {
      this.metrics.connectionStates.push({
        timestamp: Date.now(),
        event: "connecting",
      });
      logger.debug(
        `Cache connecting to DB ${Configs.redisConfig.CACHE.DB_NUMBER}`
      );
    });

    this.redisClient.on("ready", () => {
      this.metrics.connectionStates.push({
        timestamp: Date.now(),
        event: "ready",
      });
      logger.debug(
        `Cache client ready with ${Configs.redisConfig.CACHE.POOL_SIZE} connections`
      );
    });

    this.redisClient.on("error", (err) => {
      this.metrics.errors++;
      this.metrics.connectionStates.push({
        timestamp: Date.now(),
        event: "error",
        details: err.message,
      });
      logger.error(`Cache error:`, err);
    });

    this.redisClient.on("reconnecting", () => {
      this.metrics.connectionStates.push({
        timestamp: Date.now(),
        event: "reconnecting",
      });
      logger.debug("Cache client reconnecting...");
    });

    this.redisClient.on("end", () => {
      this.metrics.connectionStates.push({
        timestamp: Date.now(),
        event: "end",
      });
      logger.debug("Cache connection closed");
    });
  }

  //#endregion

  //#region Administrative Methods *********************************************

  async flushUserData(userId) {
    // Use raw userId in key pattern (no hashing)
    const pattern = `*:user:${userId}:*`;
    return this.flushPattern(pattern);
  }

  /**
   * Flushes keys by pattern across both cache tiers
   * @param {string} pattern - Redis SCAN pattern
   * @returns {Promise<{ totalFlushed: number, error?: string }>}
   */
  async flushPattern(pattern = "*") {
    const startTime = Date.now();
    let totalFlushed = 0;

    // 1. Input Validation
    if (typeof pattern !== "string" || pattern.length > 256) {
      logger.error("Invalid pattern format/length");
      return { totalFlushed: 0, error: "Invalid pattern" };
    }

    // 2. Production Safeguards
    if (process.env.NODE_ENV === "production") {
      // Allowlist Configuration
      const ALLOWED_PATTERNS = [
        /^foodLogEntries:[a-zA-Z0-9_-]+:\*$/, // foodLogEntries:userId:*
        /^diarySummary:[a-zA-Z0-9_-]+$/, // diarySummary:userId
      ];

      const isAllowed = ALLOWED_PATTERNS.some((regex) => regex.test(pattern));

      if (!isAllowed) {
        logger.error(`Blocked invalid production pattern: ${pattern}`);
        return { totalFlushed: 0, error: "Pattern not allowed" };
      }
    }

    // 3. Pattern Sanitization
    const sanitizedPattern = this._sanitizePattern(pattern);

    try {
      // Memory Cache Flush (your existing implementation)
      totalFlushed += await this._flushMemoryCache(sanitizedPattern);

      // Redis Cache Flush (your existing implementation)
      totalFlushed += await this._flushRedisCache(pattern); // Use original pattern for Redis

      // 4. Audit Logging
      this._auditFlush({
        pattern,
        flushedCount: totalFlushed,
        duration: Date.now() - startTime,
      });

      return { totalFlushed };
    } catch (error) {
      logger.error(`Flush failed for ${pattern}:`, error);
      return { totalFlushed, error: error.message };
    }
  }

  // Helper Methods ------------------------------------------------------------

  // Convert Redis pattern to RegExp with safety limits
  _sanitizePattern(pattern) {
    // Escape regex special chars except * and ?
    let safePattern = pattern
      .replace(/([.+^${}()|[\]\\])/g, "\\$1")
      .replace(/\*/g, ".*")
      .replace(/\?/g, ".")
      .slice(0, 128);

    return new RegExp(`^${safePattern}$`, "i");
  }

  // Memory cache flushing with batch processing
  async _flushMemoryCache(regex) {
    let cleared = 0;
    const batchSize = 1000;
    const keys = [];

    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
        keys.push(key);
        if (keys.length >= batchSize) {
          keys.forEach((k) => this.memoryCache.delete(k));
          cleared += keys.length;
          keys.length = 0;
        }
      }
    }

    // Final batch
    if (keys.length > 0) {
      keys.forEach((k) => this.memoryCache.delete(k));
      cleared += keys.length;
    }

    return cleared;
  }

  // Redis flushing with connection pooling
  async _flushRedisCache(pattern) {
    let cleared = 0;
    let cursor = "0";

    do {
      const reply = await this.redisClient.scan(
        cursor,
        "MATCH",
        pattern,
        "COUNT",
        100
      );

      // Handle different client response formats
      const [newCursor, keys] = Array.isArray(reply)
        ? reply
        : [reply.cursor, reply.keys];

      if (keys?.length > 0) {
        await this.redisClient.unlink(keys);
        cleared += keys.length;
      }

      cursor = newCursor;
    } while (cursor !== "0" && cursor !== 0);

    return cleared;
  }

  _auditFlush({ pattern, flushedCount, duration }) {
    // Extract userId from valid patterns like "foodLogEntries:userId:*"
    const userId = pattern.startsWith("foodLogEntries:")
      ? pattern.split(":")[1]
      : null;

    const auditEntry = {
      timestamp: new Date().toISOString(),
      pattern,
      flushedCount,
      durationMs: duration,
      memorySize: this.memoryCache.size,
      environment: process.env.NODE_ENV,
      ...(userId && { userId }), // Only include if extracted
    };

    logger.info("[Cache Flush Audit]", auditEntry);
    // Example output for "foodLogEntries:user123:*":
    // {
    //   timestamp: "2024-03-21T15:32:45Z",
    //   pattern: "foodLogEntries:user123:*",
    //   flushedCount: 5,
    //   durationMs: 42,
    //   memorySize: 892,
    //   environment: "production",
    //   userId: "user123"
    // }
  }

  /**
   * Health check with tier status reporting
   * @returns {Promise<{ status: string, tiers: object }>}
   * Detailed health status of both cache tiers
   */
  async healthCheck() {
    try {
      const redisStatus = await this.redisClient.ping();
      const totalRequests =
        this.metrics.hits.l1 + this.metrics.hits.l2 + this.metrics.misses;

      return {
        status: "healthy",
        tiers: {
          memory: {
            items: this.memoryCache.size,
            hitRate:
              totalRequests > 0 ? this.metrics.hits.l1 / totalRequests : 0,
          },
          redis: {
            status: redisStatus === "PONG" ? "healthy" : "degraded",
            hitRate:
              totalRequests > 0 ? this.metrics.hits.l2 / totalRequests : 0,
            keys: await this.redisClient.dbSize(),
          },
        },
        // Fixed division-by-zero
        userHitRates: {
          search:
            this.metrics.hits.l2 > 0
              ? (this.metrics.keyPatterns.get("search") || 0) /
                this.metrics.hits.l2
              : 0,
          barcode:
            this.metrics.hits.l2 > 0
              ? (this.metrics.keyPatterns.get("barcode") || 0) /
                this.metrics.hits.l2
              : 0,
        },
      };
    } catch (error) {
      return {
        status: "unhealthy",
        tiers: {
          memory: { status: "unknown" },
          redis: { error: error.message },
        },
      };
    }
  }

  //#endregion

  //#region Diagnostics & Metrics *********************************************

  /**
   * Records operation latency for performance analysis
   * @param {number} startTime - Performance timestamp
   */
  _recordLatency(startTime) {
    const latency = Date.now() - startTime;
    this.metrics.latencies.push(latency);

    // Keep only last 1000 measurements
    if (this.metrics.latencies.length > 1000) {
      this.metrics.latencies.shift();
    }
  }

  /**
   * Gets performance summary with cost projections
   * @returns {object} Cache performance metrics
   */
  getPerformanceSummary() {
    return {
      hitRates: {
        memory:
          this.metrics.hits.l1 / (this.metrics.hits.l1 + this.metrics.misses),
        redis:
          this.metrics.hits.l2 / (this.metrics.hits.l2 + this.metrics.misses),
      },
      avgLatency:
        this.metrics.latencies.length > 0
          ? this.metrics.latencies.reduce((a, b) => a + b, 0) /
            this.metrics.latencies.length
          : 0,
      connectionHistory: this.metrics.connectionStates.slice(-100), // Last 100 events
      keyDistribution: Object.fromEntries(this.metrics.keyPatterns),
    };
  }

  _updateCacheMetrics(type, tier, serviceType) {
    if (type === "hits") {
      this.metrics.hits[tier]++;
      this.metrics.services[serviceType].hits++;
    } else if (type === "misses") {
      this.metrics.misses++;
      this.metrics.services[serviceType].misses++;
    } else if (type === "errors") {
      this.metrics.errors++;
    }
  }

  //#endregion
}

module.exports = CacheService;
