const express = require("express");
const FoodDiaryController = require("../controllers/FoodDiaryController.js");
const router = express.Router();

/**
 * @route   GET /v1/food/diary/:userId/meal-sections
 * @desc    Fetch meal sections for a specific user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.get(
  "/v1/food/diary/:userId/meal-sections",
  FoodDiaryController.getMealSections
);

/**
 * @route   PUT /v1/food/diary/:userId/meal-sections/update-names
 * @desc    Update the names of meal sections for a specific user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.put(
  "/v1/food/diary/:userId/meal-sections/update-names",
  FoodDiaryController.updateMealSectionNames
);

/**
 * @route   GET /v1/food/diary/:userId/history
 * @desc    Get the user's food history
 * @access  Public (or Private, depending on middleware implementation)
 */
router.get(
  "/v1/food/diary/:userId/history",
  FoodDiaryController.getFoodHistory
);

/**
 * @route   GET /v1/food/diary/:userId/entries
 * @desc    Get all food entries for a specific user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.get(
  "/v1/food/diary/:userId/entries",
  FoodDiaryController.getFoodEntries
);

/**
 * @route   POST /v1/food/diary/:userId/entries/:entryId?
 * @desc    Save or update a specific food entry for a user. The entryId is optional.
 * @access  Public (or Private, depending on middleware implementation)
 */
router.post(
  "/v1/food/diary/:userId/entries/:entryId?",
  FoodDiaryController.saveOrUpdateFoodEntry
);

/**
 * @route   DELETE /v1/food/diary/:userId/entries/:entryId
 * @desc    Delete a specific food entry for a user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.delete(
  "/v1/food/diary/:userId/entries/:entryId",
  FoodDiaryController.deleteFoodEntry
);

/**
 * @route   POST /v1/food/diary/:userId/quick-entry
 * @desc    Add a quick food entry for a user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.post(
  "/v1/food/diary/:userId/quick-entry",
  FoodDiaryController.addQuickFoodEntry
);

/**
 * @route   DELETE /v1/food/diary/:userId/entries/meal-section/:mealSectionId/date/:date
 * @desc    Delete all food entries within a specific meal section on a specific date
 * @access  Public (or Private, depending on middleware implementation)
 */
router.delete(
  "/v1/food/diary/:userId/entries/meal-section/:mealSectionId/date/:date",
  FoodDiaryController.deleteMealSectionEntries
);

/**
 * @route   POST /v1/food/diary/:userId/save-meal
 * @desc    Save a specific meal to the food log for a user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.post(
  "/v1/food/diary/:userId/save-meal",
  FoodDiaryController.saveMealToFoodLog
);

/**
 * @route   POST /v1/food/diary/:userId/copy-entries
 * @desc    Copy food entries from one date to another for a user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.post(
  "/v1/food/diary/:userId/copy-entries",
  FoodDiaryController.copyEntriesToAnotherDate
);

/**
 * @route   POST /v1/food/diary/:userId/water-log
 * @desc    Add or update a water consumption entry for a user
 * @access  Public (or Private, depending on middleware implementation)
 */
router.post(
  "/v1/food/diary/:userId/water-log",
  FoodDiaryController.addOrUpdateWaterEntry
);

/**
 * @route   PUT /v1/food/diary/:userId/meal-group
 * @desc    Update meal group section and servings
 * @access  Public (or Private, depending on middleware implementation)
 */
router.put(
  "/v1/food/diary/:userId/meal-group",
  FoodDiaryController.updateMealGroup
);

module.exports = router;
