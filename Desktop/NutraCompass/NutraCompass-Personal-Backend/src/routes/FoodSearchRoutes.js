const express = require("express");
const FoodSearchController = require("../controllers/FoodSearchController.js");

const router = express.Router();

// Food search endpoints
router.get("/v1/food/search", FoodSearchController.handleSearchQuery);
router.get("/v1/food/barcode/:code", FoodSearchController.handleBarcodeSearch);

// Nutrient management endpoints
router.put(
  "/v1/food/nutrients/serving-size",
  FoodSearchController.handleServingSizeUpdate
);

module.exports = router;
