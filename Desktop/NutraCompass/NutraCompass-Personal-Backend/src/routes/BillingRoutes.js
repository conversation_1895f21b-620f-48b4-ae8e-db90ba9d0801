// routes/BillingRoutes.js
const express = require("express");
const router = express.Router();
const BillingController = require("../controllers/BillingController");
const { requireAuth } = require("../middleware/AuthMiddleware");
const bodyParser = require("body-parser");

// Stripe webhook needs raw body
const rawBodyParser = bodyParser.raw({ type: "application/json" });

router.post(
  "/create-subscription",
  requireAuth,
  express.json(),
  BillingController.createSubscription
);

router.post(
  "/update-payment-method",
  requireAuth,
  express.json(),
  BillingController.updatePaymentMethod
);

router.post("/webhook", rawBodyParser, BillingController.handleWebhook);

module.exports = router;
