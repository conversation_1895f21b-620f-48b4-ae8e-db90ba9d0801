const logger = require("../utils/logger.js");
const express = require("express");
const CacheController = require("../controllers/CacheController.js");
const router = express.Router();
const { validateAdminKey } = require("../middleware/AdminAuth.js");

// Public health check (always available)
router.get("/v1/cache/health", CacheController.handleGetCacheHealth);

// Protected endpoints (require environment flag)
if (process.env.ENABLE_CACHE_MGMT === "true") {
  router.get("/v1/cache/debug-keys", validateAdminKey, async (req, res) => {
    try {
      // Access Redis client through your CacheService
      const redis = CacheController.getCacheService().redisClient;
      const keys = await redis.keys("*");

      // Get raw key -> hashed key mappings
      const keyDetails = await Promise.all(
        keys.map(async (key) => ({
          hashedKey: key,
          ttl: await redis.ttl(key),
          memoryCached: CacheController.getCacheService().memoryCache.has(key),
        }))
      );

      res.json({
        success: true,
        data: keyDetails,
      });
    } catch (error) {
      logger.error("Debug keys error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve keys",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  });

  router.delete(
    "/v1/cache/entries/:key",
    validateAdminKey,
    CacheController.handleDeleteCacheEntry
  );
  router.post(
    "/v1/cache/flush",
    validateAdminKey,
    CacheController.handleFlushCache
  );
  router.get(
    "/v1/cache/metrics",
    validateAdminKey,
    CacheController.handleGetCacheMetrics
  );

  router.delete(
    "/v1/cache/users/:userId",
    validateAdminKey,
    CacheController.handleFlushUserCache
  );
}

module.exports = router;

/* 
CACHE MANAGEMENT CHEAT SHEET
===============================================

Basic Health Check:
------------------------------------------------
curl -X GET \
  -H "ngrok-skip-browser-warning: true" \
  "https://your-api/v1/cache/health"

Admin Endpoints (Require X-Admin-Key Header):
------------------------------------------------

1. Delete Specific Cache Entry:
curl -X DELETE \
  -H "X-Admin-Key: YOUR_ADMIN_KEY" \
  -H "ngrok-skip-browser-warning: true" \
  "https://your-api/v1/cache/entries/search%3Auser123%3Apizza"

2. Flush Cache by Pattern:
curl -X POST \
  -H "X-Admin-Key: YOUR_ADMIN_KEY" \
  -H "ngrok-skip-browser-warning: true" \
  "https://your-api/v1/cache/flush?pattern=search%3A*"

3. Get Cache Metrics:
curl -X GET \
  -H "X-Admin-Key: YOUR_ADMIN_KEY" \
  -H "ngrok-skip-browser-warning: true" \
  "https://your-api/v1/cache/metrics"

4. Flush User-specific Data:
curl -X DELETE \
  -H "X-Admin-Key: YOUR_ADMIN_KEY" \
  -H "ngrok-skip-browser-warning: true" \
  "https://your-api/v1/cache/users/user123"

5. Debug Hashed Keys (Caution - Dev Only):
curl -X GET \
  -H "X-Admin-Key: YOUR_ADMIN_KEY" \
  -H "ngrok-skip-browser-warning: true" \
  "https://your-api/v1/cache/debug-keys"

Key Management Notes:
------------------------------------------------
- All keys are HMAC-SHA256 hashed values
- Colon (:) = %3A in URLs
- Hashed keys are irreversible
- Example key generation:
  node -e "logger.debug(require('crypto').createHmac('sha256', process.env.CACHE_HMAC_SECRET).update('search:user123:pizza').digest('hex'))"

Full Workflow Example:
------------------------------------------------
# 1. Perform search to populate cache
curl "https://your-api/v1/food/search?query=pizza&userId=user123"

# 2. Check cache health
curl -s "https://your-api/v1/cache/health" | jq '.data.tiers.redis.keys'

# 3. Delete specific entry (using raw key format)
curl -X DELETE ... "/cache/entries/search%3Auser123%3Apizza"

# 4. Flush all user data
curl -X DELETE ... "/cache/users/user123"

# 5. Verify remaining keys
curl -s ".../cache/health" | jq '.data.tiers.redis.keys'

Safety Guidelines:
------------------------------------------------
- Never expose admin keys in production
- Avoid full (*) flushes in production
- Monitor cache hit rates regularly
- Rotate CACHE_HMAC_SECRET periodically
*/
