const express = require("express");
const AnalyticsController = require("../controllers/AnalyticsController");
const router = express.Router();

/**
 * Route to get various analytics metrics
 * GET: /v1/analytics
 * Returns an object with different analytics metrics
 */
router.get("/v1/analytics", AnalyticsController.getAnalytics);

/**
 * Route to get just the user count
 * GET: /v1/analytics/user-count
 * Returns the total count of registered users
 */
router.get("/v1/analytics/user-count", AnalyticsController.getUserCount);

module.exports = router;