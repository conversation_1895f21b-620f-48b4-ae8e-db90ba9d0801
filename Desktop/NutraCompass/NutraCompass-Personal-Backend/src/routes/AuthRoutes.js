const express = require("express");
const AuthController = require("../controllers/AuthController");
const router = express.Router();
const TokenService = require("../services/TokenService");
const { requireAuth, requireRole } = require("../middleware/AuthMiddleware");

// ===== OAUTH 2.0 ENDPOINTS ===== //
router.post("/token", express.json(), AuthController.handleTokenRequest);
router.post("/revoke", express.json(), AuthController.handleTokenRevocation);
router.post(
  "/introspect",
  express.json(),
  AuthController.handleTokenIntrospection
);

// ===== USER MANAGEMENT ENDPOINTS ===== //
router.post("/register", express.json(), AuthController.registerUser);
router.post("/login", express.json(), AuthController.authenticateUser);
router.post("/google", express.json(), AuthController.googleLogin); // Google login
router.post("/apple", express.json(), AuthController.appleLogin); // Apple login
router.post("/verify-token", AuthController.verifyUserSession);
router.post("/password-reset", AuthController.resetPassword);
router.delete("/delete-account", AuthController.deleteAccount);
router.post("/check-email", AuthController.checkEmailExists);
router.post("/send-verification", AuthController.sendVerificationCode);
router.post("/verify-code", AuthController.verifyCode);
router.post("/resend-code", AuthController.resendCode);

// ===== SUBSCRIPTION MANAGEMENT ENDPOINTS ===== //
router.put(
  "/:userId/upgrade-role",
  requireAuth,
  requireRole("admin"),
  express.json(),
  AuthController.upgradeRole
);

router.post(
  "/:userId/purchase-feature",
  requireAuth,
  express.json(),
  AuthController.purchaseFeature
);

router.get(
  "/:userId/feature-access/:featureId",
  requireAuth,
  (req, res, next) => {
    // Ensure user can only access their own data
    if (req.params.userId !== req.user.userId) {
      return res.status(403).json({ error: "Access denied" });
    }
    next();
  },
  AuthController.checkFeatureAccess
);

// ===== DEVELOPMENT ENDPOINTS ===== //
if (process.env.NODE_ENV === "development") {
  /**
   * Development endpoint to generate test tokens
   * POST /v1/auth/dev-token
   * body: { userId: string, clientId?: string, scope?: string }
   */
  router.post("/dev-token", express.json(), async (req, res) => {
    try {
      const {
        userId = "dev-user",
        clientId = "dev-client",
        scope = "profile email",
        role = "admin", // Add role support
      } = req.body;

      const tokens = await TokenService.generateTokens({
        userId,
        clientId,
        scope,
        role,
      });

      res.json(tokens);
    } catch (error) {
      res.status(500).json({ error: "Failed to generate dev token" });
    }
  });
}

module.exports = router;
