const admin = require("../config/firebase-admin");
const logger = require("../utils/logger.js");
const SubscriptionService = require("../services/SubscriptionService");
const TokenService = require("../services/TokenService");

/**
 * Middleware to require authentication using either OAuth 2.0 or Firebase tokens
 */
const requireAuth = async (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization || "";
    const match = authHeader.match(/^\s*Bearer\s+(\S+)\s*$/i);
    const token = match ? match[1] : null;

    if (!token) {
      throw new Error("Authorization token not found in header");
    }

    // Try OAuth 2.0 validation first
    try {
      const tokenInfo = await TokenService.validateAccessToken(token);

      // Attach OAuth user context
      req.user = {
        userId: tokenInfo.sub,
        clientId: tokenInfo.client_id,
        role: tokenInfo.role || "user",
        tokenType: "oauth",
      };
      return next();
    } catch (oauthError) {
      logger.debug("OAuth validation failed, trying Firebase...");
    }

    // Fallback to Firebase validation
    try {
      // Firebase tokens have 3 JWT segments
      const parts = token.split(".");
      if (parts.length !== 3) {
        throw new Error("Invalid token structure");
      }

      const decodedToken = await admin.auth().verifyIdToken(token);
      const userRecord = await admin.auth().getUser(decodedToken.uid);

      // Check token revocation
      const tokenIssuedAt = decodedToken.iat * 1000;
      if (userRecord.tokensValidAfterTime) {
        const validAfter = new Date(userRecord.tokensValidAfterTime).getTime();
        if (validAfter > tokenIssuedAt) {
          throw new Error("Token revoked - please reauthenticate");
        }
      }

      // Attach Firebase user context
      req.user = {
        userId: decodedToken.uid,
        email: decodedToken.email,
        role: decodedToken["https://nutracompass.io/roles"] || "user",
        tokenType: "firebase",
      };
      return next();
    } catch (firebaseError) {
      throw new Error("Both OAuth and Firebase validation failed");
    }
  } catch (error) {
    logger.error("Authentication Failure:", {
      error: error.message,
      path: req.path,
      method: req.method,
    });

    res.status(401).json({
      error: "AuthenticationError",
      message: "Authentication required",
      authRequired: true,
      errorCode: "AUTH_REQUIRED",
    });
  }
};

/**
 * Middleware to require a specific role
 */
const requireRole = (role) => {
  return async (req, res, next) => {
    if (req.user.role !== role) {
      return res.status(403).json({ error: "Insufficient permissions" });
    }
    next();
  };
};

/**
 * Middleware to require access to a specific feature
 */
const requireFeature = (featureId) => {
  return async (req, res, next) => {
    try {
      const hasAccess = await SubscriptionService.checkFeatureAccess(
        req.user.userId,
        featureId
      );

      if (!hasAccess) {
        return res.status(403).json({
          error: `You need to purchase ${featureId} to access this feature`,
        });
      }

      next();
    } catch (error) {
      logger.error("Feature check error:", error);
      res.status(500).json({ error: "Feature check failed" });
    }
  };
};

module.exports = {
  requireAuth,
  requireRole,
  requireFeature,
};
