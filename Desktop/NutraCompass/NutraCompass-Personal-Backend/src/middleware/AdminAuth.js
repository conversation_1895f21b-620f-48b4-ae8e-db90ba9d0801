const getAdminKey = (req) => {
  const authHeader =
    req.headers["x-admin-key"] || req.headers.authorization || "";
  return authHeader.replace(/^Bearer\s+/i, "");
};

const validateAdminKey = (req, res, next) => {
  const providedKey = getAdminKey(req);

  if (!providedKey) {
    return res.status(401).json({
      error: "AdminKeyRequired",
      message: "X-Admin-Key header required",
    });
  }

  if (providedKey !== process.env.ADMIN_KEY) {
    return res.status(403).json({
      error: "InvalidAdminKey",
      message: "Provided admin key is invalid",
    });
  }

  next();
};

module.exports = { validateAdminKey };
