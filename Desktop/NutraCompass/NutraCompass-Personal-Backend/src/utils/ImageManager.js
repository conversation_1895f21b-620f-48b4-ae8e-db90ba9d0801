// ImageManager.js
const sharp = require("sharp");
const { URL } = require("url");
const admin = require("../config/firebase-admin");
const logger = require("../utils/logger.js");

class ImageManager {
  constructor() {
    if (ImageManager.instance) {
      return ImageManager.instance;
    }

    // Initialize Firebase Storage bucket
    this.bucket = admin.storage().bucket();

    // Default processing configuration
    this.processingOptions = {
      maxWidth: 1000,
      maxHeight: 1000,
      quality: 80,
      allowedMimeTypes: ["image/jpeg", "image/png", "image/webp"],
    };

    ImageManager.instance = this;
  }

  /**
   * Configure image processing options
   * @param {object} options - Processing options
   */
  configure(options) {
    this.processingOptions = {
      ...this.processingOptions,
      ...options,
    };
  }

  /**
   * Processes and uploads an image to Google Cloud Storage
   * @param {Object} imageFile - Image file object
   * @param {string} storagePath - Full storage path including filename
   * @param {string} userId - Authenticated user ID for URL signing
   * @param {object} [options] - Processing overrides
   * @returns {Promise<string>} Authenticated URL of uploaded image
   */
  async uploadImage(imageFile, storagePath, userId, options = {}) {
    const config = { ...this.processingOptions, ...options };

    this.validateImageFile(imageFile, config.allowedMimeTypes);

    try {
      // Process and upload image
      const processedImage = await this.processImage(imageFile.buffer, config);
      await this.uploadToStorage(processedImage, storagePath);

      // Generate authenticated URL instead of public URL
      return await this.getAuthenticatedUrl(storagePath, userId);
    } catch (error) {
      await this.cleanupFailedUpload(storagePath);
      throw new Error(`Image upload failed: ${error.message}`);
    }
  }

  /**
   * Deletes an image from storage by path
   * @param {string} storagePath - Full storage path including filename
   * @returns {Promise<void>}
   */
  async deleteImage(storagePath) {
    const file = this.bucket.file(storagePath);
    try {
      await file.delete();
      logger.debug(`Deleted image: ${storagePath}`);
    } catch (error) {
      if (error.code === 404) {
        logger.warn(`Image not found: ${storagePath}`);
        return;
      }
      throw new Error(`Failed to delete image: ${error.message}`);
    }
  }

  /**
   * Deletes an image by its public URL
   * @param {string} imageUrl - Public image URL
   * @returns {Promise<void>}
   */
  async deleteImageByUrl(imageUrl) {
    try {
      const path = this.extractPathFromUrl(imageUrl);
      await this.deleteImage(path);
    } catch (error) {
      throw new Error(`Failed to delete image by URL: ${error.message}`);
    }
  }

  // --------------------------
  // Internal Helper Methods
  // --------------------------

  validateImageFile(imageFile, allowedMimeTypes) {
    if (!allowedMimeTypes.includes(imageFile.mimetype)) {
      throw new Error(
        `Invalid image type. Allowed types: ${allowedMimeTypes.join(", ")}`
      );
    }

    if (!imageFile.buffer || !(imageFile.buffer instanceof Buffer)) {
      throw new Error("Invalid image file format");
    }
  }

  async processImage(buffer, options) {
    return sharp(buffer)
      .resize(options.maxWidth, options.maxHeight, {
        fit: "inside",
        withoutEnlargement: true,
      })
      .jpeg({
        quality: options.quality,
        mozjpeg: true,
        force: true, // Force JPEG output regardless of input
      })
      .toBuffer();
  }

  async uploadToStorage(buffer, storagePath) {
    const file = this.bucket.file(storagePath);
    const stream = file.createWriteStream({
      metadata: {
        contentType: "image/jpeg",
        cacheControl: "public, max-age=31536000",
      },
    });

    return new Promise((resolve, reject) => {
      stream.on("error", reject).on("finish", resolve).end(buffer);
    });
  }

  /**
   * Generates secure signed URLs for private storage objects across all protected paths
   * @param {string} storagePath - Full storage path (e.g., 'profilePictures/user123/avatar.jpg')
   * @param {string} requestingUserId - Authenticated user ID from Firebase Auth
   * @returns {Promise<string>} Temporary signed URL valid for 15 minutes
   * @throws {Error} Throws structured errors for invalid paths or permissions
   */
  async getAuthenticatedUrl(storagePath, requestingUserId) {
    // Validate path structure against security rules
    const pathSegments = storagePath.split("/");

    // Ensure path follows /collection/userId/... structure
    if (pathSegments.length < 2) {
      throw new Error({
        code: "storage/invalid-path",
        message: "Path must follow /collection/userId/... structure",
      });
    }

    // Extract owner ID from path according to security rules
    const [collection, ownerUserId] = pathSegments;

    // Validate against supported collections
    const validCollections = [
      "profilePictures",
      "customMeals",
      "progressPictures",
    ];

    if (!validCollections.includes(collection)) {
      throw new Error({
        code: "storage/invalid-collection",
        message: `Unsupported collection: ${collection}`,
      });
    }

    // Verify user ownership
    if (ownerUserId !== requestingUserId) {
      throw new Error({
        code: "storage/permission-denied",
        message: "User does not own this resource",
      });
    }

    // Verify file existence
    const file = this.bucket.file(storagePath);
    const [exists] = await file.exists();

    if (!exists) {
      throw new Error({
        code: "storage/file-not-found",
        message: "Requested file does not exist",
      });
    }

    // Generate signed URL
    const [url] = await file.getSignedUrl({
      action: "read",
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
      version: "v4",
    });

    return url;
  }

  getPublicUrl(storagePath) {
    return `https://storage.googleapis.com/${
      this.bucket.name
    }/${encodeURIComponent(storagePath)}`;
  }

  extractPathFromUrl(imageUrl) {
    try {
      const url = new URL(imageUrl);
      return decodeURIComponent(url.pathname)
        .replace(`/${this.bucket.name}/`, "")
        .replace(/^\//, "");
    } catch (error) {
      throw new Error(`Invalid image URL: ${imageUrl}`);
    }
  }

  async cleanupFailedUpload(storagePath) {
    try {
      await this.bucket.file(storagePath).delete();
    } catch (error) {
      logger.error(`Failed to clean up failed upload: ${storagePath}`, error);
    }
  }
}

// Singleton instance
const instance = new ImageManager();
module.exports = instance;
