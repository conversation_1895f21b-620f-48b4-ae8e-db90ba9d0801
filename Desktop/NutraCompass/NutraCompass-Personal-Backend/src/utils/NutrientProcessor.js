const logger = require("../utils/logger");

class NutrientProcessor {
  // Master entry point
  static processFullNutrientData(rawData, source = "edamam") {
    const normalizedData = this._adaptNutrients(rawData, source);

    return {
      core: this._processCategory(normalizedData, [
        "ENERC_KCAL",
        "CHOCDF",
        "PROCNT",
        "FAT",
        "FASAT",
        "FATRN",
        "FAPU",
        "FAMS",
        "CHOLE",
        "FIBTG",
        "SUGAR",
      ]),
      vitamins: this._processCategory(normalizedData, [
        "VITA_RAE",
        "VITC",
        "VITD",
        "TOCPHA",
        "VITK1",
        "THIA",
        "RIBF",
        "NIA",
        "VITB6A",
        "FOLDFE",
        "VITB12",
      ]),
      minerals: this._processCategory(normalizedData, [
        "CA",
        "FE",
        "MG",
        "P",
        "K",
        "NA",
        "ZN",
      ]),
    };
  }

  static applyServingSize(nutrients, servings) {
    // Add null/undefined check
    if (!nutrients || typeof nutrients !== "object") {
      logger.error("[NutrientProcessor] Invalid nutrients input:", nutrients);
      return {
        core: {},
        vitamins: {},
        minerals: {},
      };
    }

    // Check for required structure
    const isValid = nutrients.core && nutrients.vitamins && nutrients.minerals;
    if (!isValid) {
      logger.warn("[NutrientProcessor] Incomplete nutrient structure:", {
        keys: Object.keys(nutrients),
        servings,
      });
    }

    const processor = (value) => this._roundToTwo(value * servings);

    return {
      core: this._adjustNutrients(nutrients.core, processor),
      vitamins: this._adjustNutrients(nutrients.vitamins, processor),
      minerals: this._adjustNutrients(nutrients.minerals, processor),
    };
  }

  // ----------------------------------
  // Private/Internal Functions Below
  // ----------------------------------

  static _adaptNutrients(data, source) {
    switch (source.toLowerCase()) {
      case "usda":
        return this._usdaAdapter(data);
      case "edamam":
        return this._edamamAdapter(data);
      // case "custom":  // Disabled temporarily
      //   return data;
      default:
        logger.warn(
          `[NutrientProcessor] Unknown source "${source}", assuming raw Edamam format`
        );
        return this._edamamAdapter(data);
    }
  }

  static _processCategory(data, keys) {
    const result = {};
    keys.forEach((key) => {
      const nutrient = data[key] || {};
      result[key] = {
        label: nutrient.label || key,
        quantity: this._safeParse(nutrient.quantity),
        unit: nutrient.unit || "g",
        totalDaily: nutrient.totalDaily || { quantity: null, unit: null },
      };
    });
    return result;
  }

  static _adjustNutrients(category, processor) {
    // Handle undefined/empty inputs
    if (!category || typeof category !== "object") return {};

    const adjusted = {};
    Object.entries(category).forEach(([key, nutrient]) => {
      // Ensure nutrient structure exists
      if (!nutrient || typeof nutrient !== "object") {
        logger.warn(`[NutrientProcessor] Invalid nutrient: ${key}`);
        return;
      }

      adjusted[key] = {
        ...nutrient,
        quantity: processor(nutrient.quantity || 0),
        totalDaily: {
          quantity: nutrient.totalDaily?.quantity
            ? processor(nutrient.totalDaily.quantity)
            : null,
          unit: nutrient.totalDaily?.unit || "%",
        },
      };
    });

    return adjusted;
  }

  // -------------------------
  // Source-specific Adapters
  // -------------------------

  static _edamamAdapter(data) {
    const keys = Object.keys(data?.totalNutrients || {});
    const result = {};

    keys.forEach((key) => {
      const nutrient = data.totalNutrients[key] || {};
      const daily = data.totalDaily?.[key] || {};

      result[key] = {
        label: nutrient.label || key,
        quantity: nutrient.quantity || 0,
        unit: nutrient.unit || "g",
        totalDaily: {
          quantity: daily.quantity || null,
          unit: daily.unit || "%",
        },
      };
    });

    return result;
  }

  static _usdaAdapter(nutrientArray) {
    const map = {
      Energy: "ENERC_KCAL",
      Protein: "PROCNT",
      "Total lipid (fat)": "FAT",
      "Carbohydrate, by difference": "CHOCDF",
      "Sugars, total including NLEA": "SUGAR",
      "Fiber, total dietary": "FIBTG",
      "Calcium, Ca": "CA",
      "Iron, Fe": "FE",
      "Magnesium, Mg": "MG",
      "Phosphorus, P": "P",
      "Potassium, K": "K",
      "Sodium, Na": "NA",
      "Zinc, Zn": "ZN",
      "Vitamin C, total ascorbic acid": "VITC",
      "Vitamin D (D2 + D3)": "VITD",
      "Vitamin B-6": "VITB6A",
      "Vitamin B-12": "VITB12",
      Cholesterol: "CHOLE",
    };

    const result = {};
    nutrientArray.forEach((n) => {
      const code = map[n.nutrientName];
      if (!code) return;
      result[code] = {
        label: n.nutrientName,
        quantity: n.value || 0,
        unit: n.unitName || "g",
        totalDaily: { quantity: null, unit: null },
      };
    });

    return result;
  }

  // -------------------------
  // Utilities
  // -------------------------

  static _safeParse(value) {
    const num = parseFloat(value);
    return isNaN(num) ? 0 : this._roundToTwo(num);
  }

  static _roundToTwo(num) {
    return Math.round((num + Number.EPSILON) * 100) / 100;
  }
}

module.exports = NutrientProcessor;

// class NutrientProcessor {
//   static processFullNutrientData(nutrientData) {
//     return {
//       core: this.processCoreNutrients(nutrientData),
//       vitamins: this.processVitamins(nutrientData),
//       minerals: this.processMinerals(nutrientData),
//     };
//   }

//   static processCoreNutrients(data) {
//     return this._processNutrientCategory(data, [
//       "ENERC_KCAL",
//       "CHOCDF",
//       "PROCNT",
//       "FAT",
//       "FASAT",
//       "FATRN",
//       "FAPU",
//       "FAMS",
//       "CHOLE",
//       "FIBTG",
//       "SUGAR",
//     ]);
//   }

//   static processVitamins(data) {
//     return this._processNutrientCategory(data, [
//       "VITA_RAE",
//       "VITC",
//       "VITD",
//       "TOCPHA",
//       "VITK1",
//       "THIA",
//       "RIBF",
//       "NIA",
//       "VITB6A",
//       "FOLDFE",
//       "VITB12",
//     ]);
//   }

//   static processMinerals(data) {
//     return this._processNutrientCategory(data, [
//       "CA",
//       "FE",
//       "MG",
//       "P",
//       "K",
//       "NA",
//       "ZN",
//     ]);
//   }

//   static applyServingSize(nutrients, servings) {
//     const processor = (value) => this._roundToTwo(value * servings);

//     return {
//       core: this._adjustNutrients(nutrients.core, processor),
//       vitamins: this._adjustNutrients(nutrients.vitamins, processor),
//       minerals: this._adjustNutrients(nutrients.minerals, processor),
//     };
//   }

//   static _processNutrientCategory(data, keys) {
//     const result = {};
//     keys.forEach((key) => {
//       const nutrient = data?.totalNutrients?.[key] || {};
//       const daily = data?.totalDaily?.[key] || null;

//       result[key] = {
//         label: this._getLabel(key),
//         quantity: this._safeParse(nutrient.quantity),
//         unit: nutrient.unit || "g",
//         totalDaily: daily
//           ? {
//               quantity: this._safeParse(daily.quantity),
//               unit: daily.unit || "%",
//             }
//           : {
//               quantity: null,
//               unit: null,
//             },
//       };
//     });

//     return result;
//   }

//   static _adjustNutrients(category, processor) {
//     if (!category || typeof category !== "object") {
//       logger.debug("[Adjust Nutrients] Invalid category input:", category);
//       return {};
//     }

//     const adjusted = {};
//     Object.entries(category).forEach(([key, nutrient]) => {
//       // Check if totalDaily exists and has a valid number
//       const hasDaily =
//         typeof nutrient?.totalDaily?.quantity === "number" &&
//         !isNaN(nutrient.totalDaily.quantity);

//       try {
//         adjusted[key] = {
//           ...nutrient,
//           quantity: this._processValue(nutrient.quantity, processor),
//           totalDaily: hasDaily
//             ? {
//                 quantity: this._processValue(
//                   nutrient.totalDaily.quantity,
//                   processor
//                 ),
//                 unit: nutrient.totalDaily.unit || "%",
//               }
//             : null,
//         };
//       } catch (error) {
//         logger.error("[Adjust Nutrients] Error processing nutrient:", {
//           key,
//           nutrient,
//           error: error.message,
//         });
//         adjusted[key] = {
//           ...nutrient,
//           quantity: null,
//           totalDaily: null,
//         };
//       }
//     });

//     return adjusted;
//   }

//   static _roundToTwo(num) {
//     return Math.round((Number(num) + Number.EPSILON) * 100) / 100;
//   }

//   static _safeParse(value) {
//     const num = parseFloat(value);
//     return isNaN(num) ? null : this._roundToTwo(num);
//   }

//   static _processValue(value, processor) {
//     if (value === null) return null;
//     if (typeof value === "number") return this._roundToTwo(processor(value));

//     const num = parseFloat(value);
//     return isNaN(num) ? null : this._roundToTwo(processor(num));
//   }

//   static _getLabel(key) {
//     const labels = {
//       ENERC_KCAL: "Calories",
//       CHOCDF: "Total Carbohydrate",
//       PROCNT: "Protein",
//       FAT: "Total Fat",
//       FASAT: "Saturated Fat",
//       FATRN: "Trans Fat",
//       FAPU: "Polyunsaturated Fat",
//       FAMS: "Monounsaturated Fat",
//       CHOLE: "Cholesterol",
//       FIBTG: "Dietary Fiber",
//       SUGAR: "Total Sugar",
//       VITA_RAE: "Vitamin A",
//       VITC: "Vitamin C",
//       VITD: "Vitamin D",
//       TOCPHA: "Vitamin E",
//       VITK1: "Vitamin K1",
//       THIA: "Thiamin (Vitamin B1)",
//       RIBF: "Riboflavin (Vitamin B2)",
//       NIA: "Niacin (Vitamin B3)",
//       VITB6A: "Vitamin B6",
//       FOLDFE: "Folate (Vitamin B9)",
//       VITB12: "Vitamin B12",
//       CA: "Calcium",
//       FE: "Iron",
//       MG: "Magnesium",
//       P: "Phosphorus",
//       K: "Potassium",
//       NA: "Sodium",
//       ZN: "Zinc",
//     };

//     return labels[key] || "Unknown Nutrient";
//   }
// }

// module.exports = NutrientProcessor;
