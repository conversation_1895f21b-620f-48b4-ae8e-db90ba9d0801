// utils/HttpService.js
const logger = require("./logger.js");

/**
 * HTTP Service - Centralized HTTP client with retry logic and error handling
 */
class HttpService {
  static DEFAULT_TIMEOUT = 8000; // 8 seconds
  static MAX_RETRIES = 2;
  static RETRY_DELAY = 1000; // 1 second base delay

  /**
   * Safe fetch wrapper with timeout and retry logic
   * @param {URL|string} url - Request URL
   * @param {Object} [options] - Fetch options
   * @param {number} [retries] - Current retry attempt
   * @returns {Promise<Object>} Parsed JSON response
   */
  static async safeFetch(url, options = {}, retries = 0) {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      options.timeout || this.DEFAULT_TIMEOUT
    );

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          "Content-Type": "application/json",
          ...(options.headers || {}),
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (this._shouldRetry(response.status, retries)) {
          return this._retry(url, options, retries);
        }
        throw new Error(
          `HTTP Error: ${response.status} ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (this._isRetryable(error) && retries < this.MAX_RETRIES) {
        return this._retry(url, options, retries);
      }

      throw this._formatError(error, url);
    }
  }

  /**
   * Determine if a request should be retried
   * @private
   */
  static _shouldRetry(statusCode, retries) {
    return (
      retries < this.MAX_RETRIES &&
      (statusCode === 429 || // Too Many Requests
        statusCode >= 500) // Server Errors
    );
  }

  /**
   * Check if error is retryable
   * @private
   */
  static _isRetryable(error) {
    return (
      error.name === "AbortError" || // Timeout
      error.message.includes("Failed to fetch")
    );
  }

  /**
   * Perform retry with exponential backoff
   * @private
   */
  static async _retry(url, options, retries) {
    const delay = this.RETRY_DELAY * Math.pow(2, retries);

    await new Promise((resolve) => setTimeout(resolve, delay));

    return this.safeFetch(url, options, retries + 1);
  }

  /**
   * Format consistent error messages
   * @private
   */
  static _formatError(error, url) {
    return new Error(
      `HTTP Request Failed: ${error.message}\n` +
        `URL: ${url}\n` +
        `Stack: ${error.stack}`
    );
  }

  /**
   * Log HTTP attempts (dev environment only)
   * @private
   */
  static _logAttempt(url, attempt) {
    if (process.env.NODE_ENV === "development") {
      logger.debug(`HTTP Attempt #${attempt} to ${url}`);
    }
  }
}

module.exports = HttpService;
