// utils/logger.js
const isProduction = process.env.NODE_ENV === "production";

// Use console as the default logger
const logger = console;

module.exports = {
  info: (...args) => {
    if (!isProduction) logger.info(...args);
  },
  debug: (...args) => {
    if (!isProduction) logger.debug(...args);
  },
  error: (...args) => logger.error(...args), // Always log errors
  warn: (...args) => logger.warn(...args), // Always log warnings
};
