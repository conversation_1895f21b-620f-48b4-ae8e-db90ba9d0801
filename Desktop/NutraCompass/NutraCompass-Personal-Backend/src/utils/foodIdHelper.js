// utils/foodIdHelper.js

/**
 * Prefix identifiers for different food data sources.
 * Ensures unique namespace separation between external systems and internal IDs.
 */
const PREFIXES = {
  EDAMAM: "edm:",
  USDA: "usda:",
  CUSTOM: "cust:",
};

/**
 * Universal converter to external ID format with source-specific prefix
 * @param {string} internalId - Original ID from external system (without prefix)
 * @param {string} source - Data source name (EDAMAM/USDA/CUSTOM)
 * @returns {string} External ID with proper namespacing
 * @throws {Error} When invalid source is specified
 */
function toExternalId(internalId, source) {
  const prefix = PREFIXES[source.toUpperCase()];
  if (!prefix) throw new Error(`Invalid source: ${source}`);
  return internalId.startsWith(prefix) ? internalId : `${prefix}${internalId}`;
}

/**
 * Universal converter from prefixed external ID to original system ID
 * @param {string} externalId - Namespaced ID containing source prefix
 * @param {string} source - Expected data source name (EDAMAM/USDA/CUSTOM)
 * @returns {string} Original system ID without prefix
 * @throws {Error} When invalid source is specified or ID doesn't match source
 */
function fromExternalId(externalId, source) {
  const prefix = PREFIXES[source.toUpperCase()];
  if (!prefix) throw new Error(`Invalid source: ${source}`);
  return externalId.replace(prefix, "");
}

/**
 * Helper methods for Edamam API ID conversion and validation
 */
const EdamamHelper = {
  /**
   * Convert Edamam API ID to namespaced internal format
   * @param {string} apiId - Edamam API food identifier
   * @returns {string} Namespaced EDAMAM ID (edm: + apiId)
   */
  toInternal: (apiId) => toExternalId(apiId, "EDAMAM"),

  /**
   * Extract original Edamam ID from namespaced internal ID
   * @param {string} internalId - Namespaced EDAMAM ID
   * @returns {string} Original Edamam API identifier
   */
  fromInternal: (internalId) => fromExternalId(internalId, "EDAMAM"),

  /**
   * Validate if ID follows Edamam namespace convention
   * @param {string} id - ID to validate
   * @returns {boolean} True if ID starts with EDAMAM prefix
   */
  validate: (id) => id.startsWith(PREFIXES.EDAMAM),
};

/**
 * Helper methods for USDA FDC ID conversion and validation
 */
const USDAHelper = {
  /**
   * Convert USDA FDC ID to namespaced internal format
   * @param {number|string} fdcId - USDA FoodData Central identifier
   * @returns {string} Namespaced USDA ID (usda: + fdcId)
   */
  toInternal: (fdcId) => toExternalId(fdcId, "USDA"),

  /**
   * Extract original USDA FDC ID from namespaced internal ID
   * @param {string} internalId - Namespaced USDA ID
   * @returns {string} Original USDA FDC identifier
   */
  fromInternal: (internalId) => fromExternalId(internalId, "USDA"),

  /**
   * Validate if ID follows USDA namespace convention
   * @param {string} id - ID to validate
   * @returns {boolean} True if ID starts with USDA prefix
   */
  validate: (id) => id.startsWith(PREFIXES.USDA),
};

/**
 * Helper methods for custom database ID conversion and validation
 */
const CustomHelper = {
  /**
   * Convert custom DB ID to namespaced internal format
   * @param {number|string} dbId - Custom database identifier
   * @returns {string} Namespaced CUSTOM ID (cust: + dbId)
   */
  toInternal: (dbId) => toExternalId(dbId, "CUSTOM"),

  /**
   * Extract original custom DB ID from namespaced internal ID
   * @param {string} internalId - Namespaced CUSTOM ID
   * @returns {string} Original custom database identifier
   */
  fromInternal: (internalId) => fromExternalId(internalId, "CUSTOM"),

  /**
   * Validate if ID follows custom namespace convention
   * @param {string} id - ID to validate
   * @returns {boolean} True if ID starts with CUSTOM prefix
   */
  validate: (id) => id.startsWith(PREFIXES.CUSTOM),
};

/**
 * Universal food ID validation across all supported sources
 * @param {string} id - ID to validate
 * @returns {boolean} True if ID contains any recognized namespace prefix
 */
function validateFoodId(id) {
  return Object.values(PREFIXES).some((prefix) => id.startsWith(prefix));
}

module.exports = { EdamamHelper, USDAHelper, CustomHelper, validateFoodId };
