// Disable verbose logs in production
if (process.env.NODE_ENV === "production") {
  console.debug = () => {};
  console.info = () => {};
  console.log = () => {};
}

const logger = require("./utils/logger");
const express = require("express");
const cors = require("cors");
const { rateLimit, MemoryStore } = require("express-rate-limit");
const { RedisStore } = require("rate-limit-redis");
const { createClient } = require("redis");
const slowDown = require("express-slow-down");
require("dotenv").config({
  path: `.env.${process.env.NODE_ENV}`,
});

const CacheService = require("./services/CacheService");
const Configs = require("../configs.js");

// Global error handlers
process.on("uncaughtException", (err) => {
  logger.error("UNCAUGHT EXCEPTION! 💥 Shutting down...", err);
  process.exit(1);
});

process.on("unhandledRejection", (err) => {
  logger.error("UNHANDLED REJECTION! 💥 Shutting down...", err);
  process.exit(1);
});

// Rate Limiter Redis Client (DB 0)
console.log(Configs.redisConfig.URL);
const rateLimitClient = createClient({
  url: Configs.redisConfig.URL,
  tls: {}, // Empty object
  socket: {
    keepAlive: 5000,
    connectTimeout: Configs.redisConfig.CACHE.CONNECTION_TIMEOUT,
    reconnectStrategy: (retries) => {
      if (retries > 5) {
        logger.debug("Maximum Redis retries exceeded");
        return new Error("Redis connection failed");
      }
      return Math.min(retries * 500, 5000);
    },
  },
  database: 0,
  disableOfflineQueue: true,
});

// Redis event handlers for rate limiter
rateLimitClient.on("error", (err) =>
  logger.error("[Rate Limit] Redis error:", err)
);
rateLimitClient.on("connect", () =>
  logger.debug("[Rate Limit] Redis connection established (DB 0)")
);
rateLimitClient.on("ready", () =>
  logger.debug("[Rate Limit] Redis client ready")
);
rateLimitClient.on("reconnecting", () =>
  logger.debug("[Rate Limit] Redis reconnecting...")
);

const createRateLimiter = (
  maxRequests,
  windowMinutes,
  message,
  skipPaths = [],
  limiterName = "default"
) => {
  // Start with in-memory store
  let store = new MemoryStore();
  let usingRedis = false;

  // Function to upgrade to Redis when available
  const upgradeToRedis = () => {
    if (rateLimitClient.isReady && !usingRedis) {
      store = new RedisStore({
        sendCommand: (...args) => rateLimitClient.sendCommand(args),
        prefix: `rl:${limiterName}:`,
      });
      usingRedis = true;
      logger.debug(`Upgraded ${limiterName} limiter to Redis`);
    }
  };

  // Check immediately
  upgradeToRedis();

  // Periodically check for Redis availability
  const upgradeInterval = setInterval(upgradeToRedis, 5000);

  return rateLimit({
    store,
    windowMs: windowMinutes * 60 * 1000,
    max: maxRequests,
    message: message,
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) =>
      process.env.NODE_ENV === "test" || skipPaths.includes(req.path),
    keyGenerator: (req) =>
      req.user?.uid ? `${req.user.uid}_${req.ip}` : req.ip,
    validate: {
      trustProxy: process.env.NODE_ENV === "production",
      default: true,
    },
  });
};

const initializeApp = async () => {
  try {
    // ===== 1. PLATFORM DETECTION =====
    const isWindows =
      process.env.IS_WINDOWS === "true" || process.platform === "win32";
    logger.debug(
      `Platform: ${process.platform}${isWindows ? " (Windows)" : ""}`
    );

    // ===== 2. REDIS CONNECTION SETUP =====
    const redisUrl = Configs.redisConfig.URL;
    logger.debug(`Using Redis URL: ${redisUrl}`);

    // Rate Limiter Redis Client (DB 0)
    const rateLimitClient = createClient({
      url: redisUrl,
      tls: isWindows ? undefined : {},
      socket: {
        keepAlive: 5000,
        connectTimeout: isWindows ? 15000 : 10000,
        reconnectStrategy: (retries) => {
          if (retries > 10) return new Error("Redis connection failed");
          return Math.min(retries * 1000, isWindows ? 15000 : 10000);
        },
        ...(isWindows && {
          family: 4, // Force IPv4 on Windows
          noDelay: true,
        }),
      },
      database: 0,
      disableOfflineQueue: true,
    });

    // Redis event handlers
    rateLimitClient.on("error", (err) =>
      logger.error("[Rate Limit] Redis error:", err)
    );
    rateLimitClient.on("connect", () =>
      logger.debug("[Rate Limit] Redis connection established (DB 0)")
    );
    rateLimitClient.on("ready", () =>
      logger.debug("[Rate Limit] Redis client ready")
    );
    rateLimitClient.on("reconnecting", () =>
      logger.debug("[Rate Limit] Redis reconnecting...")
    );

    // ===== 3. CONNECT TO REDIS SERVICES =====
    logger.debug("Starting Redis connections...");

    try {
      await rateLimitClient.connect();
      logger.debug("Rate limit client connected successfully");
    } catch (rateLimitError) {
      logger.error("Rate limit client connection failed:", rateLimitError);
    }

    try {
      await CacheService.initialize();
      logger.debug("Cache service initialized");
    } catch (cacheError) {
      logger.error("Cache service initialization failed:", cacheError);
    }

    // ===== 4. EXPRESS SERVER SETUP =====
    const app = express();

    // Request timeout middleware
    app.use((req, res, next) => {
      req.setTimeout(25000, () => {
        if (!res.headersSent) {
          logger.warn(`Request timeout: ${req.method} ${req.url}`);
          res.status(504).json({ error: "Request timeout" });
        }
      });
      next();
    });

    app.set("trust proxy", process.env.NODE_ENV === "production" ? 2 : 0);

    // Security headers
    app.use((req, res, next) => {
      res.header("Cross-Origin-Resource-Policy", "cross-origin");
      res.header("Cache-Control", "public, max-age=31536000, immutable");
      next();
    });

    // CORS configuration
    const corsOptions = {
      origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        const allowedOrigins = [
          ...(process.env.NODE_ENV === "production"
            ? process.env.CORS_ORIGIN?.split(",") || []
            : []),
          "https://nutracompass.io",
          ...(process.env.NODE_ENV !== "production"
            ? [process.env.CORS_ORIGIN || "*"]
            : []),
        ].filter(Boolean);

        if (allowedOrigins.includes("*") || allowedOrigins.includes(origin)) {
          return callback(null, true);
        }

        // Check if the origin matches any subdomains of nutracompass.io
        if (origin.endsWith(".nutracompass.io")) {
          return callback(null, true);
        }

        callback(new Error("Not allowed by CORS"));
      },
      methods: ["GET", "POST", "PUT", "DELETE"],
      allowedHeaders: ["Content-Type", "Authorization"],
      credentials: true,
    };
    app.use(cors(corsOptions));

    // Body parser
    app.use(express.json({ limit: "20mb" }));
    app.use(express.urlencoded({ limit: "20mb", extended: true }));

    // Rate limit configuration
    const RATE_CONFIG = {
      global: {
        window: Configs.redisConfig.RATE_LIMIT.GLOBAL_WINDOW,
        max: Configs.redisConfig.RATE_LIMIT.GLOBAL_MAX,
      },
      auth: {
        window: Configs.redisConfig.RATE_LIMIT.AUTH_WINDOW,
        max: Configs.redisConfig.RATE_LIMIT.AUTH_MAX,
      },
    };

    // Apply rate limits
    app.use(
      createRateLimiter(
        RATE_CONFIG.global.max,
        RATE_CONFIG.global.window,
        "Too many requests...",
        ["/health", "/", "/v1/authentication"],
        "global"
      )
    );

    // Brute-force protection for authentication
    app.use(
      "/v1/authentication",
      slowDown({
        windowMs: RATE_CONFIG.auth.window * 60 * 1000,
        delayAfter: 5,
        delayMs: (hits) => hits * 200,
        skip: (req) => process.env.NODE_ENV === "test",
      }),
      createRateLimiter(
        RATE_CONFIG.auth.max,
        RATE_CONFIG.auth.window,
        "Too many authentication attempts...",
        [],
        "auth"
      )
    );

    // Routes
    [
      require("./routes/AnalyticsRoutes"), // Analytics about the app
      require("./routes/FeedbackRoutes"),
      require("./routes/AuthRoutes"),
      require("./routes/UserSettingsRoutes"),
      require("./routes/FoodDiaryRoutes"),
      require("./routes/FoodSearchRoutes"),
      require("./routes/FoodMenuRoutes"),
      require("./routes/NutritionalProgramRoutes"),
      require("./routes/FriendManagementRoutes"),
      require("./routes/NotificationRoutes"),
      require("./routes/CacheRoutes"),
    ].forEach((route) => app.use(route));

    // Test endpoint
    app.get("/rate-test", (req, res) => {
      res.json({ remaining: req.rateLimit.remaining });
    });

    // Health endpoint
    app.get("/health", async (req, res) => {
      try {
        res.status(200).json({
          status: "ok",
          environment: process.env.NODE_ENV,
          redis: {
            rateLimit: rateLimitClient.isReady,
            cache: CacheService.instance?.redisClient?.isReady || false,
          },
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          ...(CacheService.instance && {
            cacheMetrics: CacheService.instance.getPerformanceSummary(),
          }),
        });
      } catch (error) {
        res.status(500).json({
          status: "error",
          message: "Health check failed",
          error:
            process.env.NODE_ENV === "development" ? error.message : undefined,
        });
      }
    });

    app.get("/", (req, res) => {
      res.send("Welcome to the NutraCompass Backend API!");
    });

    // Error handling
    app.use((err, req, res, next) => {
      const statusCode = err.statusCode || 500;
      const message =
        process.env.NODE_ENV === "production"
          ? "An unexpected error occurred"
          : err.message;

      logger.error(`[${statusCode}] ${req.method} ${req.url} - ${err.message}`);

      res.status(statusCode).json({
        error: err.name || "InternalServerError",
        message,
        ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
        timestamp: new Date().toISOString(),
      });
    });

    // ===== 5. START SERVER =====
    const PORT = process.env.PORT || 3000;
    const host = isWindows ? "127.0.0.1" : "0.0.0.0";

    const server = app.listen(PORT, host, () => {
      logger.debug(`Server running at http://${host}:${PORT}`);
      logger.debug("Environment:", process.env.NODE_ENV);
      logger.debug(
        "CORS Origin:",
        process.env.NODE_ENV === "production"
          ? process.env.CORS_ORIGIN || "None allowed"
          : process.env.CORS_ORIGIN || "All origins"
      );
      logger.debug("Redis Connections:", {
        rateLimit: "DB 0",
        cache: `DB ${Configs.redisConfig.CACHE.DB_NUMBER}`,
      });
      logger.debug("Rate Limits:", {
        global: `${RATE_CONFIG.global.max} req/${RATE_CONFIG.global.window}min`,
        auth: `${RATE_CONFIG.auth.max} req/${RATE_CONFIG.auth.window}min`,
      });
    });

    // Keep-alive optimization
    server.keepAliveTimeout = 55000;
    server.headersTimeout = 60000;

    // Graceful shutdown
    process.on("SIGTERM", async () => {
      logger.debug("SIGTERM signal received: closing server");
      server.close(async () => {
        logger.debug("HTTP server closed");
        try {
          await rateLimitClient.quit();
        } catch (quitError) {
          logger.error("Error quitting rate limit client:", quitError);
        }

        if (CacheService.instance) {
          try {
            await CacheService.instance.disconnect();
          } catch (disconnectError) {
            logger.error("Error disconnecting cache service:", disconnectError);
          }
        }
        logger.debug("Redis connections closed");
        process.exit(0);
      });
    });
  } catch (error) {
    logger.error("Failed to initialize application:", error);
    process.exit(1);
  }
};

initializeApp();
