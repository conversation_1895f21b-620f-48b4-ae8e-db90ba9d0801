import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  TouchableWithoutFeedback,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useThemeContext } from "../context/ThemeContext.js";
import { scaleSize } from "../utils/deviceUtils.js";

const CustomPickerModal = ({ title, options, visible, onClose, onSelect }) => {
  const { theme } = useThemeContext();

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0,0,0,0.5)",
    },
    modalContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: scaleSize(20),
      padding: scaleSize(20),
      width: "85%",
      maxHeight: "70%",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: scaleSize(15),
      paddingBottom: scaleSize(10),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    modalTitle: {
      fontSize: scaleSize(18),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      flex: 1,
    },
    closeButton: {
      marginLeft: scaleSize(10),
    },
    optionsContainer: {
      maxHeight: "80%",
    },
    optionItem: {
      paddingVertical: scaleSize(15),
      paddingHorizontal: scaleSize(10),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    optionText: {
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
      textAlign: "center",
    },
    selectedOption: {
      fontWeight: "bold",
      color: theme.colors.primary,
    },
    noOptionsText: {
      textAlign: "center",
      color: theme.colors.subTextColor,
      fontStyle: "italic",
      paddingVertical: scaleSize(20),
    },
  });

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      {/* Backdrop that closes modal on press */}
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          {/* Prevent modal from closing when pressing content */}
          <TouchableWithoutFeedback>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{title}</Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <MaterialCommunityIcons
                    name="close"
                    size={scaleSize(24)}
                    color={theme.colors.subTextColor}
                  />
                </TouchableOpacity>
              </View>

              {options.length > 0 ? (
                <FlatList
                  style={styles.optionsContainer}
                  data={options}
                  keyExtractor={(item) => item.value}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.optionItem}
                      onPress={() => {
                        onSelect(item.value);
                        onClose();
                      }}
                    >
                      <Text
                        style={[
                          styles.optionText,
                          item.selected && styles.selectedOption,
                        ]}
                      >
                        {item.label}
                      </Text>
                    </TouchableOpacity>
                  )}
                />
              ) : (
                <Text style={styles.noOptionsText}>No options available</Text>
              )}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default CustomPickerModal;
