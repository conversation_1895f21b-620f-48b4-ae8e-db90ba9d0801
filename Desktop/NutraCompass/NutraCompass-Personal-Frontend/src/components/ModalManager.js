// components/ModalManager.js
import React, { useEffect } from "react";
import { View, StyleSheet, Dimensions, Platform } from "react-native";
import Modal from "react-native-modal";
import { useModal, modalConfig } from "../context/ModalContext.js";
import { useDataLoading } from "../context/DataLoadingContext.js";

const ModalManager = () => {
  const { modalStack, closeModal } = useModal();
  const { initialLoadComplete } = useDataLoading();

  // Log modal stack changes for debugging
  useEffect(() => {
    console.log(
      "Current Modal Stack:",
      modalStack.map((m) => ({
        type: m.type,
        id: m.id,
        props: Object.keys(m.props),
      }))
    );
  }, [modalStack]);

  return (
    <View style={styles.container} pointerEvents="box-none">
      {modalStack.map((modal) => {
        const config = modalConfig[modal.type] || {};
        const ModalComponent = config.component;

        return (
          <Modal
            key={modal.id}
            isVisible={true}
            deviceHeight={Dimensions.get("window").height}
            statusBarTranslucent
            animationIn={
              config.animationConfig?.enter?.animation || "slideInUp"
            }
            animationOut={
              config.animationConfig?.exit?.animation || "slideOutDown"
            }
            animationInTiming={config.animationConfig?.enter?.duration || 300}
            animationOutTiming={config.animationConfig?.exit?.duration || 200}
            backdropOpacity={config.backdropOpacity || 0.5}
            style={[styles.modalBase, config.style]}
            onBackdropPress={closeModal}
            avoidKeyboard={true}
            coverScreen={false}
            hasBackdrop={true}
            useNativeDriverForBackdrop={true}
          >
            {ModalComponent && initialLoadComplete && (
              <ModalComponent
                {...modal.props}
                closeModal={closeModal}
                isVisible={true}
              />
            )}
          </Modal>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 999,
    elevation: Platform.OS === "android" ? 999 : 0,
    pointerEvents: "box-none",
  },
  modalBase: {
    margin: 0,
  },
});

export default ModalManager;
