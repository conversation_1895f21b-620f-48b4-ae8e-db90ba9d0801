import React, { useState, useMemo, memo } from "react";
import {
  Modal,
  View,
  FlatList,
  TouchableOpacity,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
} from "react-native";
import { MaterialCommunityIcons, FontAwesome6 } from "@expo/vector-icons";
import { useThemeContext } from "../context/ThemeContext";
import { useTime } from "../context/TimeContext";
import timezones from "../data/timezones";
import { scaleSize } from "../utils/deviceUtils.js";
const PAGE_SIZE = 50;
const US_TIMEZONES = [
  "America/New_York",
  "America/Chicago",
  "America/Denver",
  "America/Phoenix",
  "America/Los_Angeles",
  "America/Anchorage",
  "Pacific/Honolulu",
  "America/Detroit",
  "America/Boise",
  "America/Menominee",
  "America/North_Dakota/Center",
  "America/Indiana/Indianapolis",
];

// Memoized list item component
const TimezoneItem = memo(({ item, deviceTimezone, theme, onPress }) => {
  const isSelected = item === deviceTimezone;

  const handlePress = () => {
    onPress(item); // Explicitly pass the timezone string
  };

  return (
    <TouchableOpacity
      style={[
        styles.timezoneItem,
        {
          backgroundColor: isSelected ? theme.colors.surface : "transparent",
          borderBottomColor: theme.colors.sectionBorderColor,
        },
      ]}
      onPress={handlePress}
    >
      <Text
        style={{
          color: theme.colors.primaryTextColor,
          fontSize: 16,
        }}
      >
        {item.replace(/_/g, " ")}
      </Text>

      {isSelected && (
        <FontAwesome6
          name="location-crosshairs"
          size={18}
          color={theme.colors.primary}
        />
      )}
    </TouchableOpacity>
  );
});

const TimezonePickerModal = ({
  visible,
  onClose,
  currentTimezone,
  onSelect,
}) => {
  const { theme } = useThemeContext();
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const deviceTimezone = currentTimezone;

  // Filter to only show US timezones
  const processedZones = useMemo(() => {
    const searchTerm = searchQuery.toLowerCase();
    return (timezones || [])
      .filter(
        (zone) =>
          US_TIMEZONES.includes(zone) &&
          (zone.toLowerCase().includes(searchTerm) ||
            (zone.split("/")[1] || "").toLowerCase().includes(searchTerm))
      )
      .sort((a, b) => a.localeCompare(b));
  }, [searchQuery]);

  const paginatedZones = useMemo(
    () => processedZones.slice(0, page * PAGE_SIZE),
    [processedZones, page]
  );

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <SafeAreaView
        style={[
          styles.modalContainer,
          { backgroundColor: theme.colors.screenBackground },
        ]}
      >
        {/* Improved Header */}
        <View
          style={[
            styles.header,
            {
              backgroundColor: theme.colors.screenBackground,
              borderBottomColor: theme.colors.sectionBorderColor,
            },
          ]}
        >
          <TouchableOpacity onPress={onClose} style={styles.backButton}>
            <MaterialCommunityIcons
              name="chevron-left"
              size={scaleSize(28)}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
          <Text
            style={[styles.title, { color: theme.colors.primaryTextColor }]}
          >
            Timezones
          </Text>
        </View>

        {/* Search Input */}
        <TextInput
          style={[
            styles.searchInput,
            {
              color: theme.colors.primaryTextColor,
              borderColor: theme.colors.sectionBorderColor,
              backgroundColor: theme.colors.inputBackground,
              fontSize: scaleSize(14),
            },
          ]}
          placeholder="Search timezones..."
          placeholderTextColor={theme.colors.primaryTextColor}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />

        {/* Timezone List */}
        <FlatList
          data={paginatedZones}
          keyExtractor={(item) => item}
          initialNumToRender={20}
          maxToRenderPerBatch={30}
          windowSize={21}
          getItemLayout={(data, index) => ({
            length: 50,
            offset: 50 * index,
            index,
          })}
          onEndReached={() => setPage((prev) => prev + 1)}
          onEndReachedThreshold={0.5}
          renderItem={({ item }) => (
            <TimezoneItem
              item={item}
              deviceTimezone={deviceTimezone}
              theme={theme}
              onPress={onSelect}
            />
          )}
        />
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center", // Centers the title in the header
    height: scaleSize(60),
    borderBottomWidth: scaleSize(1),
  },
  backButton: {
    position: "absolute", // Keeps the back button on the left
    left: scaleSize(20),
    zIndex: 1, // Ensures the button is always on top
  },
  title: {
    fontSize: scaleSize(18),
    fontWeight: "bold",
  },
  searchInput: {
    height: scaleSize(40),
    margin: scaleSize(16),
    paddingHorizontal: scaleSize(12),
    borderRadius: scaleSize(8),
    borderWidth: scaleSize(1),
  },
  timezoneItem: {
    padding: scaleSize(16),
    borderBottomWidth: scaleSize(1),
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
});

export default TimezonePickerModal;
