import React, { useEffect, useMemo, useRef } from "react";
import { View, Text, StyleSheet } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Stop } from "react-native-svg";
import PropTypes from "prop-types";
import Feather from "react-native-vector-icons/Feather";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useThemeContext } from "../context/ThemeContext.js";
import Animated, {
  useSharedValue,
  useAnimatedProps,
  withTiming,
  Easing,
} from "react-native-reanimated";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js"; // Import the food log context

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

// Base size for reference scaling (90px is our reference size)
const BASE_SIZE = 90;

const ProgressRing = React.memo(
  ({
    size,
    strokeWidth = 15,
    currentValue,
    goalValue,
    topOuterLabel = "",
    topInteriorLabel = "",
    bottomInteriorLabel = "",
    firstBottomOuterLabel = "",
    bottomOuterNumeratorLabel = "",
    bottomOuterDenominatorLabel = "",
    consumedColor = ["#3498db", "#2980b9"],
    remainingColor = "#2980b9",
    iconName = null,
    iconType = null,
  }) => {
    const { theme } = useThemeContext();
    const { selectedDate } = useFoodLog(); // Get selectedDate from food log context
    const radius = size / 2 - strokeWidth / 2;
    const circumference = 2 * Math.PI * radius;

    // Calculate scaling factor based on current size vs base size
    const scaleFactor = size / BASE_SIZE;

    // Calculate proportional sizes
    const iconSize = 28 * scaleFactor;
    const interiorFontSize = 11 * scaleFactor;
    const topOuterFontSize = 12 * scaleFactor;
    const bottomOuterFontSize = 11 * scaleFactor;
    const topLabelOffset = -20 * scaleFactor;
    const bottomContainerOffset = -35 * scaleFactor;

    // Shared value for the animated stroke offset
    const strokeOffset = useSharedValue(circumference);
    const prevSelectedDate = useRef(selectedDate); // Track previous selectedDate

    const percentage = useMemo(() => {
      return goalValue > 0
        ? currentValue / goalValue < 0.01
          ? 0
          : Math.min(currentValue / goalValue, 1)
        : 0;
    }, [currentValue, goalValue]);

    // Animation config
    const animationConfig = {
      duration: 800,
      easing: Easing.out(Easing.exp),
    };

    // Animated props that will smoothly transition the strokeDashoffset
    const animatedCircleProps = useAnimatedProps(() => ({
      strokeDashoffset: strokeOffset.value,
    }));

    useEffect(() => {
      // Reset animation when selectedDate changes
      if (selectedDate !== prevSelectedDate.current) {
        strokeOffset.value = circumference; // Reset to 0%
        prevSelectedDate.current = selectedDate;
      }

      // Animate to new percentage
      const targetValue = circumference - percentage * circumference;
      strokeOffset.value = withTiming(targetValue, animationConfig);
    }, [percentage, selectedDate, circumference]);

    // Format the labels for use in Text components
    const formatLabel = (label) => {
      return label !== undefined && label !== null
        ? typeof label === "number"
          ? label.toLocaleString()
          : label
        : ""; // Fallback to an empty string if undefined or null
    };

    const formattedTopOuterLabel = formatLabel(topOuterLabel);
    const formattedTopInteriorLabel = formatLabel(topInteriorLabel);
    const formattedBottomInteriorLabel = formatLabel(bottomInteriorLabel);
    const formattedFirstBottomOuterLabel = formatLabel(firstBottomOuterLabel);
    const formattedBottomOuterNumeratorLabel = formatLabel(
      bottomOuterNumeratorLabel
    );
    const formattedBottomOuterDenominatorLabel = formatLabel(
      bottomOuterDenominatorLabel
    );

    return (
      <View style={{ width: size, height: size }}>
        {/* Top Outer Label */}
        {formattedTopOuterLabel && (
          <Text
            style={[
              styles.topOuterLabel,
              {
                color: theme.colors.primaryTextColor,
                fontSize: topOuterFontSize,
                top: topLabelOffset,
              },
            ]}
          >
            {formattedTopOuterLabel}
          </Text>
        )}

        <Svg width={size} height={size}>
          <Defs>
            <LinearGradient
              id="gradConsumed"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <Stop offset="0%" stopColor={consumedColor[0]} />
              <Stop offset="100%" stopColor={consumedColor[1]} />
            </LinearGradient>
          </Defs>

          {/* Background Circle */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={remainingColor}
            strokeWidth={strokeWidth}
            fill="none"
          />

          {/* Progress Circle with Gradient Stroke */}
          <AnimatedCircle
            animatedProps={animatedCircleProps}
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#gradConsumed)"
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={circumference}
            strokeLinecap="round"
            rotation="-90"
            origin={`${size / 2}, ${size / 2}`}
          />
        </Svg>

        {/* Icon */}
        {iconName && (
          <View style={styles.iconContainer}>
            {iconType === "Feather" && (
              <Feather name={iconName} size={iconSize} color={remainingColor} />
            )}
            {iconType === "MaterialCommunityIcons" && (
              <MaterialCommunityIcons
                name={iconName}
                size={iconSize}
                color={remainingColor}
              />
            )}
          </View>
        )}

        {/* Interior Labels (Inside the ring) */}
        <View style={styles.interiorLabelContainer}>
          {formattedTopInteriorLabel && (
            <Text
              style={[
                styles.interiorLabel,
                {
                  color: theme.colors.primaryTextColor,
                  fontSize: interiorFontSize,
                },
              ]}
            >
              {formattedTopInteriorLabel}
            </Text>
          )}
          {formattedBottomInteriorLabel && (
            <Text
              style={[
                styles.interiorLabel,
                {
                  color: consumedColor[1],
                  fontSize: interiorFontSize,
                },
              ]}
            >
              {formattedBottomInteriorLabel}
            </Text>
          )}
        </View>

        {/* Bottom Outer Labels (Below the ring) */}
        <View
          style={[
            styles.bottomOuterLabelContainer,
            { bottom: bottomContainerOffset },
          ]}
        >
          {formattedFirstBottomOuterLabel && (
            <Text
              style={[
                styles.bottomOuterLabel,
                {
                  color: theme.colors.primaryTextColor,
                  fontSize: bottomOuterFontSize,
                },
              ]}
            >
              {formattedFirstBottomOuterLabel}
            </Text>
          )}
          {formattedBottomOuterNumeratorLabel &&
            formattedBottomOuterDenominatorLabel && (
              <View style={{ flexDirection: "row" }}>
                <Text
                  style={[
                    styles.bottomOuterLabel,
                    {
                      color: consumedColor[1],
                      fontSize: bottomOuterFontSize,
                    },
                  ]}
                >
                  {Math.round(formattedBottomOuterNumeratorLabel)}
                </Text>
                <Text
                  style={[
                    styles.bottomOuterLabel,
                    {
                      color: theme.colors.primaryTextColor,
                      fontSize: bottomOuterFontSize,
                    },
                  ]}
                >
                  /{Math.round(formattedBottomOuterDenominatorLabel)}g
                </Text>
              </View>
            )}
        </View>
      </View>
    );
  }
);

ProgressRing.propTypes = {
  size: PropTypes.number.isRequired,
  strokeWidth: PropTypes.number,
  currentValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string])
    .isRequired,
  goalValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string])
    .isRequired,
  topOuterLabel: PropTypes.string,
  topInteriorLabel: PropTypes.string,
  bottomInteriorLabel: PropTypes.string,
  firstBottomOuterLabel: PropTypes.string,
  bottomOuterNumeratorLabel: PropTypes.string,
  bottomOuterDenominatorLabel: PropTypes.string,
  consumedColor: PropTypes.array,
  remainingColor: PropTypes.string,
  iconName: PropTypes.string,
  iconType: PropTypes.string,
  showGoalInside: PropTypes.bool,
};

const styles = StyleSheet.create({
  iconContainer: {
    position: "absolute",
    top: "36%",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  interiorLabelContainer: {
    position: "absolute",
    top: "36%",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  interiorLabel: {
    fontWeight: "500",
  },
  topOuterLabel: {
    position: "absolute",
    left: 0,
    right: 0,
    textAlign: "center",
    fontWeight: "500",
  },
  bottomOuterLabelContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  bottomOuterLabel: {
    fontWeight: "500",
    textAlign: "center",
  },
});

export default ProgressRing;
