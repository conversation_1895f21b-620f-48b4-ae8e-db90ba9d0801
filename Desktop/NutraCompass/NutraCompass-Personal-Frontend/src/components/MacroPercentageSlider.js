import React from "react";
import { View, Text, StyleSheet, Dimensions } from "react-native";
import Slider from "@react-native-community/slider";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useThemeContext } from "../context/ThemeContext.js";
import { scaleSize } from "../utils/deviceUtils.js";

const { width, height } = Dimensions.get("window");

const MacroPercentageSlider = ({ selectedValues, onSelect, calories }) => {
  const { theme } = useThemeContext();
  const {
    calculateProteinDailyGrams,
    calculateCarbDailyGrams,
    calculateFatDailyGrams,
  } = useUserSettings();

  const isSum100Percent =
    selectedValues.protein + selectedValues.carb + selectedValues.fat === 100;

  const proteinDailyGrams = calculateProteinDailyGrams(
    calories,
    selectedValues.protein
  );
  const carbDailyGrams = calculateCarbDailyGrams(calories, selectedValues.carb);
  const fatDailyGrams = calculateFatDailyGrams(calories, selectedValues.fat);

  const totalPercentage =
    selectedValues.protein + selectedValues.carb + selectedValues.fat;

  // Responsive sizing based on screen dimensions
  const sliderWidth = width * 0.5;
  const sliderHeight = height * 0.2;
  const labelWidth = width * 0.2;
  const valueWidth = width * 0.2;

  return (
    <View style={styles.contentContainer}>
      {/* Protein Slider */}
      <View style={styles.sliderRow}>
        <Text style={styles.label(theme, labelWidth)}>Protein</Text>
        <View style={styles.sliderGroup}>
          <Slider
            vertical={true}
            style={[
              styles.slider,
              {
                width: sliderWidth,
                height: sliderHeight,
              },
            ]}
            step={5}
            minimumValue={0}
            maximumValue={100}
            value={selectedValues.protein}
            onValueChange={(value) => onSelect("protein", value)}
            minimumTrackTintColor={theme.colors.primary}
            maximumTrackTintColor={theme.colors.sliderInactive}
            thumbTintColor={theme.colors.primary}
          />
          <View style={[styles.valueContainer, { minWidth: valueWidth }]}>
            <Text style={styles.percentage(theme)}>
              {selectedValues.protein}%
            </Text>
            <Text style={styles.grams(theme)}>{proteinDailyGrams} g</Text>
          </View>
        </View>
      </View>

      {/* Carb Slider */}
      <View style={styles.sliderRow}>
        <Text style={styles.label(theme, labelWidth)}>Carb</Text>
        <View style={styles.sliderGroup}>
          <Slider
            vertical={true}
            style={[
              styles.slider,
              {
                width: sliderWidth,
                height: sliderHeight,
              },
            ]}
            step={5}
            minimumValue={0}
            maximumValue={100}
            value={selectedValues.carb}
            onValueChange={(value) => onSelect("carb", value)}
            minimumTrackTintColor={theme.colors.primary}
            maximumTrackTintColor={theme.colors.sliderInactive}
            thumbTintColor={theme.colors.primary}
          />
          <View style={[styles.valueContainer, { minWidth: valueWidth }]}>
            <Text style={styles.percentage(theme)}>{selectedValues.carb}%</Text>
            <Text style={styles.grams(theme)}>{carbDailyGrams} g</Text>
          </View>
        </View>
      </View>

      {/* Fat Slider */}
      <View style={styles.sliderRow}>
        <Text style={styles.label(theme, labelWidth)}>Fat</Text>
        <View style={styles.sliderGroup}>
          <Slider
            vertical={true}
            style={[
              styles.slider,
              {
                width: sliderWidth,
                height: sliderHeight,
              },
            ]}
            step={5}
            minimumValue={0}
            maximumValue={100}
            value={selectedValues.fat}
            onValueChange={(value) => onSelect("fat", value)}
            minimumTrackTintColor={theme.colors.primary}
            maximumTrackTintColor={theme.colors.sliderInactive}
            thumbTintColor={theme.colors.primary}
          />
          <View style={[styles.valueContainer, { minWidth: valueWidth }]}>
            <Text style={styles.percentage(theme)}>{selectedValues.fat}%</Text>
            <Text style={styles.grams(theme)}>{fatDailyGrams} g</Text>
          </View>
        </View>
      </View>

      {/* Total Percentage */}
      <View style={styles.footer(theme)}>
        <View>
          <Text style={styles.footerText(theme)}>% Total</Text>
          <Text style={styles.footerText(theme)}>
            Macronutrients must equal 100%
          </Text>
        </View>
        <View style={styles.totalContainer}>
          <Text style={styles.totalText(isSum100Percent)}>
            {totalPercentage}%
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingBottom: scaleSize(8),
  },
  sliderRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: scaleSize(20),
    width: "100%",
  },
  label: (theme, width) => ({
    fontSize: scaleSize(18),
    fontWeight: "bold",
    color: theme.colors.primaryTextColor,
    width: width,
    textAlign: "right",
    marginRight: scaleSize(15),
  }),
  sliderGroup: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: scaleSize(15),
  },
  slider: {
    // Dimensions applied dynamically
  },
  valueContainer: {
    alignItems: "center",
  },
  percentage: (theme) => ({
    color: theme.colors.primary,
    fontSize: scaleSize(18),
    fontWeight: "600",
  }),
  grams: (theme) => ({
    color: theme.colors.primaryTextColor,
    fontSize: scaleSize(14),
  }),
  footer: (theme) => ({
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    paddingTop: scaleSize(15),
    marginTop: scaleSize(10),
    borderTopWidth: scaleSize(1),
    borderTopColor: theme.colors.cardBorderColor,
  }),
  footerText: (theme) => ({
    color: theme.colors.primaryTextColor,
    fontSize: scaleSize(14),
  }),
  totalText: (isValid) => ({
    color: isValid ? "green" : "red",
    fontSize: scaleSize(22),
    fontWeight: "bold",
  }),
});

export default MacroPercentageSlider;
