import React, { useEffect, useState } from "react";
import { View, Text } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Stop } from "react-native-svg";
import { AnimatedCircularProgress } from "react-native-circular-progress";
import { useThemeContext } from "../context/ThemeContext.js";
import Animated, {
  useSharedValue,
  useAnimatedProps,
  withTiming,
  Easing,
} from "react-native-reanimated";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const ThreeLevelProgressRing = React.memo(
  ({
    size,
    percentages,
    values,
    fillColorsRight,
    fillColorsCenter,
    fillColorsLeft,
  }) => {
    const { selectedDate } = useFoodLog();
    const { theme } = useThemeContext();
    const radius = size / 2;

    // Make stroke widths proportional to size
    const strokeWidthCenter = size * 0.1; // 10% of size
    const strokeWidthHalf = size * 0.09; // 9% of size

    const largerRadius = radius * 1.22;
    const fullCircleCircumference =
      2 * Math.PI * (radius - strokeWidthCenter / 2);

    const remainingCalories = values[0];
    const calorieGoal = values[1];
    const remainingColor = remainingCalories < 0 ? "red" : "green";

    // State to track if we're animating
    const [isAnimating, setIsAnimating] = useState(true);

    // Reset animation state when date changes
    useEffect(() => {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 1000);
      return () => clearTimeout(timer);
    }, [selectedDate]);

    // Clamp percentages between 0 and 1
    const clampPercentage = (percentage) =>
      Math.min(Math.max(percentage, 0), 1);
    const clampedPercentages = percentages.map(clampPercentage);

    // Shared value for the center circle's animated stroke
    const strokeOffsetCenter = useSharedValue(fullCircleCircumference);

    // Animated props for the center circle
    const animatedCenterCircleProps = useAnimatedProps(() => ({
      strokeDashoffset: strokeOffsetCenter.value,
    }));

    // Animation config
    const animationConfig = {
      duration: 800,
      easing: Easing.out(Easing.exp),
    };

    // Animate all rings when percentages change or date changes
    useEffect(() => {
      if (isAnimating) {
        strokeOffsetCenter.value = withTiming(
          fullCircleCircumference * (1 - clampedPercentages[1]),
          animationConfig
        );
      } else {
        // Set immediately if not animating
        strokeOffsetCenter.value =
          fullCircleCircumference * (1 - clampedPercentages[1]);
      }
    }, [clampedPercentages[1], isAnimating]);

    return (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {/* Left Half Circle */}
        <View style={{ marginRight: -largerRadius * 1.8 }}>
          <AnimatedCircularProgress
            key={`left-${selectedDate}`} // Reset on date change
            size={largerRadius * 2}
            width={strokeWidthHalf}
            fill={isAnimating ? 0 : clampedPercentages[2] * 100}
            tintColor={
              clampedPercentages[2] === 0
                ? fillColorsLeft.remainingColor
                : fillColorsLeft.consumedColor
            }
            backgroundColor={fillColorsLeft.remainingColor}
            rotation={180}
            lineCap="round"
            arcSweepAngle={170}
            duration={800}
            easing={Easing.out(Easing.exp)}
            onAnimationComplete={() => setIsAnimating(false)}
          />
        </View>

        {/* Full Center Circle */}
        <Svg height={size} width={size}>
          <Defs>
            <LinearGradient id="gradCenter" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor={fillColorsCenter.consumedColor[0]} />
              <Stop
                offset="100%"
                stopColor={fillColorsCenter.consumedColor[1]}
              />
            </LinearGradient>
          </Defs>
          <Circle
            cx={radius}
            cy={radius}
            r={radius - strokeWidthCenter / 2}
            stroke={fillColorsCenter.remainingColor}
            strokeWidth={strokeWidthCenter}
            strokeLinecap="round"
            fill="none"
          />
          {clampedPercentages[1] > 0 && (
            <AnimatedCircle
              animatedProps={animatedCenterCircleProps}
              cx={radius}
              cy={radius}
              r={radius - strokeWidthCenter / 2}
              stroke="url(#gradCenter)"
              strokeWidth={strokeWidthCenter}
              strokeLinecap="round"
              fill="none"
              strokeDasharray={fullCircleCircumference}
              transform={`rotate(-90 ${radius} ${radius})`}
            />
          )}
        </Svg>

        {/* Right Half Circle */}
        <View style={{ marginLeft: -largerRadius * 1.8 }}>
          <AnimatedCircularProgress
            key={`right-${selectedDate}`} // Reset on date change
            size={largerRadius * 2}
            width={strokeWidthHalf}
            fill={isAnimating ? 0 : clampedPercentages[0] * 100}
            tintColor={
              clampedPercentages[0] === 0
                ? fillColorsRight.remainingColor
                : fillColorsRight.consumedColor
            }
            backgroundColor={fillColorsRight.remainingColor}
            rotation={0}
            lineCap="round"
            arcSweepAngle={170}
            duration={800}
            easing={Easing.out(Easing.exp)}
            onAnimationComplete={() => setIsAnimating(false)}
          />
        </View>

        {/* Text inside the Center Circle */}
        <View
          style={{
            position: "absolute",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 10,
          }}
        >
          <Text
            style={{
              fontSize: size / 7,
              color: theme.colors.primaryTextColor,
              fontWeight: "bold",
            }}
          >
            {calorieGoal}
          </Text>
          <Text
            style={{
              fontSize: size / 8,
              color: remainingColor,
              fontWeight: "700",
            }}
          >
            {Math.round(remainingCalories)}
          </Text>
        </View>
      </View>
    );
  }
);

export default ThreeLevelProgressRing;
