import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  Modal,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Switch,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  Dimensions,
  Animated,
  KeyboardAvoidingView,
} from "react-native";
import { Image } from "expo-image";
import Feather from "react-native-vector-icons/Feather";
import { useThemeContext } from "../context/ThemeContext.js";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useStepsLog } from "../features/StepsLog/context/StepsLogContext.js";
import { scaleSize } from "../utils/deviceUtils.js";

const { height: screenHeight } = Dimensions.get("window");

const StepsAndDistanceGoalModal = ({
  isVisible,
  closeModal,
  stepGoal,
  distanceGoal,
  distanceUnit,
}) => {
  const { theme } = useThemeContext();
  const {
    setDistanceGoalAndUnit,
    getUserMeasurementSettings,
    getPhysicalFitnessGoals,
  } = useUserSettings();
  const { calculateCaloriesBurned, calculateDistanceFromSteps } = useStepsLog();

  // Get measurement settings to sync distance unit
  const measurementSettings = getUserMeasurementSettings();

  // Determine initial unit based on measurement settings or props
  const initialDistanceUnit =
    measurementSettings?.distanceUnit || distanceUnit || "mi";

  const [localStepGoal, setLocalStepGoal] = useState(stepGoal.toLocaleString());
  const [localDistanceGoal, setLocalDistanceGoal] = useState(
    distanceGoal.toString()
  );
  const [localDistanceUnit, setLocalDistanceUnit] =
    useState(initialDistanceUnit);

  // Animation refs
  const imageHeightAnim = useRef(new Animated.Value(scaleSize(150))).current;
  const modalPositionAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const keyboardDidShow = (e) => {
      Animated.timing(imageHeightAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();

      Animated.spring(modalPositionAnim, {
        toValue: -e.endCoordinates.height * 0.25,
        useNativeDriver: true,
        bounciness: 10,
      }).start();
    };

    const keyboardDidHide = () => {
      Animated.timing(imageHeightAnim, {
        toValue: scaleSize(150),
        duration: 300,
        useNativeDriver: false,
      }).start();

      Animated.spring(modalPositionAnim, {
        toValue: 0,
        useNativeDriver: true,
        bounciness: 8,
      }).start();
    };

    const showSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      keyboardDidShow
    );
    const hideSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      keyboardDidHide
    );

    return () => {
      showSub.remove();
      hideSub.remove();
    };
  }, []);

  useEffect(() => {
    if (isVisible) {
      imageHeightAnim.setValue(scaleSize(150));
      modalPositionAnim.setValue(0);

      // Sync with latest settings when modal opens
      const measurementSettings = getUserMeasurementSettings();
      const fitnessGoals = getPhysicalFitnessGoals();

      setLocalDistanceUnit(measurementSettings?.distanceUnit || "mi");
      setLocalStepGoal(fitnessGoals.stepsGoal.toLocaleString());
      setLocalDistanceGoal(fitnessGoals.distanceGoal.toString());
    }
  }, [isVisible]);

  useEffect(() => {
    setLocalStepGoal(stepGoal.toLocaleString());
    setLocalDistanceGoal(distanceGoal.toString());
    setLocalDistanceUnit(distanceUnit);
  }, [stepGoal, distanceGoal, distanceUnit]);

  const handleStepChange = (value) => {
    const numericValue = parseInt(value.replace(/,/g, ""), 10) || 0;
    setLocalStepGoal(numericValue.toLocaleString());
    const distance = calculateDistanceFromSteps(
      numericValue,
      localDistanceUnit
    );
    setLocalDistanceGoal(distance.toFixed(1).toString());
  };

  const handleDistanceChange = (value) => {
    const numericValue = parseFloat(value) || 0;
    setLocalDistanceGoal(numericValue.toString());
    const steps =
      (numericValue * (localDistanceUnit === "mi" ? 1609.34 : 1000)) / 0.762;
    setLocalStepGoal(Math.round(steps).toLocaleString());
  };

  const handleUnitChange = () => {
    const newUnit = localDistanceUnit === "mi" ? "km" : "mi";

    // Convert distance when changing units
    let newDistance = parseFloat(localDistanceGoal);
    if (!isNaN(newDistance)) {
      if (localDistanceUnit === "mi" && newUnit === "km") {
        // Convert miles to kilometers: 1 mile = 1.60934 km
        newDistance = newDistance * 1.60934;
      } else if (localDistanceUnit === "km" && newUnit === "mi") {
        // Convert kilometers to miles: 1 km = 0.621371 miles
        newDistance = newDistance * 0.621371;
      }
      setLocalDistanceGoal(newDistance.toFixed(1).toString());
    }

    setLocalDistanceUnit(newUnit);
  };

  const commitChanges = async () => {
    const finalSteps = parseInt(localStepGoal.replace(/,/g, ""), 10) || 0;
    const finalDistance = parseFloat(localDistanceGoal) || 0;
    const finalDistanceUnit = localDistanceUnit;

    // Single API call to update both settings
    try {
      await setDistanceGoalAndUnit(
        finalSteps,
        finalDistance,
        finalDistanceUnit
      );
    } catch (error) {
      console.error("Failed to save distance goals:", error);
    }
  };

  // Responsive sizing
  const padding = scaleSize(20);
  const borderRadius = scaleSize(12);
  const iconSize = scaleSize(24);
  const textSize = scaleSize(14);
  const inputHeight = scaleSize(40);
  const gap = scaleSize(20);
  const sectionPadding = scaleSize(16);
  const switchPadding = scaleSize(10);
  const switchSize = scaleSize(1);

  const styles = StyleSheet.create({
    backdrop: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "rgba(0,0,0,0.8)",
    },
    modalView: {
      minWidth: "80%",
      maxWidth: "90%",
      backgroundColor: theme.colors.screenBackground,
      borderRadius: borderRadius,
      padding: padding,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: scaleSize(2),
      },
      shadowOpacity: 0.25,
      shadowRadius: scaleSize(4),
      elevation: 5,
      gap: gap,
    },
    inputGroup: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    modalText: {
      fontSize: textSize,
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
      flex: 1,
    },
    textInput: {
      flex: 1 / 2,
      height: inputHeight,
      borderWidth: 1,
      borderRadius: scaleSize(8),
      color: theme.colors.primary,
      padding: scaleSize(10),
      textAlign: "center",
      backgroundColor: theme.colors.screenBackground,
      borderColor: theme.colors.border,
      fontSize: textSize,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      padding: scaleSize(10),
      marginTop: scaleSize(20),
      alignSelf: "flex-end",
    },
    infoText: {
      fontSize: textSize,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      paddingLeft: scaleSize(10),
      paddingBottom: scaleSize(10),
    },
    toggleButton: {
      backgroundColor: theme.colors.secondary,
      borderRadius: scaleSize(5),
      alignItems: "center",
      justifyContent: "center",
    },
    headerRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    section: {
      backgroundColor: theme.colors.surface,
      alignItems: "center",
      paddingHorizontal: scaleSize(22),
      paddingVertical: scaleSize(14),
      gap: scaleSize(12),
      borderRadius: borderRadius,
    },
    unitToggleContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end", // Align content to the right
      padding: scaleSize(10),
      width: "100%", // Take full width of parent
    },
    unitText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(16),
      minWidth: scaleSize(100), // Set minimum width to accommodate both texts
      textAlign: "right", // Right-align text for consistent positioning
      marginRight: scaleSize(10), // Add spacing between text and toggle
    },
    caloriesText: {
      color: theme.colors.primary,
      fontSize: textSize,
      fontWeight: "600",
    },
    imageContainer: {
      width: "100%",
      borderRadius: borderRadius,
      overflow: "hidden",
    },
    image: {
      width: "100%",
      height: "100%",
    },
  });

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={closeModal}
    >
      {/* Backdrop that closes modal on press */}
      <TouchableWithoutFeedback onPress={closeModal}>
        <View style={styles.backdrop}>
          {/* Prevent modal from closing when pressing content */}
          <TouchableWithoutFeedback>
            <KeyboardAvoidingView
              behavior={Platform.OS === "ios" ? "padding" : "height"}
              style={styles.keyboardAvoid}
            >
              <Animated.View
                style={[
                  styles.modalView,
                  {
                    transform: [{ translateY: modalPositionAnim }],
                  },
                ]}
              >
                <View style={styles.headerRow}>
                  <TouchableOpacity onPress={closeModal}>
                    <Feather
                      name="chevron-left"
                      color={theme.colors.primaryTextColor}
                      size={iconSize}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      commitChanges();
                      closeModal();
                    }}
                  >
                    <Feather
                      name="check-circle"
                      color={theme.colors.primary}
                      size={iconSize}
                    />
                  </TouchableOpacity>
                </View>

                <View style={{ alignItems: "center" }}>
                  <Text style={styles.infoText}>
                    Set a daily step and distance goal tailored to enhance your
                    well-being and endurance, without overextending yourself.
                  </Text>
                  <Animated.View
                    style={[styles.imageContainer, { height: imageHeightAnim }]}
                  >
                    <Image
                      contentFit="contain"
                      style={styles.image}
                      source={require("../../assets/steps.png")}
                    />
                  </Animated.View>
                </View>

                <View style={styles.section}>
                  <View style={styles.inputGroup}>
                    <Text style={styles.modalText}>Step Goal:</Text>
                    <TextInput
                      style={styles.textInput}
                      keyboardType="numeric"
                      value={localStepGoal}
                      onChangeText={handleStepChange}
                    />
                  </View>
                  <View style={{ width: "100%", gap: scaleSize(5) }}>
                    <Text style={styles.modalText}>
                      Estimated Daily Calories You Would Burn
                    </Text>
                    <Text style={styles.caloriesText}>
                      {calculateCaloriesBurned(
                        parseInt(localStepGoal.replace(/,/g, ""), 10)
                      ).toFixed(1)}
                    </Text>
                  </View>
                </View>

                <View style={styles.section}>
                  <View style={styles.inputGroup}>
                    <Text style={styles.modalText}>
                      Distance Goal ({localDistanceUnit}):
                    </Text>
                    <TextInput
                      style={styles.textInput}
                      keyboardType="numeric"
                      value={localDistanceGoal}
                      onChangeText={handleDistanceChange}
                    />
                  </View>

                  <View style={styles.unitToggleContainer}>
                    <Text style={styles.unitText}>
                      {localDistanceUnit === "mi" ? "Miles" : "Kilometers"}
                    </Text>
                    <Switch
                      trackColor={{ false: "#767577", true: "#81b0ff" }}
                      thumbColor={
                        localDistanceUnit === "mi" ? "#f5dd4b" : "#f4f3f4"
                      }
                      ios_backgroundColor="#3e3e3e"
                      onValueChange={handleUnitChange}
                      value={localDistanceUnit === "mi"}
                      style={{
                        transform: [
                          { scaleX: switchSize },
                          { scaleY: switchSize },
                        ],
                      }}
                    />
                  </View>
                </View>
              </Animated.View>
            </KeyboardAvoidingView>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default StepsAndDistanceGoalModal;
