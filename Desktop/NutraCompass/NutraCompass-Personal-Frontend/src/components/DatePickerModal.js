import React, { useState } from "react";
import { Modal, View, Button, StyleSheet, Text, Platform } from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useThemeContext } from "../context/ThemeContext.js";
import { useTime } from "../context/TimeContext.js";
import { scaleSize, isTablet } from "../utils/deviceUtils.js";

const DatePickerModal = ({ isVisible, onClose }) => {
  const tablet = isTablet();
  const { theme } = useThemeContext();
  const { getSelectedDateAsDate, updateSelectedDate } = useTime();
  const [temporaryDate, setTemporaryDate] = useState(getSelectedDateAsDate());
  const [showAndroidPicker, setShowAndroidPicker] = useState(false);

  // Initialize date when modal becomes visible
  React.useEffect(() => {
    if (isVisible && Platform.OS === "android") {
      setShowAndroidPicker(true);
    }
  }, [isVisible]);

  const handleDateChange = (event, selectedDate) => {
    if (Platform.OS === "android") {
      // Android specific handling
      if (event.type === "set") {
        setTemporaryDate(selectedDate);
        updateSelectedDate(selectedDate);
      }
      setShowAndroidPicker(false);
      onClose();
    } else {
      // iOS handling
      setTemporaryDate(selectedDate);
    }
  };

  const handleConfirm = () => {
    updateSelectedDate(temporaryDate);
    onClose();
  };

  const handleCancel = () => {
    setTemporaryDate(getSelectedDateAsDate());
    onClose();
  };

  // Render Android picker separately
  if (Platform.OS === "android" && showAndroidPicker) {
    return (
      <DateTimePicker
        value={temporaryDate}
        mode="date"
        display="calendar"
        onChange={handleDateChange}
        themeVariant={theme.dark ? "dark" : "light"}
        accentColor={theme.colors.primary}
      />
    );
  }

  const styles = StyleSheet.create({
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
    },
    modalView: {
      margin: scaleSize(20),
      backgroundColor: theme.colors.surface,
      borderRadius: scaleSize(20),
      padding: scaleSize(35),
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: scaleSize(2),
      },
      shadowOpacity: 0.25,
      shadowRadius: scaleSize(4),
      elevation: scaleSize(5),
      width: tablet ? "50%" : "85%", // Responsive width
      maxWidth: scaleSize(500), // Max width for tablets
    },
    buttonContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginTop: scaleSize(20),
      width: "100%",
    },
  });

  // iOS implementation remains unchanged
  return Platform.OS === "ios" ? (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontSize: scaleSize(18),
              fontWeight: "600",
              marginBottom: scaleSize(10),
            }}
          >
            Select A Date
          </Text>
          {isVisible && (
            <DateTimePicker
              value={temporaryDate}
              mode="date"
              display="spinner"
              onChange={handleDateChange}
              textColor={theme.colors.primaryTextColor}
              style={{
                width: "100%",
                height: scaleSize(200),
              }}
            />
          )}
          <View style={styles.buttonContainer}>
            <Button title="Cancel" onPress={handleCancel} color="red" />
            <Button title="Confirm" onPress={handleConfirm} color="green" />
          </View>
        </View>
      </View>
    </Modal>
  ) : null;
};

export default DatePickerModal;
