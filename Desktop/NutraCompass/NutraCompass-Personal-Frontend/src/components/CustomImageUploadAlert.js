// CustomImageUploadAlert.js
import React from "react";
import { Modal, View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useThemeContext } from "../context/ThemeContext.js";
import { scaleSize } from "../utils/deviceUtils.js";
export const CustomImageUploadAlert = ({
  isVisible,
  onClose,
  onPickImage,
  onTakePhoto,
  onRemoveImage,
  hasImage, // New prop
}) => {
  const { theme } = useThemeContext();

  const styles = StyleSheet.create({
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.6)",
    },
    modalView: {
      width: scaleSize(200),
      backgroundColor: theme.colors.surface,
      borderRadius: scaleSize(12),
      padding: scaleSize(20),
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.3,
      shadowRadius: 6,
      elevation: 10,
    },
    button: {
      width: "100%",
      paddingVertical: scaleSize(12),
      borderRadius: scaleSize(8),
      backgroundColor: theme.colors.primary,
      alignItems: "center",
      marginVertical: scaleSize(6),
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    removeButton: {
      backgroundColor: theme.colors.screenBackground,
      borderColor: theme.colors.primary,
      borderWidth: scaleSize(1),
    },
    cancelButton: {
      backgroundColor: theme.colors.screenBackground,
      borderColor: theme.colors.primary,
      borderWidth: scaleSize(1),
    },
    buttonText: {
      color: theme.colors.primaryTextColor,
      fontWeight: "600",
      fontSize: scaleSize(16),
    },
    modalText: {
      fontSize: scaleSize(18),
      fontWeight: "600",
      marginBottom: scaleSize(15),
      color: theme.colors.primaryTextColor,
    },
  });

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text style={styles.modalText}>Upload Image</Text>

          <TouchableOpacity style={styles.button} onPress={onPickImage}>
            <Text style={styles.buttonText}>Gallery</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={onTakePhoto}>
            <Text style={styles.buttonText}>Camera</Text>
          </TouchableOpacity>

          {/* Conditionally render the Remove button only if hasImage is true */}
          {hasImage && (
            <TouchableOpacity
              style={[styles.button, styles.removeButton]}
              onPress={onRemoveImage}
            >
              <Text style={styles.buttonText}>Remove</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onClose}
          >
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
