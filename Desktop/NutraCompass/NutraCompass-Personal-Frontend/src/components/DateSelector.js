import React, { useState, useMemo } from "react";
import { Text, View, TouchableOpacity, Modal } from "react-native";
import * as Haptics from "expo-haptics";
import { Feather, Entypo, FontAwesome, Ionicons } from "@expo/vector-icons";
import { Calendar } from "react-native-calendars";
import { LinearGradient } from "expo-linear-gradient";
import { useThemeContext } from "../context/ThemeContext.js";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";
import { useTime } from "../context/TimeContext.js";
import { scaleSize } from "../utils/deviceUtils.js";

export default function DateSelector() {
  const { selectedDate, updateSelectedDate, getSelectedDateAsDate } = useTime();
  const { mealSections, foodEntries } = useFoodLog();
  const { theme } = useThemeContext();

  const [isCalendarModalVisible, setIsCalendarModalVisible] = useState(false);
  const [temporarySelectedDate, setTemporarySelectedDate] =
    useState(selectedDate);
  const [isInfoVisible, setIsInfoVisible] = useState(true);
  const [sevenDayRange, setSevenDayRange] = useState(
    getInitial7Days(selectedDate)
  );

  const handleCalendarToggle = () => {
    setIsCalendarModalVisible(!isCalendarModalVisible);
  };

  const handleConfirmDate = () => {
    updateSelectedDate(temporarySelectedDate);
    update7DayRangeIfNeeded(temporarySelectedDate);
    setIsCalendarModalVisible(false);
  };

  const handlePrevDay = () => {
    const newDate = subtractDay(selectedDate);
    update7DayRangeIfNeeded(newDate); // Check if we need to update the 7-day range
    updateSelectedDate(newDate);
  };

  const handleNextDay = () => {
    const newDate = addDay(selectedDate);
    update7DayRangeIfNeeded(newDate); // Check if we need to update the 7-day range
    updateSelectedDate(newDate);
  };

  const handleDateSelection = (date) => {
    setTemporarySelectedDate(date.dateString);
  };

  const handleCancel = () => {
    setIsCalendarModalVisible(false);
  };

  const getCurrentDate = () => {
    const date = getSelectedDateAsDate();
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const addDay = (date) => {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + 1);
    return newDate.toISOString().split("T")[0];
  };

  const subtractDay = (date) => {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() - 1);
    return newDate.toISOString().split("T")[0];
  };

  // Function to load the initial 7-day range based on selected date
  function getInitial7Days(date) {
    const days = [];
    let currentDate = new Date(date);
    currentDate.setHours(0, 0, 0, 0);

    // Load initial 7-day range
    for (let i = -2; i <= 4; i++) {
      const newDate = new Date(currentDate);
      newDate.setDate(currentDate.getDate() + i);
      newDate.setHours(0, 0, 0, 0);

      days.push({
        date: newDate,
        dateString: newDate.toISOString().split("T")[0],
      });
    }

    return days;
  }

  // Function to update the 7-day range if the selected date goes outside the current range
  const update7DayRangeIfNeeded = (newSelectedDate) => {
    const firstDay = sevenDayRange[0].dateString;
    const lastDay = sevenDayRange[6].dateString;

    // Check if the new selected date is outside the current 7-day range
    if (newSelectedDate < firstDay || newSelectedDate > lastDay) {
      // Update the 7-day range to center around the new selected date
      setSevenDayRange(getInitial7Days(newSelectedDate));
    }
  };

  const completionStatus = useMemo(() => {
    const status = {};
    const visibleMealSections = mealSections.filter((section) => section.name);

    // Iterate through all dates
    Object.entries(foodEntries).forEach(([date, meals]) => {
      // Initialize date status
      if (!status[date]) {
        status[date] = {
          total: visibleMealSections.length,
          count: 0,
          completedSections: new Set(),
        };
      }

      // Iterate through meal types for this date
      Object.entries(meals).forEach(([mealType, entries]) => {
        // Skip if no entries or entries not an array
        if (!Array.isArray(entries)) return;

        // Check if this meal type has at least one food entry
        const hasFoodEntries = entries.some(
          (entry) => entry.type !== "meal-header" // Exclude headers
        );

        // Only count if it's a visible meal section and has food entries
        if (
          hasFoodEntries &&
          visibleMealSections.some((section) => section.id === mealType)
        ) {
          status[date].completedSections.add(mealType);
        }
      });

      // Update count based on completed sections
      status[date].count = status[date].completedSections.size;
    });

    const markedDates = {};
    Object.entries(status).forEach(([date, { total, count }]) => {
      const completionRatio = total > 0 ? count / total : 0;

      markedDates[date] = {
        customStyles: {
          container: {
            backgroundColor:
              completionRatio === 1
                ? "green"
                : completionRatio >= 0.5
                ? "orange"
                : "transparent",
            borderRadius: scaleSize(20),
          },
        },
      };
    });

    return markedDates;
  }, [mealSections, foodEntries]);

  const realTodayDate = new Date().toISOString().split("T")[0];

  // Helper function to adjust alpha of RGBA color
  const adjustAlpha = (rgbaColor, alpha) => {
    // Extract numbers from "rgba(76, 175, 80, 1)" format
    const match = rgbaColor.match(/[\d.]+/g);

    if (!match || match.length < 4) {
      // Return original color if format is invalid
      console.warn(`Invalid RGBA color format: ${rgbaColor}`);
      return rgbaColor;
    }

    const r = match[0];
    const g = match[1];
    const b = match[2];

    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  };

  // Memoize semi-transparent colors based on theme
  const semiTransparentColors = useMemo(() => {
    const alpha = 0.4; // 50% transparency
    return {
      primary: adjustAlpha(theme.colors.primary, alpha),
      secondary: adjustAlpha(theme.colors.secondary, alpha),
    };
  }, [theme.colors.primary, theme.colors.secondary]);
  return (
    <View>
      <View
        style={{
          backgroundColor: theme.colors.surface,
          borderRadius: scaleSize(10),
          paddingVertical: scaleSize(4),
          paddingHorizontal: scaleSize(4),
        }}
      >
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <TouchableOpacity
            style={{
              borderRadius: scaleSize(30),
              padding: scaleSize(8),
              flexDirection: "row",
              alignItems: "center",
            }}
            onPress={() => {
              handlePrevDay();
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            }}
          >
            <Feather
              name="chevron-left"
              size={scaleSize(18)}
              color={theme.colors.primaryTextColor}
            />
            <FontAwesome
              name="calendar-check-o"
              size={scaleSize(18)}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
          <View
            style={{
              padding: scaleSize(8),
              borderRadius: scaleSize(16),
            }}
          >
            <TouchableOpacity onPress={handleCalendarToggle}>
              <Text
                style={{
                  paddingLeft: scaleSize(10),
                  fontSize: scaleSize(14),
                  fontWeight: "bold",
                  color: theme.colors.primaryTextColor,
                }}
              >
                {getCurrentDate(selectedDate)}
              </Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={{
              borderRadius: scaleSize(30),
              padding: scaleSize(8),
              flexDirection: "row",
              alignItems: "center",
            }}
            onPress={() => {
              handleNextDay();
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            }}
          >
            <FontAwesome
              name="calendar-o"
              size={scaleSize(18)}
              color={theme.colors.primaryTextColor}
            />
            <Feather
              name="chevron-right"
              size={scaleSize(18)}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Visual-only 7-day bar */}
      <View
        style={{
          alignItems: "center",
          paddingHorizontal: scaleSize(8),
          paddingTop: scaleSize(4),
          flexDirection: "row",
          justifyContent: "space-evenly",
        }}
      >
        {sevenDayRange.map((day) => {
          const isSelectedDate = day.dateString === selectedDate;
          return (
            <View
              key={day.dateString}
              style={[
                {
                  borderRadius: scaleSize(60),
                  height: scaleSize(30),
                  width: scaleSize(30),
                  justifyContent: "center",
                  alignItems: "center",
                },
              ]}
            >
              {isSelectedDate ? (
                <LinearGradient
                  colors={[
                    semiTransparentColors.primary,
                    semiTransparentColors.secondary,
                  ]}
                  style={{
                    borderRadius: scaleSize(60),
                    height: scaleSize(30),
                    width: scaleSize(30),
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text
                    style={{
                      fontSize: scaleSize(12),
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    {day.date.getDate()}
                  </Text>
                </LinearGradient>
              ) : (
                <Text
                  style={{
                    fontSize: scaleSize(12),
                    color: theme.colors.primaryTextColor,
                  }}
                >
                  {day.date.getDate()}
                </Text>
              )}
            </View>
          );
        })}
      </View>

      <Modal
        visible={isCalendarModalVisible}
        animationType="slide"
        transparent={true}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0,0,0,1)",
          }}
        >
          <View
            style={{
              width: "95%",
              minHeight: "85%",
              justifyContent: "space-between",
              backgroundColor: theme.colors.screenBackground,
            }}
          >
            <View>
              <Calendar
                style={{
                  minHeight: "65%",
                  height: "auto",
                  width: "100%",
                  backgroundColor: theme.colors.surface,
                }}
                current={selectedDate.toString()}
                markedDates={completionStatus}
                markingType="custom"
                hideExtraDays
                renderArrow={(direction) => (
                  <Ionicons
                    name={
                      direction === "left" ? "chevron-back" : "chevron-forward"
                    }
                    size={scaleSize(24)} // Increased arrow size
                    color={theme.colors.primaryTextColor}
                  />
                )}
                theme={{
                  calendarBackground: theme.colors.surface,
                  selectedDayBackgroundColor: theme.colors.primary,
                  selectedDayTextColor: theme.colors.primaryTextColor,
                  todayTextColor: theme.colors.primaryTextColor,
                  "stylesheet.calendar.header": {
                    header: {
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      paddingLeft: scaleSize(10),
                      paddingRight: scaleSize(10),
                      marginTop: scaleSize(6),
                      marginBottom: scaleSize(6),
                    },
                    monthText: {
                      color: theme.colors.primaryTextColor,
                      fontSize: scaleSize(18),
                      fontWeight: "bold",
                      margin: scaleSize(10),
                    },
                    dayHeader: {
                      color: theme.colors.primaryTextColor,
                      fontSize: scaleSize(14),
                      flex: 1,
                      textAlign: "center",
                    },
                    arrow: {
                      padding: scaleSize(15), // Increased touch area
                    },
                    arrowImage: {
                      tintColor: theme.colors.primaryTextColor,
                      width: scaleSize(24), // Ensure width matches icon size
                      height: scaleSize(24), // Ensure height matches icon size
                    },
                  },
                }}
                dayComponent={({ date, state }) => {
                  const isSelectedDate =
                    date.dateString === temporarySelectedDate;
                  const isToday = date.dateString === realTodayDate;
                  const dayCompletion = completionStatus[date.dateString];

                  return (
                    <TouchableOpacity
                      style={{
                        height: scaleSize(36),
                        width: scaleSize(36),
                        marginVertical: scaleSize(2),
                      }}
                      onPress={() =>
                        handleDateSelection({ dateString: date.dateString })
                      }
                    >
                      <View
                        style={{
                          flex: 1,
                          alignItems: "center",
                          justifyContent: "center",
                          backgroundColor: isToday
                            ? theme.colors.primary
                            : "transparent",
                          borderWidth: scaleSize(2),
                          borderRadius: scaleSize(22.5),
                          borderColor: isSelectedDate
                            ? theme.colors.secondary
                            : isToday
                            ? theme.colors.primary
                            : "transparent",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: scaleSize(14),
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          {date.day}
                        </Text>
                        {dayCompletion && (
                          <Entypo
                            name="check"
                            size={scaleSize(16)}
                            color={
                              dayCompletion.customStyles.container
                                .backgroundColor
                            }
                            style={{
                              position: "absolute",
                              top: scaleSize(-14),
                              right: scaleSize(-10),
                            }}
                          />
                        )}
                      </View>
                    </TouchableOpacity>
                  );
                }}
              />
            </View>

            {isInfoVisible && (
              <View
                style={{
                  position: "absolute",
                  bottom: scaleSize(50),
                  left: scaleSize(40),
                  padding: scaleSize(10),
                  gap: scaleSize(10),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(10),
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(1) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(1.5),
                  elevation: scaleSize(4),
                  alignItems: "flex-start",
                }}
              >
                <Text
                  style={{
                    fontSize: scaleSize(16),
                    fontWeight: "bold",
                    color: theme.colors.primaryTextColor,
                  }}
                >
                  Legend:
                </Text>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Entypo
                    name="check"
                    size={scaleSize(16)}
                    color="green"
                    style={{ height: scaleSize(20), width: scaleSize(20) }}
                  />
                  <Text
                    style={{
                      marginLeft: scaleSize(10),
                      fontSize: scaleSize(14),
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - All entries completed (100%)
                  </Text>
                </View>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Entypo
                    name="check"
                    size={scaleSize(16)}
                    color="orange"
                    style={{ height: scaleSize(20), width: scaleSize(20) }}
                  />
                  <Text
                    style={{
                      marginLeft: scaleSize(10),
                      fontSize: scaleSize(14),
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - Partially completed (More than 50%)
                  </Text>
                </View>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={{
                      height: scaleSize(20),
                      width: scaleSize(20),
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: theme.colors.primary,
                      borderWidth: scaleSize(2),
                      borderRadius: scaleSize(22.5),
                      borderColor: theme.colors.primary,
                    }}
                  />
                  <Text
                    style={{
                      marginLeft: scaleSize(10),
                      fontSize: scaleSize(14),
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - Today's Date (
                    {new Date().toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    })}
                    )
                  </Text>
                </View>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={{
                      height: scaleSize(20),
                      width: scaleSize(20),
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "transparent",
                      borderWidth: scaleSize(2),
                      borderRadius: scaleSize(22.5),
                      borderColor: theme.colors.secondary,
                    }}
                  />
                  <Text
                    style={{
                      marginLeft: scaleSize(10),
                      fontSize: scaleSize(14),
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    - Selected Date
                  </Text>
                </View>
              </View>
            )}

            <View
              style={{
                flexDirection: "row",
                backgroundColor: theme.colors.cardBackgroundColor,
                justifyContent: "flex-end",
              }}
            >
              <TouchableOpacity
                onPress={() => setIsInfoVisible(!isInfoVisible)}
                style={{
                  position: "absolute",
                  left: 0,
                  color: theme.colors.primaryTextColor,
                  textAlign: "center",
                  padding: scaleSize(15),
                }}
              >
                <Entypo
                  name="info-with-circle"
                  size={scaleSize(18)}
                  color={theme.colors.primaryTextColor}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={{ padding: scaleSize(15) }}
                onPress={() => handleCancel()}
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    textAlign: "center",
                    fontSize: scaleSize(14),
                  }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{ padding: scaleSize(15) }}
                onPress={() => handleConfirmDate()}
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    textAlign: "center",
                    fontSize: scaleSize(14),
                  }}
                >
                  OK
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
