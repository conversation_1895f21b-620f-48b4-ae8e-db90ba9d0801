import React, { useState, useEffect } from "react";
import {
  Text,
  View,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { <PERSON>, Button } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import measurementSettingsStyles from "./styles/measurementSettingsStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../context/UserSettingsContext.js";
import CustomPickerModal from "../../../components/CustomPickerModal.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const MeasurementSettings = ({ navigation }) => {
  const styles = measurementSettingsStyles();
  const { theme } = useThemeContext();
  const { getUserMeasurementSettings, setUserMeasurementSettings } =
    useUserSettings();

  const [settings, setSettings] = useState({
    waterUnit: "fl oz", // Default: Fluid Ounces
    distanceUnit: "mi", // Default: Miles
    bodyWeightUnit: "lbs", // Default: Pounds
    // foodUnit: "g", // Default: Grams
  });
  const [originalSettings, setOriginalSettings] = useState({});
  const [isPickerVisible, setIsPickerVisible] = useState(false);
  const [currentSetting, setCurrentSetting] = useState(null);
  const [pickerOptions, setPickerOptions] = useState([]);
  const [pickerTitle, setPickerTitle] = useState("");

  // Define available options for each setting
  const measurementOptions = {
    waterUnit: [
      { label: "Fluid Ounces (fl oz)", value: "fl oz" },
      { label: "Milliliters (mL)", value: "mL" },
    ],
    distanceUnit: [
      { label: "Miles (mi)", value: "mi" },
      { label: "Kilometers (km)", value: "km" },
    ],
    bodyWeightUnit: [
      { label: "Pounds (lbs)", value: "lbs" },
      { label: "Kilograms (kg)", value: "kg" },
    ],
    // foodUnit: [
    //   { label: "Grams (g)", value: "g" },
    //   { label: "Ounces (oz)", value: "oz" },
    //   { label: "Cups", value: "cups" },
    //   { label: "Milliliters (mL)", value: "mL" },
    //   { label: "Fluid Ounces (fl oz)", value: "fl oz" },
    // ],
  };

  useEffect(() => {
    const loadSettings = async () => {
      const userSettings = await getUserMeasurementSettings();
      if (userSettings) {
        setSettings(userSettings);
        setOriginalSettings(userSettings);
      }
    };

    loadSettings();
  }, []);

  const handleSave = async () => {
    await setUserMeasurementSettings(settings);
    setOriginalSettings(settings);
  };

  const showPicker = (settingKey) => {
    setCurrentSetting(settingKey);
    setPickerOptions(measurementOptions[settingKey]);
    setPickerTitle(getSettingLabel(settingKey));
    setIsPickerVisible(true);
  };

  const handleSelectOption = (value) => {
    if (currentSetting && value) {
      setSettings((prev) => ({ ...prev, [currentSetting]: value }));
    }
    setIsPickerVisible(false);
  };

  const getSettingLabel = (key) => {
    const labels = {
      waterUnit: "Water Measurement",
      distanceUnit: "Distance Units",
      bodyWeightUnit: "Body Weight Units",
      // foodUnit: "Food Measurement",
    };
    return labels[key] || "Select Unit";
  };

  const getSettingValueLabel = (value) => {
    const option = Object.values(measurementOptions)
      .flat()
      .find((opt) => opt.value === value);
    return option ? option.label : "Select";
  };

  const hasChanges =
    JSON.stringify(originalSettings) !== JSON.stringify(settings);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons
            name="chevron-left"
            size={scaleSize(28)}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Measurement Units</Text>
      </View>

      <ScrollView contentContainerStyle={{ paddingBottom: scaleSize(50) }}>
        <Card style={styles.card}>
          <Text style={styles.sectionHeader}>Measurement Preferences</Text>

          {/* Water Measurement */}
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => showPicker("waterUnit")}
          >
            <Text style={styles.label}>Water Measurement:</Text>
            <View style={styles.valueContainer}>
              <Text style={styles.valueText}>
                {getSettingValueLabel(settings.waterUnit)}
              </Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={scaleSize(20)}
                color={theme.colors.subTextColor}
              />
            </View>
          </TouchableOpacity>

          {/* Distance Units */}
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => showPicker("distanceUnit")}
          >
            <Text style={styles.label}>Distance Units:</Text>
            <View style={styles.valueContainer}>
              <Text style={styles.valueText}>
                {getSettingValueLabel(settings.distanceUnit)}
              </Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={scaleSize(20)}
                color={theme.colors.subTextColor}
              />
            </View>
          </TouchableOpacity>

          {/* Body Weight Units */}
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => showPicker("bodyWeightUnit")}
          >
            <Text style={styles.label}>Body Weight Units:</Text>
            <View style={styles.valueContainer}>
              <Text style={styles.valueText}>
                {getSettingValueLabel(settings.bodyWeightUnit)}
              </Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={scaleSize(20)}
                color={theme.colors.subTextColor}
              />
            </View>
          </TouchableOpacity>

          {/* Food Measurement */}
          {/* <TouchableOpacity
            style={styles.settingItem}
            onPress={() => showPicker("foodUnit")}
          >
            <Text style={styles.label}>Food Measurement:</Text>
            <View style={styles.valueContainer}>
              <Text style={styles.valueText}>
                {getSettingValueLabel(settings.foodUnit)}
              </Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={scaleSize(20)}
                color={theme.colors.subTextColor}
              />
            </View>
          </TouchableOpacity> */}

          <Button
            onPress={handleSave}
            textColor={theme.colors.primary}
            mode="elevated"
            disabled={!hasChanges}
            style={styles.saveButton}
          >
            Save Changes
          </Button>
        </Card>
      </ScrollView>

      {/* Unit Picker Modal */}
      <CustomPickerModal
        title={pickerTitle}
        options={pickerOptions}
        visible={isPickerVisible}
        onClose={() => setIsPickerVisible(false)}
        onSelect={handleSelectOption}
      />
    </SafeAreaView>
  );
};

export default MeasurementSettings;
