import React, { useState, useEffect, useMemo, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Button, Snackbar } from "react-native-paper";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../context/UserSettingsContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const NutritionSettings = ({ navigation }) => {
  const { theme } = useThemeContext();
  const styles = nutritionSettingsStyles(theme);
  const { getNutritionalGoals, setCalorieAndMacroGoals } = useUserSettings();
  const goals = getNutritionalGoals(); // Assuming this is async

  // State management
  const [calorieSettings, setCalorieSettings] = useState({
    total: "",
    proteinPercent: "",
    carbsPercent: "",
    fatPercent: "",
  });

  const [macroSettings, setMacroSettings] = useState({
    proteinGrams: "",
    carbsGrams: "",
    fatGrams: "",
  });

  const [mode, setMode] = useState("calories");
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");

  // Initial values references
  const initialCalorieRef = useRef({ ...calorieSettings });
  const initialMacroRef = useRef({ ...macroSettings });

  // Reusable function to load goals
  const loadGoals = async () => {
    try {
      const calorieGoalVal = goals?.calorieGoal ?? 0;
      const proteinPctVal = ((goals?.macroGoals?.protein?.dailyPercentage ?? 0) * 100).toFixed(1);
      const carbsPctVal = ((goals?.macroGoals?.carb?.dailyPercentage ?? 0) * 100).toFixed(1);
      const fatPctVal = ((goals?.macroGoals?.fat?.dailyPercentage ?? 0) * 100).toFixed(1);

      const proteinGramsVal = goals?.macroGoals?.protein?.dailyGrams ?? 0;
      const carbsGramsVal = goals?.macroGoals?.carb?.dailyGrams ?? 0;
      const fatGramsVal = goals?.macroGoals?.fat?.dailyGrams ?? 0;

      const newCalorieSettings = {
        total: String(calorieGoalVal),
        proteinPercent: String(proteinPctVal),
        carbsPercent: String(carbsPctVal),
        fatPercent: String(fatPctVal),
      };

      const newMacroSettings = {
        proteinGrams: String(proteinGramsVal),
        carbsGrams: String(carbsGramsVal),
        fatGrams: String(fatGramsVal),
      };

      initialCalorieRef.current = newCalorieSettings;
      initialMacroRef.current = newMacroSettings;
      setCalorieSettings(newCalorieSettings);
      setMacroSettings(newMacroSettings);
    } catch (error) {
      console.error("Failed to load goals:", error);
    }
  };

  // Load goals on screen focus and initial mount
  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", loadGoals);
    loadGoals(); // Initial load
    return unsubscribe;
  }, []);

  useEffect(() => {
    loadGoals(); // load new nutritional goals.
  }, [goals]);

  // Input handlers
  const handleCalorieChange = (field, value) => {
    // Allow numbers and single decimal point
    let sanitized = value
      .replace(/[^0-9.]/g, "") // Allow digits and dots
      .replace(/(\..*)\./g, "$1"); // Remove multiple dots

    // Handle leading dot
    if (sanitized.startsWith(".")) {
      sanitized = "0" + sanitized;
    }

    // Split into whole and decimal parts
    const parts = sanitized.split(".");
    if (parts.length > 1) {
      // Limit to 1 decimal place
      sanitized = `${parts[0]}.${parts[1].slice(0, 1)}`;
    }

    setCalorieSettings((prev) => ({
      ...prev,
      [field]: sanitized,
    }));
  };

  const handleMacroChange = (field, value) => {
    setMacroSettings((prev) => ({
      ...prev,
      [field]: value.replace(/[^0-9]/g, ""),
    }));
  };

  // Change detection
  const hasChanges = useMemo(() => {
    return mode === "calories"
      ? JSON.stringify(calorieSettings) !==
          JSON.stringify(initialCalorieRef.current)
      : JSON.stringify(macroSettings) !==
          JSON.stringify(initialMacroRef.current);
  }, [calorieSettings, macroSettings, mode]);

  // Save handler
  const handleSave = () => {
    Keyboard.dismiss();

    if (mode === "calories") {
      const newCalories = Number(calorieSettings.total);
      setCalorieAndMacroGoals({
        calorieGoal: newCalories,
        proteinPercentage: Number(calorieSettings.proteinPercent),
        carbPercentage: Number(calorieSettings.carbsPercent),
        fatPercentage: Number(calorieSettings.fatPercent),
      });
      initialCalorieRef.current = { ...calorieSettings };
    } else {
      const totalCal =
        Number(macroSettings.proteinGrams) * 4 +
        Number(macroSettings.carbsGrams) * 4 +
        Number(macroSettings.fatGrams) * 9;

      setCalorieAndMacroGoals({
        calorieGoal: totalCal,
        proteinPercentage:
          (Number(macroSettings.proteinGrams) * 4 * 100) / totalCal,
        carbPercentage: (Number(macroSettings.carbsGrams) * 4 * 100) / totalCal,
        fatPercentage: (Number(macroSettings.fatGrams) * 9 * 100) / totalCal,
      });
      initialMacroRef.current = { ...macroSettings };
    }

    setSnackbarMessage("Settings saved successfully!");
    setSnackbarVisible(true);
  };

  // Calculated values
  const calculatedMacros = useMemo(
    () => ({
      protein: Math.round(
        (Number(calorieSettings.total) *
          Number(calorieSettings.proteinPercent)) /
          400
      ),
      carbs: Math.round(
        (Number(calorieSettings.total) * Number(calorieSettings.carbsPercent)) /
          400
      ),
      fat: Math.round(
        (Number(calorieSettings.total) * Number(calorieSettings.fatPercent)) /
          900
      ),
    }),
    [calorieSettings]
  );

  const calculatedPercentages = useMemo(() => {
    const totalCal =
      Number(macroSettings.proteinGrams) * 4 +
      Number(macroSettings.carbsGrams) * 4 +
      Number(macroSettings.fatGrams) * 9;

    return {
      totalCal,
      protein: totalCal
        ? (Number(macroSettings.proteinGrams) * 4 * 100) / totalCal
        : 0,
      carbs: totalCal
        ? (Number(macroSettings.carbsGrams) * 4 * 100) / totalCal
        : 0,
      fat: totalCal ? (Number(macroSettings.fatGrams) * 9 * 100) / totalCal : 0,
    };
  }, [macroSettings]);

  // Validation
  const totalPercentage = useMemo(
    () =>
      Number(calorieSettings.proteinPercent) +
      Number(calorieSettings.carbsPercent) +
      Number(calorieSettings.fatPercent),
    [calorieSettings]
  );

  const percentageError = useMemo(() => {
    if (mode !== "calories") return false;

    // Allow 0.1% tolerance for floating point precision
    return Math.abs(Math.round(totalPercentage) - 100) > 0.0001;
  }, [calorieSettings, mode]);

  return (
    <TouchableWithoutFeedback
      onPress={() => Keyboard.dismiss()}
      accessible={false}
    >
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialCommunityIcons
              name="chevron-left"
              size={scaleSize(28)}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Nutrition Settings</Text>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.modeToggleContainer}>
            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === "calories" && styles.activeMode,
              ]}
              onPress={() => setMode("calories")}
            >
              <Text style={styles.modeText}>Calorie Mode</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === "macros" && styles.activeMode,
              ]}
              onPress={() => setMode("macros")}
            >
              <Text style={styles.modeText}>Macro Mode</Text>
            </TouchableOpacity>
          </View>

          {mode === "calories" ? (
            <>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Daily Calories</Text>
                <TextInput
                  style={styles.input}
                  value={calorieSettings.total}
                  onChangeText={(v) => handleCalorieChange("total", v)}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Protein (%)</Text>
                <TextInput
                  style={styles.input}
                  value={calorieSettings.proteinPercent}
                  onChangeText={(v) => handleCalorieChange("proteinPercent", v)}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Carbs (%)</Text>
                <TextInput
                  style={styles.input}
                  value={calorieSettings.carbsPercent}
                  onChangeText={(v) => handleCalorieChange("carbsPercent", v)}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Fat (%)</Text>
                <TextInput
                  style={styles.input}
                  value={calorieSettings.fatPercent}
                  onChangeText={(v) => handleCalorieChange("fatPercent", v)}
                  keyboardType="numeric"
                />
              </View>

              {percentageError && (
                <Text style={styles.errorText}>
                  Total must equal 100% (Current: {totalPercentage}%)
                </Text>
              )}

              <View style={styles.resultsContainer}>
                <Text style={styles.resultText}>
                  Protein: {calculatedMacros.protein}g
                </Text>
                <Text style={styles.resultText}>
                  Carbs: {calculatedMacros.carbs}g
                </Text>
                <Text style={styles.resultText}>
                  Fat: {calculatedMacros.fat}g
                </Text>
              </View>
            </>
          ) : (
            <>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Protein (g)</Text>
                <TextInput
                  style={styles.input}
                  value={macroSettings.proteinGrams}
                  onChangeText={(v) => handleMacroChange("proteinGrams", v)}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Carbs (g)</Text>
                <TextInput
                  style={styles.input}
                  value={macroSettings.carbsGrams}
                  onChangeText={(v) => handleMacroChange("carbsGrams", v)}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Fat (g)</Text>
                <TextInput
                  style={styles.input}
                  value={macroSettings.fatGrams}
                  onChangeText={(v) => handleMacroChange("fatGrams", v)}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.resultsContainer}>
                <Text style={styles.resultText}>
                  Total Calories: {calculatedPercentages.totalCal}
                </Text>
                <Text style={styles.resultText}>Derived Percentages:</Text>
                <Text style={styles.resultText}>
                  Protein: {calculatedPercentages.protein.toFixed(1)}%
                </Text>
                <Text style={styles.resultText}>
                  Carbs: {calculatedPercentages.carbs.toFixed(1)}%
                </Text>
                <Text style={styles.resultText}>
                  Fat: {calculatedPercentages.fat.toFixed(1)}%
                </Text>
              </View>
            </>
          )}

          <TouchableOpacity
            onPress={handleSave}
            disabled={!hasChanges || percentageError}
            style={[
              styles.saveButton,
              (!hasChanges || percentageError) && styles.disabledButton,
            ]}
          >
            <Text style={styles.saveButtonText}>Save Changes</Text>
          </TouchableOpacity>
        </ScrollView>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          action={{ label: "OK", onPress: () => {} }}
          style={{ backgroundColor: theme.colors.surface }}
        >
          <Text
            style={{
              fontSize: scaleSize(16),
              alignSelf: "center",
              color: theme.colors.primaryTextColor,
            }}
          >
            {snackbarMessage}
          </Text>
        </Snackbar>
      </View>
    </TouchableWithoutFeedback>
  );
};

const nutritionSettingsStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: scaleSize(60),
      borderBottomWidth: scaleSize(1),
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    backButton: {
      position: "absolute",
      left: scaleSize(20),
      zIndex: 1,
    },
    headerTitle: {
      fontSize: scaleSize(18),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    scrollContent: {
      padding: scaleSize(16),
    },
    modeToggleContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: scaleSize(20),
    },
    modeButton: {
      flex: 1,
      padding: scaleSize(12),
      marginHorizontal: scaleSize(4),
      borderRadius: 8,
      backgroundColor: theme.colors.surface,
      alignItems: "center",
    },
    activeMode: {
      backgroundColor: theme.colors.primary,
    },
    modeText: {
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
      fontWeight: "500",
    },
    inputGroup: {
      marginBottom: scaleSize(16),
    },
    label: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
      marginBottom: scaleSize(8),
    },
    input: {
      height: scaleSize(40),
      borderWidth: scaleSize(1),
      borderColor: theme.colors.sectionBorderColor,
      borderRadius: 8,
      paddingHorizontal: scaleSize(12),
      backgroundColor: theme.colors.surface,
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(14),
    },
    resultsContainer: {
      marginTop: scaleSize(20),
      padding: scaleSize(16),
      borderRadius: 8,
      backgroundColor: theme.colors.surface,
    },
    resultText: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
      marginBottom: scaleSize(8),
    },
    errorText: {
      color: theme.colors.error,
      fontSize: scaleSize(12),
      marginTop: scaleSize(8),
      textAlign: "center",
    },
    saveButton: {
      backgroundColor: theme.colors.surface,
      marginTop: scaleSize(20),
      paddingVertical: scaleSize(12),
      paddingHorizontal: scaleSize(24),
      borderRadius: scaleSize(8),
      elevation: 2,
      minWidth: scaleSize(150),
      justifyContent: "center",
      alignItems: "center",
    },
    disabledButton: {
      backgroundColor: theme.colors.subTextColor,
      opacity: 0.6,
    },
    saveButtonText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(16),
      fontWeight: "bold",
    },
  });

export default NutritionSettings;
