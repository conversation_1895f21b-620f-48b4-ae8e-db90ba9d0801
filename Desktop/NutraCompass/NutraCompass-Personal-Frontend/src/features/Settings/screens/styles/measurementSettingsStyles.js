import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";

const measurementSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: scaleSize(60),
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    backButton: {
      position: "absolute",
      left: scaleSize(20),
      zIndex: 1,
    },
    headerTitle: {
      fontSize: scaleSize(18),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    card: {
      backgroundColor: theme.colors.surface,
      margin: scaleSize(10),
      padding: scaleSize(15),
      borderRadius: theme.dimensions.cardBorderRadius,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 4,
    },
    sectionHeader: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(14),
      fontWeight: "500",
      paddingBottom: scaleSize(12),
      marginBottom: scaleSize(10),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    settingItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: scaleSize(15),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    label: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
      flex: 1,
    },
    valueContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    valueText: {
      fontSize: scaleSize(14),
      color: theme.colors.subTextColor,
      marginRight: scaleSize(5),
    },
    saveButton: {
      marginTop: scaleSize(20),
      borderRadius: theme.dimensions.buttonBorderRadius,
    },
  });
};

export default measurementSettingsStyles;
