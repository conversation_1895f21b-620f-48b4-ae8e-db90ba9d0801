import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext";
import { scaleSize } from "../../../../utils/deviceUtils";

const permissionSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    scrollContainer: {
      flex: 1,
    },
    scrollContent: {
      paddingHorizontal: scaleSize(16),
      paddingTop: scaleSize(10),
      paddingBottom: scaleSize(30),
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: scaleSize(60),
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: scaleSize(1),
      borderBottomColor: theme.colors.sectionBorderColor,
      paddingHorizontal: scaleSize(16),
    },
    backButton: {
      position: "absolute",
      left: scaleSize(16),
      zIndex: 1,
    },
    headerTitle: {
      fontSize: scaleSize(18),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    permissionContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: scaleSize(12),
      padding: scaleSize(16),
      marginBottom: scaleSize(16),
      borderWidth: scaleSize(1),
      borderColor: theme.colors.sectionBorderColor,
    },
    permissionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: scaleSize(8),
    },
    permissionTitle: {
      fontSize: scaleSize(16),
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
    },
    permissionStatus: {
      fontSize: scaleSize(14),
      fontWeight: "500",
    },
    permissionDescription: {
      fontSize: scaleSize(14),
      color: theme.colors.subTextColor,
      marginBottom: scaleSize(16),
      lineHeight: scaleSize(20),
    },
    buttonContainer: {
      flexDirection: "row",
      justifyContent: "flex-end",
    },
    permissionButton: {
      paddingVertical: scaleSize(10),
      paddingHorizontal: scaleSize(20),
      borderRadius: scaleSize(8),
      alignItems: "center",
      justifyContent: "center",
    },
    buttonText: {
      fontSize: scaleSize(14),
      fontWeight: "500",
    },
    footer: {
      marginTop: scaleSize(10),
      padding: scaleSize(16),
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: scaleSize(12),
    },
    footerText: {
      fontSize: scaleSize(12),
      color: theme.colors.subTextColor,
      textAlign: "center",
      lineHeight: scaleSize(18),
    },
  });
};

export default permissionSettingsStyles;
