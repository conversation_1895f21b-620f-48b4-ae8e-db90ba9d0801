import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";

const accountSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center", // Centers the title in the header
      height: scaleSize(60),
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: scaleSize(1),
      borderBottomColor: theme.colors.sectionBorderColor, // Optional: Adds a bottom border for separation
    },
    backButton: {
      position: "absolute", // Keeps the back button on the left
      left: scaleSize(20),
      zIndex: 1, // Ensures the button is always on top
    },
    headerTitle: {
      fontSize: scaleSize(18),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: scaleSize(12),
      paddingHorizontal: scaleSize(16),
      borderBottomWidth: scaleSize(1),
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    rowText: {
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
    },
    subText: {
      fontSize: scaleSize(14),
      color: theme.colors.subTextColor,
    },
    loadingOverlay: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
    },
    loadingText: {
      marginTop: scaleSize(20),
      fontSize: scaleSize(18),
      fontWeight: "500",
    },
  });
};

export default accountSettingsStyles;
