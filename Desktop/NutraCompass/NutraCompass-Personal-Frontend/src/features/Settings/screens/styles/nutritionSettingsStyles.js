import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";

const nutritionSettingsStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: scaleSize(60),
      backgroundColor: theme.colors.screenBackground,
      borderBottomWidth: scaleSize(1),
      borderBottomColor: theme.colors.sectionBorderColor,
    },
    backButton: {
      position: "absolute",
      left: scaleSize(20),
      zIndex: 1,
    },
    headerTitle: {
      fontSize: scaleSize(18),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    card: {
      backgroundColor: theme.colors.surface,
      margin: scaleSize(10),
      padding: scaleSize(15),
      borderRadius: 8,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 4,
    },
    headerText: {
      fontSize: scaleSize(14),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      alignSelf: "center",
      paddingBottom: scaleSize(18),
    },
    inputContainer: {
      marginBottom: scaleSize(10),
      flexDirection: "row",
      alignItems: "center",
    },
    label: {
      flex: 1 / 2,
      fontSize: scaleSize(12),
      color: theme.colors.subTextColor,
      marginBottom: scaleSize(5),
    },
    input: {
      flex: 1,
      height: scaleSize(40),
      backgroundColor: theme.colors.surface,
      borderColor: theme.colors.sectionBorderColor,
      borderWidth: scaleSize(1),
      borderRadius: 8,
      paddingHorizontal: scaleSize(10),
      fontSize: scaleSize(12),
      color: theme.colors.primaryTextColor,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: scaleSize(12),
      marginTop: scaleSize(5),
      textAlign: "center",
    },
  });
};

export default nutritionSettingsStyles;
