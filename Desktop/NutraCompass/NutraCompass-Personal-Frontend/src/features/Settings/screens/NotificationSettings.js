import React from "react";
import {
  View,
  Text,
  SafeAreaView,
  Switch,
  TouchableOpacity,
  Alert,
  Linking,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext";
import notificationSettingsStyles from "./styles/notificationSettingsStyles";
import { useFoodLog } from "../../FoodDiary/context/FoodLogContext";
import { usePushNotification } from "../../../context/PushNotificationContext";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { scaleSize } from "../../../utils/deviceUtils.js";

const TimeUtils = {
  formatTime: (hours, minutes) => {
    const date = new Date();
    date.setHours(hours);
    date.setMinutes(minutes);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  },

  getDate: (hours, minutes) => {
    const date = new Date();
    date.setHours(hours);
    date.setMinutes(minutes);
    return date;
  },
};

const NotificationHandler = {
  showPermissionAlert: (onRetry) => {
    Alert.alert(
      "Notifications Required",
      "This feature requires notification permissions",
      [
        { text: "Open Settings", onPress: () => Linking.openSettings() },
        { text: "Retry", onPress: onRetry },
        { text: "Cancel", style: "cancel" },
      ]
    );
  },
};

const NotificationSettings = ({ navigation }) => {
  const { theme } = useThemeContext();
  const { mealSections } = useFoodLog();
  const {
    requestPermissions,
    notificationSettings,
    updateMealSettings,
    updateStepSettings,
  } = usePushNotification();
  const styles = notificationSettingsStyles();

  const [activeMealTimePicker, setActiveMealTimePicker] = React.useState(null);
  const [stepTimePickerVisible, setStepTimePickerVisible] =
    React.useState(false);

  // Merge meal sections with stored settings
  const notificationMeals = React.useMemo(() => {
    return (mealSections || []).reduce((acc, section) => {
      if (!section?.id) return acc;

      // Use stored settings or initialize with defaults
      acc[section.id] = notificationSettings.meals[section.id] || {
        name: section.name,
        hour: 12,
        minute: 0,
        enabled: false,
      };
      return acc;
    }, {});
  }, [mealSections, notificationSettings.meals]);

  const handleToggleMeal = async (mealId) => {
    try {
      if (!(await requestPermissions())) {
        NotificationHandler.showPermissionAlert(() => handleToggleMeal(mealId));
        return;
      }

      const currentSettings = notificationMeals[mealId];
      updateMealSettings(mealId, {
        ...currentSettings,
        enabled: !currentSettings.enabled,
      });
    } catch (error) {
      Alert.alert("Error", `Failed to toggle meal reminder: ${error.message}`);
    }
  };

  const handleMealTimeChange = async (mealId, date) => {
    try {
      updateMealSettings(mealId, {
        ...notificationMeals[mealId],
        hour: date.getHours(),
        minute: date.getMinutes(),
      });
      setActiveMealTimePicker(null);
    } catch (error) {
      Alert.alert("Error", `Failed to update meal time: ${error.message}`);
    }
  };

  const handleToggleSteps = async (enabled) => {
    try {
      if (!(await requestPermissions())) {
        NotificationHandler.showPermissionAlert(() =>
          handleToggleSteps(enabled)
        );
        return;
      }

      updateStepSettings({
        ...notificationSettings.steps,
        enabled: enabled,
      });
    } catch (error) {
      Alert.alert("Error", "Failed to toggle step reminder");
    }
  };

  const handleStepTimeChange = (date) => {
    try {
      updateStepSettings({
        ...notificationSettings.steps,
        hour: date.getHours(),
        minute: date.getMinutes(),
      });
      setStepTimePickerVisible(false);
    } catch (error) {
      Alert.alert("Error", "Failed to update step reminder time");
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons
            name="chevron-left"
            size={scaleSize(28)}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.subText}>Set up notifications for your meals</Text>
      </View>

      {Object.entries(notificationMeals)
        .filter(([_, mealConfig]) => mealConfig.name.trim() !== "")
        .map(([mealId, mealConfig]) => {
          const pickerDate = new Date();
          pickerDate.setHours(mealConfig.hour);
          pickerDate.setMinutes(mealConfig.minute);

          return (
            <View key={mealId}>
              <View style={styles.row}>
                <Text style={styles.rowText}>{mealConfig.name}</Text>
                <Switch
                  value={mealConfig.enabled}
                  onValueChange={() => handleToggleMeal(mealId)}
                />
              </View>

              <TouchableOpacity
                style={styles.row}
                onPress={() => setActiveMealTimePicker(mealId)}
                disabled={!mealConfig.enabled}
              >
                <Text
                  style={[
                    styles.subText,
                    !mealConfig.enabled && styles.disabledText,
                    !mealConfig.enabled && { color: theme.colors.subTextColor },
                  ]}
                >
                  {TimeUtils.formatTime(mealConfig.hour, mealConfig.minute)}
                </Text>
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={scaleSize(24)}
                  color={
                    mealConfig.enabled
                      ? theme.colors.primaryTextColor
                      : theme.colors.subTextColor
                  }
                />
              </TouchableOpacity>

              <DateTimePickerModal
                isVisible={activeMealTimePicker === mealId}
                mode="time"
                date={pickerDate}
                onConfirm={(date) => handleMealTimeChange(mealId, date)}
                onCancel={() => setActiveMealTimePicker(null)}
              />
            </View>
          );
        })}

      <View style={[styles.row, { paddingTop: scaleSize(16) }]}>
        <Text style={styles.subText}>Step Goal Reminders</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.rowText}>Remind Me To Walk</Text>
        <Switch
          value={notificationSettings.steps.enabled}
          onValueChange={handleToggleSteps}
        />
      </View>

      <TouchableOpacity
        style={styles.row}
        onPress={() => setStepTimePickerVisible(true)}
        disabled={!notificationSettings.steps.enabled}
      >
        <Text
          style={[
            styles.subText,
            !notificationSettings.steps.enabled && styles.disabledText,
            !notificationSettings.steps.enabled && {
              color: theme.colors.subTextColor,
            },
          ]}
        >
          {TimeUtils.formatTime(
            notificationSettings.steps.hour,
            notificationSettings.steps.minute
          )}
        </Text>
        <MaterialCommunityIcons
          name="clock-outline"
          size={scaleSize(24)}
          color={
            notificationSettings.steps.enabled
              ? theme.colors.primaryTextColor
              : theme.colors.subTextColor
          }
        />
      </TouchableOpacity>

      <DateTimePickerModal
        isVisible={stepTimePickerVisible}
        mode="time"
        date={TimeUtils.getDate(
          notificationSettings.steps.hour,
          notificationSettings.steps.minute
        )}
        onConfirm={handleStepTimeChange}
        onCancel={() => setStepTimePickerVisible(false)}
      />
    </SafeAreaView>
  );
};

export default NotificationSettings;

// import React from "react";
// import {
//   View,
//   Text,
//   SafeAreaView,
//   Switch,
//   TouchableOpacity,
//   Alert,
//   Linking,
// } from "react-native";
// import { MaterialCommunityIcons } from "@expo/vector-icons";
// import { useThemeContext } from "../../../context/ThemeContext";
// import notificationSettingsStyles from "./styles/notificationSettingsStyles";
// import { useFoodLog } from "../../FoodDiary/context/FoodLogContext";
// import { useAuth } from "../../../authentication/context/AuthContext.js";
// import { usePushNotification } from "../../../context/PushNotificationContext";
// import DateTimePickerModal from "react-native-modal-datetime-picker";

// // 1. Time Zone Management Utilities
// const TimeUtils = {
//   toUTCIsoString: (input) => {
//     try {
//       // Handle both Date objects and timestamps
//       const date = input instanceof Date ? input : new Date(input);

//       if (isNaN(date)) {
//         console.error("Invalid date input:", input);
//         return new Date().toISOString();
//       }

//       return new Date(
//         Date.UTC(
//           date.getFullYear(),
//           date.getMonth(),
//           date.getDate(),
//           date.getHours(),
//           date.getMinutes()
//         )
//       ).toISOString();
//     } catch (error) {
//       console.error("Date conversion error:", error);
//       return new Date().toISOString();
//     }
//   },

//   fromUTCIsoToLocal: (isoString) => {
//     try {
//       const utcDate = new Date(isoString);
//       return new Date(
//         utcDate.getUTCFullYear(),
//         utcDate.getUTCMonth(),
//         utcDate.getUTCDate(),
//         utcDate.getUTCHours(),
//         utcDate.getUTCMinutes()
//       );
//     } catch (error) {
//       console.error("Date parsing error:", error);
//       return new Date();
//     }
//   },

//   formatTime: (utcIsoString) => {
//     try {
//       if (!utcIsoString) return "12:00 PM";
//       const localDate = TimeUtils.fromUTCIsoToLocal(utcIsoString);
//       return localDate.toLocaleTimeString("en-US", {
//         hour: "numeric",
//         minute: "2-digit",
//         hour12: true,
//       });
//     } catch (error) {
//       console.error("Time formatting error:", error);
//       return "12:00 PM";
//     }
//   },
// };

// // 2. Notification Configuration
// const MEAL_DEFAULTS = {
//   "Meal 1": { hour: 8, minute: 0 },
//   "Meal 2": { hour: 12, minute: 0 },
//   "Meal 3": { hour: 18, minute: 0 },
//   "Meal 4": { hour: 10, minute: 30 },
//   "Meal 5": { hour: 15, minute: 30 },
//   "Meal 6": { hour: 21, minute: 0 },
//   Water: { hour: 12, minute: 0 },
//   step: { hour: 14, minute: 0 },
// };

// // 3. Notification Handling Utilities
// const NotificationHandler = {
//   showPermissionAlert: (onRetry) => {
//     Alert.alert(
//       "Notifications Required",
//       "This feature requires notification permissions",
//       [
//         {
//           text: "Open Settings",
//           onPress: () => Linking.openSettings(),
//         },
//         {
//           text: "Try Again",
//           onPress: onRetry,
//         },
//         { text: "Cancel", style: "cancel" },
//       ]
//     );
//   },

//   handleError: (error) => {
//     console.error("Notification error:", error);
//     const message = error.message.includes("Network")
//       ? "Check your internet connection"
//       : "Failed to update settings. Please try again.";
//     Alert.alert("Error", message);
//   },
// };

// const NotificationSettings = ({ navigation }) => {
//   const { theme } = useThemeContext();
//   const { mealSections } = useFoodLog();
//   const { user } = useAuth();
//   const {
//     notifications,
//     updateNotificationSettings,
//     retryPushTokenRegistration,
//     expoPushToken,
//     checkPermissions,
//   } = usePushNotification();
//   const styles = notificationSettingsStyles();

//   const [activeMealTimePicker, setActiveMealTimePicker] = React.useState(null);
//   const [stepTimePickerVisible, setStepTimePickerVisible] =
//     React.useState(false);

//   // 2. Optimized syncMealSettings with deep comparison
//   const syncMealSettings = async () => {
//     try {
//       // Early exit conditions
//       if (!expoPushToken) {
//         console.log("Skipping sync - no push token");
//         return;
//       }

//       if (!notifications || !mealSections?.length) return;

//       // Create temporary objects for comparison
//       const existingSettings = notifications.mealReminders || {};
//       const newSettings = {};
//       let hasChanges = false;

//       // 1. Build new settings and detect changes
//       mealSections.forEach((section) => {
//         if (!section?.id) return;

//         // Get default time properly
//         const defaultDate = new Date();
//         const mealDefaults = MEAL_DEFAULTS[section.id] || MEAL_DEFAULTS.Water;
//         defaultDate.setHours(mealDefaults.hour, mealDefaults.minute);

//         const currentConfig = existingSettings[section.id] || {};
//         const newName = (section.name || "").trim();

//         // Create new config
//         const newConfig = {
//           enabled: newName ? currentConfig.enabled || false : false,
//           time: currentConfig.time || TimeUtils.toUTCIsoString(defaultDate),
//           mealName: newName,
//           isVisible: !!newName,
//         };

//         // Deep comparison with existing settings
//         const existing = existingSettings[section.id];
//         if (
//           !existing ||
//           existing.enabled !== newConfig.enabled ||
//           existing.time !== newConfig.time ||
//           existing.mealName !== newConfig.mealName ||
//           existing.isVisible !== newConfig.isVisible
//         ) {
//           hasChanges = true;
//         }

//         newSettings[section.id] = newConfig;
//       });

//       // 2. Check for removed meals
//       const existingIds = new Set(mealSections.map((s) => s.id));
//       const removedMeals = Object.keys(existingSettings).filter(
//         (id) => !existingIds.has(id)
//       );

//       if (removedMeals.length > 0) {
//         hasChanges = true;
//         removedMeals.forEach((id) => delete newSettings[id]);
//       }

//       // 3. Only update if changes detected
//       if (!hasChanges) {
//         console.log("No changes detected in meal settings");
//         return;
//       }

//       console.log("Applying meal settings updates");
//       await updateNotificationSettings({
//         mealReminders: newSettings,
//         _version: (notifications._version || 0) + 1,
//       });
//     } catch (error) {
//       NotificationHandler.handleError(error);
//     }
//   };

//   // 3. Updated useEffect with proper dependencies
//   React.useEffect(() => {
//     // Only sync when meal sections change, not on every render
//     if (user?.uid && mealSections?.length) {
//       syncMealSettings();
//     }
//   }, [mealSections]); // Only run when meal sections array changes

//   // 4. Notification Meal Mapping
//   const notificationMealMap = React.useMemo(() => {
//     return (mealSections || [])
//       .filter((section) => {
//         // Maintain filter for non-empty names
//         return (
//           typeof section.name === "string" && section.name.trim().length > 0
//         );
//       })
//       .reduce((acc, section) => {
//         if (!section?.id) return acc;

//         const settings = notifications?.mealReminders?.[section.id] || {};
//         acc[section.id] = {
//           name: section.name.trim(), // Already filtered empty names
//           enabled: settings.enabled || false,
//           time:
//             settings.time ||
//             TimeUtils.toUTCIsoString(
//               new Date().setHours(
//                 MEAL_DEFAULTS[section.id]?.hour || 12,
//                 MEAL_DEFAULTS[section.id]?.minute || 0
//               )
//             ),
//         };
//         return acc;
//       }, {});
//   }, [mealSections, notifications?.mealReminders]);

//   // 5. Core Interaction Handlers
//   const handleMealTimeChange = async (mealId, localDate) => {
//     try {
//       // 1. Check permissions FIRST
//       const { status } = await checkPermissions();
//       if (status !== "granted") {
//         NotificationHandler.showPermissionAlert(() =>
//           handleMealTimeChange(mealId, localDate)
//         );
//         return;
//       }

//       // 2. Get token if missing
//       if (!expoPushToken) {
//         await retryPushTokenRegistration();
//       }

//       // 3. Proceed with update
//       await updateNotificationSettings({
//         mealReminders: {
//           ...(notifications?.mealReminders || {}),
//           [mealId]: {
//             enabled: true,
//             time: TimeUtils.toUTCIsoString(localDate),
//             mealName: notificationMealMap[mealId]?.name || "",
//           },
//         },
//       });

//       setActiveMealTimePicker(null);
//     } catch (error) {
//       NotificationHandler.handleError(error);
//     }
//   };

//   const handleToggleMeal = async (mealId) => {
//     try {
//       // 1. Check permissions FIRST
//       const { status } = await checkPermissions();
//       if (status !== "granted") {
//         NotificationHandler.showPermissionAlert(() => handleToggleMeal(mealId));
//         return;
//       }

//       // 2. Get token if missing
//       if (!expoPushToken) {
//         await retryPushTokenRegistration();
//       }

//       // 3. Proceed with toggle
//       const current = notifications?.mealReminders?.[mealId] || {};
//       const newEnabled = !current.enabled;

//       await updateNotificationSettings({
//         mealReminders: {
//           ...(notifications?.mealReminders || {}),
//           [mealId]: {
//             ...current,
//             enabled: newEnabled,
//             time: newEnabled
//               ? current.time || notificationMealMap[mealId]?.time
//               : null,
//           },
//         },
//       });
//     } catch (error) {
//       NotificationHandler.handleError(error);
//     }
//   };

//   // 6. Step Goal Handlers
//   const handleStepTimeChange = async (localDate) => {
//     try {
//       // 1. Check permissions FIRST
//       const { status } = await checkPermissions();
//       if (status !== "granted") {
//         NotificationHandler.showPermissionAlert(() =>
//           handleStepTimeChange(localDate)
//         );
//         return;
//       }

//       // 2. Get token if missing
//       if (!expoPushToken) {
//         await retryPushTokenRegistration();
//       }

//       // 3. Proceed with update
//       await updateNotificationSettings({
//         stepGoal: {
//           ...notifications?.stepGoal,
//           enabled: true,
//           time: TimeUtils.toUTCIsoString(localDate),
//         },
//       });
//       setStepTimePickerVisible(false);
//     } catch (error) {
//       NotificationHandler.handleError(error);
//     }
//   };

//   const handleToggleSteps = async (enabled) => {
//     try {
//       // 1. Check permissions FIRST
//       const { status } = await checkPermissions();
//       if (status !== "granted") {
//         NotificationHandler.showPermissionAlert(() =>
//           handleToggleSteps(enabled)
//         );
//         return;
//       }

//       // 2. Get token if missing
//       if (!expoPushToken) {
//         await retryPushTokenRegistration();
//       }

//       // 3. Proceed with toggle
//       const current = notifications?.stepGoal || {};
//       await updateNotificationSettings({
//         stepGoal: {
//           ...current,
//           enabled,
//           time: enabled
//             ? current.time ||
//               TimeUtils.toUTCIsoString(
//                 new Date().setHours(
//                   MEAL_DEFAULTS.step.hour,
//                   MEAL_DEFAULTS.step.minute
//                 )
//               )
//             : current.time,
//         },
//       });
//     } catch (error) {
//       NotificationHandler.handleError(error);
//     }
//   };

//   return (
//     <SafeAreaView style={styles.container}>
//       <View style={styles.headerContainer}>
//         <TouchableOpacity
//           style={styles.backButton}
//           onPress={() => navigation.goBack()}
//         >
//           <MaterialCommunityIcons
//             name="chevron-left"
//             size={28}
//             color={theme.colors.primaryTextColor}
//           />
//         </TouchableOpacity>
//         <Text style={styles.headerTitle}>Notifications</Text>
//       </View>

//       <View style={styles.row}>
//         <Text style={styles.subText}>Set up notifications for your meals</Text>
//       </View>

//       {Object.entries(notificationMealMap || {}).map(([mealId, meal]) => {
//         // Create default date using MEAL_DEFAULTS
//         const defaultDate = new Date();
//         defaultDate.setHours(
//           MEAL_DEFAULTS[mealId]?.hour || 12,
//           MEAL_DEFAULTS[mealId]?.minute || 0
//         );

//         return (
//           <View key={mealId}>
//             <View style={styles.row}>
//               <Text style={styles.rowText}>{meal.name}</Text>
//               <Switch
//                 value={meal.enabled}
//                 onValueChange={() => handleToggleMeal(mealId)}
//               />
//             </View>

//             <TouchableOpacity
//               style={styles.row}
//               onPress={() => setActiveMealTimePicker(mealId)}
//               disabled={!meal.enabled}
//             >
//               <Text
//                 style={[
//                   styles.subText,
//                   !meal.enabled && styles.disabledText,
//                   !meal.enabled && { color: theme.colors.subTextColor },
//                 ]}
//               >
//                 {TimeUtils.formatTime(meal.time)}
//               </Text>
//               <MaterialCommunityIcons
//                 name="clock-outline"
//                 size={24}
//                 color={
//                   meal.enabled
//                     ? theme.colors.primaryTextColor
//                     : theme.colors.subTextColor
//                 }
//               />
//             </TouchableOpacity>

//             <DateTimePickerModal
//               key={`picker-${mealId}`}
//               isVisible={activeMealTimePicker === mealId}
//               mode="time"
//               date={
//                 meal.time ? TimeUtils.fromUTCIsoToLocal(meal.time) : defaultDate
//               }
//               onConfirm={(time) => handleMealTimeChange(mealId, time)}
//               onCancel={() => setActiveMealTimePicker(null)}
//             />
//           </View>
//         );
//       })}

//       <View style={[styles.row, { paddingTop: 16 }]}>
//         <Text style={styles.subText}>Step Goal Reminders</Text>
//       </View>

//       <View style={styles.row}>
//         <Text style={styles.rowText}>Remind Me To Walk</Text>
//         <Switch
//           value={notifications?.stepGoal?.enabled || false}
//           onValueChange={handleToggleSteps}
//         />
//       </View>

//       <TouchableOpacity
//         style={styles.row}
//         onPress={() => setStepTimePickerVisible(true)}
//         disabled={!notifications?.stepGoal?.enabled}
//       >
//         <Text
//           style={[
//             styles.subText,
//             !notifications?.stepGoal?.enabled && styles.disabledText,
//             !notifications?.stepGoal?.enabled && {
//               color: theme.colors.subTextColor,
//             },
//           ]}
//         >
//           {TimeUtils.formatTime(
//             notifications?.stepGoal?.time ||
//               TimeUtils.toUTCIsoString(
//                 new Date().setHours(
//                   MEAL_DEFAULTS.step.hour,
//                   MEAL_DEFAULTS.step.minute
//                 )
//               )
//           )}
//         </Text>
//         <MaterialCommunityIcons
//           name="clock-outline"
//           size={24}
//           color={
//             notifications?.stepGoal?.enabled
//               ? theme.colors.primaryTextColor
//               : theme.colors.subTextColor
//           }
//         />
//       </TouchableOpacity>

//       <DateTimePickerModal
//         isVisible={stepTimePickerVisible}
//         mode="time"
//         date={
//           notifications?.stepGoal?.time
//             ? TimeUtils.fromUTCIsoToLocal(notifications.stepGoal.time)
//             : new Date().setHours(
//                 MEAL_DEFAULTS.step.hour,
//                 MEAL_DEFAULTS.step.minute
//               )
//         }
//         onConfirm={handleStepTimeChange}
//         onCancel={() => setStepTimePickerVisible(false)}
//       />
//     </SafeAreaView>
//   );
// };

// export default NotificationSettings;
