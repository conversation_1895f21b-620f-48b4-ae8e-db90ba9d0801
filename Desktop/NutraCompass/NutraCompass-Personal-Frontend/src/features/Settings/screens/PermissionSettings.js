import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Linking,
  Platform,
  ScrollView,
  AppState,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { Pedometer } from "expo-sensors"; // Import Pedometer directly
import permissionSettingsStyles from "./styles/permissionSettingsStyles.js";
import { useThemeContext } from "../../../context/ThemeContext";
import { scaleSize } from "../../../utils/deviceUtils";
import { useCameraPermissions } from "expo-camera";
import { useFocusEffect } from "@react-navigation/native";

const PermissionSettings = ({ navigation }) => {
  const styles = permissionSettingsStyles();
  const { theme } = useThemeContext();

  const [permissions, setPermissions] = useState({
    camera: null,
    photos: null,
    motion: null,
  });

  // Use camera permissions hook
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();

  // Updated permission descriptions
  const permissionDescriptions = {
    camera:
      "Camera access allows you to scan food barcodes and capture profile pictures, nutritional progress photos, and custom meal images.",
    photos:
      "Photo library access enables you to select images for your profile, progress tracking, and custom meal creations.",
    motion:
      "Motion & fitness data tracks your workouts, steps, and activity levels to provide personalized health insights.",
  };

  // Function to check all permissions
  const checkAllPermissions = useCallback(async () => {
    try {
      // Camera permission from hook
      const cameraStatus = cameraPermission?.status || null;

      // Check media library permission
      let mediaStatus = "denied";
      try {
        const mediaResult = await ImagePicker.getMediaLibraryPermissionsAsync();
        mediaStatus = mediaResult.status;
      } catch (error) {
        console.error("Error checking media permissions:", error);
      }

      // Check motion permission using Pedometer API
      let motionStatus = "denied";
      try {
        // First check if pedometer is available
        const isAvailable = await Pedometer.isAvailableAsync();

        if (isAvailable) {
          // Check actual permission status
          const motionResult = await Pedometer.getPermissionsAsync();
          motionStatus = motionResult.status;
        } else {
          motionStatus = "unavailable";
        }
      } catch (error) {
        console.error("Error checking motion permissions:", error);
      }

      setPermissions({
        camera: cameraStatus,
        photos: mediaStatus,
        motion: motionStatus,
      });
    } catch (error) {
      console.error("Error checking all permissions:", error);
    }
  }, [cameraPermission]);

  // Refresh permissions when screen gains focus
  useFocusEffect(
    useCallback(() => {
      checkAllPermissions();
    }, [checkAllPermissions])
  );

  // Refresh permissions when app state changes (returning from settings)
  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      if (nextAppState === "active") {
        // Wait 1 second to ensure iOS has updated permissions
        setTimeout(() => {
          checkAllPermissions();
        }, 1000);
      }
    };

    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => subscription.remove();
  }, [checkAllPermissions]);

  // Initial permission check
  useEffect(() => {
    checkAllPermissions();
  }, [checkAllPermissions]);

  // Refresh camera permission when it changes
  useEffect(() => {
    if (cameraPermission) {
      setPermissions((prev) => ({
        ...prev,
        camera: cameraPermission.status,
      }));
    }
  }, [cameraPermission]);

  const requestPermission = async (permissionType) => {
    try {
      let status;

      switch (permissionType) {
        case "camera":
          const response = await requestCameraPermission();
          status = response.status;
          break;
        case "photos":
          const mediaResponse =
            await ImagePicker.requestMediaLibraryPermissionsAsync();
          status = mediaResponse.status;
          break;
        case "motion":
          // Request motion permission using Pedometer API
          const motionResponse = await Pedometer.requestPermissionsAsync();
          status = motionResponse.status;
          break;
        default:
          return;
      }

      // Update state for non-camera permissions
      if (permissionType !== "camera") {
        setPermissions((prev) => ({ ...prev, [permissionType]: status }));
      }

      // Re-check all permissions after a short delay
      setTimeout(() => {
        checkAllPermissions();
      }, 500);
    } catch (error) {
      console.error(`Error requesting ${permissionType} permission:`, error);
    }
  };

  const openAppSettings = () => {
    Linking.openSettings();
  };

  const renderPermissionRow = (type, title) => {
    const status = permissions[type];
    const isEnabled = status === "granted";
    const isUndetermined = status === "undetermined" || status === null;
    const statusText = isEnabled ? "Enabled" : "Disabled";

    // Only show "Allow Access" for undetermined status
    const canRequest = isUndetermined;

    return (
      <View key={type} style={styles.permissionContainer}>
        <View style={styles.permissionHeader}>
          <Text style={styles.permissionTitle}>{title}</Text>
          <Text
            style={[
              styles.permissionStatus,
              {
                color: isEnabled
                  ? theme.colors.success || "#4CAF50"
                  : theme.colors.error || "#F44336",
              },
            ]}
          >
            {statusText}
          </Text>
        </View>

        <Text style={styles.permissionDescription}>
          {permissionDescriptions[type]}
        </Text>

        <View style={styles.buttonContainer}>
          {canRequest ? (
            <TouchableOpacity
              style={[
                styles.permissionButton,
                { backgroundColor: theme.colors.primary },
              ]}
              onPress={() => requestPermission(type)}
            >
              <Text
                style={[styles.buttonText, { color: theme.colors.onPrimary }]}
              >
                Allow Access
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[
                styles.permissionButton,
                { backgroundColor: theme.colors.surfaceVariant },
              ]}
              onPress={openAppSettings}
            >
              <Text
                style={[styles.buttonText, { color: theme.colors.primary }]}
              >
                Manage in Settings
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Fixed Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons
            name="chevron-left"
            size={scaleSize(28)}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>App Permissions</Text>
      </View>

      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
      >
        {renderPermissionRow("camera", "Camera Access")}
        {renderPermissionRow("photos", "Photo Library Access")}
        {renderPermissionRow("motion", "Motion & Fitness Access")}

        {/* Explanation Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Permissions are only used for the features they enable. Manage them
            anytime in device settings.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

export default PermissionSettings;
