import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import accountSettingsStyles from "./styles/accountSettingsStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useUserSettings } from "../context/UserSettingsContext.js";
import TimezonePickerModal from "../../../components/TimezonePickerModal.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const AccountSettings = ({ navigation }) => {
  const styles = accountSettingsStyles();
  const { theme } = useThemeContext();
  const { user, loggingOut, resetPassword, deleteAccount } = useAuth();
  const { userSettings, setUserLocation } = useUserSettings();
  const [showTimezoneModal, setShowTimezoneModal] = useState(false);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({});
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);

  const showAlert = (title, message, buttons, options) => {
    setAlertConfig({
      title,
      message,
      buttons,
      cancelable: options?.cancelable || true,
    });
    setAlertVisible(true);
  };

  const handleDeleteAccount = () => {
    showAlert(
      "Delete Account",
      "Are you sure you want to delete your account? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
          onPress: () => console.log("Cancel pressed"),
        },
        {
          text: "Delete",
          style: "destructive",
          // Wrap delete operation with loading state
          onPress: async () => {
            setIsDeletingAccount(true); // Show loading screen
            try {
              await deleteAccount(
                user?.uid,
                String(userSettings?.profile?.firstName)
              );
            } catch (error) {
              setIsDeletingAccount(false); // Hide loading on error
              console.error("Account deletion failed:", error);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  const handleChangePassword = () => {
    showAlert(
      "Change Password",
      "Are you sure you want to change your password?",
      [
        {
          text: "No",
          style: "cancel",
          onPress: () => console.log("Cancel pressed"),
        },
        {
          text: "Yes",
          style: "destructive",
          onPress: () =>
            resetPassword(
              user?.email,
              String(userSettings?.profile?.firstName)
            ),
        },
      ],
      { cancelable: true }
    );
  };

  const handleTimezoneSelect = async (selectedTimezone) => {
    // Add validation
    if (
      typeof selectedTimezone !== "string" ||
      !selectedTimezone.includes("/")
    ) {
      console.error("Invalid timezone format received:", selectedTimezone);
      return;
    }

    // Create new location object without spreading potential React references
    const newLocation = {
      region: "US", // Hardcode or use existing region
      timezone: selectedTimezone,
    };

    await setUserLocation(newLocation);
    setShowTimezoneModal(false);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons
            name="chevron-left"
            size={scaleSize(28)}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Account</Text>
      </View>

      {/* Name Row */}
      <View style={styles.row} onPress={() => console.log("Name Pressed")}>
        <Text style={styles.rowText}>Name</Text>
        <Text style={styles.subText}>
          {String(userSettings?.profile?.firstName || "")}{" "}
          {String(userSettings?.profile?.lastName || "")}
        </Text>
      </View>

      {/* Email Row */}
      <View style={styles.row} onPress={() => console.log("Email Pressed")}>
        <Text style={styles.rowText}>Email</Text>
        <Text style={styles.subText}>{String(user?.email || "")}</Text>
      </View>

      {/* Timezone Row */}
      <TouchableOpacity
        style={styles.row}
        onPress={() => setShowTimezoneModal(true)}
      >
        <Text style={styles.rowText}>Timezone</Text>
        <Text style={styles.subText}>
          {String(userSettings?.location?.timezone || "Not set")}
        </Text>
      </TouchableOpacity>

      {/* Change Password Row */}
      <TouchableOpacity style={styles.row} onPress={handleChangePassword}>
        <Text style={styles.rowText}>Change Password</Text>
      </TouchableOpacity>

      {/* Export Data Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Export Data (Excel) Pressed")}
      >
        <Text style={styles.rowText}>Export Data (Excel)</Text>
      </TouchableOpacity> */}

      {/* User ID Row */}
      <View style={styles.row}>
        <Text style={styles.rowText}>User ID</Text>
        <Text style={styles.subText}>{String(user?.uid || "")}</Text>
      </View>

      {/* Restore Subscription Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Restore Subscription Pressed")}
      >
        <Text style={styles.rowText}>Restore Subscription</Text>
      </TouchableOpacity> */}

      {/* Terms of Service Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Terms of Service Pressed")}
      >
        <Text style={styles.rowText}>Terms of Service</Text>
      </TouchableOpacity> */}

      {/* Scientific References Row */}
      {/* <TouchableOpacity
        style={styles.row}
        onPress={() => console.log("Scientific References Pressed")}
      >
        <Text style={styles.rowText}>Scientific References and Citations</Text>
      </TouchableOpacity> */}

      {/* Log Out Row */}
      <TouchableOpacity style={styles.row} onPress={() => loggingOut()}>
        <Text
          style={[styles.rowText, { color: theme.colors.primaryTextColor }]}
        >
          Log Out
        </Text>
      </TouchableOpacity>

      {/* Delete Account Row */}
      <TouchableOpacity style={styles.row} onPress={handleDeleteAccount}>
        <Text style={[styles.rowText, { color: "red" }]}>Delete Account</Text>
      </TouchableOpacity>

      {/* Timezone Picker Modal */}
      <TimezonePickerModal
        visible={showTimezoneModal}
        onClose={() => setShowTimezoneModal(false)}
        currentTimezone={userSettings?.location?.timezone}
        onSelect={handleTimezoneSelect}
      />

      <ScaledAlert
        visible={alertVisible}
        title={alertConfig.title}
        message={alertConfig.message}
        buttons={alertConfig.buttons}
        cancelable={alertConfig.cancelable}
        onDismiss={() => setAlertVisible(false)}
      />

      {/* Loading Modal */}
      <Modal
        transparent={true}
        animationType="fade"
        visible={isDeletingAccount}
        onRequestClose={() => {}} // Prevent closing by back button
      >
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text
            style={[
              styles.loadingText,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            Deleting Account...
          </Text>
        </View>
      </Modal>
    </View>
  );
};

const ScaledAlert = ({
  visible,
  title,
  message,
  buttons,
  cancelable,
  onDismiss,
}) => {
  const { theme } = useThemeContext(); // Make sure this is available in your context

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onDismiss}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={cancelable ? onDismiss : null}
      >
        <TouchableOpacity
          activeOpacity={1}
          style={[
            styles.alertContainer,
            { backgroundColor: theme.colors.surface },
          ]}
        >
          <Text
            style={[
              styles.alertTitle,
              {
                fontSize: scaleSize(18),
                color: theme.colors.primaryTextColor,
              },
            ]}
          >
            {title}
          </Text>

          <Text
            style={[
              styles.alertMessage,
              {
                fontSize: scaleSize(16),
                color: theme.colors.primaryTextColor,
                marginBottom: scaleSize(20),
              },
            ]}
          >
            {message}
          </Text>

          <View style={styles.buttonContainer}>
            {buttons?.map((button, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.alertButton,
                  {
                    backgroundColor:
                      button.style === "destructive"
                        ? theme.colors.error
                        : "transparent",
                    marginLeft: index > 0 ? scaleSize(10) : 0,
                  },
                ]}
                onPress={() => {
                  onDismiss();
                  button.onPress?.();
                }}
              >
                <Text
                  style={[
                    styles.buttonText,
                    {
                      fontSize: scaleSize(16),
                      color:
                        button.style === "destructive"
                          ? theme.colors.onError
                          : theme.colors.primary,
                    },
                  ]}
                >
                  {button.text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: scaleSize(20),
  },
  alertContainer: {
    width: "100%",
    maxWidth: scaleSize(400),
    borderRadius: scaleSize(16),
    padding: scaleSize(24),
    elevation: 5,
  },
  alertTitle: {
    fontWeight: "bold",
    marginBottom: scaleSize(12),
    textAlign: "center",
  },
  alertMessage: {
    textAlign: "center",
    lineHeight: scaleSize(22),
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: scaleSize(10),
  },
  alertButton: {
    paddingVertical: scaleSize(10),
    paddingHorizontal: scaleSize(20),
    borderRadius: scaleSize(8),
    minWidth: scaleSize(100),
    alignItems: "center",
  },
  buttonText: {
    fontWeight: "500",
  },
  loadingOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  loadingText: {
    marginTop: scaleSize(20),
    fontSize: scaleSize(18),
    fontWeight: "500",
  },
});

export default AccountSettings;
