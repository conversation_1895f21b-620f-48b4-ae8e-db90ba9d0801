import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import Configs from "../../../../configs.js";
import { ImageUploader } from "../../../utils/ImageService.js";

const USER_SETTINGS_CACHE_KEY = (userId) => `@user_settings_${userId}`;
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hour

const UserSettingsContext = createContext();

export function useUserSettings() {
  return useContext(UserSettingsContext);
}

export function UserSettingsProvider({ children }) {
  const { user, token } = useAuth();
  const userId = user?.uid;
  const [userSettings, setUserSettings] = useState(null);
  const apiUrl = Configs.NutraCompass_API_URL;

  // AppLoadingScreen: Load user data only when userId and token are confirmed
  const fetchUserSettings = async (forceRefresh = false) => {
    if (!userId || !token) {
      console.log("Skipping fetch due to missing userId or token.");
      return;
    }

    const cacheKey = USER_SETTINGS_CACHE_KEY(userId);

    try {
      if (!forceRefresh) {
        const cachedData = await AsyncStorage.getItem(cacheKey);
        if (cachedData) {
          const { data, timestamp } = JSON.parse(cachedData);

          // Validate cache structure
          const isValidCache = data?.profile && data?.nutritionalGoals;
          //const isRecent = Date.now() - timestamp < CACHE_TTL;

          if (isValidCache) {
            console.log("[CACHE] Applying cached user settings");
            setUserSettings(data);
            return data;
          }
        }
      }

      console.log("[NETWORK] Fetching fresh user settings");
      const response = await fetch(`${apiUrl}/v1/settings/${userId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }

      const newData = await response.json();

      // Validate and normalize response
      const validatedData = {
        profile: newData.profile || {},
        nutritionalGoals: newData.nutritionalGoals || {},
        physicalFitnessGoals: newData.physicalFitnessGoals || {},
        appAppearance: newData.appAppearance || {},
        location: newData.location || {},
        measurementSettings: newData.measurementSettings || {
          // WATER UNIT OPTIONS: "fl oz" OR "mL"
          // Default: "fl oz" (Fluid Ounces)
          waterUnit: "fl oz",

          // DISTANCE UNIT OPTIONS: "mi" (miles) OR "km" (kilometers)
          // Default: "mi" (Miles)
          distanceUnit: "mi",

          // BODY WEIGHT UNIT OPTIONS: "lbs" (pounds) OR "kg" (kilograms)
          // Default: "lbs" (Pounds)
          bodyWeightUnit: "lbs",

          // FOOD MEASUREMENT OPTIONS:
          // "g" (grams), "oz" (ounces),
          // "cups" (cups), "mL" (milliliters), "fl oz" (fluid ounces)
          // Default: "g" (Grams)
          // foodUnit: "g",
        },
      };

      setUserSettings(validatedData);
      await AsyncStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: validatedData,
          timestamp: Date.now(),
        })
      );

      return validatedData;
    } catch (error) {
      console.error("Settings load failed:", error);

      // Fallback to cache if available
      const fallback = await AsyncStorage.getItem(cacheKey);
      if (fallback) {
        console.log("[FALLBACK] Using cached settings");
        const { data } = JSON.parse(fallback);
        setUserSettings(data);
        return data;
      }

      // Fallback to empty defaults
      const defaults = {
        profile: {},
        nutritionalGoals: {},
        physicalFitnessGoals: {},
        appAppearance: {},
        location: {},
        measurementSettings: {
          waterUnit: "fl oz", // Default: Fluid Ounces
          distanceUnit: "mi", // Default: Miles
          bodyWeightUnit: "lbs", // Default: Pounds
          // foodUnit: "g", // Default: Grams
        },
      };
      setUserSettings(defaults);
      return defaults;
    }
  };

  // Update user settings in the backend and cache
  const updateUserSettings = async (updatedSettings) => {
    if (!userId) return;

    try {
      // Network update
      await fetch(`${apiUrl}/v1/settings/${userId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(updatedSettings),
      });

      // Update local state and cache
      setUserSettings((prev) => {
        const newSettings = { ...prev, ...updatedSettings };
        AsyncStorage.setItem(
          USER_SETTINGS_CACHE_KEY(userId),
          JSON.stringify({
            data: newSettings,
            timestamp: Date.now(),
          })
        );
        return newSettings;
      });
    } catch (error) {
      console.error("Update failed:", error);
      throw error;
    }
  };

  // Method to calculate total daily calorie carbs goal based on percentage of the total daily calories
  const calculateCarbDailyCalories = (totalDailyCalories, carbPercentage) => {
    return Math.round((totalDailyCalories * carbPercentage) / 100);
  };

  // Method to calculate total daily calorie protein goal based on percentage of the total daily calories
  const calculateProteinDailyCalories = (
    totalDailyCalories,
    proteinPercentage
  ) => {
    return Math.round((totalDailyCalories * proteinPercentage) / 100);
  };

  // Method to calculate total daily calorie fat goal based on percentage of the total daily calories
  const calculateFatDailyCalories = (totalDailyCalories, fatPercentage) => {
    return Math.round((totalDailyCalories * fatPercentage) / 100);
  };

  const calculateCarbDailyGrams = (totalDailyCalories, carbPercentage) => {
    const carbCaloriesPerGram = 4;
    return Math.round(
      (totalDailyCalories * (carbPercentage / 100)) / carbCaloriesPerGram
    );
  };

  const calculateProteinDailyGrams = (
    totalDailyCalories,
    proteinPercentage
  ) => {
    const proteinCaloriesPerGram = 4;
    return Math.round(
      (totalDailyCalories * (proteinPercentage / 100)) / proteinCaloriesPerGram
    );
  };

  const calculateFatDailyGrams = (totalDailyCalories, fatPercentage) => {
    const fatCaloriesPerGram = 9;
    return Math.round(
      (totalDailyCalories * (fatPercentage / 100)) / fatCaloriesPerGram
    );
  };

  // Function to upload profile picture with ImageUploader-only rollback
  const uploadProfilePicture = async ({ uri }) => {
    if (!userId || !uri) return null;

    // Store previous state
    const previousImageUrl = userSettings.profile?.pictureUrl;
    let newImageUrl = null;

    try {
      // 1. Upload new image (overwrites existing)
      newImageUrl = await ImageUploader.upload({
        userId,
        uri,
        type: "PROFILE",
      });

      // 2. Update database
      const currentProfile = userSettings.profile || {};
      const newProfile = { ...currentProfile, pictureUrl: newImageUrl };
      await setUserProfile(newProfile);

      return newImageUrl;
    } catch (error) {
      console.error("Upload failed, initiating rollback:", error);

      // Rollback sequence
      try {
        // 3a. Delete newly uploaded image
        if (newImageUrl) {
          await ImageUploader.remove({
            userId,
            type: "PROFILE",
          });
        }

        // 3b. Restore previous image if it existed
        if (previousImageUrl) {
          // Download previous image to temp file
          const tempPath = `${
            RNFS.CachesDirectoryPath
          }/rollback_${Date.now()}.jpg`;
          await RNFS.downloadFile({
            fromUrl: previousImageUrl,
            toFile: tempPath,
          });

          // Re-upload using ImageUploader
          await ImageUploader.upload({
            userId,
            uri: tempPath,
            type: "PROFILE",
          });

          // Cleanup temp file
          await RNFS.unlink(tempPath);
        }
      } catch (rollbackError) {
        console.error("Rollback failed:", rollbackError);
        throw new Error("Upload failed and rollback unsuccessful");
      }

      // 4. Revert database state
      try {
        await setUserProfile({
          ...userSettings.profile,
          pictureUrl: previousImageUrl || "",
        });
      } catch (dbError) {
        console.error("Database rollback failed:", dbError);
      }

      throw error;
    }
  };

  // Function to remove profile picture with ImageUploader rollback
  const removeProfilePicture = async () => {
    if (!userId) return;

    // Store previous state
    const previousImageUrl = userSettings.profile?.pictureUrl;
    let removalSuccessful = false;

    try {
      // 1. Remove from storage
      await ImageUploader.remove({
        userId,
        type: "PROFILE",
      });
      removalSuccessful = true;

      // 2. Update database
      const currentProfile = userSettings.profile || {};
      const newProfile = { ...currentProfile, pictureUrl: "" };
      await setUserProfile(newProfile);
    } catch (error) {
      console.error("Removal failed, initiating rollback:", error);

      if (removalSuccessful && previousImageUrl) {
        try {
          // Re-upload previous image
          const tempPath = `${
            RNFS.CachesDirectoryPath
          }/rollback_${Date.now()}.jpg`;
          await RNFS.downloadFile({
            fromUrl: previousImageUrl,
            toFile: tempPath,
          });

          await ImageUploader.upload({
            userId,
            uri: tempPath,
            type: "PROFILE",
          });

          await RNFS.unlink(tempPath);
        } catch (rollbackError) {
          console.error("Removal rollback failed:", rollbackError);
          throw new Error("Removal failed and rollback unsuccessful");
        }
      }

      // Restore database state
      try {
        await setUserProfile({
          ...userSettings.profile,
          pictureUrl: previousImageUrl || "",
        });
      } catch (dbError) {
        console.error("Database rollback failed:", dbError);
      }

      throw error;
    }
  };

  // Getter methods
  const getUserProfile = () => userSettings?.profile || {};
  const getUserLocation = () => userSettings?.location || {};
  const getAppAppearance = () => userSettings?.appAppearance || {};
  const getNutritionalGoals = () => userSettings?.nutritionalGoals || {};

  const getPhysicalFitnessGoals = () => {
    return {
      stepsGoal: userSettings?.physicalFitnessGoals?.stepsGoal ?? 0,
      distanceGoal: userSettings?.physicalFitnessGoals?.distanceGoal ?? 0,
      distanceUnit: userSettings?.physicalFitnessGoals?.distanceUnit ?? "km",
    };
  };

  const getUserMeasurementSettings = () =>
    userSettings?.measurementSettings || {
      // WATER UNIT OPTIONS: "fl oz" OR "mL"
      waterUnit: "fl oz",

      // DISTANCE UNIT OPTIONS: "mi" (miles) OR "km" (kilometers)
      distanceUnit: "mi",

      // BODY WEIGHT UNIT OPTIONS: "lbs" (pounds) OR "kg" (kilograms)
      bodyWeightUnit: "lbs",

      // FOOD MEASUREMENT OPTIONS:
      // "g" (grams), "oz" (ounces),
      // "cups" (cups), "mL" (milliliters), "fl oz" (fluid ounces)
      // foodUnit: "g",
    };

  // Setter methods with added error handling

  const setUserProfile = async (newProfile) => {
    try {
      // Get current settings
      const currentSettings = { ...userSettings };

      // Extract current body weight
      const currentBodyWeight = currentSettings.profile?.bodyWeight || "";

      // Check if body weight has changed
      if (
        newProfile.bodyWeight &&
        newProfile.bodyWeight !== currentBodyWeight
      ) {
        // Parse the body weight string - handle different formats
        const parts = newProfile.bodyWeight.split(" ");
        let value, unit;

        if (parts.length >= 2) {
          // Standard format: "150 lbs"
          value = parts[0];
          unit = parts[1];
        } else if (currentBodyWeight) {
          // If new format is invalid, try to extract unit from current value
          const currentParts = currentBodyWeight.split(" ");
          unit = currentParts.length >= 2 ? currentParts[1] : "lbs";
        } else {
          // Default to lbs if no current value
          unit = "lbs";
        }

        // Validate unit
        if (unit === "kg" || unit === "lbs") {
          // Update measurement settings to match the new unit
          const updatedMeasurementSettings = {
            ...currentSettings.measurementSettings,
            bodyWeightUnit: unit,
          };

          // Update profile
          const updatedProfile = {
            ...currentSettings.profile,
            ...newProfile,
          };

          // Prepare combined update
          const updatedSettings = {
            profile: updatedProfile,
            measurementSettings: updatedMeasurementSettings,
          };

          // Single API call to update both
          await updateUserSettings(updatedSettings);
          return;
        }
      }

      // If no body weight change or invalid unit, just update profile
      await updateUserSettings({ profile: newProfile });
    } catch (error) {
      console.error("Failed to update profile:", error);
    }
  };

  const setUserLocation = async (newLocation) => {
    try {
      // Add final validation layer
      if (!newLocation?.timezone || typeof newLocation.timezone !== "string") {
        throw new Error("Invalid location format");
      }

      await updateUserSettings({ location: newLocation });
    } catch (error) {
      console.error("Failed to update user location:", error);
    }
  };

  const setAppAppearance = async (newAppearance) => {
    try {
      await updateUserSettings({ appAppearance: newAppearance });
    } catch (error) {
      console.error("Failed to update app appearance:", error);
    }
  };

  // Set calorie and macro goals that's in nutritional goals
  const setCalorieAndMacroGoals = async (newGoals) => {
    if (!userId) return; // Ensure there's a user logged in

    try {
      const currentGoals = userSettings.nutritionalGoals || {}; // Fetch existing goals

      // Calculate new macros based on the input
      const newMacroGoals = {
        carb: {
          dailyPercentage: newGoals.carbPercentage / 100,
          dailyCalories: (newGoals.calorieGoal * newGoals.carbPercentage) / 100,
          dailyGrams: Math.round(
            (newGoals.calorieGoal * (newGoals.carbPercentage / 100)) / 4
          ),
        },
        protein: {
          dailyPercentage: newGoals.proteinPercentage / 100,
          dailyCalories:
            (newGoals.calorieGoal * newGoals.proteinPercentage) / 100,
          dailyGrams: Math.round(
            (newGoals.calorieGoal * (newGoals.proteinPercentage / 100)) / 4
          ),
        },
        fat: {
          dailyPercentage: newGoals.fatPercentage / 100,
          dailyCalories: (newGoals.calorieGoal * newGoals.fatPercentage) / 100,
          dailyGrams: Math.round(
            (newGoals.calorieGoal * (newGoals.fatPercentage / 100)) / 9
          ),
        },
      };

      // Merge new goals with existing ones
      const updatedGoals = {
        ...currentGoals,
        calorieGoal: newGoals.calorieGoal, // Overwrite calorie goal
        macroGoals: { ...currentGoals.macroGoals, ...newMacroGoals }, // Merge macro goals
      };

      // Update user settings with the merged goals
      await updateUserSettings({ nutritionalGoals: updatedGoals });
    } catch (error) {
      console.error("Failed to update nutritional goals:", error);
    }
  };

  // Set water consumption goal that's in nutritional goals
  const setWaterGoalAndUnit = async (waterGoal, unit) => {
    if (!userId) return;

    try {
      // Get current settings
      const currentSettings = { ...userSettings };

      // Update water goal
      const updatedNutritionalGoals = {
        ...currentSettings.nutritionalGoals,
        waterGoal: {
          amount: waterGoal,
          unit: unit,
        },
      };

      // Update measurement settings
      const updatedMeasurementSettings = {
        ...currentSettings.measurementSettings,
        waterUnit: unit,
      };

      // Prepare combined update
      const updatedSettings = {
        nutritionalGoals: updatedNutritionalGoals,
        measurementSettings: updatedMeasurementSettings,
      };

      // Single API call to update both
      await updateUserSettings(updatedSettings);
    } catch (error) {
      console.error("Failed to update water goal and unit:", error);
      throw error;
    }
  };

  const setDistanceGoalAndUnit = async (
    stepsGoal,
    distanceGoal,
    distanceUnit
  ) => {
    if (!userId) return;

    try {
      // Get current settings
      const currentSettings = { ...userSettings };

      // Update physical fitness goals
      const updatedPhysicalFitnessGoals = {
        ...currentSettings.physicalFitnessGoals,
        stepsGoal: stepsGoal,
        distanceGoal: distanceGoal,
        distanceUnit: distanceUnit,
      };

      // Update measurement settings
      const updatedMeasurementSettings = {
        ...currentSettings.measurementSettings,
        distanceUnit: distanceUnit,
      };

      // Prepare combined update
      const updatedSettings = {
        physicalFitnessGoals: updatedPhysicalFitnessGoals,
        measurementSettings: updatedMeasurementSettings,
      };

      // Single API call to update both
      await updateUserSettings(updatedSettings);
    } catch (error) {
      console.error("Failed to update distance goal and unit:", error);
      throw error;
    }
  };

  const setPhysicalFitnessGoals = async (newGoals) => {
    try {
      await updateUserSettings({ physicalFitnessGoals: newGoals });
    } catch (error) {
      console.error("Failed to update physical fitness goals:", error);
    }
  };

  const setUserMeasurementSettings = async (newSettings) => {
    try {
      const updatedSettings = { ...userSettings };

      // Check if body weight unit is being updated
      if (newSettings.bodyWeightUnit) {
        const newUnit = newSettings.bodyWeightUnit;

        // Check if body weight exists in profile
        if (updatedSettings.profile?.bodyWeight) {
          const currentWeightStr = updatedSettings.profile.bodyWeight;

          // Extract numeric value and unit from string
          const parts = currentWeightStr.trim().split(/\s+/);
          const valuePart = parts[0];
          const currentUnit = parts.slice(1).join(" ").toLowerCase();

          const currentValue = parseFloat(valuePart);

          if (!isNaN(currentValue)) {
            let convertedValue = currentValue;
            const newUnitLower = newUnit.toLowerCase();

            // Only convert if units are different
            if (currentUnit !== newUnitLower) {
              // Convert kg to lb
              if (currentUnit.includes("kg") && newUnitLower.includes("lb")) {
                convertedValue = currentValue * 2.20462;
              }
              // Convert lb to kg
              else if (
                currentUnit.includes("lb") &&
                newUnitLower.includes("kg")
              ) {
                convertedValue = currentValue / 2.20462;
              }

              // Round to 2 decimal places
              convertedValue = parseFloat(convertedValue.toFixed(1));

              // Update body weight string with new value and unit
              updatedSettings.profile.bodyWeight = `${convertedValue} ${newUnit}`;
            }
          }
        }
      }

      // Check if water unit is being updated
      if (newSettings.waterUnit) {
        const newWaterUnit = newSettings.waterUnit;

        // Convert existing water goal if it exists
        if (updatedSettings.nutritionalGoals?.waterGoal) {
          const currentWaterGoal = updatedSettings.nutritionalGoals.waterGoal;
          let convertedAmount = currentWaterGoal.amount;

          // Use the exact same conversion logic as in toggleUnit
          if (currentWaterGoal.unit === "fl oz" && newWaterUnit === "mL") {
            // Convert fl oz to mL: 1 fl oz = 29.5735 mL
            convertedAmount = Math.round(currentWaterGoal.amount * 29.5735);
          } else if (
            currentWaterGoal.unit === "mL" &&
            newWaterUnit === "fl oz"
          ) {
            // Convert mL to fl oz: 1 mL = 0.033814 fl oz
            convertedAmount = Math.round(currentWaterGoal.amount / 29.5735);
          }

          updatedSettings.nutritionalGoals = {
            ...updatedSettings.nutritionalGoals,
            waterGoal: {
              amount: convertedAmount,
              unit: newWaterUnit,
            },
          };
        }
      }

      // Check if distance unit is being updated
      if (newSettings.distanceUnit) {
        const newDistanceUnit = newSettings.distanceUnit;

        // Convert existing distance goal if it exists
        if (updatedSettings.physicalFitnessGoals?.distanceGoal) {
          const currentGoal = updatedSettings.physicalFitnessGoals;
          let convertedDistance = currentGoal.distanceGoal;

          if (currentGoal.distanceUnit === "mi" && newDistanceUnit === "km") {
            convertedDistance = convertedDistance * 1.60934;
          } else if (
            currentGoal.distanceUnit === "km" &&
            newDistanceUnit === "mi"
          ) {
            convertedDistance = convertedDistance * 0.621371;
          }

          // Fix to 2 decimal places
          convertedDistance = parseFloat(convertedDistance.toFixed(2));

          updatedSettings.physicalFitnessGoals = {
            ...currentGoal,
            distanceGoal: convertedDistance,
            distanceUnit: newDistanceUnit,
          };
        }
      }

      // Update measurement settings
      updatedSettings.measurementSettings = {
        ...updatedSettings.measurementSettings,
        ...newSettings,
      };

      // Update backend and cache
      await updateUserSettings(updatedSettings);

      return updatedSettings.measurementSettings;
    } catch (error) {
      console.error("Failed to update measurement settings:", error);
      throw error;
    }
  };

  const contextValue = useMemo(() => {
    return {
      fetchUserSettings,
      userSettings,
      getUserProfile,
      getUserLocation,
      getAppAppearance,
      getNutritionalGoals,
      getPhysicalFitnessGoals,
      getUserMeasurementSettings,
      setUserProfile,
      setUserLocation,
      setAppAppearance,
      setCalorieAndMacroGoals,
      setPhysicalFitnessGoals,
      setUserMeasurementSettings,
      calculateProteinDailyGrams,
      calculateCarbDailyGrams,
      calculateFatDailyGrams,
      calculateProteinDailyCalories,
      calculateCarbDailyCalories,
      calculateFatDailyCalories,
      uploadProfilePicture,
      removeProfilePicture,
      setWaterGoalAndUnit,
      setDistanceGoalAndUnit,
    };
  }, [
    fetchUserSettings,
    userSettings,
    getUserProfile,
    getUserLocation,
    getAppAppearance,
    getNutritionalGoals,
    getPhysicalFitnessGoals,
    getUserMeasurementSettings,
    setUserProfile,
    setUserLocation,
    setAppAppearance,
    setCalorieAndMacroGoals,
    setPhysicalFitnessGoals,
    setUserMeasurementSettings,
    calculateProteinDailyGrams,
    calculateCarbDailyGrams,
    calculateFatDailyGrams,
    calculateProteinDailyCalories,
    calculateCarbDailyCalories,
    calculateFatDailyCalories,
    uploadProfilePicture,
    removeProfilePicture,
    setWaterGoalAndUnit,
    setDistanceGoalAndUnit,
  ]);

  return (
    <UserSettingsContext.Provider value={contextValue}>
      {children}
    </UserSettingsContext.Provider>
  );
}
