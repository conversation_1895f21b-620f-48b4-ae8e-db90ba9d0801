import React from "react";
import { View, Text, Switch, SafeAreaView } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import ThemeDisplay from "../components/ThemeDisplay.js";
import ThemeSelector from "../components/ThemeSelector.js";
import { scaleSize, isTablet } from "../../../utils/deviceUtils.js";

const ThemeScreen = () => {
  const { toggleDarkMode, theme, mode } = useThemeContext();
  const tablet = isTablet();
  const handleToggleDarkMode = () => {
    toggleDarkMode();
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
      }}
    >
      {/* Screen Header */}
      <View
        style={{
          flexDirection: "row",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "baseline",
          padding: scaleSize(10),
          paddingTop: scaleSize(30),
        }}
      >
        <Text
          style={{
            color: theme.colors.primaryTextColor,
            fontSize: scaleSize(26),
            alignSelf: "center",
          }}
        >
          Theme Palette
        </Text>

        {/* Toggle for switching between dark and light mode */}
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            gap: tablet ? scaleSize(16) : scaleSize(8),
            paddingRight: tablet ? scaleSize(16) : scaleSize(8),
          }}
        >
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              marginRight: scaleSize(4),
              fontSize: scaleSize(16),
            }}
          >
            {mode.charAt(0).toUpperCase() + mode.slice(1)} Mode
          </Text>
          <Switch
            style={{
              transform: [{ scaleX: scaleSize(1) }, { scaleY: scaleSize(1) }],
            }}
            trackColor={{ false: "#767577", true: "#81b0ff" }}
            thumbColor={mode === "dark" ? "#f5dd4b" : "#f4f3f4"}
            ios_backgroundColor="#3e3e3e"
            onValueChange={handleToggleDarkMode}
            value={mode === "dark"}
          />
        </View>
      </View>

      <View
        style={{
          flex: 1,
          backgroundColor: theme.colors.surface,
          padding: scaleSize(5),
          paddingTop: scaleSize(20),
        }}
      >
        <Text
          style={{
            fontSize: scaleSize(18),
            color: theme.colors.primaryTextColor,
            alignSelf: "center",
          }}
        >
          Current Theme
        </Text>
        <View style={{ flex: 1 }}>
          <ThemeDisplay />
        </View>
      </View>

      <View
        style={{
          flex: 4,
          paddingBottom: "20%",
          alignItems: "center",
        }}
      >
        <ThemeSelector renderStructure="structure1" />
      </View>
    </SafeAreaView>
  );
};

export default ThemeScreen;
