import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const ThemeDisplay = () => {
  const { theme } = useThemeContext(); // Make sure you have the correct context hook

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    section: {
      padding: scaleSize(10),
      margin: scaleSize(5),
      backgroundColor: theme.colors.sectionBackgroundColor,
      borderColor: theme.colors.sectionBorderColor,
      borderWidth: 1,
      borderRadius: theme.dimensions.sectionBorderRadius,
    },
    card: {
      padding: scaleSize(10),
      margin: scaleSize(5),
      backgroundColor: theme.colors.cardBackgroundColor,
      borderColor: theme.colors.cardBorderColor,
      borderWidth: 1,
      borderRadius: theme.dimensions.cardBorderRadius,
    },
    sectionLowOpacity: {
      padding: scaleSize(10),
      margin: scaleSize(5),
      backgroundColor: theme.colors.sectionBackgroundColorLowOpacity,
      borderColor: theme.colors.sectionBorderColor,
      borderWidth: 1,
      borderRadius: theme.dimensions.sectionBorderRadius,
    },
    cardLowOpacity: {
      padding: scaleSize(10),
      margin: scaleSize(5),
      backgroundColor: theme.colors.cardBackgroundColorLowOpacity,
      borderColor: theme.colors.cardBorderColor,
      borderWidth: 1,
      borderRadius: theme.dimensions.cardBorderRadius,
    },
    cardDarkGrayBackgroundColor: {
      padding: scaleSize(10),
      margin: scaleSize(5),
      backgroundColor: theme.colors.cardDarkGrayBackgroundColor,
      borderColor: theme.colors.cardBorderColor,
      borderWidth: 1,
      borderRadius: theme.dimensions.cardBorderRadius,
    },
    cardShadow: {
      padding: scaleSize(10),
      margin: scaleSize(5),
      backgroundColor: theme.colors.shadow,
      borderColor: theme.colors.cardBorderColor,
      borderWidth: 1,
      borderRadius: theme.dimensions.cardBorderRadius,
    },
    headerText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(16),
      fontWeight: "bold",
      marginBottom: 8,
    },
    cardHeaderText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(16),
      fontWeight: "bold",
      marginBottom: scaleSize(8),
    },
  });

  return (
    <View style={styles.container}>
      <View
        style={{
          flex: 1,
          margin: scaleSize(10),
          elevation: 4,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-evenly",
            marginTop: scaleSize(16),
          }}
        >
          <Text
            style={{
              fontSize: scaleSize(18),
              alignSelf: "center",
              color: theme.colors.primaryTextColor,
            }}
          >
            {theme.name}
          </Text>
          <View
            style={{
              backgroundColor: theme.colors.primary,
              width: scaleSize(40),
              height: scaleSize(40),
              borderRadius: 8,
            }}
          />
          <View
            style={{
              backgroundColor: theme.colors.secondary,
              width: scaleSize(40),
              height: scaleSize(40),
              borderRadius: 8,
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default ThemeDisplay;
