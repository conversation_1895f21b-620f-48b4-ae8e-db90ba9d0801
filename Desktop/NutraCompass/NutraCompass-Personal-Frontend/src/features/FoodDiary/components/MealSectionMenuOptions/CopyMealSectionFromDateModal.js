import React, { useEffect, useState } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  Platform,
  TouchableOpacity,
} from "react-native";
import { Button, IconButton } from "react-native-paper";
import { Picker } from "@react-native-picker/picker";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { useFoodLog } from "../../context/FoodLogContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";
const CopyMealSectionFromDate = ({
  isVisible,
  onClose,
  onDateConfirm,
  activeMealSection,
  modalType,
}) => {
  const { theme } = useThemeContext();
  const { mealSections } = useFoodLog();
  const [targetDate, setTargetDate] = useState(new Date());
  const [targetMealType, setTargetMealType] = useState(
    activeMealSection?.id || ""
  );

  useEffect(() => {
    if (isVisible && activeMealSection?.id) {
      setTargetMealType(activeMealSection.id);
    }
  }, [activeMealSection, isVisible]);

  const handleDateChange = (date) => {
    if (date instanceof Date) {
      const adjustedDate = new Date(date);
      adjustedDate.setHours(12); // Set to noon to avoid timezone issues
      setTargetDate(adjustedDate);
    }
  };

  const handleSubmit = () => {
    if (!(targetDate instanceof Date)) {
      console.error("Invalid targetDate, defaulting to now:", targetDate);
      setTargetDate(new Date()); // Default to now if there's an issue
    }

    const formattedDate = targetDate.toISOString().split("T")[0];

    if (!activeMealSection) return null; // or some loading indicator

    onDateConfirm(activeMealSection.id, targetMealType, formattedDate);
    onClose();
  };

  const styles = StyleSheet.create({
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.4)",
    },
    modalView: {
      width: "90%", // Increase the width of the modal
      backgroundColor: theme.colors.surface,
      borderRadius: scaleSize(20),
      padding: scaleSize(20),
      alignItems: "center",
      elevation: scaleSize(5),
    },
    pickerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      width: "100%",
      marginVertical: scaleSize(10),
    },
    header: {
      width: "100%",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    buttonContainer: {
      flexDirection: "row",
      gap: scaleSize(8),
      marginTop: scaleSize(20),
    },
    button: {
      flex: 1,
      alignItems: "center",
      padding: scaleSize(12),
      borderRadius: scaleSize(12),
      backgroundColor: "rgba(255, 255, 255, 0.05)",
    },
    picker: {
      flex: 1,
      width: Platform.OS === "ios" ? scaleSize(200) : undefined, // Adjust width for iOS picker to accommodate date text
    },
    pickerItem: {
      fontSize: scaleSize(14), // Adjust font size for better visibility
    },
  });

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.header}>
            <IconButton
              icon="close"
              color={theme.colors.primaryTextColor}
              size={scaleSize(20)}
              onPress={onClose}
            />
            <Text
              style={{
                color: theme.colors.primaryTextColor,
                fontSize: scaleSize(20),
                fontWeight: "500",
              }}
            >
              {modalType === "From Date" ? `Copy From Date` : `Copy To Date`}
            </Text>
            <IconButton
              icon="check"
              color={theme.colors.primaryTextColor}
              size={scaleSize(20)}
              onPress={handleSubmit}
            />
          </View>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={targetMealType}
              onValueChange={setTargetMealType}
              style={styles.picker}
              itemStyle={styles.pickerItem}
            >
              {mealSections
                .filter((section) => section.name && section.id !== "Water")
                .map((section) => (
                  <Picker.Item
                    label={section.name}
                    value={section.id}
                    key={section.id}
                    color={theme.colors.primaryTextColor}
                  />
                ))}
            </Picker>

            <Picker
              selectedValue={targetDate.toDateString()}
              onValueChange={(itemValue) => setTargetDate(new Date(itemValue))}
              style={styles.picker}
              itemStyle={styles.pickerItem}
            >
              {Array.from({ length: 60 }, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - 30 + i);
                // Format the date to show the day of the week, month, and day
                const label = date.toLocaleDateString("en-US", {
                  weekday: "short",
                  month: "long",
                  day: "numeric",
                });
                return (
                  <Picker.Item
                    label={label}
                    value={date.toDateString()}
                    key={date.toDateString()}
                    color={theme.colors.primaryTextColor}
                  />
                );
              })}
            </Picker>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity onPress={onClose} style={styles.button}>
              <Text style={{ fontSize: scaleSize(16), color: "red" }}>
                Cancel
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleSubmit} style={styles.button}>
              <Text style={{ fontSize: scaleSize(16), color: "green" }}>
                Confirm
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default CopyMealSectionFromDate;
