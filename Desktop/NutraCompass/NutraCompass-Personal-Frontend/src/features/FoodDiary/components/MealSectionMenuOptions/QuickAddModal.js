import React from "react";
import {
  View,
  TouchableWithoutFeedback,
  Dimensions,
  StyleSheet,
  Keyboard,
  Platform,
  Text,
} from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { useFoodLog } from "../../context/FoodLogContext.js";
import QuickAddForm from "../QuickAddForm";
import { scaleSize } from "../../../../utils/deviceUtils.js";

const QuickAddModal = ({ closeModal, mealType, activeItem }) => {
  const { theme } = useThemeContext();
  const { addQuickFoodEntry, setActiveFoodItem, setActiveMealSection } =
    useFoodLog();

  const screen = Dimensions.get("window");

  const handleAdd = async (foodData) => {
    try {
      const isEditing = activeItem && activeItem.id;
      const finalFoodData = {
        ...foodData,
        ...(isEditing && { id: activeItem.id }),
      };

      await addQuickFoodEntry(mealType, finalFoodData);
      handleClose();
    } catch (error) {
      console.error("Add food error:", error.message);
    }
  };

  const handleClose = () => {
    setActiveFoodItem(null);
    setActiveMealSection(null);
    Keyboard.dismiss();
    closeModal();
  };

  return (
    <View style={styles.outerContainer}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View
          style={[
            styles.modalContainer,
            {
              backgroundColor: theme.colors.surface,
              borderBottomLeftRadius: scaleSize(20),
              borderBottomRightRadius: scaleSize(20),
              height: screen.height * 0.85, // Fixed height
              width: "100%",
            },
          ]}
        >
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontSize: scaleSize(20),
              alignSelf: "center",
            }}
          >
            Quick Add
          </Text>
          <QuickAddForm
            theme={theme}
            initialData={activeItem}
            onSubmit={handleAdd}
            onCancel={handleClose}
            isEditing={!!(activeItem && activeItem.id)}
            compact={true}
          />
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    justifyContent: "flex-start",
    width: "100%",
    paddingTop: Platform.OS === "ios" ? 40 : 0,
  },
  modalContainer: {
    width: "100%",
    padding: scaleSize(20),
    overflow: "hidden", // Ensure content doesn't overflow
  },
});

export default QuickAddModal;
