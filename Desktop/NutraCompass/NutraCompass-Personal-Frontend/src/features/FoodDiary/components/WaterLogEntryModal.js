import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
} from "react-native";
import { AntDesign, MaterialIcons } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodLog } from "../context/FoodLogContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const WaterLogEntryModal = ({
  isVisible, // From modal context
  closeModal, // From modal context
  activeItem,
  onClose,
}) => {
  const { theme } = useThemeContext();
  const { getUserMeasurementSettings } = useUserSettings();
  const measurementSettings = getUserMeasurementSettings();

  const { addWaterEntry } = useFoodLog();
  const [amount, setAmount] = useState("");
  const [unit, setUnit] = useState(measurementSettings?.waterUnit || "ml");

  useEffect(() => {
    if (isVisible && activeItem && activeItem.mealType === "Water") {
      setAmount(activeItem.volume.toString());
      setUnit(activeItem.unit);
    } else {
      resetForm();
    }
    return () => {
      if (!isVisible) resetForm(); // Reset when modal is closed
    };
  }, [isVisible, activeItem]);

  const handleLogEntry = () => {
    const waterAmount = parseInt(amount, 10);
    if (!isNaN(waterAmount) && waterAmount > 0) {
      addWaterEntry(waterAmount, unit, activeItem?.id); // Pass ID if updating
      onClose?.();
      closeModal();
    } else {
      alert(`Please enter a valid amount in ${unit}`);
    }
  };

  const handleSetAmount = (value) => {
    setAmount(value.toString());
  };

  const toggleUnit = () => {
    setUnit((prev) => (prev === "ml" ? "fl oz" : "ml"));
  };

  const quickOptions = [
    { label: "1 cup", value: unit === "ml" ? 240 : 8 },
    { label: "4 cups", value: unit === "ml" ? 960 : 32 },
    { label: "8 cups", value: unit === "ml" ? 1920 : 64 },
  ];

  const resetForm = () => {
    setAmount("");
    setUnit(measurementSettings?.waterUnit || "ml");
  };

  const styles = StyleSheet.create({
    modalView: {
      width: "100%",
      borderRadius: scaleSize(20),
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.surface,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
      paddingVertical: scaleSize(20),
    },
    header: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: scaleSize(20),
      paddingHorizontal: scaleSize(20),
    },
    inputContainer: {
      width: "100%",
      alignItems: "center",
      justifyContent: "center",
      gap: scaleSize(12),
    },
    input: {
      borderWidth: scaleSize(1),
      borderColor: theme.colors.primary,
      padding: scaleSize(10),
      width: "80%",
      marginBottom: scaleSize(20),
      borderRadius: scaleSize(10),
      textAlign: "center",
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
    },
    iconButton: {
      padding: scaleSize(10),
    },
    icon: {
      color: theme.colors.primaryTextColor,
    },
    quickOption: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: scaleSize(10),
      backgroundColor: theme.colors.surface,
      borderRadius: scaleSize(10),
      margin: scaleSize(5),
    },
    quickOptionText: {
      marginLeft: scaleSize(5),
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
    },
    optionsContainer: {
      flexDirection: "row",
      justifyContent: "space-evenly",
      width: "100%",
    },
    optionMiddle: {
      marginTop: scaleSize(10),
      alignItems: "center",
    },
  });

  return (
    <View>
      {/* Wrap the entire backdrop in TouchableWithoutFeedback to dismiss keyboard on tap */}
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.modalView}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                closeModal();
                resetForm();
              }}
            >
              <AntDesign name="left" size={scaleSize(24)} style={styles.icon} />
            </TouchableOpacity>
            <Text
              style={{
                fontSize: scaleSize(18),
                color: theme.colors.primaryTextColor,
              }}
            >
              Add Water
            </Text>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={handleLogEntry}
            >
              <MaterialIcons
                name="check"
                size={scaleSize(24)}
                style={styles.icon}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer}>
            <TouchableOpacity
              style={{ alignItems: "center" }}
              onPress={toggleUnit}
            >
              <MaterialIcons
                name="local-drink"
                size={scaleSize(60)}
                color="#00BFFF"
              />
              <Text
                style={{
                  fontSize: scaleSize(16),
                  color: theme.colors.primaryTextColor,
                }}
              >
                Switch to {unit === "ml" ? "fl oz" : "ml"}
              </Text>
            </TouchableOpacity>
            <TextInput
              style={styles.input}
              onChangeText={setAmount}
              value={amount}
              keyboardType="numeric"
              placeholder={`Enter amount (${unit})`}
              placeholderTextColor={theme.colors.subTextColor}
            />
          </View>

          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={styles.quickOption}
              onPress={() => handleSetAmount(quickOptions[0].value)}
            >
              <MaterialIcons
                name="local-drink"
                size={scaleSize(24)}
                color="#00BFFF"
              />
              <Text style={styles.quickOptionText}>
                {`${quickOptions[0].label} (${quickOptions[0].value} ${unit})`}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickOption}
              onPress={() => handleSetAmount(quickOptions[1].value)}
            >
              <MaterialIcons
                name="local-drink"
                size={scaleSize(24)}
                color="#00BFFF"
              />
              <Text style={styles.quickOptionText}>
                {`${quickOptions[1].label} (${quickOptions[1].value} ${unit})`}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles.optionMiddle}>
            <TouchableOpacity
              style={styles.quickOption}
              onPress={() => handleSetAmount(quickOptions[2].value)}
            >
              <MaterialIcons
                name="local-drink"
                size={scaleSize(24)}
                color="#00BFFF"
              />
              <Text style={styles.quickOptionText}>
                {`${quickOptions[2].label} (${quickOptions[2].value} ${unit})`}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

export default WaterLogEntryModal;
