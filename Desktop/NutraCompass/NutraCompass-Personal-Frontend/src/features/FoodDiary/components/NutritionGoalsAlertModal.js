import React from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import * as Haptics from "expo-haptics";
import { MaterialIcons } from "@expo/vector-icons";
import { scaleSize } from "../../../utils/deviceUtils.js";
const NutritionGoalsAlertModal = ({
  isVisible,
  onConfirm,
  onCancel,
  onAlignWithProgram,
}) => {
  const { theme } = useThemeContext();

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0, 0, 0, 0.5)",
        }}
      >
        <View
          style={{
            width: "80%",
            backgroundColor: theme.colors.surface,
            borderRadius: scaleSize(10),
            padding: scaleSize(20),
            alignItems: "center",
          }}
        >
          {/* Alert <PERSON>con */}
          <MaterialIcons
            name="error-outline"
            size={scaleSize(30)}
            color="red"
            style={{
              paddingBottom: scaleSize(8),
            }}
          />
          <Text
            style={{
              fontSize: scaleSize(16),
              fontWeight: "bold",
              marginBottom: scaleSize(15),
              textAlign: "center",
              color: theme.colors.primaryTextColor,
            }}
          >
            Are you sure you want to update your nutritional goals?
          </Text>
          <Text
            style={{
              fontSize: scaleSize(13),
              color: theme.colors.primaryTextColor,
              marginBottom: scaleSize(20),
              textAlign: "center",
            }}
          >
            These goals do not align with the goals set by your active
            nutritional program.
          </Text>

          {/* Buttons */}
          <View style={{ width: "100%", gap: scaleSize(18) }}>
            {/* Align with Program Button */}
            <TouchableOpacity
              style={{
                alignItems: "center",
                padding: scaleSize(10),
                backgroundColor: theme.colors.screenBackground,
                borderRadius: scaleSize(5),
                borderWidth: scaleSize(1),
                borderColor: theme.colors.primaryTextColor,
              }}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                onAlignWithProgram();
              }}
            >
              <Text
                style={{
                  fontSize: scaleSize(14),
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                }}
              >
                Align With My Nutritional Program
              </Text>
            </TouchableOpacity>

            {/* Cancel and Confirm Buttons */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              {/* Cancel Button */}
              <TouchableOpacity
                style={{
                  flex: 1,
                  alignItems: "center",
                  paddingVertical: scaleSize(10),
                  marginRight: scaleSize(10),
                  backgroundColor: theme.colors.screenBackground,
                  borderRadius: scaleSize(5),
                }}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  onCancel();
                }}
              >
                <Text
                  style={{
                    fontSize: scaleSize(14),
                    color: "red",
                  }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>

              {/* Confirm Button */}
              <TouchableOpacity
                style={{
                  flex: 1,
                  alignItems: "center",
                  paddingVertical: scaleSize(10),
                  marginLeft: scaleSize(10),
                  backgroundColor: theme.colors.screenBackground,
                  borderRadius: scaleSize(5),
                }}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  onConfirm();
                }}
              >
                <Text
                  style={{
                    fontSize: scaleSize(14),
                    color: "green",
                  }}
                >
                  Confirm
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default NutritionGoalsAlertModal;
