import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Linking,
  Keyboard,
  TouchableWithoutFeedback,
  Animated,
  Easing,
  Dimensions,
  Button,
} from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import barcodeScannerStyles from "./styles/barcodeScannerStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import Configs from "../../../../configs.js";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const { height: screenHeight } = Dimensions.get("window");

const BarcodeScanner = ({
  isVisible,
  closeModal,
  onBarcodeScanned,
  onClose,
  onError,
}) => {
  const apiUrl = Configs.NutraCompass_API_URL;
  const { user } = useAuth();
  const userId = user?.uid;
  const { theme } = useThemeContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [manualBarcode, setManualBarcode] = useState("");
  const [isTextInputFocused, setIsTextInputFocused] = useState(false);
  const [facing, setFacing] = useState("back");
  const [permission, requestPermission] = useCameraPermissions();
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [cameraTimeout, setCameraTimeout] = useState(false);
  const cameraRef = useRef(null);
  const scanCooldownRef = useRef(false);
  const manualInputRef = useRef(null);
  const isMounted = useRef(true);
  const keyboardHeight = useRef(new Animated.Value(0)).current;

  const styles = barcodeScannerStyles();

  // Animation for loading state
  const pulseAnim = useRef(new Animated.Value(0)).current;
  const spinAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (loading) {
      // Pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Spin animation
      Animated.loop(
        Animated.timing(spinAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start();
    } else {
      pulseAnim.setValue(0);
      spinAnim.setValue(0);
    }
  }, [loading]);

  // Camera timout effect after 8 seconds if not ready
  useEffect(() => {
    let timer;

    if (!isCameraReady && permission?.status === "granted") {
      timer = setTimeout(() => {
        setCameraTimeout(true);
      }, 8000);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isCameraReady, permission]);

  // Track component mount state
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Smooth keyboard handling with animation
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      "keyboardWillShow",
      (e) => {
        Animated.timing(keyboardHeight, {
          toValue: e.endCoordinates.height,
          duration: e.duration,
          easing: Easing.keyboard,
          useNativeDriver: false,
        }).start();
        setIsTextInputFocused(true);
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      "keyboardWillHide",
      (e) => {
        Animated.timing(keyboardHeight, {
          toValue: 0,
          duration: e.duration,
          easing: Easing.keyboard,
          useNativeDriver: false,
        }).start(() => {
          if (isMounted.current) setIsTextInputFocused(false);
        });
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  // Handle visibility changes
  useEffect(() => {
    if (!isVisible) return;

    const initializeCamera = async () => {
      try {
        if (!permission?.granted) {
          await requestPermission();
        }
      } catch (err) {
        handleError("Camera permission required");
      }
    };

    initializeCamera();

    return () => {
      if (cameraRef.current) {
        cameraRef.current.pausePreview();
      }
    };
  }, [isVisible]);

  const handleError = useCallback(
    (error) => {
      if (!isMounted.current) return;
      setError(error);
      if (onError) onError(error);
      setTimeout(() => {
        if (isMounted.current) setError(null);
      }, 3000);
    },
    [onError]
  );

  // Central result handler with delay
  const handleResult = useCallback(
    (result) => {
      if (!isMounted.current) return;

      console.log("[BarcodeScanner] Sending result to parent:", result);

      // FIRST: Send results to parent
      try {
        if (typeof onBarcodeScanned === "function") {
          console.log("[BarcodeScanner] Calling onBarcodeScanned");
          onBarcodeScanned(result); // Just pass result, don't close
        } else {
          console.warn("[BarcodeScanner] onBarcodeScanned is not a function");
        }
      } catch (e) {
        console.error("[BarcodeScanner] Error in onBarcodeScanned:", e);
      }
    },
    [onBarcodeScanned]
  );

  // Validate food item structure
  const isValidFoodItem = (foodItem) => {
    return (
      foodItem &&
      typeof foodItem === "object" &&
      foodItem.foodLabel &&
      foodItem.nutrients?.core?.ENERC_KCAL?.quantity !== undefined &&
      foodItem.activeMeasure?.weight !== undefined &&
      foodItem.activeMeasure?.label
    );
  };

  // Fixed barcode scanning
  const handleBarcodeScanned = useCallback(
    async ({ data }) => {
      if (!data?.trim() || scanCooldownRef.current) return;

      scanCooldownRef.current = true;
      setLoading(true);

      try {
        const response = await fetch(
          `${apiUrl}/v1/food/barcode/${encodeURIComponent(
            data
          )}?userId=${userId}`
        );

        const responseData = await response.json();

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("Product not found in database");
          }
          throw new Error(responseData.message || "Barcode not recognized");
        }

        const foodItem =
          Array.isArray(responseData.data) && responseData.data.length > 0
            ? responseData.data[0]
            : responseData.data;

        // Validate food item structure
        if (!isValidFoodItem(foodItem)) {
          throw new Error("Invalid food item data received from server");
        }

        console.log("[BarcodeScanner] Processed food item:", foodItem);
        handleResult(foodItem);
      } catch (error) {
        handleError(error.message);
      } finally {
        setTimeout(() => {
          if (isMounted.current) {
            setLoading(false);
            scanCooldownRef.current = false;
          }
        }, 1000);
      }
    },
    [userId, handleResult, handleError]
  );

  // Fixed manual search - ALWAYS ENABLED
  const handleManualSearch = async () => {
    const barcode = manualBarcode?.trim();
    if (!barcode || !barcode.match(/^\d+$/)) {
      handleError("Valid numeric barcode required");
      return;
    }

    setLoading(true);
    // Dismiss keyboard immediately for smoother UI transition
    Keyboard.dismiss();

    try {
      const response = await fetch(
        `${apiUrl}/v1/food/barcode/${encodeURIComponent(
          barcode
        )}?userId=${userId}`
      );

      const responseData = await response.json();
      console.log("[BarcodeScanner] Barcode search response:", responseData);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Product not found in database");
        }
        throw new Error(responseData.message || "Barcode search failed");
      }

      const foodItem =
        Array.isArray(responseData.data) && responseData.data.length > 0
          ? responseData.data[0]
          : responseData.data;

      // Validate food item structure
      if (!isValidFoodItem(foodItem)) {
        throw new Error("Invalid food item data received from server");
      }

      console.log("[BarcodeScanner] Processed food item:", foodItem);
      handleResult(foodItem);
    } catch (error) {
      handleError(error.message);
    } finally {
      if (isMounted.current) setLoading(false);
    }
  };

  const handleCameraReady = useCallback(() => {
    if (isMounted.current) {
      setIsCameraReady(true);
      setCameraTimeout(false);
    }
  }, []);

  const toggleCameraFacing = useCallback(() => {
    setFacing((current) => (current === "back" ? "front" : "back"));
  }, []);

  const handleClose = useCallback(() => {
    if (isMounted.current) {
      setError(null);
      setIsCameraReady(false);
    }
    closeModal();
  }, [closeModal]);

  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
    if (isMounted.current) setIsTextInputFocused(false);
  }, []);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      enabled
    >
      {/* Loading overlay with animation */}
      {loading && (
        <Animated.View
          style={[
            styles.loadingOverlay,
            {
              opacity: pulseAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.7, 0.9],
              }),
              transform: [
                {
                  scale: pulseAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.02],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.compassContainer}>
            {/* Static compass body */}
            <Image
              source={require("../../../../assets/CompassBody.png")}
              contentFit="contain"
              style={[
                styles.compassBody,
                {
                  width: scaleSize(180),
                  height: scaleSize(180),
                },
              ]}
            />

            {/* Needle positioning wrapper */}
            <View style={styles.needlePositionWrapper}>
              {/* Animated compass needle */}
              <Animated.View
                style={[
                  styles.needleContainer,
                  {
                    transform: [
                      {
                        rotate: spinAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: ["0deg", "360deg"],
                        }),
                      },
                      {
                        scale: pulseAnim.interpolate({
                          inputRange: [0, 0.5, 1],
                          outputRange: [1, 1.05, 1],
                        }),
                      },
                    ],
                  },
                ]}
              >
                <Image
                  source={require("../../../../assets/NutraCompass_Needle.png")}
                  contentFit="contain"
                  style={{
                    width: scaleSize(120),
                    height: scaleSize(120),
                    transform: [{ scale: 0.28 }],
                  }}
                />
              </Animated.View>
            </View>
          </View>

          <Text style={styles.loadingText}>Searching database...</Text>
          <View style={styles.scanningDots}>
            <Animated.View
              style={[
                styles.scanningDot,
                {
                  opacity: pulseAnim.interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [0.3, 1, 0.3],
                  }),
                },
              ]}
            />
            <Animated.View
              style={[
                styles.scanningDot,
                {
                  opacity: pulseAnim.interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [0.3, 1, 0.3],
                  }),
                  marginLeft: scaleSize(10),
                },
              ]}
            />
            <Animated.View
              style={[
                styles.scanningDot,
                {
                  opacity: pulseAnim.interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [0.3, 1, 0.3],
                  }),
                  marginLeft: scaleSize(10),
                },
              ]}
            />
          </View>
        </Animated.View>
      )}

      {/* Manual barcode input - Always rendered and ALWAYS ENABLED */}
      <Animated.View
        style={[
          styles.manualInputContainer,
          {
            bottom: keyboardHeight,
            paddingBottom: keyboardHeight.interpolate({
              inputRange: [0, 100],
              outputRange: [scaleSize(20), scaleSize(30)],
            }),
          },
        ]}
      >
        <Ionicons
          name="barcode-outline"
          size={scaleSize(34)}
          color={theme.colors.primaryTextColor}
          style={styles.barcodeIcon}
        />
        <TextInput
          ref={manualInputRef}
          style={styles.manualInput}
          placeholder="Manually Enter Barcode"
          placeholderTextColor={theme.colors.primaryTextColor}
          value={manualBarcode}
          onChangeText={setManualBarcode}
          onFocus={() => setIsTextInputFocused(true)}
          keyboardType="number-pad"
          maxLength={20}
          returnKeyType="search"
          onSubmitEditing={handleManualSearch}
          editable={true} // ALWAYS ENABLED
        />
        <TouchableOpacity
          style={styles.manualSearchButton}
          onPress={handleManualSearch}
          disabled={loading} // Only disable when loading
        >
          <MaterialIcons name="check" size={scaleSize(24)} color="white" />
        </TouchableOpacity>
      </Animated.View>

      <TouchableWithoutFeedback onPress={dismissKeyboard} accessible={false}>
        <View style={{ flex: 1 }}>
          {/* Camera view - Conditionally rendered */}
          {isVisible && permission?.granted && (
            <CameraView
              ref={cameraRef}
              style={styles.camera}
              onCameraReady={handleCameraReady}
              facing={facing}
              onBarcodeScanned={loading ? undefined : handleBarcodeScanned}
              barcodeScannerSettings={{
                barcodeTypes: ["ean13", "upc_a", "upc_e", "ean8", "code128"],
              }}
              androidCameraPermissionOptions={{
                title: "Camera Permission",
                message: "We need access to your camera",
                buttonPositive: "OK",
                buttonNegative: "Cancel",
              }}
            >
              {/* Translucent masks */}
              <View style={styles.maskTop} />
              <View style={styles.maskLeft} />
              <View style={styles.maskRight} />
              <View style={styles.maskBottom} />

              {/* Clear overlay */}
              <Animated.View
                style={[
                  styles.targetAreaClearOverlay,
                  {
                    opacity: keyboardHeight.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, 0.7],
                    }),
                  },
                ]}
              />

              {!isTextInputFocused && (
                <>
                  <View style={styles.scanbarCodeInstructionsContainer}>
                    <Text style={styles.scanText}>Scan Barcode</Text>
                    <Text style={styles.instructionText}>
                      Keep the barcode in the frame to scan it
                    </Text>
                  </View>

                  <View style={styles.targetArea}>
                    <View style={styles.targetBorder} />
                  </View>
                </>
              )}

              {/* Camera loading indicator */}
              {!isCameraReady && !loading && (
                <View style={styles.cameraLoadingOverlay}>
                  {!cameraTimeout ? (
                    <>
                      <ActivityIndicator size="large" color="white" />
                      <Text style={styles.loadingText}>
                        {permission?.status === "granted"
                          ? "Starting camera..."
                          : "Requesting access..."}
                      </Text>
                    </>
                  ) : (
                    <Text style={styles.loadingText}>Camera is disabled</Text>
                  )}
                </View>
              )}

              {/* Close button */}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={handleClose}
              >
                <MaterialIcons
                  name="keyboard-arrow-left"
                  size={scaleSize(40)}
                  color="white"
                />
              </TouchableOpacity>

              {/* Flip camera button */}
              <TouchableOpacity
                style={styles.flipButton}
                onPress={toggleCameraFacing}
              >
                <MaterialIcons
                  name="flip-camera-ios"
                  size={scaleSize(30)}
                  color="white"
                />
              </TouchableOpacity>
            </CameraView>
          )}

          {/* Permission message if needed */}
          {isVisible && permission && !permission.granted && (
            <View style={styles.permissionContainer}>
              <Text style={styles.permissionText}>
                {permission.canAskAgain
                  ? "Camera access is required to scan barcodes and capture meal photos"
                  : "Camera permission is required for barcode scanning. Please enable in settings"}
              </Text>
              <Button
                title={
                  permission.canAskAgain ? "Grant Permission" : "Open Settings"
                }
                onPress={
                  permission.canAskAgain
                    ? requestPermission
                    : Linking.openSettings
                }
              />
            </View>
          )}
          {/* Error message */}
          {error && (
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              {error}
            </Text>
          )}
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default BarcodeScanner;
