import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import Modal from "react-native-modal";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useNutritionProgram } from "../../NutritionalProgram/context/NutritionProgramContext.js";
import * as Haptics from "expo-haptics";
import { MaterialIcons } from "@expo/vector-icons";
import { scaleSize } from "../../../utils/deviceUtils.js";
const NutritionGoalsAlignmentModal = ({ isVisible, onClose }) => {
  const { theme } = useThemeContext();
  const { setCalorieAndMacroGoals } = useUserSettings();
  const { activeNutritionalProgram } = useNutritionProgram();

  const programNutritionalGoals =
    activeNutritionalProgram?.nutritionalGoals || {};
  const programMacroGoals = programNutritionalGoals.macroGoals || {};

  const handleAutoAlign = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      setCalorieAndMacroGoals({
        calorieGoal: programNutritionalGoals.calorieGoal || 0,
        proteinPercentage:
          (programMacroGoals.protein?.dailyPercentage || 0) * 100,
        carbPercentage: (programMacroGoals.carb?.dailyPercentage || 0) * 100,
        fatPercentage: (programMacroGoals.fat?.dailyPercentage || 0) * 100,
      });

      // Close the modal after updating nutritional goals
      onClose && onClose();
    } catch (error) {
      console.error("Error auto-aligning nutritional goals:", error);
    }
  };

  const handleIgnore = () => {
    // Close the modal without making changes
    onClose && onClose();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Modal
      isVisible={isVisible}
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View
        style={{
          backgroundColor: theme.colors.surface,
          borderRadius: scaleSize(10),
          padding: scaleSize(20),
          width: "90%",
          alignItems: "center",
          position: "relative", // Enable absolute positioning for the icon
        }}
      >
        {/* Alert Icon */}
        <MaterialIcons
          name="error-outline" // Use an appropriate icon name
          size={scaleSize(26)} // Adjust size as needed
          color="red" // Adjust color as needed
          style={{
            position: "absolute", // Position it absolutely
            top: scaleSize(10), // Adjust position
            left: scaleSize(10), // Adjust position
          }}
        />

        <Text
          style={{
            fontSize: scaleSize(18),
            fontWeight: "bold",
            marginBottom: scaleSize(15),
            color: theme.colors.primaryTextColor,
          }}
        >
          Align Nutritional Goals
        </Text>

        {/* Modal Content */}
        <View style={{ width: "100%", marginBottom: scaleSize(15) }}>
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              marginBottom: scaleSize(10),
              fontSize: scaleSize(14),
            }}
          >
            Your current nutritional goals don't match your active program:
          </Text>

          <View
            style={{
              backgroundColor: theme.colors.screenBackground,
              borderRadius: scaleSize(5),
              padding: scaleSize(10),
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: scaleSize(8),
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: scaleSize(14),
                }}
              >
                Calories:
              </Text>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: scaleSize(14),
                }}
              >
                {programNutritionalGoals.calorieGoal || 0} cal
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: scaleSize(8),
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: scaleSize(14),
                }}
              >
                Protein:
              </Text>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(14),
                }}
              >
                {(
                  (programMacroGoals.protein?.dailyPercentage || 0) * 100
                ).toFixed(0)}
                % ({programMacroGoals.protein?.dailyGrams || 0}g)
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 8,
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: scaleSize(14),
                }}
              >
                Carbs:
              </Text>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(14),
                }}
              >
                {((programMacroGoals.carb?.dailyPercentage || 0) * 100).toFixed(
                  0
                )}
                % ({programMacroGoals.carb?.dailyGrams || 0}g)
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: scaleSize(8),
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontWeight: "bold",
                  fontSize: scaleSize(14),
                }}
              >
                Fat:
              </Text>
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(14),
                }}
              >
                {((programMacroGoals.fat?.dailyPercentage || 0) * 100).toFixed(
                  0
                )}
                % ({programMacroGoals.fat?.dailyGrams || 0}g)
              </Text>
            </View>
          </View>
        </View>

        {/* Buttons */}
        <View
          style={{
            flexDirection: "row-reverse",
            justifyContent: "space-between",
            gap: scaleSize(6),
          }}
        >
          <TouchableOpacity
            onPress={handleAutoAlign}
            style={{
              backgroundColor: theme.colors.primary,
              borderRadius: scaleSize(5),
              flex: 2,
              alignItems: "center",
              justifyContent: "center",
              paddingHorizontal: scaleSize(4),
              paddingVertical: scaleSize(8),
            }}
          >
            <Text
              style={{
                color: "white",
                fontWeight: "bold",
                fontSize: scaleSize(14),
              }}
            >
              Update Nutritional Goals
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleIgnore}
            style={{
              backgroundColor: "red",
              borderRadius: 5,
              flex: 1,
              alignItems: "center",
              justifyContent: "center",
              paddingHorizontal: 4,
              paddingVertical: 8,
            }}
          >
            <Text
              style={{
                color: "white",
                fontWeight: "bold",
                fontSize: scaleSize(14),
              }}
            >
              Ignore
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default NutritionGoalsAlignmentModal;
