import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  View,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  Keyboard,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { Image } from "expo-image";
import { TextInput, Appbar, ProgressBar, IconButton } from "react-native-paper";
import * as Haptics from "expo-haptics";
import Modal from "react-native-modal";
import { Ionicons, Feather } from "@expo/vector-icons";
import foodNutrientModalStyles from "./styles/foodNutrientModalStyles.js";
import Svg, { Circle, G, Text as SvgText, Path } from "react-native-svg";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { useFoodLog } from "../context/FoodLogContext.js";
import { useFoodMenu } from "../../FoodMenu/context/FoodMenuContext.js";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useNavigation } from "@react-navigation/native";
import { scaleSize } from "../../../utils/deviceUtils.js";
import Configs from "../../../../configs.js";

const FoodNutrientModal = React.memo(
  ({
    isVisible, // From modal context
    closeModal, // From modal context
    activeMealSection,
    isBuildingMeal,
    foodNutrientModalType,
    toggleSnackbar,
  }) => {
    // Ref-based synchronization system
    const activeFoodItemRef = useRef(null); // Tracks the current food item from parent context
    const [localFoodItem, setLocalFoodItem] = useState({}); // Local copy for UI updates
    const initializedRef = useRef(false); // Flag for first-time initialization
    const previousServingSizeRef = useRef(null); // Cache previous serving measure
    const tempActiveFoodItemRef = useRef(null); // Base item for scaling calculations
    const debounceTimeout = useRef(null); // Timeout reference for serving input
    const abortController = useRef(new AbortController()); // API request cancellation
    const isMounted = useRef(false); // Component mount state tracking

    const apiUrl = Configs.NutraCompass_API_URL;
    const navigation = useNavigation();
    const styles = foodNutrientModalStyles();
    const { theme, mode } = useThemeContext();
    const { user } = useAuth();
    const userId = user?.uid;

    const { selectedDate, getSelectedDateAsDate } = useTime();
    const {
      getActiveFoodItem,
      setActiveFoodItem,
      mealSections,
      saveOrUpdateSingleFoodItemToFoodLog,
      saveMealToFoodLog,
    } = useFoodLog();
    const { saveOrUpdateCustomMeal, saveFoodToTempCustomMeal } = useFoodMenu();
    const { getNutritionalGoals } = useUserSettings();
    const { calorieGoal, macroGoals } = getNutritionalGoals();

    // State initialization with safe defaults
    const [showItemList, setShowItemList] = useState(false); // Toggle ingredient list
    const [showNutritionFactsList, setShowNutritionFactsList] = useState(false); // Toggle core nutrients
    const [showVitaminList, setShowVitaminList] = useState(false); // Toggle vitamins
    const [showMineralList, setShowMineralList] = useState(false); // Toggle minerals
    const [selectedServing, setSelectedServing] = useState(
      // Active serving measure
      localFoodItem?.activeMeasure ?? null
    );
    const [numberOfServings, setNumberOfServings] = useState(
      // User-input servings
      localFoodItem?.numberOfServings ?? 1
    );
    const [selectedMealSection, setSelectedMealSection] = useState(() => {
      // Meal type selection
      if (localFoodItem?.mealType) return localFoodItem.mealType;
      return mealSections.find((m) => m.id === activeMealSection) || null;
    });
    const [isSelectServingSizeVisible, setIsSelectServingSizeVisible] = // Serving picker UI
      useState(false);
    const [isSelectMealVisible, setIsSelectMealVisible] = useState(false); // Meal type picker UI
    const [disableInputs, setDisableInputs] = useState(false); // Input lock for custom meals

    const screenWidth = Dimensions.get("window").width;

    const calorieColor = "blue";
    const carbColor = "orange";
    const proteinColor = "green";
    const fatColor = "red";

    // 1. Parent State Synchronization Effect
    // --------------------------------------
    // Purpose: Handle external changes from parent (new food items, modal visibility)
    useEffect(() => {
      if (!isVisible || !getActiveFoodItem) return;

      const latestItem = getActiveFoodItem() || {};
      activeFoodItemRef.current = latestItem;

      // Only reset state for new food items
      // Force reset for custom meals to ensure nested data loads
      if (
        latestItem.foodId !== localFoodItem?.foodId ||
        (latestItem.isCustomMeal && !localFoodItem?.mealItems)
      ) {
        console.log("[Sync] New food item detected");
        setLocalFoodItem(latestItem);
        setNumberOfServings(latestItem.numberOfServings ?? 1);
        setSelectedServing(latestItem.activeMeasure ?? null);
        initializedRef.current = false; // Force re-initialization
      }
    }, [isVisible, getActiveFoodItem, localFoodItem?.foodId]);

    // 2. Main Data Synchronization Effect
    // -----------------------------------
    // Purpose: Handle initialization and serving size changes
    useEffect(() => {
      console.log("[Main Sync] Running", activeFoodItemRef.current?.foodLabel);

      const runSync = () => {
        if (!isVisible || !activeFoodItemRef.current) return;

        // One-time initialization
        if (!initializedRef.current) {
          console.log("[Main Sync] Initializing food item");

          // Set base scaling reference (1 serving)
          tempActiveFoodItemRef.current = scaleFoodItem(
            activeFoodItemRef.current,
            1
          );

          // Set meal section and input states
          const mealSection = mealSections.find(
            (m) => m.id === activeFoodItemRef.current?.mealType
          );
          setSelectedMealSection(
            activeMealSection || mealSection || mealSections[0]
          );
          setDisableInputs(
            Boolean(activeFoodItemRef.current?.isCustomMeal) && isBuildingMeal
          );

          initializedRef.current = true;
        }

        // Track serving size changes without resetting servings
        if (
          activeFoodItemRef.current.activeMeasure !==
          previousServingSizeRef.current
        ) {
          console.log("[Main Sync] Serving measure changed");
          previousServingSizeRef.current =
            activeFoodItemRef.current.activeMeasure;

          // Update scaling base while preserving current servings
          const baseItem = scaleFoodItem(activeFoodItemRef.current, 1);
          tempActiveFoodItemRef.current = baseItem;
        }
      };

      runSync();

      return () => {
        console.log("[Main Sync] Cleanup");
        abortController.current.abort();
      };
    }, [
      isVisible,
      mealSections,
      activeMealSection,
      isBuildingMeal,
      activeFoodItemRef.current?.activeMeasure?.uri,
    ]);

    // Cleanup effect
    useEffect(() => {
      return () => {
        if (!isVisible) {
          setSelectedServing(null);
          setNumberOfServings(1);
          tempActiveFoodItemRef.current = null;
          initializedRef.current = false;
        }
      };
    }, [isVisible]);

    // Component lifecycle
    useEffect(() => {
      isMounted.current = true;
      return () => {
        isMounted.current = false;
        abortController.current.abort();
        clearTimeout(debounceTimeout.current);
      };
    }, []);

    const debouncedUpdateServings = useCallback(
      (servings) => {
        clearTimeout(debounceTimeout.current);
        abortController.current.abort();

        if (!tempActiveFoodItemRef.current) {
          console.error("[Debounce] No base item to scale from");
          return;
        }

        debounceTimeout.current = setTimeout(async () => {
          try {
            // Scales from tempActiveFoodItemRef which already has correct measure
            const scaledItem = scaleFoodItem(
              tempActiveFoodItemRef.current,
              servings
            );

            if (!scaledItem) {
              console.error("[Debounce] Scaling returned undefined");
              return;
            }

            setActiveFoodItem((prev) => {
              const updated = { ...prev, ...scaledItem };
              // console.log(
              //   "[State] Updated item:",
              //   JSON.stringify(updated.nutrients, null, 2)
              // );
              return updated;
            });

            setLocalFoodItem(scaledItem);
          } catch (error) {
            console.error("[Debounce] Final scaling error:", error);
          }
        }, 500);
      },
      [scaleFoodItem]
    );

    const handleServingsChange = (text) => {
      // Allow only numbers and decimal points
      const numericValue = text
        .replace(/[^0-9.]/g, "")
        .replace(/(\..*)\./g, "$1");

      // Always update input display
      setNumberOfServings(numericValue);

      // Handle empty state and invalid numbers
      const parsed = numericValue ? parseFloat(numericValue) : 0;

      if (!isNaN(parsed)) {
        const sanitized = Math.max(parsed, 0); // Only prevent negative numbers

        // Scale from current measure's base (tempActiveFoodItemRef)
        debouncedUpdateServings(sanitized);
      } else {
        // Fallback to 0 for invalid values
        debouncedUpdateServings(0);
      }
    };

    // Serving size selection
    const handleSelectServing = async (servingSizeOption) => {
      if (!activeFoodItemRef.current || !servingSizeOption) return;

      try {
        const newMeasure = servingSizeOption;

        // Update backend
        const response = await fetch(
          `${apiUrl}/v1/food/nutrients/serving-size?userId=${userId}`,
          {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              userId,
              foodItem: {
                ...scaleFoodItem(activeFoodItemRef.current, numberOfServings),
              },
              newServing: newMeasure,
            }),
          }
        );

        const { data: updatedItem } = await response.json();

        // Update references and state
        activeFoodItemRef.current = updatedItem;
        tempActiveFoodItemRef.current = scaleFoodItem(updatedItem, 1); // Base for new measure
        previousServingSizeRef.current = newMeasure;

        // Update UI state
        setActiveFoodItem(updatedItem);
        setLocalFoodItem(updatedItem);
        setSelectedServing(newMeasure);
      } catch (error) {
        console.error("Serving size update failed:", error);
      }
    };

    // Scaling function for custom meals
    const scaleFoodItem = (item, targetServings) => {
      if (!item?.nutrients) {
        console.error("[Scaling] Invalid item:", item);
        return item;
      }

      // Improved cloning function
      const deepClone = (obj) => {
        if (obj === null || typeof obj !== "object") return obj;
        let clone = Array.isArray(obj) ? [] : {};
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            clone[key] = deepClone(obj[key]);
          }
        }
        return clone;
      };

      try {
        const scaledItem = deepClone(item);
        const currentServings = scaledItem.numberOfServings || 1;
        const servingFactor = targetServings / currentServings;

        // Ensure all nutrient categories exist
        scaledItem.nutrients = scaledItem.nutrients || {};
        scaledItem.nutrients.core = scaleCategory(
          scaledItem.nutrients.core || {},
          servingFactor
        );
        scaledItem.nutrients.vitamins = scaleCategory(
          scaledItem.nutrients.vitamins || {},
          servingFactor
        );
        scaledItem.nutrients.minerals = scaleCategory(
          scaledItem.nutrients.minerals || {},
          servingFactor
        );

        // Recursively scale meal items with proper reference maintenance
        if (item.isCustomMeal && item.mealItems) {
          scaledItem.mealItems = item.mealItems.map((mealItem) => {
            // Calculate scaled servings for nested items
            const nestedServings = mealItem.numberOfServings * servingFactor;
            const scaledNestedItem = scaleFoodItem(mealItem, nestedServings);

            // Preserve critical identifiers
            return {
              ...scaledNestedItem,
              id: mealItem.id, // Maintain original ID
              foodId: mealItem.foodId,
            };
          });
        }

        scaledItem.numberOfServings = _roundToTwo(targetServings);
        return scaledItem;
      } catch (error) {
        console.error("[Scaling] Error:", error);
        return item;
      }
    };

    // Local scaling functions
    const scaleCategory = (category, servingFactor) => {
      return Object.entries(category || {}).reduce((acc, [key, nutrient]) => {
        const scaled = {
          ...nutrient,
          quantity: _roundToTwo(nutrient.quantity * servingFactor),
          ...(nutrient.totalDaily?.quantity && {
            totalDaily: {
              ...nutrient.totalDaily,
              quantity: _roundToTwo(
                nutrient.totalDaily.quantity * servingFactor
              ),
            },
          }),
        };
        acc[key] = scaled;
        return acc;
      }, {});
    };

    const _roundToTwo = (num) =>
      Math.round((Number(num) + Number.EPSILON) * 100) / 100;

    const toggleSelectServing = () => {
      if (isSelectMealVisible) {
        toggleSelectMeal();
      }

      setIsSelectServingSizeVisible(!isSelectServingSizeVisible);
    };

    const toggleSelectMeal = () => {
      if (isSelectServingSizeVisible) {
        toggleSelectServing();
      }

      setIsSelectMealVisible(!isSelectMealVisible);
    };

    const hideSelectServingAndSelectMealOnTextInputFocus = () => {
      if (isSelectServingSizeVisible) setIsSelectServingSizeVisible(false);

      if (isSelectMealVisible) setIsSelectMealVisible(false);
    };

    const handleSelectMeal = (mealOption) => {
      if (isSelectMealVisible) {
        toggleSelectMeal(); // Close the meal options modal
      }
      setSelectedMealSection(mealOption);
    };

    const handleEditFoodEntryAndSave = () => {
      const foodItem = activeFoodItemRef.current;
      const scaledItem = scaleFoodItem(foodItem, numberOfServings);

      // If not building a meal, a selected meal section exists, selected date is valid,
      // an active food item is selected, and the active food item is a custom meal
      if (
        !isBuildingMeal &&
        selectedMealSection &&
        selectedDate &&
        activeFoodItemRef.current &&
        activeFoodItemRef.current?.isCustomMeal &&
        scaledItem
      ) {
        // Save the custom meal to the food log
        saveMealToFoodLog(selectedMealSection.id, selectedDate, scaledItem);
      }
      // If not building a meal, a selected meal section exists, selected date is valid,
      // and an active food item is selected
      else if (
        !isBuildingMeal &&
        selectedDate &&
        selectedMealSection &&
        activeFoodItemRef.current &&
        scaledItem
      ) {
        // Update or save the individual food item to the food log
        saveOrUpdateSingleFoodItemToFoodLog(
          activeFoodItemRef.current?.id,
          selectedMealSection.id,
          scaledItem
        );
      }
      // If building a meal and the active food item is a custom meal
      else if (
        isBuildingMeal &&
        activeFoodItemRef.current?.isCustomMeal &&
        scaledItem
      ) {
        // Save or update the custom meal
        saveOrUpdateCustomMeal(scaledItem);

        // if (navigation.getState().routeNames.includes("Custom Meals")) {
        //   navigation.navigate("Custom Meals");
        // }
      }
      // If building a meal and the active food item is not a custom meal
      else if (isBuildingMeal && activeFoodItemRef.current && scaledItem) {
        // Save the active food item to the temporary custom meal
        saveFoodToTempCustomMeal(scaledItem);
      }
      // If none of the above conditions are met
      else {
        // Log an error indicating an invalid state
        console.error(
          "Invalid state encountered while attempting to handle food entry: " +
            "isBuildingMeal:",
          isBuildingMeal,
          ", selectedMealSection:",
          selectedMealSection,
          ", selectedDate:",
          selectedDate,
          ", activeFoodItem:",
          activeFoodItemRef.current
        );
        return;
      }
      handleCloseModal();
      // Trigger snackbar if the user is not saving or updating a customMeal

      if (foodNutrientModalType === "Edit Entry") {
        return;
      } else if (foodNutrientModalType === "Edit Meal Item") {
        return;
      } else if (!(isBuildingMeal && activeFoodItemRef.current?.isCustomMeal)) {
        toggleSnackbar();
      }
    };

    const handleCloseModal = () => {
      setShowMineralList(false);
      setShowVitaminList(false);
      setShowNutritionFactsList(false);
      setLocalFoodItem(null);
      setActiveFoodItem(null);
      setIsSelectServingSizeVisible(false);
      setIsSelectMealVisible(false);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      closeModal();
    };

    const toggleItemList = () => {
      setShowItemList(!showItemList);
    };

    const toggleNutritionFactsList = () => {
      setShowNutritionFactsList(!showNutritionFactsList);
    };

    const toggleVitaminList = () => {
      setShowVitaminList(!showVitaminList);
    };

    const toggleMineralList = () => {
      setShowMineralList(!showMineralList);
    };

    /**
     * Calculates the percentage of calories from carbs, protein, and fat
     * relative to totalCalories. If the total exceeds 100%, the values are
     * scaled down uniformly.
     *
     * @param {number} totalCalories - The total calories for the food item.
     * @param {number} gramsOfCarbs
     * @param {number} gramsOfProtein
     * @param {number} gramsOfFat
     * @returns {{
     *   percentageOfCarbs: number,
     *   percentageOfProtein: number,
     *   percentageOfFat: number
     * }}
     */
    const calculateMacroPercentages = (
      totalCalories,
      gramsOfCarbs,
      gramsOfProtein,
      gramsOfFat
    ) => {
      if (
        isNaN(totalCalories) ||
        isNaN(gramsOfCarbs) ||
        isNaN(gramsOfProtein) ||
        isNaN(gramsOfFat) ||
        totalCalories <= 0
      ) {
        return {
          percentageOfCarbs: 0,
          percentageOfProtein: 0,
          percentageOfFat: 0,
        };
      }

      const totalCaloriesFloat = parseFloat(totalCalories);
      const caloriesFromCarbs = gramsOfCarbs * 4;
      const caloriesFromProtein = gramsOfProtein * 4;
      const caloriesFromFat = gramsOfFat * 9;

      // Calculate percentage values (which might be > 100)
      const calcPercentage = (macroCalories) => {
        const fraction = macroCalories / totalCaloriesFloat;
        return fraction < 0 ? 0 : fraction * 100;
      };

      let percentageOfCarbs = calcPercentage(caloriesFromCarbs);
      let percentageOfProtein = calcPercentage(caloriesFromProtein);
      let percentageOfFat = calcPercentage(caloriesFromFat);

      let totalPercentage =
        percentageOfCarbs + percentageOfProtein + percentageOfFat;

      // Scale down if total > 100%
      if (totalPercentage > 100) {
        const adjustmentFactor = 100 / totalPercentage;
        percentageOfCarbs *= adjustmentFactor;
        percentageOfProtein *= adjustmentFactor;
        percentageOfFat *= adjustmentFactor;
      }

      // Return numeric percentages, rounded to 2 decimals
      return {
        percentageOfCarbs: parseFloat(percentageOfCarbs.toFixed(2)),
        percentageOfProtein: parseFloat(percentageOfProtein.toFixed(2)),
        percentageOfFat: parseFloat(percentageOfFat.toFixed(2)),
      };
    };

    // Extract nutrient values for the food item:
    const totalCalories =
      localFoodItem?.nutrients?.core?.ENERC_KCAL?.quantity || 0;
    const gramsOfCarbs = localFoodItem?.nutrients?.core?.CHOCDF?.quantity || 0;
    const gramsOfProtein =
      localFoodItem?.nutrients?.core?.PROCNT?.quantity || 0;
    const gramsOfFat = localFoodItem?.nutrients?.core?.FAT?.quantity || 0;

    // Calculate macro percentages relative to the food item:
    const macroPercentagesInRelationToItemCalories = calculateMacroPercentages(
      totalCalories,
      gramsOfCarbs,
      gramsOfProtein,
      gramsOfFat
    );

    // console.log(
    //   "Macro Percentages in Relation To Item Calories: ",
    //   JSON.stringify(macroPercentagesInRelationToItemCalories, null, 2)
    // );

    // Calculate fractions for progress bars (should be numbers between 0 and 1)
    const safeCalorieGoal = calorieGoal || 1;
    const carbDailyGramsGoal = macroGoals?.carb?.dailyGrams || 1;
    const proteinDailyGramsGoal = macroGoals?.protein?.dailyGrams || 1;
    const fatDailyGramsGoal = macroGoals?.fat?.dailyGrams || 1;

    const caloriesFraction = Math.min(1, totalCalories / safeCalorieGoal);
    const carbsFraction = Math.min(1, gramsOfCarbs / carbDailyGramsGoal);
    const proteinFraction = Math.min(1, gramsOfProtein / proteinDailyGramsGoal);
    const fatFraction = Math.min(1, gramsOfFat / fatDailyGramsGoal);

    // console.log("Calories fraction (0-1):", caloriesFraction);
    // console.log("Carbs fraction (0-1):", carbsFraction);
    // console.log("Protein fraction (0-1):", proteinFraction);
    // console.log("Fat fraction (0-1):", fatFraction);

    // For display purposes, you may want to show the actual computed percentage (even if > 100)
    const caloriesDisplay = Math.round((totalCalories / safeCalorieGoal) * 100);
    const carbsDisplay = Math.round((gramsOfCarbs / carbDailyGramsGoal) * 100);
    const proteinDisplay = Math.round((gramsOfProtein / proteinDailyGramsGoal) * 100);
    const fatDisplay = Math.round((gramsOfFat / fatDailyGramsGoal) * 100);
    
    // console.log("Calories display percentage:", caloriesDisplay);
    // console.log("Carbs display percentage:", carbsDisplay);
    // console.log("Protein display percentage:", proteinDisplay);
    // console.log("Fat display percentage:", fatDisplay);

    const renderCircularChart = (
      carbsPercentage,
      fatPercentage,
      proteinPercentage,
      calories,
      size = scaleSize(90) // Default size with scaling
    ) => {
      const chartData = [
        { percentage: carbsPercentage, color: carbColor, label: "Carbs" },
        {
          percentage: proteinPercentage,
          color: proteinColor,
          label: "Protein",
        },
        { percentage: fatPercentage, color: fatColor, label: "Fat" },
      ];

      // Calculate dimensions based on the size parameter
      const radius = size / 2;
      const gap = scaleSize(5); // Scaled gap
      const innerRadius = radius - gap;

      let currentAngle = 0;

      return (
        <Svg height={size} width={size}>
          <G transform={{ translate: `${radius}, ${radius}` }}>
            {chartData.map((segment, index) => {
              const angle = (segment.percentage * 360) / 100;
              // Calculate the path for the entire circle (neutral background)
              const fullCirclePath = `M 0 -${radius} A ${radius} ${radius} 0 1 1 0 ${radius} A ${radius} ${radius} 0 1 1 0 -${radius}`;
              const path = `M 0 0 L ${
                radius * Math.cos((currentAngle * Math.PI) / 180)
              } ${
                radius * Math.sin((currentAngle * Math.PI) / 180)
              } A ${radius} ${radius} 0 ${angle > 180 ? 1 : 0} 1 ${
                radius * Math.cos(((currentAngle + angle) * Math.PI) / 180)
              } ${
                radius * Math.sin(((currentAngle + angle) * Math.PI) / 180)
              } Z`;
              currentAngle += angle;

              return (
                <React.Fragment key={index}>
                  <Path
                    d={fullCirclePath}
                    fill={
                      mode === "dark"
                        ? "rgba(255, 255, 255, 0.1)"
                        : "rgba(0, 0, 0, 0.1)"
                    }
                  />
                  <Path d={path} fill={segment.color} />
                </React.Fragment>
              );
            })}
            <Circle r={innerRadius} fill={theme.colors.surface} />

            <SvgText
              fill={theme.colors.primaryTextColor}
              fontSize={scaleSize(16)} // Scaled font
              fontWeight="bold"
              textAnchor="middle"
              alignmentBaseline="middle"
              transform={`translate(0, ${-scaleSize(6)})`} // Scaled position
            >
              {Math.round(calories)}
            </SvgText>
            <SvgText
              fill={theme.colors.primaryTextColor}
              fontSize={scaleSize(12)} // Scaled font
              textAnchor="middle"
              alignmentBaseline="middle"
              transform={`translate(0, ${scaleSize(15)})`} // Scaled position
            >
              cal
            </SvgText>
          </G>
        </Svg>
      );
    };

    const renderCoreNutrientRows = (nutrientKeysOrder, isIndented = false) => {
      if (!Array.isArray(nutrientKeysOrder)) return;

      const NutrientComponent = isIndented
        ? NutritionFactsIndentedRow
        : NutritionFactsRow;

      return nutrientKeysOrder.map((nutrientKey, index) => (
        <NutrientComponent
          key={`${nutrientKey}_${index}`}
          label={localFoodItem?.nutrients?.core?.[nutrientKey]?.label}
          quantity={localFoodItem?.nutrients?.core?.[nutrientKey]?.quantity}
          unit={
            nutrientKey === "ENERC_KCAL"
              ? ""
              : localFoodItem?.nutrients?.core?.[nutrientKey]?.unit
          }
          totalDailyQuantity={
            localFoodItem?.nutrients?.core?.[nutrientKey]?.totalDaily?.quantity
          }
          totalDailyUnit={
            localFoodItem?.nutrients?.core?.[nutrientKey]?.totalDaily?.unit
          }
          color={theme.colors.primaryTextColor}
        />
      ));
    };

    const renderVitaminNutrientRows = (
      nutrientKeysOrder,
      isIndented = false
    ) => {
      if (!Array.isArray(nutrientKeysOrder)) return;

      const NutrientComponent = isIndented
        ? NutritionFactsIndentedRow
        : NutritionFactsRow;

      return nutrientKeysOrder.map((nutrientKey, index) => (
        <NutrientComponent
          key={`${nutrientKey}_${index}`}
          label={localFoodItem?.nutrients?.vitamins?.[nutrientKey]?.label}
          quantity={localFoodItem?.nutrients?.vitamins?.[nutrientKey]?.quantity}
          unit={localFoodItem?.nutrients?.vitamins?.[nutrientKey]?.unit}
          totalDailyQuantity={
            localFoodItem?.nutrients?.vitamins?.[nutrientKey]?.totalDaily
              ?.quantity
          }
          totalDailyUnit={
            localFoodItem?.nutrients?.vitamins?.[nutrientKey]?.totalDaily?.unit
          }
          color={theme.colors.primaryTextColor}
        />
      ));
    };

    const renderMineralNutrientRows = (
      nutrientKeysOrder,
      isIndented = false
    ) => {
      if (!Array.isArray(nutrientKeysOrder)) return;

      const NutrientComponent = isIndented
        ? NutritionFactsIndentedRow
        : NutritionFactsRow;

      return nutrientKeysOrder.map((nutrientKey, index) => (
        <NutrientComponent
          key={`${nutrientKey}_${index}`}
          label={localFoodItem?.nutrients?.minerals?.[nutrientKey]?.label}
          quantity={localFoodItem?.nutrients?.minerals?.[nutrientKey]?.quantity}
          unit={localFoodItem?.nutrients?.minerals?.[nutrientKey]?.unit}
          totalDailyQuantity={
            localFoodItem?.nutrients?.minerals?.[nutrientKey]?.totalDaily
              ?.quantity
          }
          totalDailyUnit={
            localFoodItem?.nutrients?.minerals?.[nutrientKey]?.totalDaily?.unit
          }
          color={theme.colors.primaryTextColor}
        />
      ));
    };

    const vitaminKeys = [
      "VITA_RAE",
      "VITC",
      "VITD",
      "TOCPHA",
      "VITK1",
      "THIA",
      "RIBF",
      "NIA",
      "VITB6A",
      "FOLDFE",
      "VITB12",
    ];
    const mineralKeys = ["CA", "FE", "MG", "P", "K", "NA", "ZN"];

    const conversionFactors = {
      ounce: 0.035274,
      pound: 0.00220462,
      kilogram: 0.001,
      gram: 1,
      pinch: 0.355, // Assuming a pinch is around 0.355 grams
      liter: 0.001, // Assuming density similar to water
      fluid_ounce: 0.0295735,
      gallon: 0.00378541,
      pint: 0.00211338,
      quart: 0.00105669,
      milliliter: 1,
      drop: 0.05, // Assuming a drop is about 0.05 ml
      cup: 0.00422675,
      tablespoon: 0.0147868,
      teaspoon: 0.00492892,
    };

    function convertGramsToUnit(grams, unit) {
      console.log("Grams: ", grams);
      console.log("Unit: ", unit);
      console.log(
        "Converted Value: ",
        grams * conversionFactors[unit.toLowerCase()]
      );
      return grams * conversionFactors[unit.toLowerCase()];
    }

    return (
      <View
        style={{
          height: "100%",
          width: "100%",
          backgroundColor: theme.colors.screenBackground,
        }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.modalContentContainer}>
            {/* Custom Header */}
            <View style={styles.customHeader}>
              {/* Back Button */}
              <TouchableOpacity
                onPress={handleCloseModal}
                style={styles.headerButton}
              >
                <Ionicons
                  name="chevron-back"
                  size={scaleSize(32)}
                  color={theme.colors.primaryTextColor}
                />
              </TouchableOpacity>

              {/* Title */}
              <Text style={styles.headerTitle}>{foodNutrientModalType}</Text>

              {/* Save Button */}
              <TouchableOpacity
                onPress={() => handleEditFoodEntryAndSave()}
                style={styles.headerButton}
              >
                <Ionicons
                  name="checkmark"
                  size={scaleSize(32)}
                  color={theme.colors.primary}
                />
              </TouchableOpacity>
            </View>

            {localFoodItem && (
              <ScrollView>
                <TouchableOpacity
                  onPress={() => {
                    if (isSelectServingSizeVisible) {
                      toggleSelectServing();
                    }
                    if (isSelectMealVisible) {
                      toggleSelectMeal();
                    }
                  }}
                  activeOpacity={1}
                  style={{
                    gap: scaleSize(10),
                  }}
                >
                  {/* Food Item Section */}
                  <View
                    style={{
                      ...styles.sectionContainer,
                      flexDirection: "row",
                      justifyContent: "space-between",
                      gap: scaleSize(5),
                    }}
                  >
                    <View style={{ flex: 1 }}>
                      {activeMealSection?.name && (
                        <Text
                          style={{
                            color: theme.colors.primaryTextColor,
                            fontSize: scaleSize(14),
                            paddingBottom: 4,
                          }}
                        >
                          {selectedMealSection?.name || activeMealSection?.name}
                        </Text>
                      )}

                      <Text style={styles.foodItemName}>
                        {localFoodItem?.foodLabel}
                      </Text>
                      <Text style={styles.brandCompany}>
                        {localFoodItem?.foodBrand ||
                          localFoodItem?.foodCategory}
                      </Text>
                    </View>
                    {localFoodItem?.mealImageUrl && (
                      <View style={{ width: "40%" }}>
                        <Image
                          source={{
                            uri: localFoodItem?.mealImageUrl,
                          }}
                          style={{
                            width: scaleSize(150),
                            height: scaleSize(150),
                            borderRadius: scaleSize(10),
                            borderWidth: scaleSize(1),
                          }}
                        />
                      </View>
                    )}
                  </View>
                  {/* Input Fields Section */}
                  <View style={styles.sectionContainer}>
                    {/* Inputs that are disabled if its activeFoodItem is a custom meal */}
                    {!disableInputs && (
                      <View>
                        <View style={{ marginBottom: scaleSize(16) }}>
                          <ServingSizeRow
                            isVisible={isSelectServingSizeVisible}
                            toggleSelectServing={toggleSelectServing}
                            label="Serving Size"
                            selectedServing={selectedServing}
                            isCustomMeal={localFoodItem?.isCustomMeal}
                          />
                        </View>
                        <View style={{ marginBottom: scaleSize(16) }}>
                          <NumberOfServingsRow
                            label="Number of Servings"
                            keyboardType="numeric"
                            numberOfServings={numberOfServings}
                            updateNumberOfServings={handleServingsChange}
                            hideSelectServingAndSelectMealOnTextInputFocus={
                              hideSelectServingAndSelectMealOnTextInputFocus
                            }
                          />
                        </View>

                        {!isBuildingMeal && (
                          <View style={{ marginBottom: scaleSize(16) }}>
                            <MealRow
                              isVisible={isSelectMealVisible}
                              toggleSelectMeal={toggleSelectMeal}
                              label="Meal"
                              selectedMealOption={selectedMealSection}
                            />
                          </View>
                        )}
                      </View>
                    )}

                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      {/* Macronutrient Content: Carbs, Protein, Fat */}
                      {/* Example data, replace with actual values */}
                      {/* Circular Chart that shows calorie and macronutrient breakdown */}
                      <View style={styles.macroNutrientColumn}>
                        {renderCircularChart(
                          macroPercentagesInRelationToItemCalories.percentageOfCarbs,
                          macroPercentagesInRelationToItemCalories.percentageOfFat,
                          macroPercentagesInRelationToItemCalories.percentageOfProtein,
                          totalCalories,
                          scaleSize(100)
                        )}
                      </View>

                      <View style={styles.macroNutrientColumn}>
                        <Text
                          style={{
                            ...styles.macroNutrientPercentage,
                            color: carbColor,
                          }}
                        >
                          {Math.round(
                            macroPercentagesInRelationToItemCalories.percentageOfCarbs
                          )}
                          %
                        </Text>
                        <Text style={styles.macroNutrientValue}>
                          {Math.round(gramsOfCarbs)}{" "}
                          {localFoodItem?.nutrients?.core?.CHOCDF?.unit}
                        </Text>
                        <Text style={styles.macroNutrientLabel}>Carbs</Text>
                      </View>
                      <View style={styles.macroNutrientColumn}>
                        <Text
                          style={{
                            ...styles.macroNutrientPercentage,
                            color: proteinColor,
                          }}
                        >
                          {Math.round(
                            macroPercentagesInRelationToItemCalories.percentageOfProtein
                          )}
                          %
                        </Text>
                        <Text style={styles.macroNutrientValue}>
                          {Math.round(gramsOfProtein)}{" "}
                          {localFoodItem?.nutrients?.core?.PROCNT?.unit}
                        </Text>
                        <Text style={styles.macroNutrientLabel}>Protein</Text>
                      </View>
                      <View style={styles.macroNutrientColumn}>
                        <Text
                          style={{
                            ...styles.macroNutrientPercentage,
                            color: fatColor,
                          }}
                        >
                          {Math.round(
                            macroPercentagesInRelationToItemCalories.percentageOfFat
                          )}
                          %
                        </Text>
                        <Text style={styles.macroNutrientValue}>
                          {Math.round(gramsOfFat)}{" "}
                          {localFoodItem?.nutrients?.core?.FAT?.unit}
                        </Text>
                        <Text style={styles.macroNutrientLabel}>Fat</Text>
                      </View>
                    </View>
                  </View>
                  {/* Calorie and Macronutrient Percent of Daily Goals */}
                  <View style={styles.sectionContainer}>
                    <Text style={styles.nutritionFactsLabel}>
                      Percent of Daily Goals
                    </Text>
                    <View style={styles.progressContainer}>
                      {/* Progress bar for Calories */}
                      <View style={styles.progressItem}>
                        <ProgressBar
                          progress={caloriesFraction} // Fraction between 0 and 1
                          color={calorieColor}
                          style={{
                            height: scaleSize(10),
                            width: screenWidth * 0.22,
                            borderRadius: scaleSize(5),
                          }}
                        />
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          {caloriesDisplay}%
                        </Text>
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            fontWeight: "bold",
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          Calories
                        </Text>
                      </View>
                      {/* Progress bar for Carbs */}
                      <View style={styles.progressItem}>
                        <ProgressBar
                          progress={carbsFraction}
                          color={carbColor}
                          style={{
                            height: scaleSize(10),
                            width: screenWidth * 0.22,
                            borderRadius: scaleSize(5),
                          }}
                        />
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          {carbsDisplay}%
                        </Text>
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            fontWeight: "bold",
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          Carbs
                        </Text>
                      </View>
                      {/* Progress bar for Protein */}
                      <View style={styles.progressItem}>
                        <ProgressBar
                          progress={proteinFraction}
                          color={proteinColor}
                          style={{
                            height: scaleSize(10),
                            width: screenWidth * 0.22,
                            borderRadius: scaleSize(5),
                          }}
                        />
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          {proteinDisplay}%
                        </Text>
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            fontWeight: "bold",
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          Protein
                        </Text>
                      </View>
                      {/* Progress bar for Fat */}
                      <View style={styles.progressItem}>
                        <ProgressBar
                          progress={fatFraction}
                          color={fatColor}
                          style={{
                            height: scaleSize(10),
                            width: screenWidth * 0.22,
                            borderRadius: scaleSize(5),
                          }}
                        />
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          {fatDisplay}%
                        </Text>
                        <Text
                          style={{
                            fontSize: scaleSize(12),
                            fontWeight: "bold",
                            color: theme.colors.primaryTextColor,
                          }}
                        >
                          Fat
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Render Meal Items Section if it's a custom meal */}
                  {localFoodItem?.isCustomMeal && (
                    <View style={styles.sectionContainer}>
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                        }}
                      >
                        <Text style={styles.nutritionFactsLabel}>Items</Text>
                        <TouchableOpacity
                          onPress={toggleItemList}
                          style={{
                            flexDirection: "row",
                            gap: scaleSize(5),
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontWeight: "bold",
                              fontSize: scaleSize(14),
                            }}
                          >
                            {showItemList ? "Hide" : "Show"}
                          </Text>
                          <Feather
                            name={showItemList ? "arrow-up" : "arrow-down"}
                            color={theme.colors.primary}
                            size={scaleSize(20)}
                          />
                        </TouchableOpacity>
                      </View>

                      {/* Meal Items */}
                      {showItemList &&
                        localFoodItem?.mealItems.map((item, index) => (
                          <View
                            style={{
                              flexDirection: "row",
                              justifyContent: "space-between",
                              padding: scaleSize(10),
                              paddingTop: scaleSize(14),
                              borderBottomWidth: scaleSize(1),
                              borderBottomColor: theme.colors.cardBorderColor,
                            }}
                            key={index}
                          >
                            <Text
                              style={{
                                flex: 2,
                                fontSize: scaleSize(16),
                                color: theme.colors.primaryTextColor,
                              }}
                            >
                              {item.foodCategory === "Quick Add" &&
                                "Quick Add - "}
                              {item.foodLabel}
                            </Text>
                            <View style={{ flex: 1, gap: scaleSize(5) }}>
                              <Text
                                style={{
                                  fontSize: scaleSize(14),
                                  color: theme.colors.primaryTextColor,
                                }}
                              >
                                {Math.round(
                                  item.nutrients.core.ENERC_KCAL.quantity
                                )}{" "}
                                cal
                              </Text>
                              <Text
                                style={{
                                  fontSize: scaleSize(14),
                                  color: theme.colors.primaryTextColor,
                                }}
                              >
                                {item.foodCategory !== "Quick Add" ? (
                                  item.activeMeasure ? (
                                    <>
                                      {Math.round(item.numberOfServings)}{" "}
                                      {item.activeMeasure.label}
                                    </>
                                  ) : (
                                    "N/A"
                                  )
                                ) : (
                                  "N/A"
                                )}
                              </Text>
                            </View>
                          </View>
                        ))}
                    </View>
                  )}
                  {/* Nutritional Information List Toggle */}
                  <View style={styles.sectionContainer}>
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <Text style={styles.nutritionFactsLabel}>
                        Nutrition Facts
                      </Text>
                      <TouchableOpacity
                        onPress={toggleNutritionFactsList}
                        style={{
                          flexDirection: "row",
                          gap: scaleSize(5),
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Text
                          style={{
                            color: theme.colors.primary,
                            fontWeight: "bold",
                            fontSize: scaleSize(14),
                          }}
                        >
                          {showNutritionFactsList ? "Hide" : "Show"}
                        </Text>
                        <Feather
                          name={
                            showNutritionFactsList ? "arrow-up" : "arrow-down"
                          }
                          color={theme.colors.primary}
                          size={scaleSize(20)}
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                  {showNutritionFactsList && (
                    <View
                      style={{
                        ...styles.sectionContainer,
                        paddingHorizontal: 0,
                      }}
                    >
                      <Text
                        style={{
                          fontSize: scaleSize(14),
                          color: theme.colors.primaryTextColor,
                          alignSelf: "flex-end",
                          marginBottom: scaleSize(10),
                          paddingHorizontal: scaleSize(16),
                        }}
                      >
                        % Daily Value
                      </Text>
                      {renderCoreNutrientRows(["ENERC_KCAL"], false)}

                      <View style={styles.nutritionFactsSeparator} />

                      <View style={styles.nutritionFactsSection}>
                        {renderCoreNutrientRows(["FAT"], false)}
                      </View>

                      {renderCoreNutrientRows(
                        ["FASAT", "FATRN", "FAPU", "FAMS"],
                        true
                      )}

                      <View style={styles.nutritionFactsSeparator} />

                      <View style={styles.nutritionFactsSection}>
                        {renderCoreNutrientRows(["CHOLE"], false)}
                      </View>

                      <View style={styles.nutritionFactsSeparator} />

                      <View style={styles.nutritionFactsSection}>
                        {renderMineralNutrientRows(["NA"], false)}
                      </View>

                      <View style={styles.nutritionFactsSeparator} />

                      <View style={styles.nutritionFactsSection}>
                        {renderCoreNutrientRows(["CHOCDF"], false)}

                        {renderCoreNutrientRows(["FIBTG"], true)}

                        {renderCoreNutrientRows(["SUGAR"], true)}
                      </View>

                      <View style={styles.nutritionFactsSeparator} />

                      <View style={styles.nutritionFactsSection}>
                        {renderCoreNutrientRows(["PROCNT"], false)}
                      </View>

                      <View style={styles.nutritionFactsSeparator} />

                      <View style={styles.nutritionFactsSection}>
                        <View style={styles.nutritionFactsRow}>
                          <Text style={styles.nutritionFactsLabel}>
                            Vitamins
                          </Text>
                          <TouchableOpacity
                            onPress={toggleVitaminList}
                            style={{
                              flexDirection: "row",
                              gap: scaleSize(5),
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Text
                              style={{
                                color: theme.colors.primary,
                                fontWeight: "bold",
                                fontSize: scaleSize(14),
                              }}
                            >
                              {showVitaminList ? "Hide" : "Show"}
                            </Text>
                            <Feather
                              name={showVitaminList ? "arrow-up" : "arrow-down"}
                              color={theme.colors.primary}
                              size={scaleSize(20)}
                            />
                          </TouchableOpacity>
                        </View>
                        <View style={{ paddingTop: scaleSize(10) }}>
                          {showVitaminList &&
                            renderVitaminNutrientRows(vitaminKeys, true)}
                        </View>
                      </View>

                      <View style={styles.nutritionFactsSeparator} />

                      <View style={styles.nutritionFactsSection}>
                        <View style={styles.nutritionFactsRow}>
                          <Text style={styles.nutritionFactsLabel}>
                            Minerals
                          </Text>
                          <TouchableOpacity
                            onPress={toggleMineralList}
                            style={{
                              flexDirection: "row",
                              gap: scaleSize(5),
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Text
                              style={{
                                color: theme.colors.primary,
                                fontWeight: "bold",
                                fontSize: scaleSize(14),
                              }}
                            >
                              {showMineralList ? "Hide" : "Show"}
                            </Text>
                            <Feather
                              name={showMineralList ? "arrow-up" : "arrow-down"}
                              color={theme.colors.primary}
                              size={scaleSize(20)}
                            />
                          </TouchableOpacity>
                        </View>
                        <View style={{ paddingTop: scaleSize(10) }}>
                          {showMineralList &&
                            renderMineralNutrientRows(mineralKeys, true)}
                        </View>
                      </View>
                    </View>
                  )}
                </TouchableOpacity>
              </ScrollView>
            )}
            {isSelectServingSizeVisible && (
              <View
                style={{
                  position: "absolute",
                  bottom: 0,
                  right: 0,
                  left: 0,
                  top: "66%",
                  backgroundColor: theme.colors.screenBackground,
                  zIndex: 2,
                }}
              >
                <View style={{ flex: 1 }}>
                  <View
                    style={{
                      height: "20%",
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      paddingHorizontal: scaleSize(10),
                      borderBottomWidth: scaleSize(1),
                      borderBottomColor: "gray",
                    }}
                  >
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(20),
                      }}
                    >
                      Select Unit
                    </Text>
                    <IconButton
                      icon="close"
                      iconColor={theme.colors.primaryTextColor}
                      size={scaleSize(26)}
                      onPress={toggleSelectServing}
                    />
                  </View>
                  <ScrollView>
                    <TouchableOpacity activeOpacity={1}>
                      {localFoodItem &&
                        localFoodItem?.measures?.map(
                          (servingSizeOption, index) => {
                            // Determine if this option matches the active or selected serving
                            const isActiveMeasure =
                              servingSizeOption.uri ===
                              localFoodItem?.activeMeasure.uri;
                            const isSelected =
                              selectedServing?.uri === servingSizeOption.uri ||
                              isActiveMeasure;

                            return (
                              <TouchableOpacity
                                key={`${servingSizeOption?.uri}_${index}`}
                                onPress={() =>
                                  handleSelectServing(servingSizeOption)
                                }
                                style={{
                                  width: "100%",
                                  flexDirection: "row",
                                  alignItems: "center",
                                  justifyContent: "space-between",
                                  backgroundColor: isSelected
                                    ? theme.colors.surface
                                    : "transparent",
                                  marginVertical: scaleSize(5),
                                  padding: scaleSize(10),
                                }}
                              >
                                <View
                                  style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                    width: "85%",
                                    paddingLeft: scaleSize(10),
                                  }}
                                >
                                  <Text
                                    style={{
                                      color: theme.colors.primaryTextColor,
                                      fontSize: scaleSize(20),
                                      width: "15%",
                                      textAlign: "right",
                                    }}
                                  >
                                    {Math.round(servingSizeOption?.weight)}
                                  </Text>
                                  <Text
                                    style={{
                                      color: theme.colors.primaryTextColor,
                                      fontSize: scaleSize(20),
                                      width: "20%",
                                      paddingLeft: scaleSize(5),
                                    }}
                                  >
                                    grams
                                  </Text>
                                  <Text
                                    style={{
                                      color: theme.colors.primaryTextColor,
                                      fontSize: scaleSize(20),
                                      width: "10%",
                                      paddingLeft: scaleSize(5),
                                      textAlign: "center",
                                    }}
                                  >
                                    =
                                  </Text>
                                  <Text
                                    style={{
                                      color: theme.colors.primaryTextColor,
                                      fontSize: scaleSize(20),
                                      width: "5%",
                                      paddingLeft: scaleSize(5),
                                    }}
                                  >
                                    1
                                  </Text>
                                  <Text
                                    style={{
                                      color: theme.colors.primaryTextColor,
                                      fontSize: scaleSize(20),
                                      width: "50%",
                                      paddingLeft: scaleSize(5),
                                    }}
                                  >
                                    {servingSizeOption?.label}
                                  </Text>
                                </View>

                                <View
                                  style={{
                                    width: "15%",
                                    alignItems: "flex-end",
                                    paddingRight: scaleSize(5),
                                    minHeight: scaleSize(28), // Prevent layout shift
                                  }}
                                >
                                  {isSelected && (
                                    <Feather
                                      name="check-square"
                                      size={scaleSize(28)}
                                      color={theme.colors.primary}
                                    />
                                  )}
                                </View>
                              </TouchableOpacity>
                            );
                          }
                        )}
                    </TouchableOpacity>
                  </ScrollView>
                </View>
              </View>
            )}
            {isSelectMealVisible && (
              <View
                style={{
                  position: "absolute",
                  bottom: 0,
                  right: 0,
                  left: 0,
                  top: "66%",
                  backgroundColor: theme.colors.screenBackground,
                  zIndex: 2,
                }}
              >
                <View style={{ flex: 1 }}>
                  <View
                    style={{
                      height: "20%",
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      paddingHorizontal: scaleSize(10),
                      borderBottomWidth: scaleSize(1),
                      borderBottomColor: "gray",
                    }}
                  >
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(20),
                      }}
                    >
                      Select Meal
                    </Text>
                    <IconButton
                      icon="close"
                      iconColor={theme.colors.primaryTextColor}
                      size={scaleSize(26)}
                      onPress={toggleSelectMeal}
                    />
                  </View>
                  <ScrollView>
                    <TouchableOpacity activeOpacity={1}>
                      {localFoodItem &&
                        selectedMealSection &&
                        mealSections
                          ?.filter(
                            (mealOption) =>
                              mealOption.name && mealOption.id !== "Water"
                          )
                          .map((mealOption, index) => (
                            <TouchableOpacity
                              key={`${mealOption?.id}_${index}`}
                              onPress={() => handleSelectMeal(mealOption)}
                              style={{
                                width: "100%",
                                flexDirection: "row",
                                alignItems: "center",
                                backgroundColor:
                                  selectedMealSection?.id === mealOption?.id
                                    ? theme.colors.surface
                                    : "transparent",
                                marginVertical: scaleSize(5),
                                padding: scaleSize(10),
                              }}
                            >
                              <View style={{ flex: 2 }}>
                                <Text
                                  style={{
                                    color: theme.colors.primaryTextColor,
                                    fontSize: scaleSize(20),
                                  }}
                                >
                                  {mealOption?.name}
                                </Text>
                              </View>
                              <View
                                style={{
                                  flex: 1,
                                  alignItems: "flex-end",
                                  paddingRight: scaleSize(5),
                                }}
                              >
                                {selectedMealSection?.id === mealOption?.id ? (
                                  <Feather
                                    name="check-square"
                                    size={scaleSize(28)}
                                    color={theme.colors.primary}
                                  />
                                ) : null}
                              </View>
                            </TouchableOpacity>
                          ))}
                    </TouchableOpacity>
                  </ScrollView>
                </View>
              </View>
            )}
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }
);

// Sub-components

const NutritionFactsRow = ({
  label,
  quantity,
  unit,
  totalDailyQuantity,
  totalDailyUnit,
  color,
}) => {
  const styles = foodNutrientModalStyles();

  // Calculate modified daily value
  const modifiedDailyQuantity =
    quantity === 0 && typeof totalDailyQuantity === "number"
      ? 0
      : totalDailyQuantity;

  // Check if we should show daily value with cleanup
  const showDailyValue =
    typeof modifiedDailyQuantity === "number" &&
    typeof totalDailyUnit === "string" &&
    !(quantity === 0 && modifiedDailyQuantity === 0 && totalDailyUnit === ""); // Extra guard

  return (
    <View style={styles.nutritionFactsRow}>
      <View style={styles.nutritionalFactsLabelContainer}>
        <Text style={styles.nutritionFactsLabel}>{label}</Text>
      </View>
      <View style={styles.nutritionalFactsValuesContainer}>
        <Text style={{ ...styles.nutritionFactsValue, color: color }}>
          {quantity !== null ? Math.round(quantity) : ""}{" "}
          {quantity !== null ? unit : ""}
        </Text>
        {showDailyValue && (
          <Text style={{ ...styles.nutritionFactsDailyValue, color: color }}>
            {Math.round(modifiedDailyQuantity)}
            {totalDailyUnit}
          </Text>
        )}
      </View>
    </View>
  );
};

const NutritionFactsIndentedRow = ({
  label,
  quantity,
  unit,
  totalDailyQuantity,
  totalDailyUnit,
  color,
}) => {
  const styles = foodNutrientModalStyles();

  // Shared cleanup logic
  const modifiedDailyQuantity =
    quantity === 0 && typeof totalDailyQuantity === "number"
      ? 0
      : totalDailyQuantity;

  const showDailyValue =
    typeof modifiedDailyQuantity === "number" &&
    typeof totalDailyUnit === "string" &&
    !(quantity === 0 && modifiedDailyQuantity === 0 && totalDailyUnit === "");

  return (
    <View style={styles.nutritionFactsRow}>
      <View style={styles.nutritionalFactsLabelContainer}>
        <Text style={styles.nutritionFactsIndentLabel}>{label}</Text>
      </View>
      <View style={styles.nutritionalFactsValuesContainer}>
        <Text style={{ ...styles.nutritionFactsIndentValue, color: color }}>
          {quantity !== null ? Math.round(quantity) : ""}{" "}
          {quantity !== null ? unit : ""}
        </Text>
        {showDailyValue && (
          <Text
            style={{ ...styles.nutritionFactsIndentDailyValue, color: color }}
          >
            {Math.round(modifiedDailyQuantity)}
            {totalDailyUnit}
          </Text>
        )}
      </View>
    </View>
  );
};

const ServingSizeRow = ({
  isVisible,
  toggleSelectServing,
  label,
  selectedServing,
  isCustomMeal,
}) => {
  const { theme } = useThemeContext();
  const styles = foodNutrientModalStyles();

  return (
    <View style={styles.rowContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <TouchableOpacity
        style={{
          width: "40%",
          alignItems: "center",
          borderWidth: scaleSize(1),
          borderColor: isVisible ? theme.colors.primary : "gray",
          borderRadius: scaleSize(8),
          paddingVertical: scaleSize(10),
          paddingHorizontal: scaleSize(20),
        }}
        onPress={toggleSelectServing}
        disabled={isCustomMeal}
      >
        {isCustomMeal ? (
          <Text
            style={{
              ...styles.inputLabel,
              color: theme.colors.primaryTextColor,
            }}
          >
            1 meal
          </Text>
        ) : (
          <Text
            style={{
              ...styles.inputLabel,
              color: theme.colors.primaryTextColor,
            }}
          >
            1 {selectedServing?.label}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const NumberOfServingsRow = ({
  label,
  keyboardType,
  numberOfServings,
  updateNumberOfServings,
  hideSelectServingAndSelectMealOnTextInputFocus,
}) => {
  const styles = foodNutrientModalStyles();

  // Convert NaN or null to an empty string for better user experience
  const formattedNumberOfServings =
    numberOfServings !== null && !isNaN(numberOfServings)
      ? numberOfServings.toString()
      : "";

  return (
    <View style={styles.rowContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <TextInput
        onFocus={hideSelectServingAndSelectMealOnTextInputFocus}
        keyboardType={keyboardType}
        style={{ ...styles.textInput, width: "40%" }}
        value={formattedNumberOfServings}
        onChangeText={(text) => {
          updateNumberOfServings(text);
        }}
      />
    </View>
  );
};

const MealRow = ({
  isVisible,
  toggleSelectMeal,
  label,
  selectedMealOption,
}) => {
  const { theme } = useThemeContext();
  const styles = foodNutrientModalStyles();

  return (
    <View style={styles.rowContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <TouchableOpacity
        style={{
          width: "40%",
          alignItems: "center",
          borderWidth: scaleSize(1),
          borderColor: isVisible ? theme.colors.primary : "gray",
          borderRadius: scaleSize(8),
          paddingVertical: scaleSize(10),
          paddingHorizontal: scaleSize(20),
        }}
        onPress={toggleSelectMeal}
      >
        <Text
          style={{ ...styles.inputLabel, color: theme.colors.primaryTextColor }}
        >
          {selectedMealOption?.name}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default FoodNutrientModal;
