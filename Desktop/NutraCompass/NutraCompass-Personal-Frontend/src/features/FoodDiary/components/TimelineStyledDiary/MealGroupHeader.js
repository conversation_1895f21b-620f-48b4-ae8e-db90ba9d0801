// MealGroupHeader.js
import React from "react";
import { View, Text, Image } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext";
import { scaleSize } from "../../../../utils/deviceUtils";

const MealGroupHeader = ({
  header,
  scaled,
  theme,
  isGrouped,
  isLeftAligned,
}) => {
  return (
    <View
      style={{
        padding: scaleSize(12),
        borderTopLeftRadius: scaleSize(16),
        borderTopRightRadius: scaleSize(16),
        backgroundColor: theme.colors.screenBackground,
        borderColor: theme.colors.primary,
        borderWidth: 1,
        borderBottomWidth: 0,
        alignSelf: isLeftAligned ? "flex-start" : "flex-end", // New alignment
        width: "80%", // Match content width
        marginLeft: isLeftAligned ? "16%" : 0, // Align with time column
        marginRight: !isLeftAligned ? "16%" : 0,
      }}
    >
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        {header.mealImageUrl && (
          <Image
            source={{ uri: header.isTemporary ? null : header.mealImageUrl }}
            style={{
              width: scaleSize(100),
              height: scaleSize(100),
              borderRadius: scaleSize(6),
              marginRight: scaleSize(12),
            }}
            onError={(e) => {
              if (!item.header.isTemporary) {
                console.warn("Failed to load meal image:", e.nativeEvent.error);
              }
            }}
          />
        )}

        <View>
          <Text
            style={{
              fontWeight: "bold",
              marginBottom: scaleSize(4),
              fontSize: scaleSize(14),
              color: theme.colors.primaryTextColor,
            }}
          >
            {header.mealName}
          </Text>
          <Text
            style={{
              fontSize: scaled.fontM,
              color: theme.colors.primaryTextColor,
            }}
          >
            {Math.round(header.totalCalories)} cal
          </Text>
        </View>
      </View>
    </View>
  );
};

export default MealGroupHeader;
