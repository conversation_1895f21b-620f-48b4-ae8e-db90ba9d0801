import React from "react";
import { Animated } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext";
import { scaleSize } from "../../../../utils/deviceUtils";

const AnimatedTimeMarker = ({ time, scaled }) => {
  const { theme } = useThemeContext();

  return (
    <Animated.Text
      style={{
        marginTop: scaleSize(4),
        fontWeight: "500",
        fontSize: scaled.fontS,
        color: theme.colors.primaryTextColor,
        textAlign: "center",
      }}
    >
      {time}
    </Animated.Text>
  );
};

export default AnimatedTimeMarker;
