import React from "react";
import { Text } from "react-native";
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from "react-native-popup-menu";
import Entypo from "@expo/vector-icons/Entypo";
import { Divider } from "react-native-paper";
import { scaleSize } from "../../../../../utils/deviceUtils";

const MenuItem = ({ text, iconName, iconColor, onSelect, theme, scaled }) => {
  return (
    <MenuOption
      onSelect={onSelect}
      customStyles={{
        optionWrapper: {
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          paddingVertical: scaleSize(8),
        },
      }}
    >
      <Text
        style={{
          color: theme.colors.primaryTextColor,
          fontSize: scaled.fontM,
        }}
      >
        {text}
      </Text>
      <Entypo name={iconName} size={scaled.iconM} color={iconColor} />
    </MenuOption>
  );
};

const MealSectionToggleMenu = ({
  mealType,
  openQuickAddModal,
  deleteMealSectionEntries,
  handleOpenCopyMealSectionModal,
  addLoggedFoodItemsToTempCustomMeal,
  openModal,
  clearTempCustomMeal,
  MODAL_TYPES = {},
  theme,
  scaled,
}) => {
  // Safe function calls with null checks
  const safeOpenQuickAddModal = (mealType) => {
    if (typeof openQuickAddModal === "function") {
      // Only pass mealType for new entries
      openQuickAddModal(mealType);
    }
  };

  const safeHandleCopyFromDate = (mealType) => {
    if (typeof handleOpenCopyMealSectionModal === "function") {
      handleOpenCopyMealSectionModal(mealType, "From Date");
    }
  };

  const safeHandleCopyToDate = (mealType) => {
    if (typeof handleOpenCopyMealSectionModal === "function") {
      handleOpenCopyMealSectionModal(mealType, "To Date");
    }
  };

  const safeSaveAsMeal = () => {
    if (
      typeof addLoggedFoodItemsToTempCustomMeal === "function" &&
      typeof openModal === "function" &&
      typeof clearTempCustomMeal === "function"
    ) {
      addLoggedFoodItemsToTempCustomMeal(mealType.data);
      openModal(MODAL_TYPES.CREATE_CUSTOM_MEAL, {
        onClose: () => clearTempCustomMeal(),
      });
    }
  };

  const safeClearEntries = () => {
    if (
      typeof deleteMealSectionEntries === "function" &&
      mealType.data?.length > 0
    ) {
      deleteMealSectionEntries(mealType.id);
    }
  };

  return (
    <Menu>
      <MenuTrigger
        style={{
          alignSelf: "center",
          borderRadius: scaled.borderRadiusM,
          padding: scaled.paddingS,
        }}
      >
        <Entypo
          name="dots-three-horizontal"
          size={scaled.iconS}
          color={theme.colors.primaryTextColor}
        />
      </MenuTrigger>
      <MenuOptions
        customStyles={{
          optionsContainer: {
            borderRadius: scaled.borderRadiusL,
            borderWidth: 1,
            borderColor: theme.colors.cardBorderColor,
            backgroundColor: theme.colors.surface,
            minWidth: scaleSize(180),
          },
        }}
      >
        {mealType.id !== "Water" && (
          <>
            <MenuItem
              text="Quick Add"
              iconName="add-to-list"
              iconColor="#53E032"
              onSelect={() => safeOpenQuickAddModal(mealType)}
              theme={theme}
              scaled={scaled}
            />
            <Divider />
            <MenuItem
              text="Copy From Date"
              iconName="cycle"
              iconColor="#FED589"
              onSelect={() => safeHandleCopyFromDate(mealType)}
              theme={theme}
              scaled={scaled}
            />
            <Divider />
            <MenuItem
              text="Copy To Date"
              iconName="copy"
              iconColor="lightblue"
              onSelect={() => safeHandleCopyToDate(mealType)}
              theme={theme}
              scaled={scaled}
            />
            <Divider />
            <MenuItem
              text="Save as Meal"
              iconName="database"
              iconColor="#9089FE"
              onSelect={safeSaveAsMeal}
              theme={theme}
              scaled={scaled}
            />
            <Divider />
          </>
        )}

        <MenuItem
          text="Clear All Entries"
          iconName="trash"
          iconColor="#FE9089"
          onSelect={safeClearEntries}
          theme={theme}
          scaled={scaled}
        />
      </MenuOptions>
    </Menu>
  );
};

export default MealSectionToggleMenu;
