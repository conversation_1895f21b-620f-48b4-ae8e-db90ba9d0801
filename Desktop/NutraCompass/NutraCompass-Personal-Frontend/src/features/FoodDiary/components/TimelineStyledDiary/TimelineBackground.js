import React from "react";
import { View, StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext";

const TimelineBackground = () => {
  const { theme } = useThemeContext();

  return (
    <View
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: theme.colors.screenBackground,
      }}
    />
  );
};

export default TimelineBackground;
