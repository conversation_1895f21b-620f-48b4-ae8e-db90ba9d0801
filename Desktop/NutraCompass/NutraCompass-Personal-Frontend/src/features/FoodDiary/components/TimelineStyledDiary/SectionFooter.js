import React from "react";
import { View, TouchableOpacity } from "react-native";
import Ionicons from "react-native-vector-icons/Ionicons";
import * as Haptics from "expo-haptics";
import MealSectionToggleMenu from "./MealSectionMenu/MealSectionToggleMenu";

const SectionFooter = ({
  section,
  scaled,
  theme,
  isCollapsed,
  handleOpenWaterLogEntryModal,
  handleOpenFoodEntryModal,
  openQuickAddModal,
  deleteMealSectionEntries,
  handleOpenCopyMealSectionFromDateModal,
  addLoggedFoodItemsToTempCustomMeal,
  openModal,
  clearTempCustomMeal,
  MODAL_TYPES,
}) => {
  if (isCollapsed) return null;

  return (
    <View
      style={{
        paddingTop: 0,
        padding: scaled.paddingXS,
        marginBottom: scaled.paddingXL,
        backgroundColor: theme.colors.screenBackground,
        borderBottomLeftRadius: scaled.borderRadiusM,
        borderBottomRightRadius: scaled.borderRadiusM,
      }}
    >
      <View
        style={{
          flex: 1,
          alignItems: "center",
          flexDirection: "row",
          justifyContent: "space-between",
          backgroundColor: theme.colors.screenBackground,
          borderBottomLeftRadius: scaled.borderRadiusM,
          borderBottomRightRadius: scaled.borderRadiusM,
          paddingVertical: scaled.paddingM,
          paddingLeft: scaled.paddingXL,
          paddingRight: scaled.paddingM,
        }}
      >
        <TouchableOpacity
          style={{
            shadowColor: "#555",
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.7,
            shadowRadius: scaled.shadowRadius,
            backgroundColor: theme.colors.surface,
            paddingHorizontal: scaled.paddingM,
            borderRadius: scaled.borderRadiusM,
          }}
          onPress={() => {
            if (section.id === "Water") {
              handleOpenWaterLogEntryModal();
            } else {
              handleOpenFoodEntryModal(section);
            }
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
        >
          <Ionicons
            name="add"
            size={scaled.iconM}
            color={theme.colors.primary}
          />
        </TouchableOpacity>

        {/* Use the menu directly with all props */}
        <MealSectionToggleMenu
          mealType={section.id}
          theme={theme}
          scaled={scaled}
          openQuickAddModal={openQuickAddModal}
          deleteMealSectionEntries={deleteMealSectionEntries}
          handleOpenCopyMealSectionModal={
            handleOpenCopyMealSectionFromDateModal
          }
          addLoggedFoodItemsToTempCustomMeal={
            addLoggedFoodItemsToTempCustomMeal
          }
          openModal={openModal}
          clearTempCustomMeal={clearTempCustomMeal}
          MODAL_TYPES={MODAL_TYPES}
        />
      </View>
    </View>
  );
};

export default SectionFooter;
