// TimelineEntry.js
import React, { useMemo } from "react";
import { View, Text } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext";
import { useFoodLog } from "../../context/FoodLogContext";
import { scaleSize } from "../../../../utils/deviceUtils";
import SwipeableFoodEntryListItem from "../SwipeableFoodEntryListItem";

const TimelineEntry = ({
  item,
  scaled,
  handleOpenFoodNutrientModal,
  openQuickAddModal,
  handleOpenWaterLogEntryModal,
  section,
  isGrouped = false,
  mealGroupItems, // Passed from parent
  isLeftAligned, // New prop to determine alignment
}) => {
  const { theme } = useThemeContext();
  const { formatTimestamp } = useFoodLog();

  // Calculate if this is the last entry in the meal group
  const isLastInMealGroup = useMemo(() => {
    if (!isGrouped || !item.mealHeaderId || !mealGroupItems) return false;

    // Find the highest mealItemIndex in this group
    const maxIndex = Math.max(
      ...mealGroupItems.map((entry) => entry.mealItemIndex)
    );

    return item.mealItemIndex === maxIndex;
  }, [item, mealGroupItems, isGrouped]);

  // Calculate if this is the first entry in the meal group
  const isFirstInMealGroup = useMemo(() => {
    if (!isGrouped || !item.mealHeaderId || !mealGroupItems) return false;

    // Find the lowest mealItemIndex in this group
    const minIndex = Math.min(
      ...mealGroupItems.map((entry) => entry.mealItemIndex)
    );

    return item.mealItemIndex === minIndex;
  }, [item, mealGroupItems, isGrouped]);

  // Prefer clientTimestamp for display
  const displayTimestamp =
    item.clientTimestamp || item.timestamp || item.loggedAt;
  const formattedTime = formatTimestamp(displayTimestamp);

  return (
    <View
      style={{
        flexDirection: "row",
        paddingHorizontal: scaleSize(14),
        backgroundColor: isGrouped ? "transparent" : "transparent",
        borderColor: isGrouped ? theme.colors.secondary : "transparent",
        borderLeftWidth: isGrouped ? 1 : 0,
        borderRightWidth: isGrouped ? 1 : 0,
        borderBottomWidth: isGrouped && isLastInMealGroup ? 1 : 0,
        borderBottomLeftRadius:
          isGrouped && isLastInMealGroup ? scaleSize(16) : 0,
        borderBottomRightRadius:
          isGrouped && isLastInMealGroup ? scaleSize(16) : 0,
        marginBottom: isGrouped && isLastInMealGroup ? scaleSize(8) : 0,
        width: "80%", // Match content width
        marginLeft: isLeftAligned ? "16%" : 0, // Align with time column
        marginRight: !isLeftAligned ? "16%" : 0,
        alignSelf: isLeftAligned ? "flex-start" : "flex-end", // New alignment
      }}
    >
      {/* Time container - conditionally rendered based on alignment */}
      {isLeftAligned && (
        <View
          style={{
            width: "12%",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {formattedTime ? (
            <Text
              style={{
                fontSize: scaleSize(12),
                color: theme.colors.subTextColor,
                textAlign: "center",
                lineHeight: scaleSize(15),
              }}
            >
              {formattedTime}
            </Text>
          ) : (
            <View
              style={{
                backgroundColor: theme.colors.primary,
                width: scaleSize(11),
                height: scaleSize(11),
                borderRadius: scaleSize(11) / 2,
              }}
            />
          )}
        </View>
      )}

      {/* Content area */}
      <View
        style={{
          overflow: "hidden",
          width: "90%", // Take most of the width
          paddingVertical: scaleSize(8),
          paddingLeft: isLeftAligned ? scaleSize(12) : 0,
          paddingRight: !isLeftAligned ? scaleSize(12) : 0,
          borderTopWidth: isGrouped && !isFirstInMealGroup ? 1 : 0,
          borderTopColor: isGrouped
            ? theme.colors.outlineVariant
            : "transparent",
        }}
      >
        <SwipeableFoodEntryListItem
          item={item}
          section={section.id}
          handleOpenFoodNutrientModal={handleOpenFoodNutrientModal}
          handleOpenQuickAddModal={openQuickAddModal}
          handleOpenWaterLogEntryModal={handleOpenWaterLogEntryModal}
          isPartOfMealGroup={item.mealGroupId !== undefined}
          theme={theme}
          scaled={scaled}
        />
      </View>

      {/* Time container for right-aligned items */}
      {!isLeftAligned && (
        <View
          style={{
            width: "12%",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {formattedTime ? (
            <Text
              style={{
                fontSize: scaleSize(12),
                color: theme.colors.subTextColor,
                textAlign: "center",
                lineHeight: scaleSize(15),
              }}
            >
              {formattedTime}
            </Text>
          ) : (
            <View
              style={{
                backgroundColor: theme.colors.primary,
                width: scaleSize(11),
                height: scaleSize(11),
                borderRadius: scaleSize(11) / 2,
              }}
            />
          )}
        </View>
      )}
    </View>
  );
};

export default TimelineEntry;
