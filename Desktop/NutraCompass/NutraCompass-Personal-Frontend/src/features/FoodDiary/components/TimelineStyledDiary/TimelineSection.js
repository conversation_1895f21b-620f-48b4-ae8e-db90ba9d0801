import React from "react";
import { View, Text } from "react-native";
import { IconButton } from "react-native-paper";
import { useThemeContext } from "../../../../context/ThemeContext";
import { scaleSize } from "../../../../utils/deviceUtils";
import AnimatedTimeMarker from "./AnimatedTimeMarker";

const TimelineSection = ({
  section,
  totalCalories,
  isCollapsed,
  toggleSection,
  scaled,
}) => {
  const { theme } = useThemeContext();

  return (
    <View
      style={{
        flexDirection: "row",
        marginVertical: scaleSize(12),
        paddingHorizontal: scaleSize(16),
      }}
    >
      <View
        style={{
          width: scaleSize(40),
          alignItems: "center",
        }}
      >
        <View
          style={{
            width: scaleSize(2),
            flex: 1,
            backgroundColor: theme.colors.primary,
          }}
        />
        <AnimatedTimeMarker time={section.time} scaled={scaled} />
      </View>

      <View
        style={{
          flex: 1,
          paddingLeft: scaleSize(12),
        }}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            paddingBottom: scaleSize(8),
          }}
        >
          <Text
            style={{
              fontWeight: "bold",
              letterSpacing: 0.5,
              fontSize: scaled.fontL,
              color: theme.colors.primary,
            }}
          >
            {section.name}
          </Text>

          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                fontWeight: "600",
                marginRight: scaleSize(8),
                fontSize: scaled.fontM,
                color: theme.colors.primaryTextColor,
              }}
            >
              {Math.round(totalCalories)}
            </Text>
            <IconButton
              icon={isCollapsed ? "chevron-down" : "chevron-up"}
              iconColor={theme.colors.primaryTextColor}
              size={scaled.iconS}
              onPress={() => toggleSection(section.id)}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default TimelineSection;
