import React, { useState, useEffect, useRef } from "react";
import {
  Modal,
  View,
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Animated,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
} from "react-native";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import Feather from "react-native-vector-icons/Feather";
import { scaleSize } from "../../../utils/deviceUtils.js";

const WaterGoalModal = ({ isVisible, closeModal }) => {
  const { theme } = useThemeContext();
  const {
    getNutritionalGoals,
    getUserMeasurementSettings,
    setWaterGoalAndUnit,
  } = useUserSettings();

  // Get measurement settings
  const measurementSettings = getUserMeasurementSettings();

  // Get current water goal
  const { waterGoal: storedWaterGoal } = getNutritionalGoals();

  // Determine initial unit based on measurement settings or stored goal
  const initialUnit =
    measurementSettings?.waterUnit || storedWaterGoal?.unit || "fl oz";

  // Determine initial amount based on stored goal or default for unit
  const initialAmount =
    storedWaterGoal?.amount || (initialUnit === "fl oz" ? 64 : 2000);

  const [inputGoal, setInputGoal] = useState(initialAmount.toString());
  const [unit, setUnit] = useState(initialUnit);

  // Animation references
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;
  const keyboardHeight = useRef(0);

  // Handle keyboard events
  useEffect(() => {
    const keyboardDidShow = (e) => {
      keyboardHeight.current = e.endCoordinates.height;
      Animated.spring(translateYAnim, {
        toValue: -e.endCoordinates.height * 0.1,
        useNativeDriver: true,
        bounciness: 10,
      }).start();
    };

    const keyboardDidHide = () => {
      Animated.spring(translateYAnim, {
        toValue: 0,
        useNativeDriver: true,
        bounciness: 4,
      }).start();
    };

    const showSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      keyboardDidShow
    );
    const hideSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      keyboardDidHide
    );

    return () => {
      showSub.remove();
      hideSub.remove();
    };
  }, []);

  // Handle modal visibility changes
  useEffect(() => {
    if (isVisible) {
      // Reset animations
      scaleAnim.setValue(0.8);
      translateYAnim.setValue(0);

      // Animate in with bounce
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        bounciness: 15,
        speed: 12,
      }).start();

      // Update state with latest values
      const measurementSettings = getUserMeasurementSettings();
      const { waterGoal } = getNutritionalGoals();

      if (waterGoal) {
        setInputGoal(waterGoal.amount.toString());
        setUnit(waterGoal.unit);
      } else {
        const defaultUnit = measurementSettings?.waterUnit || "fl oz";
        const defaultAmount = defaultUnit === "fl oz" ? 64 : 2000;
        setInputGoal(defaultAmount.toString());
        setUnit(defaultUnit);
      }
    }
  }, [isVisible]);

  const handleSave = async () => {
    const newGoalAmount = parseInt(inputGoal, 10);
    if (!isNaN(newGoalAmount)) {
      try {
        // Single API call to update both settings
        await setWaterGoalAndUnit(newGoalAmount, unit);
        closeModal();
      } catch (error) {
        console.error("Failed to save water goal:", error);
      }
    }
  };

  const toggleUnit = () => {
    setUnit((prev) => {
      const newUnit = prev === "mL" ? "fl oz" : "mL";

      // Convert amount when changing units
      let newAmount = parseInt(inputGoal, 10);
      if (!isNaN(newAmount)) {
        if (prev === "fl oz" && newUnit === "mL") {
          newAmount = Math.round(newAmount * 29.5735); // Convert fl oz to mL
        } else if (prev === "mL" && newUnit === "fl oz") {
          newAmount = Math.round(newAmount / 29.5735); // Convert mL to fl oz
        }
        setInputGoal(newAmount.toString());
      }

      return newUnit;
    });
  };

  // Create responsive sizes using scaleSize
  const padding = scaleSize(20);
  const borderRadius = scaleSize(12);
  const iconSize = scaleSize(24);
  const textSize = scaleSize(14);
  const inputHeight = scaleSize(40);
  const imageHeight = scaleSize(150);
  const gap = scaleSize(20);
  const sectionPadding = scaleSize(16);
  const textPadding = scaleSize(10);
  const switchPadding = scaleSize(10);
  const switchSize = scaleSize(1);

  const styles = StyleSheet.create({
    backdrop: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "rgba(0,0,0,0.8)",
    },
    modalView: {
      minWidth: "80%",
      maxWidth: "90%",
      backgroundColor: theme.colors.screenBackground,
      borderRadius: borderRadius,
      padding: padding,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: scaleSize(2) },
      shadowOpacity: 0.25,
      shadowRadius: scaleSize(4),
      elevation: 5,
      gap: gap,
    },
    keyboardAvoid: {
      width: "100%",
      alignItems: "center",
      justifyContent: "center",
    },
    headerRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    inputGroup: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    modalText: {
      fontSize: textSize,
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
      flex: 1,
    },
    textInput: {
      flex: 1 / 2,
      height: inputHeight,
      borderWidth: 1,
      color: theme.colors.primary,
      padding: scaleSize(10),
      textAlign: "center",
      backgroundColor: theme.colors.screenBackground,
      borderRadius: scaleSize(8),
      borderColor: theme.colors.border,
      fontSize: textSize,
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: borderRadius,
      padding: sectionPadding,
      gap: gap,
    },
    infoText: {
      fontSize: textSize,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      paddingBottom: textPadding,
    },
    unitToggle: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      padding: switchPadding,
    },
    image: {
      width: "100%", // Full width of modal content
      height: imageHeight,
      borderRadius: borderRadius,
      marginVertical: textPadding,
    },
  });

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isVisible}
      onRequestClose={closeModal}
    >
      {/* Backdrop that closes modal on press */}
      <TouchableWithoutFeedback onPress={closeModal}>
        <View style={styles.backdrop}>
          {/* Prevent modal from closing when pressing content */}
          <TouchableWithoutFeedback>
            <KeyboardAvoidingView
              behavior={Platform.OS === "ios" ? "padding" : "height"}
              style={styles.keyboardAvoid}
            >
              <Animated.View
                style={[
                  styles.modalView,
                  {
                    transform: [
                      { scale: scaleAnim },
                      { translateY: translateYAnim },
                    ],
                  },
                ]}
              >
                <View style={styles.headerRow}>
                  <TouchableOpacity onPress={closeModal}>
                    <Feather
                      name="chevron-left"
                      color={theme.colors.primaryTextColor}
                      size={iconSize}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity onPress={handleSave}>
                    <Feather
                      name="check-circle"
                      color={theme.colors.primary}
                      size={iconSize}
                    />
                  </TouchableOpacity>
                </View>

                <View style={{ alignItems: "center" }}>
                  <Text style={styles.infoText}>
                    Set your daily water intake goal to stay hydrated and
                    support optimal bodily functions.
                  </Text>
                  <Image
                    style={styles.image}
                    source={require("../../../../assets/water.png")}
                    resizeMode="contain" // Ensure proper scaling
                  />
                </View>

                <View style={styles.section}>
                  <View style={styles.inputGroup}>
                    <Text style={styles.modalText}>Water Goal:</Text>
                    <TextInput
                      style={styles.textInput}
                      keyboardType="numeric"
                      value={inputGoal}
                      onChangeText={setInputGoal}
                    />
                  </View>

                  <View style={styles.unitToggle}>
                    <Text
                      style={[styles.modalText, { paddingRight: textPadding }]}
                    >
                      {unit === "mL" ? "Milliliters" : "Fluid Ounces"}
                    </Text>
                    <Switch
                      trackColor={{ false: "#767577", true: "#81b0ff" }}
                      thumbColor={unit === "mL" ? "#f5dd4b" : "#f4f3f4"}
                      ios_backgroundColor="#3e3e3e"
                      onValueChange={toggleUnit}
                      value={unit === "mL"}
                      style={{
                        transform: [
                          { scaleX: switchSize },
                          { scaleY: switchSize },
                        ],
                      }} // Scale switch for better visibility
                    />
                  </View>
                </View>
              </Animated.View>
            </KeyboardAvoidingView>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default WaterGoalModal;
