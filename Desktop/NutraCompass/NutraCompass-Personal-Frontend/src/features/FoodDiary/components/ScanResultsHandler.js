import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
} from "react-native";
import { Image } from "expo-image";
import * as Haptics from "expo-haptics";
import { IconButton, Snackbar } from "react-native-paper";
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from "react-native-popup-menu";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodLog } from "../context/FoodLogContext.js";
import { useModal, MODAL_TYPES } from "../../../context/ModalContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { scaleSize, isTablet } from "../../../utils/deviceUtils.js";
import DatePickerModal from "../../../components/DatePickerModal.js";

const ScanResultsHandler = React.memo(
  ({ isVisible, closeModal, scannedItem, onSaveSuccess }) => {
    const { openModal } = useModal();
    const { user } = useAuth();
    const { theme } = useThemeContext();
    const {
      mealSections,
      activeMealSection: contextActiveMealSection,
      setActiveMealSection,
      setActiveFoodItem,
      saveOrUpdateSingleFoodItemToFoodLog,
      saveMealToFoodLog,
    } = useFoodLog();
    const { selectedDate, updateSelectedDate, getSelectedDateAsDate } =
      useTime();

    const [isSnackbarVisible, setSnackbarTriggered] = useState(false);
    const [selectedScale] = useState(new Animated.Value(1));
    const [menuVisible, setMenuVisible] = useState(false);
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    // Local state for active meal section with default to "Meal 1"
    const [localActiveMealSection, setLocalActiveMealSection] = useState(null);

    // Use selectedDate directly from context
    const currentDate = getSelectedDateAsDate();

    // Reset state when modal closes
    useEffect(() => {
      if (!isVisible) {
        setSnackbarTriggered(false);
        setMenuVisible(false);
        setShowDatePicker(false);
        setLocalActiveMealSection(null);
      }
    }, [isVisible]);

    // Initialize meal section when scanned item changes
    useEffect(() => {
      if (scannedItem) {
        setActiveFoodItem(scannedItem);

        // Set active meal section: use context if available, otherwise default to "Meal 1"
        if (contextActiveMealSection) {
          setLocalActiveMealSection(contextActiveMealSection);
        } else {
          const defaultSection =
            mealSections?.find((section) => section.id === "Meal 1") ||
            mealSections?.[0];
          setLocalActiveMealSection(defaultSection);
        }
      }
    }, [scannedItem, contextActiveMealSection, mealSections]);

    // Extract scanned item properties
    const {
      foodLabel,
      foodBrand,
      activeMeasure,
      defaultNutrients = {},
      nutrients = {},
      mealImageUrl,
      isCustomMeal,
      mealItems,
    } = scannedItem || {};

    // Calculate calories
    const calories =
      defaultNutrients.calories || nutrients?.core?.ENERC_KCAL?.quantity || 0;

    // Format serving size
    const servingWeight = activeMeasure ? Math.round(activeMeasure.weight) : 0;
    const measureLabel = activeMeasure?.label?.toLowerCase?.() || "";

    // Determine if this is a multi-item scan
    const hasMultipleItems = isCustomMeal && Array.isArray(mealItems) && mealItems.length > 1;

    const handleSaveScannedItem = async () => {
      if (!scannedItem || !localActiveMealSection || isSaving) return;
      setIsSaving(true);

      try {
        // Animate the save button
        await new Promise((resolve) => {
          Animated.sequence([
            Animated.timing(selectedScale, {
              toValue: 0.8,
              duration: 100,
              useNativeDriver: false,
            }),
            Animated.timing(selectedScale, {
              toValue: 1,
              duration: 100,
              useNativeDriver: false,
            }),
          ]).start(resolve);
        });

        if (isCustomMeal && mealItems) {
          // Save as custom meal
          const mealToSave = {
            ...scannedItem,
            mealType: localActiveMealSection.id,
            date: selectedDate,
          };

          await saveMealToFoodLog(
            localActiveMealSection.id,
            selectedDate,
            mealToSave
          );
        } else {
          // Save as single item
          const foodToSave = {
            ...scannedItem,
            mealType: localActiveMealSection.id,
            date: selectedDate,
          };

          await saveOrUpdateSingleFoodItemToFoodLog(
            null,
            localActiveMealSection.id,
            foodToSave
          );
        }

        // Provide feedback
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        setSnackbarTriggered(true);

        // Hide snackbar after 2 seconds
        setTimeout(() => {
          setSnackbarTriggered(false);
        }, 2000);
      } finally {
        // Re-enable buttons after a short delay to avoid rapid re-taps
        setTimeout(() => setIsSaving(false), 600);
      }
    };

    const handleSaveIndividualItems = () => {
      if (!scannedItem || !localActiveMealSection || !hasMultipleItems) return;

      // Save each item individually
      mealItems.forEach((item) => {
        const foodToSave = {
          ...item,
          mealType: localActiveMealSection.id,
          date: selectedDate,
        };

        saveOrUpdateSingleFoodItemToFoodLog(
          null,
          localActiveMealSection.id,
          foodToSave
        );
      });

      // Provide feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setSnackbarTriggered(true);

      // Hide snackbar after 2 seconds
      setTimeout(() => {
        setSnackbarTriggered(false);
      }, 2000);
    };

    const toggleSnackbar = () => {
      setSnackbarTriggered(true);
    };

    const handleViewNutrients = () => {
      // Explicitly set active food item before opening modal
      setActiveFoodItem(scannedItem);

      openModal(MODAL_TYPES.FOOD_NUTRIENT, {
        foodNutrientModalType: "Add Food",
        toggleSnackbar,
      });
    };

    const handleDateChange = (date) => {
      if (date) {
        updateSelectedDate(date);
      }
      setShowDatePicker(false);
    };

    const handleMealSectionMenuToggle = () => setMenuVisible(!menuVisible);

    const handleMealSectionMenuSelect = (section) => {
      setLocalActiveMealSection(section);
      setMenuVisible(false);
    };

    if (!scannedItem) return null;

    // Get meal section name with fallbacks
    const getMealSectionName = () => {
      if (!localActiveMealSection) return "Select Meal";
      return (
        localActiveMealSection.name ||
        localActiveMealSection.id ||
        "Select Meal"
      );
    };

    // Inline styles
    const styles = {
      modalContainer: {
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
      },
      modalHeader: {
        width: "100%",
        paddingTop: scaleSize(60),
        paddingHorizontal: scaleSize(16),
      },
      headerRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: scaleSize(10),
      },
      headerTitle: {
        fontSize: scaleSize(22),
        fontWeight: "600",
        color: theme.colors.primaryTextColor,
      },
      headerControlsRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingTop: scaleSize(16),
        paddingBottom: scaleSize(32),
      },
      dateButton: {
        flexDirection: "row",
        alignItems: "center",
        paddingHorizontal: scaleSize(12),
        paddingVertical: scaleSize(8),
        borderRadius: scaleSize(8),
        backgroundColor: theme.colors.surface,
      },
      dateButtonText: {
        color: theme.colors.primaryTextColor,
        marginRight: scaleSize(4),
        fontSize: scaleSize(18),
      },
      foodItemContainer: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: theme.colors.surface,
        elevation: scaleSize(4),
        paddingHorizontal: scaleSize(16),
        paddingVertical: scaleSize(12),
        marginBottom: scaleSize(16),
        marginHorizontal: scaleSize(16),
        borderRadius: scaleSize(16),
        borderWidth: scaleSize(1),
        borderColor: "rgba(204, 204, 204, 0.3)",
      },
      foodImage: {
        width: scaleSize(70),
        height: scaleSize(70),
        borderRadius: scaleSize(8),
        marginRight: scaleSize(10),
      },
      foodImagePlaceholder: {
        width: scaleSize(70),
        height: scaleSize(70),
        borderRadius: scaleSize(8),
        justifyContent: "center",
        alignItems: "center",
        marginRight: scaleSize(10),
        backgroundColor: theme.colors.surfaceVariant,
      },
      foodInfoContainer: {
        flex: 1,
        gap: scaleSize(4),
      },
      foodLabel: {
        fontSize: scaleSize(16),
        fontWeight: "500",
        color: theme.colors.primaryTextColor,
      },
      foodLabelCalories: {
        fontSize: scaleSize(14),
        color: theme.colors.subTextColor,
      },
      foodLabelServingSize: {
        fontSize: scaleSize(14),
        color: theme.colors.subTextColor,
      },
      mealSectionButton: {
        flexDirection: "row",
        alignItems: "center",
        paddingHorizontal: scaleSize(12),
        paddingVertical: scaleSize(8),
        borderRadius: scaleSize(8),
        backgroundColor: theme.colors.surface,
        minWidth: scaleSize(120),
      },
      mealSectionText: {
        fontSize: scaleSize(18),
        fontWeight: "500",
        color: theme.colors.primaryTextColor,
        flexShrink: 1,
        marginRight: scaleSize(4),
      },
    };

    return (
      <View style={styles.modalContainer}>
        {/* Header */}
        <View style={styles.modalHeader}>
          <View style={styles.headerRow}>
            <IconButton
              icon="arrow-left"
              size={scaleSize(24)}
              onPress={closeModal}
              color={theme.colors.primaryTextColor}
            />

            <Text style={styles.headerTitle}>Scanned Item</Text>

            {/* Empty space for layout balance */}
            <View style={{ width: scaleSize(40) }} />
          </View>

          <View style={styles.headerControlsRow}>
            {/* Date Picker Button */}
            <TouchableOpacity
              onPress={() => setShowDatePicker(true)}
              style={styles.dateButton}
            >
              <Text style={styles.dateButtonText}>
                {currentDate
                  ? currentDate.toLocaleDateString("en-US", {
                      month: "long",
                      day: "numeric",
                      year: "numeric",
                    })
                  : "Select Date"}
              </Text>
              <Ionicons
                name="calendar"
                size={scaleSize(16)}
                color={theme.colors.primaryTextColor}
              />
            </TouchableOpacity>

            {/* Meal Section Selector */}
            <Menu
              opened={menuVisible}
              onBackdropPress={() => setMenuVisible(false)}
            >
              <MenuTrigger>
                <TouchableOpacity
                  onPress={handleMealSectionMenuToggle}
                  style={styles.mealSectionButton}
                >
                  <Text
                    style={styles.mealSectionText}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    {getMealSectionName()}
                  </Text>
                  <Ionicons
                    name={menuVisible ? "chevron-up" : "chevron-down"}
                    size={scaleSize(14)}
                    color={theme.colors.primaryTextColor}
                  />
                </TouchableOpacity>
              </MenuTrigger>

              <MenuOptions
                customStyles={{
                  optionsContainer: {
                    backgroundColor: theme.colors.surface,
                    borderRadius: scaleSize(8),
                    padding: 0,
                    minWidth: scaleSize(120),
                  },
                }}
              >
                {mealSections
                  ?.filter(
                    (section) =>
                      section.id !== "Water" && section.name?.trim() !== ""
                  )
                  .map((section) => (
                    <MenuOption
                      key={section.id}
                      onSelect={() => handleMealSectionMenuSelect(section)}
                      customStyles={{
                        optionWrapper: {
                          minHeight: scaleSize(40),
                          backgroundColor:
                            localActiveMealSection?.id === section.id
                              ? theme.colors.primary
                              : "transparent",
                          paddingHorizontal: scaleSize(12),
                          paddingVertical: scaleSize(8),
                        },
                      }}
                    >
                      <Text
                        style={{
                          color:
                            localActiveMealSection?.id === section.id
                              ? theme.colors.onPrimary
                              : theme.colors.primaryTextColor,
                          fontSize: scaleSize(14),
                        }}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {section.name || section.id}
                      </Text>
                    </MenuOption>
                  ))}
              </MenuOptions>
            </Menu>
          </View>
        </View>

        {/* Main Content */}
        <View style={{ flex: 1, width: "100%" }}>
          {/* Scanned Item Display */}
          <TouchableOpacity
            style={styles.foodItemContainer}
            onPress={handleViewNutrients}
          >
            {/* Food Image - only show if mealImageUrl exists */}
            {mealImageUrl && (
              <Image
                source={{ uri: mealImageUrl }}
                style={styles.foodImage}
                contentFit="cover"
              />
            )}

            <View style={styles.foodInfoContainer}>
              <Text style={styles.foodLabel}>{foodLabel}</Text>

              <View
                style={{
                  flexDirection: "row",
                  gap: scaleSize(5),
                  flexWrap: "wrap",
                }}
              >
                <Text style={styles.foodLabelCalories}>
                  {Math.round(calories)} cal
                  {(servingWeight || foodBrand) && ","}
                </Text>

                {servingWeight > 0 && (
                  <Text style={styles.foodLabelServingSize}>
                    {servingWeight} {measureLabel}
                    {foodBrand && ","}
                  </Text>
                )}

                {foodBrand && (
                  <Text style={styles.foodLabelServingSize}>{foodBrand}</Text>
                )}
              </View>

              {/* Show individual items preview if multiple items detected */}
              {hasMultipleItems && (
                <View style={{ marginTop: scaleSize(8) }}>
                  <Text style={{
                    fontSize: scaleSize(12),
                    color: theme.colors.subTextColor,
                    fontStyle: "italic",
                  }}>
                    Contains: {mealItems.map(item => item.foodLabel).join(", ")}
                  </Text>
                </View>
              )}
            </View>

            {/* Save Button on the right side of food item */}
            <Animated.View
              style={{
                transform: [{ scale: selectedScale }],
                marginLeft: scaleSize(10),
              }}
            >
              <IconButton
                icon={isSnackbarVisible ? "check" : (isSaving ? "timer" : "plus")}
                size={scaleSize(30)}
                onPress={handleSaveScannedItem}
                disabled={isSaving}
                color={theme.colors.primary}
                style={{ backgroundColor: theme.colors.surfaceVariant, opacity: isSaving ? 0.6 : 1 }}
              />
            </Animated.View>
          </TouchableOpacity>

          {/* Multiple Items Save Options - only show if multiple items detected */}
          {hasMultipleItems && (
            <View style={{
              marginHorizontal: scaleSize(16),
              marginBottom: scaleSize(16),
              gap: scaleSize(12),
            }}>
              <Text style={{
                fontSize: scaleSize(16),
                fontWeight: "500",
                color: theme.colors.primaryTextColor,
                textAlign: "center",
              }}>
                Save Options
              </Text>

              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: scaleSize(12),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  borderWidth: 1,
                  borderColor: theme.colors.primary,
                  opacity: isSaving ? 0.6 : 1,
                }}
                disabled={isSaving}
                onPress={handleSaveIndividualItems}
              >
                <Ionicons
                  name="list"
                  size={scaleSize(18)}
                  color={theme.colors.primary}
                />
                <Text
                  style={{
                    color: theme.colors.primary,
                    marginLeft: scaleSize(8),
                    fontSize: scaleSize(14),
                    fontWeight: "500",
                  }}
                >
                  Save as {mealItems.length} Separate Items
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: scaleSize(12),
                  backgroundColor: theme.colors.primary,
                  borderRadius: scaleSize(8),
                  opacity: isSaving ? 0.7 : 1,
                }}
                disabled={isSaving}
                onPress={handleSaveScannedItem}
              >
                <Ionicons
                  name="restaurant"
                  size={scaleSize(18)}
                  color={theme.colors.onPrimary}
                />
                <Text
                  style={{
                    color: theme.colors.onPrimary,
                    marginLeft: scaleSize(8),
                    fontSize: scaleSize(14),
                    fontWeight: "500",
                  }}
                >
                  Save as Combined Meal
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {/* View Nutrients Button */}
          <TouchableOpacity
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: hasMultipleItems ? scaleSize(16) : scaleSize(30),
              padding: scaleSize(10),
              paddingVertical: scaleSize(14),
              backgroundColor: theme.colors.surfaceVariant,
              borderRadius: scaleSize(8),
              marginHorizontal: scaleSize(16),
              borderWidth: 1,
              borderColor: theme.colors.primary,
            }}
            onPress={handleViewNutrients}
          >
            <Ionicons
              name="nutrition"
              size={scaleSize(20)}
              color={theme.colors.primary}
            />
            <Text
              style={{
                color: theme.colors.primary,
                marginLeft: scaleSize(10),
                fontSize: scaleSize(16),
                fontWeight: "500",
              }}
            >
              View & Edit Nutrients
            </Text>
          </TouchableOpacity>
        </View>

        {/* Custom Date Picker Modal */}
        <DatePickerModal
          isVisible={showDatePicker}
          onClose={() => setShowDatePicker(false)}
          onDateSelected={handleDateChange}
        />

        <Snackbar
          visible={isSnackbarVisible}
          onDismiss={() => setSnackbarTriggered(false)}
          style={{ backgroundColor: theme.colors.surface }}
        >
          <Text
            style={{
              fontSize: scaleSize(16),
              alignSelf: "center",
              color: theme.colors.primaryTextColor,
            }}
          >
            Item Saved to Diary!
          </Text>
        </Snackbar>
      </View>
    );
  }
);

export default ScanResultsHandler;
