import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";
const mealSectionCustomizationModalStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    header: {
      height: scaleSize(75),
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
    },
    closeModalButton: {
      marginTop: scaleSize(20),
    },
    saveButton: {
      marginTop: scaleSize(20),
    },
    modalContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "transparent", // transparent background
    },
    modalContent: {
      flex: 1,
      width: "100%", // Adjust the width
      minHeight: "97%", // Adjust the height
      backgroundColor: theme.colors.screenBackground, // Use theme background color
      padding: scaleSize(20),
    },
    modalButtonHeader: {
      width: "100%",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    sectionRow: {
      backgroundColor: "transparent",
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: scaleSize(10),
      marginTop: scaleSize(1),
    },
    sectionIdText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(18),
    },
    sectionInputText: {
      color: theme.colors.primary,
      fontSize: scaleSize(18),
    },
  });
};

export default mealSectionCustomizationModalStyles;
