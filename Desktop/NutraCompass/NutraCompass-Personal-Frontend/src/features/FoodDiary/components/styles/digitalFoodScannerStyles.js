import { StyleSheet } from "react-native";
import {
  ThemeContext,
  useThemeContext,
} from "../../../../context/ThemeContext.js";

const digitalFoodScannerStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      width: "100%",
      justifyContent: "center",
      backgroundColor: theme.colors.screenBackground,
    },
    camera: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 10,
      paddingTop: 50,
      paddingBottom: 10,
      backgroundColor: theme.colors.surface,
    },
    closeButton: {
      padding: 5,
    },
    flipButton: {
      padding: 5,
    },
    title: {
      fontSize: 20,
      color: theme.colors.primaryTextColor,
      textAlign: "center",
    },
    footer: {
      flexDirection: "row",
      justifyContent: "space-around",
      alignItems: "center",
      padding: 20,
      paddingBottom: 40,
      backgroundColor: theme.colors.surfaces,
    },
    footerButton: {
      justifyContent: "center",
      alignItems: "center",
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: "rgba(255,255,255,0.1)",
    },
    captureButton: {
      justifyContent: "center",
      alignItems: "center",
      width: 72,
      height: 72,
      borderRadius: 36,
      backgroundColor: theme.colors.primary,
    },
    captureButtonInner: {
      justifyContent: "center",
      alignItems: "center",
      width: 64,
      height: 64,
      borderRadius: 32,
      backgroundColor: "transparent",
      borderWidth: 2,
      borderColor: "white",
    },
    disabledButton: {
      opacity: 0.6,
    },
    loadingIndicator: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: "center",
      alignItems: "center",
    },
    permissionText: {
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      fontSize: 16,
      marginVertical: 10,
    },
    permissionButton: {
      alignSelf: "center",
      padding: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.primary,
    },
    permissionButtonText: {
      color: theme.colors.screenBackground,
      fontSize: 16,
    },
    cornerHooksContainer: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    cornerHook: {
      position: "absolute",
      width: 40,
      height: 40,
      borderColor: "white",
    },
    topLeftHook: {
      top: 0,
      left: 0,
      borderLeftWidth: 4,
      borderTopWidth: 4,
    },
    topRightHook: {
      top: 0,
      right: 0,
      borderRightWidth: 4,
      borderTopWidth: 4,
    },
    bottomLeftHook: {
      bottom: 0,
      left: 0,
      borderLeftWidth: 4,
      borderBottomWidth: 4,
    },
    bottomRightHook: {
      bottom: 0,
      right: 0,
      borderRightWidth: 4,
      borderBottomWidth: 4,
    },
    scanningOverlay: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0,0,0,0.25)",
    },
    scanningIndicator: {
      width: 100,
      height: 100,
      borderRadius: 50,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(255,255,255,0.15)",
      borderWidth: 2,
      borderColor: "rgba(255,255,255,0.5)",
    },
    scanningText: {
      marginTop: 12,
      color: "white",
      fontSize: 16,
      fontWeight: "600",
    },
    errorContainer: {
      position: "absolute",
      bottom: 20,
      left: 20,
      right: 20,
      padding: 12,
      borderRadius: 8,
      backgroundColor: "rgba(255,0,0,0.7)",
    },
    errorText: {
      color: "white",
      textAlign: "center",
      fontWeight: "600",
    },
  });
};

export default digitalFoodScannerStyles;
