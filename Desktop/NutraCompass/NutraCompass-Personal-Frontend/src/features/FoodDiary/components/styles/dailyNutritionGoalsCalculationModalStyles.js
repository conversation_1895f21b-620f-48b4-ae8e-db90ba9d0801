import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";
const dailyNutritionGoalsCalculationModalStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    // ... your existing styles

    contentContainer: {
      flex: 1,
      width: "100%",
    },
    formContainer: {
      flex: 1,
      justifyContent: "flex-start",
      gap: scaleSize(20),
      padding: scaleSize(20),
    },
    header: {
      minWidth: "100%",
      padding: scaleSize(10),
      justifyContent: "flex-end",
    },
    title: {
      fontSize: scaleSize(22),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      marginBottom: scaleSize(10),
      alignSelf: "center",
    },
    infoBox: {
      fontSize: scaleSize(14),
      textAlign: "left",
      color: "gray",
    },
    label: {
      fontSize: scaleSize(20),
      color: theme.colors.primaryTextColor,
      alignSelf: "center",
      paddingTop: scaleSize(20),
    },
    answer: {
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
      marginBottom: scaleSize(20),
    },
    segmentedButtonContainer: {
      alignSelf: "center",
    },
    input: {
      flex: 1,
      height: scaleSize(40),
    },
    inputValue: {
      fontSize: scaleSize(14),
    },
    resultsLabel: {
      color: theme.colors.primaryTextColor,
    },
    results: {
      color: theme.colors.primaryTextColor,
    },
    navigationButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-end",
      padding: scaleSize(12),
      height: "15%",
      width: "100%",
    },
    backButton: {
      marginRight: "auto", // Aligns to the left
    },
    nextButton: {
      marginLeft: "auto", // Aligns to the right
    },
    card: {
      backgroundColor: theme.colors.surface,
      marginVertical: scaleSize(10),
      padding: scaleSize(10),
      borderRadius: scaleSize(8),
      elevation: 2,
    },
    selectedCard: {
      backgroundColor: theme.colors.primary,
      marginVertical: scaleSize(10),
      padding: scaleSize(10),
      borderRadius: scaleSize(8),
      elevation: 4,
    },
    cardText: {
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
      textAlign: "center",
    },
  });
};

export default dailyNutritionGoalsCalculationModalStyles;
