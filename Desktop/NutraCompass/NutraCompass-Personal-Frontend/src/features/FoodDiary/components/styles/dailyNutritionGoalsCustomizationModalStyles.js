import { Dimensions, StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";
const dailyNutritionGoalsCustomizationModalStyles = () => {
  const { theme } = useThemeContext();
  // Get the screen height
  const screenHeight = Dimensions.get("window").height;

  return StyleSheet.create({
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      width: "100%",
      paddingHorizontal: scaleSize(20),
    },
    closeModalButton: {
      marginTop: scaleSize(20),
    },
    saveButton: {
      marginTop: scaleSize(20),
    },
    formContainer: {
      position: "absolute",
      top: screenHeight * 0.15, // Set top to about 20% below the height
      width: "80%",
      padding: scaleSize(10),
      elevation: 4,
      borderColor: theme.colors.cardBorderColor,
      borderBottomWidth: scaleSize(1),
      justifyContent: "center",
    },
    inputRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    label: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(16),
    },
    input: {
      width: "50%",
      height: scaleSize(40),
      marginBottom: scaleSize(10),
      color: theme.colors.primary,
      backgroundColor: theme.colors.screenBackground,
      textAlign: "center",
      fontSize: scaleSize(16),
    },
    macroInputContainer: {
      justifyContent: "center",
      alignItems: "center",
      flex: 1,
    },
    inputText: {
      color: theme.colors.primary,
    },
  });
};

export default dailyNutritionGoalsCustomizationModalStyles;
