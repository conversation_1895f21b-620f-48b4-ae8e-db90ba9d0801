import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils"; // Import scaleSize

const swipeableFoodEntryItemStyles = (isPartOfMealGroup) => {
  const { theme } = useThemeContext();

  // Create scaled size constants
  const scaled = {
    paddingXS: scaleSize(2),
    paddingS: scaleSize(4),
    paddingM: scaleSize(8),
    paddingL: scaleSize(12),
    borderRadiusM: scaleSize(8),
    fontSizeS: scaleSize(12),
    fontSizeM: scaleSize(14),
    elevationS: scaleSize(3),
  };

  return StyleSheet.create({
    containerStyle: {
      flex: 1,
      flexDirection: "row",
      elevation: isPartOfMealGroup ? scaled.elevationS : scaled.elevationS,
      backgroundColor: isPartOfMealGroup
        ? theme.colors.primary
        : theme.colors.surface,
      borderRadius: scaled.borderRadiusM,
      overflow: "hidden", // Contain everything within
    },
    textContainer: {
      flex: 1,
      backgroundColor: isPartOfMealGroup
        ? theme.colors.surface
        : theme.colors.surface,
      elevation: isPartOfMealGroup ? scaled.elevationS : scaled.elevationS,
      zIndex: 2,
      padding: isPartOfMealGroup ? scaled.paddingXS : scaled.paddingXS,
      borderRadius: scaled.borderRadiusM,
    },
    textStyle: {
      fontSize: scaled.fontSizeM,
    },
    entryInfo: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: scaled.paddingM,
    },
    entryFoodNameText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaled.fontSizeM,
    },
    entryCaloriesText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaled.fontSizeM,
    },
    rightButtonContainer: {
      position: "absolute",
      left: null,
      right: 0,
      alignItems: "center",
      justifyContent: "center",
      borderTopRightRadius: scaled.borderRadiusM,
      borderBottomRightRadius: scaled.borderRadiusM,
      paddingHorizontal: scaleSize(18),
      paddingVertical: scaleSize(10),
      elevation: scaled.elevationS,
      backgroundColor: "#D50000",
      zIndex: 1,
    },
  });
};

export default swipeableFoodEntryItemStyles;
