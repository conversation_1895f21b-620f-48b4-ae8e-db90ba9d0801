import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";

const barcodeScannerStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      width: "100%",
      justifyContent: "center",
      backgroundColor: "white",
    },
    camera: {
      flex: 1,
    },
    maskTop: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      height: "30%", // Adjust the height as needed
      backgroundColor: "rgba(0, 0, 0, 0.7)", // Translucent gray
      zIndex: 1,
    },
    maskLeft: {
      position: "absolute",
      top: "30%",
      left: 0,
      width: "15%", // Adjust the width as needed
      height: "40%", // Adjust the height as needed
      backgroundColor: "rgba(0, 0, 0, 0.7)", // Translucent gray
      zIndex: 1,
    },
    maskRight: {
      position: "absolute",
      top: "30%",
      right: 0,
      width: "15%", // Adjust the width as needed
      height: "40%", // Adjust the height as needed
      backgroundColor: "rgba(0, 0, 0, 0.7)", // Translucent gray
      zIndex: 1,
    },
    maskBottom: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      height: "30%", // Adjust the height as needed
      backgroundColor: "rgba(0, 0, 0, 0.7)", // Translucent gray
      zIndex: 1,
    },
    targetAreaClearOverlay: {
      top: "30%",
      left: "15%",
      width: "70%",
      height: "40%",
      backgroundColor: "rgba(0, 0, 0, 0.0)",
      zIndex: 2,
    },
    targetAreaTintedOverlay: {
      top: "30%",
      left: "15%",
      width: "70%",
      height: "40%",
      backgroundColor: "rgba(0, 0, 0, 0.7)", // Translucent gray
      zIndex: 2,
    },
    targetArea: {
      justifyContent: "center",
      alignItems: "center",
      position: "absolute",
      top: "30%",
      left: "15%",
      width: "70%",
      height: "40%",
      zIndex: 2,
    },
    targetBorder: {
      borderWidth: scaleSize(2),
      borderColor: "white", // Adjust border color as needed
      width: "100%",
      height: "100%",
      position: "absolute",
      top: 0,
      left: 0,
    },
    scanbarCodeInstructionsContainer: {
      justifyContent: "center",
      alignItems: "center",
      position: "absolute",
      top: "14%",
      left: "15%",
      width: "70%",
      zIndex: 2,
    },
    scanText: {
      fontSize: scaleSize(20),
      fontWeight: "bold",
      color: "white", // Adjust text color as needed
      marginBottom: scaleSize,
    },
    instructionText: {
      fontSize: scaleSize(16),
      color: "white", // Adjust text color as needed
    },
    closeButton: {
      position: "absolute",
      top: "8%",
      left: scaleSize(20),
      zIndex: 2,
    },
    flipButton: {
      position: "absolute",
      top: "8%",
      right: scaleSize(20),
      zIndex: 2,
    },
    manualInputContainer: {
      position: "absolute",
      flexDirection: "row",
      alignItems: "center",
      bottom: 0,
      left: 0,
      right: 0,
      padding: scaleSize(10),
      paddingHorizontal: scaleSize(20),
      paddingBottom: scaleSize(20),
      backgroundColor: theme.colors.screenBackground, // Adjust background color as needed
      zIndex: 2,
    },
    barcodeIcon: {
      marginRight: scaleSize(10),
    },
    manualInput: {
      flex: 1,
      height: scaleSize(45),
      borderRadius: scaleSize(4),
      backgroundColor: "transparent",
      paddingHorizontal: scaleSize(10),
      fontSize: scaleSize(18),
      color: theme.colors.primaryTextColor, // Text color
    },
    manualSearchButton: {
      padding: scaleSize(8),
      borderRadius: scaleSize(4),
      backgroundColor: theme.colors.primary, // Adjust background color as needed
      marginLeft: scaleSize(10),
    },
    manualSearchButtonText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(16),
      fontWeight: "bold",
    },
    loadingIndicator: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: "center",
      alignItems: "center",
    },
    errorText: {
      position: "absolute",
      top: "75%",
      justifyContent: "center",
      alignSelf: "center",
      color: "red", // Adjust error text color as needed
    },
    permissionContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "black",
      padding: scaleSize(20),
    },
    permissionText: {
      color: "white",
      fontSize: scaleSize(18),
      textAlign: "center",
      marginBottom: scaleSize(20),
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "black",
      padding: scaleSize(20),
    },
    errorDetail: {
      color: "red",
      marginVertical: scaleSize(10),
    },
    cameraLoadingOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: "rgba(0,0,0,0.7)",
      justifyContent: "center",
      alignItems: "center",
    },
    loadingOverlay: {
      position: "absolute",
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: "rgba(0,0,0,1)",
      alignItems: "center",
      zIndex: 20,
      justifyContent: "center", // Add this to center vertically
    },
    compassContainer: {
      position: "relative",
      width: scaleSize(180),
      height: scaleSize(180),
      justifyContent: "center",
      alignItems: "center",
      marginBottom: scaleSize(5),
    },
    compassBody: {
      position: "absolute",
      zIndex: 1, // Body behind needle
    },
    needlePositionWrapper: {
      position: "absolute",
      top: scaleSize(-10), // Adjust vertical position here
      zIndex: 2,
      width: "100%",
      height: "100%",
      justifyContent: "center",
      alignItems: "center",
    },
    needleContainer: {
      position: "relative",
      width: "100%",
      height: "100%",
      justifyContent: "center",
      alignItems: "center",
      transformOrigin: "center", // Ensure rotation around center
    },
    compassNeedle: {
      // Size controlled inline
    },
    loadingText: {
      color: "white",
      fontSize: scaleSize(18),
      fontWeight: "500",
    },
    scanningDots: {
      flexDirection: "row",
      marginTop: scaleSize(15),
    },
    scanningDot: {
      width: scaleSize(12),
      height: scaleSize(12),
      borderRadius: scaleSize(6),
      backgroundColor: theme.colors.primary,
    },
  });
};

export default barcodeScannerStyles;
