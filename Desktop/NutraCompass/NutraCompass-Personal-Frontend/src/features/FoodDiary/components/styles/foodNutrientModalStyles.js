// foodNutrientModalStyles.js
import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils.js";

const foodNutrientModalStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    modalContentContainer: {
      flex: 1,
      backgroundColor: theme.screenBackground,
      paddingTop: scaleSize(50), // Added top padding for status bar
    },
    customHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      height: scaleSize(50),
      paddingHorizontal: scaleSize(16),
      backgroundColor: theme.colors.screenBackground,
    },
    headerButton: {
      width: scaleSize(40),
      height: scaleSize(40),
      justifyContent: "center",
      alignItems: "center",
    },
    headerTitle: {
      fontSize: scaleSize(20),
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      flex: 1, // Allows title to take available space
    },
    headerSpacer: {
      width: scaleSize(40), // Matches back button width for balance
    },
    sectionContainer: {
      padding: scaleSize(16),
      backgroundColor: theme.colors.surface,
    },
    foodItemName: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(20),
      fontWeight: "bold",
      marginBottom: scaleSize(8),
    },
    brandCompany: {
      color: "gray",
      fontSize: scaleSize(14),
    },
    rowContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    inputLabel: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(15),
      marginRight: scaleSize(16),
      flex: 1,
    },
    textInput: {
      flex: 0.5, // Occupies about a quarter of the row width
      fontSize: scaleSize(16),
      height: scaleSize(40),
      borderWidth: scaleSize(1),
      borderRadius: scaleSize(8),
      backgroundColor: theme.colors.surface,
      borderColor: theme.colors.primary,
    },
    macroNutrientContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: scaleSize(50),
      paddingRight: scaleSize(30),
    },
    macroNutrientColumn: {
      flex: 1,
      flexDirection: "column",
      gap: scaleSize(10),
      alignItems: "center",
      justifyContent: "center",
    },
    macroNutrientPercentage: {
      fontSize: scaleSize(12),
    },
    macroNutrientValue: {
      fontSize: scaleSize(16),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    macroNutrientLabel: {
      fontSize: scaleSize(12),
      color: theme.colors.primaryTextColor,
    },
    progressContainer: {
      flexDirection: "row",
      marginTop: scaleSize(20),
      gap: scaleSize(20),
    },
    progressItem: {
      flex: 1,
      marginBottom: scaleSize(10),
      gap: scaleSize(5),
      alignItems: "center",
      justifyContent: "center",
    },
    percentOfDailyGoalsSectionHeaderText: {
      color: theme.colors.primaryTextColor,
    },
    nutritionFactsLabel: {
      fontSize: scaleSize(16),
      color: theme.colors.primaryTextColor,
    },
    nutritionFactsIndentLabel: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
      paddingLeft: scaleSize(15),
    },
    nutritionalFactsLabelContainer: {
      justifyContent: "flex-start",
      flex: 1,
    },
    nutritionalFactsValuesContainer: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    nutritionFactsValue: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
    },
    nutritionFactsIndentValue: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
    },
    nutritionFactsDailyValue: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
    },
    nutritionFactsIndentDailyValue: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
    },
    nutritionFactsRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: scaleSize(5),
      paddingHorizontal: scaleSize(16),
    },
    nutritionFactsSeparator: {
      borderBottomWidth: scaleSize(1),
      borderBottomColor: theme.colors.cardBorderColor,
      marginVertical: scaleSize(10),
    },
    nutritionFactsSection: {
      marginTop: scaleSize(10),
    },
    nutritionFactsSectionHeader: {
      fontSize: scaleSize(18),
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
  });
};

export default foodNutrientModalStyles;
