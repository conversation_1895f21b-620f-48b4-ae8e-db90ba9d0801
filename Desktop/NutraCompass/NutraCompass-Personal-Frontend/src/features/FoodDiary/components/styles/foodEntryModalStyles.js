import { StyleSheet } from "react-native";
import { useThemeContext } from "../../../../context/ThemeContext.js";
import { scaleSize } from "../../../../utils/deviceUtils";

const foodEntryModalStyles = () => {
  const { theme, mode } = useThemeContext();

  return StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "transparent",
    },
    modalHeader: {
      width: "100%",
      backgroundColor: theme.colors.screenBackground,
      paddingTop: scaleSize(50), // Added top padding for status bar
    },
    customHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      height: scaleSize(50),
      paddingHorizontal: scaleSize(16),
      backgroundColor: theme.colors.screenBackground,
    },
    backButton: {
      width: scaleSize(40),
      height: scaleSize(40),
      justifyContent: "center",
      alignItems: "center",
    },
    headerTitle: {
      fontSize: scaleSize(20),
      fontWeight: "600",
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      flex: 1, // Allows title to take available space
    },
    headerSpacer: {
      width: scaleSize(40), // Matches back button width for balance
    },
    modalContent: {
      flex: 1,
      width: "100%",
      backgroundColor: theme.colors.screenBackground,
    },
    flatlist: {
      flex: 1,
    },
    modalTitle: {
      fontSize: scaleSize(20),
      fontWeight: "bold",
      marginBottom: scaleSize(16),
      color: theme.colors.primaryTextColor,
    },
    input: {
      borderWidth: scaleSize(1),
      borderColor: theme.colors.primary,
      color: theme.colors.primaryTextColor,
      padding: scaleSize(10),
      marginBottom: scaleSize(12),
      width: "100%",
      borderRadius: scaleSize(4),
    },
    modalButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: scaleSize(10),
      paddingHorizontal: scaleSize(20),
      borderRadius: scaleSize(4),
      marginBottom: scaleSize(12),
      marginTop: scaleSize(12),
    },
    modalButtonText: {
      color: theme.colors.primaryTextColor,
      fontWeight: "bold",
      fontSize: scaleSize(16),
    },
    foodItemContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      backgroundColor: theme.colors.surface,
      elevation: scaleSize(4),
      paddingHorizontal: scaleSize(6),
      paddingVertical: scaleSize(6),
      marginBottom: scaleSize(6),
      borderRadius: scaleSize(16),
      borderWidth: scaleSize(1),
      borderColor: "rgba(204, 204, 204, 0.3)",
    },
    foodInfoContainer: {
      flex: 1,
      marginRight: scaleSize(12),
      gap: scaleSize(2),
    },
    foodLabel: {
      fontSize: scaleSize(14),
      color: theme.colors.primaryTextColor,
    },
    foodLabelCalories: {
      fontSize: scaleSize(12),
      color:
        mode === "light"
          ? theme.colors.primaryTextColor
          : theme.colors.subTextColor,
    },
    foodLabelServingSize: {
      fontSize: scaleSize(12),
      color:
        mode === "light"
          ? theme.colors.primaryTextColor
          : theme.colors.subTextColor,
    },
    selectButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: scaleSize(10),
      paddingHorizontal: scaleSize(20),
      borderRadius: scaleSize(4),
    },
    barcodeScannerContainer: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
  });
};

export default foodEntryModalStyles;
