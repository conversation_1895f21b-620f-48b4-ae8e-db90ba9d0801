import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  Dimensions,
} from "react-native";
import Modal from "react-native-modal";
import Feather from "react-native-vector-icons/Feather";
import MacroPercentageSlider from "../../../components/MacroPercentageSlider.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const { height } = Dimensions.get("window");

const MacroSettingsModal = ({
  isVisible,
  closeModal,
  tempChanges,
  setTempChanges,
  setActiveField,
  handleInputChange,
  handleInputUpdate,
  calories,
  protein,
  carb,
  fat,
}) => {
  const { theme } = useThemeContext();

  const numericProtein =
    tempChanges?.protein !== undefined
      ? parseFloat(tempChanges.protein)
      : protein;

  const numericCarb =
    tempChanges?.carb !== undefined ? parseFloat(tempChanges.carb) : carb;

  const numericFat =
    tempChanges?.fat !== undefined ? parseFloat(tempChanges.fat) : fat;

  const isSum100Percent = numericProtein + numericCarb + numericFat === 100;

  return (
    <Modal
      coverScreen={false}
      onBackdropPress={() => {
        closeModal();
        setActiveField(null);
      }}
      isVisible={isVisible}
      style={{
        margin: 0,
        justifyContent: "flex-end",
      }}
      avoidKeyboard={true}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={400}
      animationOutTiming={400}
    >
      <View
        style={{
          backgroundColor: theme.colors.surface,
          borderTopLeftRadius: scaleSize(20),
          borderTopRightRadius: scaleSize(20),
          maxHeight: height * 0.8,
        }}
      >
        {/* Header */}
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            padding: scaleSize(15),
            borderBottomWidth: scaleSize(1),
            borderBottomColor: theme.colors.cardBorderColor,
          }}
        >
          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss();
              setTempChanges({});
              setActiveField(null);
              closeModal();
            }}
            style={{ width: scaleSize(40) }}
          >
            <Feather
              name="x"
              size={scaleSize(24)}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>

          <Text
            style={{
              flex: 1,
              color: theme.colors.primaryTextColor,
              fontSize: scaleSize(18),
              textAlign: "center",
              fontWeight: "bold",
            }}
          >
            Macros
          </Text>

          <TouchableOpacity
            onPress={() => {
              handleInputUpdate();
              closeModal();
            }}
            style={{
              width: scaleSize(40),
              alignItems: "center",
              opacity: isSum100Percent ? 1 : 0.5,
            }}
            disabled={!isSum100Percent}
          >
            <Feather
              name="check"
              size={scaleSize(24)}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
        </View>

        {/* Slider Content */}
        <View style={{ padding: scaleSize(15) }}>
          <MacroPercentageSlider
            selectedValues={{
              protein: numericProtein,
              carb: numericCarb,
              fat: numericFat,
            }}
            onSelect={(field, value) => handleInputChange(field, value)}
            calories={calories}
          />
        </View>
      </View>
    </Modal>
  );
};

export default MacroSettingsModal;
