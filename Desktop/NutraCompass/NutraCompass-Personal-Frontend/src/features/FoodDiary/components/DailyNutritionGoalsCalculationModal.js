import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
  Platform,
  useWindowDimensions,
} from "react-native";
import { Image } from "expo-image";
import { Card, Button, TextInput, Snackbar } from "react-native-paper";
import * as Haptics from "expo-haptics";
import Feather from "react-native-vector-icons/Feather";
import dailyNutritionGoalsCalculationModalStyles from "./styles/dailyNutritionGoalsCalculationModalStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useNutritionProgram } from "../../NutritionalProgram/context/NutritionProgramContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const ResultsSlideContent = React.memo(
  ({
    goal,
    sex,
    heightFeet,
    heightInches,
    weight,
    weightUnit,
    age,
    bodyFatPercentageRange,
    activityLevel,
    theme,
    generatedNutritionalGoals,
  }) => {
    const styles = dailyNutritionGoalsCalculationModalStyles();

    return (
      <ScrollView
        style={{ height: "100%", width: "100%" }}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: scaleSize(200),
        }}
      >
        <TouchableOpacity activeOpacity={1} style={{ flexGrow: 1 }}>
          {/* Avatar and Floating Cards Section */}
          <View style={{ alignItems: "center", gap: scaleSize(scaleSize(8)) }}>
            {/* Floating Cards Around the Avatar */}
            <View
              style={{
                flexDirection: "row",
                flexWrap: "wrap",
                justifyContent: "center",
                width: "100%",
                gap: scaleSize(scaleSize(8)),
              }}
            >
              {/* Floating Cards */}
              <View
                style={{
                  paddingHorizontal: scaleSize(scaleSize(8)),
                  paddingVertical: scaleSize(scaleSize(8)),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(scaleSize(8)),
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(2) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Goal: {goal}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(2) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Sex: {sex || "Not specified"}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(2) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Height: {heightFeet} ft {heightInches} in
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(2) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Weight: {weight} {weightUnit}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(2) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Age: {age} years
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(2) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Body Fat: {bodyFatPercentageRange || "Not specified"}%
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: 3,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: scaleSize(2) },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Activity Level:{" "}
                  {activityLevel
                    ? activityLevel.replace(/\s*\([^)]*\)/g, "") // Removes text between parentheses
                    : "Not specified"}
                </Text>
              </View>
            </View>

            {/* User Avatar in the center */}
            {/* <View style={{ flexDirection: "row", paddingTop: scaleSize(8) }}>
              <View style={{ flex: 1, alignItems: "center" }}>
                <Image
                  source={require("../../../../assets/BodyFatImages/1.png")}
                  style={{
                    width: 120,
                    height: 120,
                    borderRadius: 60,
                    backgroundColor: "#ddd", // Placeholder background, change as needed
                  }}
                />
              </View> 
            </View> */}
          </View>

          {/* Nutritional Goals Section When There Is No Goal Weight, Start Date, nor End Date */}
          {generatedNutritionalGoals && (
            <View
              style={{
                paddingHorizontal: scaleSize(16),
                marginTop: scaleSize(16),
              }}
            >
              <Card style={{ marginBottom: scaleSize(20) }}>
                <Card.Title
                  title="Your Nutritional Goals"
                  titleStyle={{
                    fontSize: scaleSize(16),
                    color: theme.colors.primaryTextColor,
                    paddingVertical: scaleSize(12),
                  }}
                />
                <Card.Content style={{ gap: scaleSize(6) }}>
                  {/* Daily Calories */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: scaleSize(10),
                      borderRadius: scaleSize(8),
                    }}
                  >
                    <Text style={[styles.cardText]}>Calories</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {generatedNutritionalGoals?.dailyCalories.toLocaleString() ||
                        "Not calculated yet"}
                    </Text>
                  </View>

                  {/* Carbs */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: scaleSize(10),
                      borderRadius: scaleSize(8),
                    }}
                  >
                    <Text style={[styles.cardText]}>Carbs</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {`${generatedNutritionalGoals?.carbGrams} grams` ||
                        "Not calculated yet"}{" "}
                      ({generatedNutritionalGoals?.carbPercentage * 100}%)
                    </Text>
                  </View>

                  {/* Protein */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: scaleSize(10),
                      borderRadius: scaleSize(8),
                    }}
                  >
                    <Text style={[styles.cardText]}>Protein</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {`${generatedNutritionalGoals?.proteinGrams} grams` ||
                        "Not calculated yet"}{" "}
                      ({generatedNutritionalGoals?.proteinPercentage * 100}%)
                    </Text>
                  </View>

                  {/* Fats */}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: theme.colors.screenBackground,
                      padding: scaleSize(10),
                      borderRadius: scaleSize(8),
                    }}
                  >
                    <Text style={[styles.cardText]}>Fats</Text>
                    <Text
                      style={[
                        styles.inputValue,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {`${generatedNutritionalGoals?.fatGrams} grams` ||
                        "Not calculated yet"}{" "}
                      ({generatedNutritionalGoals?.fatPercentage * 100}%)
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            </View>
          )}
        </TouchableOpacity>
      </ScrollView>
    );
  }
);

const DailyNutritionGoalsCalculationModal = ({ isVisible, closeModal }) => {
  const styles = dailyNutritionGoalsCalculationModalStyles();
  const { theme } = useThemeContext();
  const { calculateNutritionalGoals, generatedNutritionalGoals } =
    useNutritionProgram();
  const { setCalorieAndMacroGoals, getUserProfile } = useUserSettings();
  const userProfile = getUserProfile();

  // Parse bodyWeight string to extract value and unit
  const parseBodyWeight = (bodyWeight) => {
    if (!bodyWeight) return { value: "", unit: "lbs" };

    const parts = bodyWeight.trim().split(/\s+/);
    if (parts.length < 2) return { value: "", unit: "lbs" };

    const value = parts[0];
    const unit = parts[1].toLowerCase();

    return {
      value,
      unit: unit.includes("kg") ? "kg" : "lbs", // Default to lbs if not kg
    };
  };

  const { value: initialWeight, unit: initialWeightUnit } = parseBodyWeight(
    userProfile?.bodyWeight
  );
  const numericAge = userProfile?.age ? String(userProfile.age) : ""; // Convert age to string

  const [formState, setFormState] = useState({
    goal: "Maintain",
    sex: userProfile?.sex || "",
    heightFeet: "",
    heightInches: "",
    weight: initialWeight,
    weightUnit: initialWeightUnit,
    age: numericAge,
    bodyFatPercentageRange: null,
    activityLevel: "",
  });

  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [currentSlide, setCurrentSlide] = useState(0);
  const [heightFeetFocused, setHeightFeetFocused] = useState(false);
  const [heightInchesFocused, setHeightInchesFocused] = useState(false);
  const [weightFocused, setWeightFocused] = useState(false);
  const [ageFocused, setAgeFocused] = useState(false);

  useEffect(() => {
    if (!isVisible) {
      resetFormFields();
      setSnackbarMessage("");
      setSnackbarVisible(false);
      setCurrentSlide(0); // Reset to the first slide
    }
  }, [isVisible]);

  const resetFormFields = () => {
    setFormState({
      goal: "Maintain",
      sex: userProfile?.sex || "",
      heightFeet: "",
      heightInches: "",
      weight: initialWeight,
      weightUnit: initialWeightUnit,
      age: numericAge,
      bodyFatPercentageRange: null,
      activityLevel: "",
    });
  };

  const updateFormState = (key, value) => {
    setFormState((prevState) => {
      if (prevState[key] === value) return prevState;
      return { ...prevState, [key]: value };
    });
  };

  const handleOptionSelect = (key, value) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    updateFormState(key, value);
  };

  const handleSlideChange = (newSlide) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (newSlide >= 0 && newSlide < slides.length) {
      setHeightFeetFocused(false);
      setHeightInchesFocused(false);
      setWeightFocused(false);
      setAgeFocused(false);
      setCurrentSlide(newSlide);
      Keyboard.dismiss(); // Dismiss the keyboard when changing slides
    } else {
      console.error("Invalid slide index:", newSlide);
    }
  };

  const handleUpdateNutritionalGoals = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      if (
        generatedNutritionalGoals.dailyCalories &&
        generatedNutritionalGoals.proteinPercentage &&
        generatedNutritionalGoals.carbPercentage &&
        generatedNutritionalGoals.fatPercentage
      ) {
        setCalorieAndMacroGoals({
          calorieGoal: generatedNutritionalGoals.dailyCalories,
          proteinPercentage: generatedNutritionalGoals.proteinPercentage * 100,
          carbPercentage: generatedNutritionalGoals.carbPercentage * 100,
          fatPercentage: generatedNutritionalGoals.fatPercentage * 100,
        });
        setSnackbarMessage("Nutritional goals updated successfully!");
        setSnackbarVisible(true);
      }
    } catch (error) {
      console.error("Error updating nutritional goals:", error);
      setSnackbarMessage("Failed to update nutritional goals.");
      setSnackbarVisible(true);
    }
  };

  const calculateDailyNutritionalGoals = useCallback(async () => {
    try {
      // Ensure all necessary fields are provided
      if (
        !formState.sex ||
        !formState.heightFeet ||
        !formState.heightInches ||
        !formState.weight ||
        !formState.weightUnit ||
        !formState.age ||
        !formState.activityLevel ||
        !formState.goal
      ) {
        console.error("Missing or invalid input fields");
        return;
      }

      // Call the backend for calculating daily nutritional goals
      await calculateNutritionalGoals(
        formState.sex,
        formState.heightFeet,
        formState.heightInches,
        formState.weight,
        formState.weightUnit,
        formState.age,
        formState.activityLevel,
        formState.goal
      );
    } catch (error) {
      console.error("Error calculating nutritional goals:", error);
    }
  }, [
    formState.sex,
    formState.heightFeet,
    formState.heightInches,
    formState.weight,
    formState.weightUnit,
    formState.age,
    formState.activityLevel,
    formState.goal,
  ]);

  const bodyTypes = [
    {
      id: 1,
      value: 8,
      description: "Very Lean\n5-10% body fat",
      image: require("../../../../assets/BodyFatImages/1.png"),
    },
    {
      id: 2,
      value: 13,
      description: "Lean\n11-15% body fat",
      image: require("../../../../assets/BodyFatImages/2.png"),
    },
    {
      id: 3,
      value: 18,
      description: "Moderately Lean\n16-20% body fat",
      image: require("../../../../assets/BodyFatImages/3.png"),
    },
    {
      id: 4,
      value: 23,
      description: "Average\n21-25% body fat",
      image: require("../../../../assets/BodyFatImages/4.png"),
    },
    {
      id: 5,
      value: 28,
      description: "Above Average\n26-30% body fat",
      image: require("../../../../assets/BodyFatImages/5.png"),
    },
    {
      id: 6,
      value: 33,
      description: "Overweight\n31-35% body fat",
      image: require("../../../../assets/BodyFatImages/6.png"),
    },
    {
      id: 7,
      value: 40,
      description: "Obese\n36%+ body fat",
      image: require("../../../../assets/BodyFatImages/7.png"),
    },
  ];

  const slides = [
    {
      title: "Goals Based Nutritional Calculator ",
      content: (
        <View style={{ alignItems: "center", padding: scaleSize(20) }}>
          <Image
            source={require("../../../../assets/GoalsBasedNutritionalCalculator.png")}
            style={{
              width: "90%",
              height: scaleSize(300),
              contentFit: "cover",
              borderRadius: scaleSize(30),
            }}
          />
          <Card
            style={{
              marginVertical: scaleSize(20),
              marginHorizontal: scaleSize(20),
            }}
          >
            <Card.Content>
              <Text
                style={{
                  fontSize: scaleSize(18),
                  textAlign: "center",
                  color: "gray",
                }}
              >
                Get ready to enter some key details about your body and
                lifestyle. We'll use this information to estimate your daily
                calorie needs and suggest the best macronutrient distribution to
                help you achieve your goals.
              </Text>
            </Card.Content>
          </Card>
        </View>
      ),
    },
    {
      title: "Your Sex",
      content: (
        <View>
          <TouchableOpacity onPress={() => handleOptionSelect("sex", "Male")}>
            <Card
              style={
                formState?.sex === "Male" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Male</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleOptionSelect("sex", "Female")}>
            <Card
              style={
                formState?.sex === "Female" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Female</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
        </View>
      ),
    },
    {
      title: "Your Height",
      content: (
        <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
          <View
            style={[
              {
                flex: 1,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              heightFeetFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="Feet"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.heightFeet}
              onFocus={() => setHeightFeetFocused(true)}
              onBlur={() => setHeightFeetFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^[1-8]$/.test(text)) {
                  updateFormState("heightFeet", text);
                }
              }}
            />
          </View>
          <View
            style={[
              {
                flex: 1,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              heightInchesFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="Inches"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.heightInches}
              onFocus={() => setHeightInchesFocused(true)}
              onBlur={() => setHeightInchesFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^([0-9]|1[0-1])$/.test(text)) {
                  updateFormState("heightInches", text);
                }
              }}
            />
          </View>
        </View>
      ),
    },
    {
      title: "Your Bodyweight",
      content: (
        <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
          {/* Weight Input */}
          <View
            style={[
              {
                flex: 0.7,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              weightFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder={formState?.weightUnit === "kg" ? "kg" : "lbs"}
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.weight}
              onFocus={() => setWeightFocused(true)}
              onBlur={() => setWeightFocused(false)}
              onChangeText={(text) => {
                // Allow decimals and empty value
                if (text === "" || /^\d*\.?\d*$/.test(text)) {
                  updateFormState("weight", text);
                }
              }}
            />
          </View>

          {/* Unit Selector */}
          <View
            style={{
              flex: 0.3,
              flexDirection: "row",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              borderRadius: scaleSize(10),
              overflow: "hidden",
            }}
          >
            <TouchableOpacity
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor:
                  formState?.weightUnit === "lbs"
                    ? "rgba(255, 255, 255, 0.3)"
                    : "transparent",
              }}
              onPress={() => updateFormState("weightUnit", "lbs")}
            >
              <Text
                style={{
                  fontSize: scaleSize(20),
                  color: theme.colors.primaryTextColor,
                }}
              >
                lbs
              </Text>
            </TouchableOpacity>

            <View
              style={{
                width: 1,
                backgroundColor: "rgba(255,255,255,0.2)",
              }}
            />

            <TouchableOpacity
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor:
                  formState?.weightUnit === "kg"
                    ? "rgba(255, 255, 255, 0.3)"
                    : "transparent",
              }}
              onPress={() => updateFormState("weightUnit", "kg")}
            >
              <Text
                style={{
                  fontSize: scaleSize(20),
                  color: theme.colors.primaryTextColor,
                }}
              >
                kg
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      ),
    },
    {
      title: "Your Age",
      content: (
        <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
          <View
            style={[
              {
                flex: 1,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              ageFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="# years old"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.age}
              onFocus={() => setAgeFocused(true)}
              onBlur={() => setAgeFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^[0-9]{1,2}$/.test(text)) {
                  updateFormState("age", text);
                }
              }}
            />
          </View>
        </View>
      ),
    },
    {
      title: "Your Body Type",
      content: (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: scaleSize(12) }}
        >
          {bodyTypes.map((bodyType) => (
            <TouchableOpacity
              key={bodyType.id}
              onPress={() =>
                updateFormState(
                  "bodyFatPercentageRange",
                  bodyType.value.toString()
                )
              }
              style={[
                formState?.bodyFatPercentageRange ===
                  bodyType.value.toString() && {
                  backgroundColor: theme.colors.primary,
                },
                {
                  borderWidth: scaleSize(1),
                  flex: 1,
                  aspectRatio: 3 / 4,
                  borderRadius: scaleSize(10),
                  justifyContent: "center",
                  alignItems: "center",
                  margin: scaleSize(10),
                  padding: scaleSize(10),
                },
              ]}
            >
              <Image
                source={bodyType.image}
                style={{
                  width: scaleSize(175),
                  height: scaleSize(200),
                  alignSelf: "center",
                }}
                contentFit="contain"
              />
              <Text
                style={[
                  styles.cardText,
                  { paddingBottom: scaleSize(12) },
                  formState?.bodyFatPercentageRange ===
                    bodyType.value.toString(),
                ]}
              >
                {bodyType.description}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      ),
    },
    {
      title: "Your Activity Level",
      content: (
        <View
          style={{
            flexDirection: "row",
            flexWrap: "wrap",
            justifyContent: "space-between",
          }}
        >
          {[
            { value: "Sedentary (BMR x 0.2)", label: "Sedentary" },
            { value: "Lightly Active (BMR x 0.375)", label: "Lightly Active" },
            {
              value: "Moderately Active (BMR x 0.5)",
              label: "Moderately Active",
            },
            { value: "Very Active (BMR x 0.9)", label: "Very Active" },
          ].map((activity) => (
            <TouchableOpacity
              key={activity.value}
              onPress={() => updateFormState("activityLevel", activity.value)}
              style={[
                {
                  width: "48%",
                  borderRadius: scaleSize(10),
                  padding: scaleSize(10),
                  paddingVertical: scaleSize(20),
                  justifyContent: "center",
                  alignItems: "center",
                  marginVertical: scaleSize(10),
                  borderWidth: scaleSize(1),
                  backgroundColor: theme.colors.surface,
                },
                formState?.activityLevel === activity.value && {
                  backgroundColor: theme.colors.primary,
                },
              ]}
            >
              <Text
                style={{
                  textAlign: "center",
                  fontSize: scaleSize(14),
                  color: theme.colors.primaryTextColor,
                  paddingHorizontal: scaleSize(5),
                }}
              >
                {activity.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      ),
    },
    {
      title: "I want to",
      content: (
        <View>
          <TouchableOpacity onPress={() => handleOptionSelect("goal", "Lose")}>
            <Card
              style={
                formState?.goal === "Lose" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Lose Weight</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleOptionSelect("goal", "Maintain")}
          >
            <Card
              style={
                formState?.goal === "Maintain"
                  ? styles.selectedCard
                  : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Maintain Weight</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleOptionSelect("goal", "Build")}>
            <Card
              style={
                formState?.goal === "Build" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Build Muscle</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
        </View>
      ),
    },
    {
      title: "Your Results",
      content: (
        <ResultsSlideContent
          goal={formState?.goal}
          sex={formState?.sex}
          heightFeet={formState?.heightFeet}
          heightInches={formState?.heightInches}
          weight={formState?.weight}
          weightUnit={formState?.weightUnit}
          age={formState?.age}
          bodyFatPercentageRange={formState?.bodyFatPercentageRange}
          activityLevel={formState?.activityLevel}
          theme={theme}
          generatedNutritionalGoals={generatedNutritionalGoals}
        />
      ),
    },
  ];

  return (
    <View style={{ flex: 1, height: "100%", width: "100%", margin: 0 }}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView
          style={{
            flex: 1,
            flexDirection: "column",
            backgroundColor: theme.colors.screenBackground,
            alignItems: "center",
            paddingBottom: 0,
          }}
        >
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                closeModal();
              }}
            >
              <Feather
                name="chevron-left"
                color={theme.colors.primaryTextColor}
                size={scaleSize(38)}
              />
            </TouchableOpacity>
          </View>
          {/* Main content with keyboard avoidance */}
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={[styles.contentContainer, { flex: 1 }]}
          >
            <View
              style={{
                flex: 1,
                paddingBottom: scaleSize(6),
              }}
            >
              <View
                style={{
                  flex: 1,
                  gap:
                    currentSlide > 0 && currentSlide < slides.length - 1
                      ? scaleSize(34)
                      : scaleSize(12),
                  paddingTop:
                    currentSlide > 0 && currentSlide < slides.length - 1
                      ? "5%"
                      : 0,
                }}
              >
                <Text
                  style={{
                    ...styles.label,
                    textAlign: "center",
                    paddingTop: currentSlide == slides.length - 1 ? 0 : 0,
                  }}
                >
                  {slides[currentSlide].title}
                </Text>
                <View>
                  <View>{slides[currentSlide].content}</View>
                </View>
              </View>
            </View>

            {/* Floating buttons container */}
            <View style={styles.navigationButtons}>
              {currentSlide > 0 && currentSlide !== slides.length - 1 && (
                <TouchableOpacity
                  style={{
                    paddingVertical: scaleSize(12),
                    paddingHorizontal: scaleSize(16),
                    borderRadius: scaleSize(16),
                    borderWidth: scaleSize(1),
                    borderColor: theme.colors.primaryTextColor,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  onPress={() => {
                    handleSlideChange(currentSlide - 1);
                  }}
                  accessibilityLabel="Go Back"
                  accessibilityRole="button"
                >
                  <Text
                    style={{
                      color: theme.colors.primary,
                      fontSize: scaleSize(16),
                    }}
                  >
                    Back
                  </Text>
                </TouchableOpacity>
              )}
              <View style={{ flex: 1 }} />

              {currentSlide < slides.length - 1 && (
                <>
                  {slides[currentSlide].title === "I want to" ? (
                    <TouchableOpacity
                      style={{
                        paddingVertical: scaleSize(12),
                        paddingHorizontal: scaleSize(16),
                        borderRadius: scaleSize(16),
                        borderWidth: scaleSize(1),
                        borderColor: theme.colors.primaryTextColor,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                      onPress={() => {
                        if (
                          formState.goal === "Maintain" ||
                          formState.goal === "Build" ||
                          formState.goal === "Lose"
                        ) {
                          calculateDailyNutritionalGoals();
                          handleSlideChange(currentSlide + 1);
                        } else {
                          setSnackbarMessage("Please select a goal.");
                          setSnackbarVisible(true);
                        }
                      }}
                      accessibilityLabel="Calculate Nutritional Goals"
                      accessibilityRole="button"
                    >
                      <Text
                        style={{
                          color: theme.colors.primary,
                          fontSize: scaleSize(16),
                        }}
                      >
                        Calculate
                      </Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      style={{
                        paddingVertical: scaleSize(12),
                        paddingHorizontal: scaleSize(16),
                        borderRadius: scaleSize(16),
                        borderWidth: scaleSize(1),
                        borderColor: theme.colors.primaryTextColor,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                      onPress={() => {
                        // Validate input for the slide
                        if (
                          (currentSlide === 1 && !formState?.sex) ||
                          (currentSlide === 2 &&
                            (!formState?.heightFeet ||
                              !formState?.heightInches)) ||
                          (currentSlide === 3 && !formState?.weight) ||
                          (currentSlide === 4 && !formState?.age) ||
                          (currentSlide === 5 &&
                            !formState?.bodyFatPercentageRange) ||
                          (currentSlide === 6 && !formState?.activityLevel)
                        ) {
                          setSnackbarMessage("Please provide an answer.");
                          setSnackbarVisible(true);
                        } else {
                          handleSlideChange(currentSlide + 1);
                        }
                      }}
                      accessibilityLabel="Next Slide"
                      accessibilityRole="button"
                    >
                      <Text
                        style={{
                          color: theme.colors.primary,
                          fontSize: scaleSize(16),
                        }}
                      >
                        Next
                      </Text>
                    </TouchableOpacity>
                  )}
                </>
              )}

              {currentSlide === slides.length - 1 && (
                <TouchableOpacity
                  style={{
                    paddingVertical: scaleSize(12),
                    paddingHorizontal: scaleSize(16),
                    borderRadius: scaleSize(16),
                    borderWidth: scaleSize(1),
                    borderColor: theme.colors.primaryTextColor,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  accessibilityLabel="Update Nutritional Goals"
                  accessibilityRole="button"
                  onPress={handleUpdateNutritionalGoals} // Method for updating nutritional goals
                >
                  <Text
                    style={{
                      color: theme.colors.primary,
                      fontSize: scaleSize(16),
                    }}
                  >
                    Update Nutritional Goals
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </TouchableWithoutFeedback>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: "OK",
          onPress: () => {},
          labelStyle: {
            fontSize: scaleSize(16),
            paddingVertical: scaleSize(8), // Added vertical padding
            color: theme.colors.primary, // Use primary color
          },
          style: {
            paddingHorizontal: scaleSize(12), // Add horizontal padding
          },
        }}
        style={{
          backgroundColor: theme.colors.surface,
          paddingVertical: scaleSize(12), // Increased vertical padding
          minHeight: scaleSize(56), // Ensure minimum height
          alignItems: "center", // Center content vertically
          justifyContent: "center", // Center content horizontally
        }}
        wrapperStyle={{
          bottom: scaleSize(80), // Adjust position if needed
        }}
      >
        <Text
          style={{
            fontSize: scaleSize(16),
            color: theme.colors.primaryTextColor,
            textAlign: "center",
            lineHeight: scaleSize(24), // Add line height for vertical spacing
            paddingHorizontal: scaleSize(8), // Add horizontal padding
          }}
          numberOfLines={2} // Allow text to wrap
        >
          {snackbarMessage}
        </Text>
      </Snackbar>
    </View>
  );
};

export default DailyNutritionGoalsCalculationModal;
