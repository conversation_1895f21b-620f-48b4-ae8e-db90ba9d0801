import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Easing,
  Keyboard,
} from "react-native";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { scaleSize } from "../../../utils/deviceUtils";
import {
  generateNutrientStructure,
  NUTRIENT_LABELS,
  NUTRIENT_UNITS,
} from "../../../utils/nutrientUtils";

const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);

// Nutrient descriptions
const NUTRIENT_DESCRIPTIONS = {
  ENERC_KCAL: "Energy for daily activities and bodily functions",
  PROCNT: "Builds and repairs tissues, muscles, and organs",
  CHOCDF: "Primary energy source, especially for the brain",
  FAT: "Stores energy, produces hormones, absorbs nutrients",
  FASAT: "Saturated fats should be limited for heart health",
  FATRN: "Trans fats increase heart disease risk",
  FAPU: "Polyunsaturated fats support cell function and heart health",
  FAMS: "Monounsaturated fats help maintain healthy cholesterol",
  CHOLE: "Essential for hormones but excess increases heart disease risk",
  FIBTG: "Supports digestion, blood sugar control, and heart health",
  SUGAR: "Simple carbohydrates providing quick energy",
  // Vitamins
  VITA_RAE: "Essential for vision, immune function, and skin health",
  VITC: "Antioxidant that supports immune system and collagen production",
  VITD: "Crucial for bone health and immune function",
  TOCPHA: "Powerful antioxidant that protects cells from damage",
  VITK1: "Essential for blood clotting and bone metabolism",
  THIA: "Converts food into energy and supports nerve function",
  RIBF: "Helps convert food into energy and maintain healthy skin",
  NIA: "Supports metabolism and nervous system function",
  VITB6A: "Important for brain development and immune function",
  FOLDFE: "Crucial for DNA synthesis and cell division",
  VITB12: "Essential for nerve function and red blood cell formation",
  // Minerals
  CA: "Builds strong bones and teeth, supports muscle function",
  FE: "Essential for oxygen transport in blood and energy production",
  MG: "Supports muscle, nerve function, and energy production",
  P: "Builds bones and teeth, helps filter waste in kidneys",
  K: "Regulates fluid balance, nerve signals, and muscle contractions",
  NA: "Maintains fluid balance but excess can raise blood pressure",
  ZN: "Supports immune function and wound healing",
};

// Nutrient icon mapping with valid icons
const NUTRIENT_ICONS = {
  ENERC_KCAL: { icon: "fire", color: "#FF9800" },
  PROCNT: { icon: "food-steak", color: "#4CAF50" },
  CHOCDF: { icon: "corn", color: "#2196F3" },
  FAT: { icon: "sausage", color: "#FF5722" },
  FASAT: { icon: "food-drumstick", color: "#795548" },
  FATRN: { icon: "alert-octagon", color: "#9C27B0" },
  FAPU: { icon: "fish", color: "#00BCD4" },
  FAMS: { icon: "chart-bell-curve", color: "#8BC34A" },
  CHOLE: { icon: "egg", color: "#FFEB3B" },
  FIBTG: { icon: "chart-line", color: "#FF9800" },
  SUGAR: { icon: "candy", color: "#F44336" },
  // Vitamins
  VITA_RAE: { icon: "carrot", color: "#FF9800" },
  VITC: { icon: "fruit-citrus", color: "#4CAF50" },
  VITD: { icon: "weather-sunny", color: "#FFC107" },
  TOCPHA: { icon: "seed", color: "#8BC34A" },
  VITK1: { icon: "leaf", color: "#4CAF50" },
  THIA: { icon: "grain", color: "#795548" },
  RIBF: { icon: "mushroom", color: "#FF5722" },
  NIA: { icon: "corn", color: "#2196F3" },
  VITB6A: { icon: "seed", color: "#8BC34A" },
  FOLDFE: { icon: "leaf", color: "#4CAF50" },
  VITB12: { icon: "egg", color: "#FFEB3B" },
  // Minerals
  CA: { icon: "bone", color: "#9E9E9E" },
  FE: { icon: "magnet", color: "#F44336" },
  MG: { icon: "magnify", color: "#4CAF50" },
  P: { icon: "fire", color: "#FF9800" },
  K: { icon: "fruit-cherries", color: "#FFEB3B" },
  NA: { icon: "shaker-outline", color: "#9E9E9E" },
  ZN: { icon: "atom", color: "#795548" },
};

const ExpandableSection = ({ title, expanded, onToggle, children, theme }) => {
  return (
    <View style={styles.sectionContainer}>
      <TouchableOpacity onPress={onToggle} style={styles.sectionHeader}>
        <Text
          style={[
            styles.sectionTitle,
            { color: theme.colors.primaryTextColor },
          ]}
        >
          {title}
        </Text>
        <MaterialIcons
          name={expanded ? "expand-less" : "expand-more"}
          size={scaleSize(24)}
          color={theme.colors.primary}
        />
      </TouchableOpacity>

      {expanded && <View style={styles.sectionContent}>{children}</View>}
    </View>
  );
};

const QuickAddForm = ({
  theme,
  initialData,
  onSubmit,
  onCancel,
  isEditing,
  compact = false,
}) => {
  const scrollViewRef = useRef();

  // Auto-scroll to input when keyboard appears
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  // State for expanded sections
  const [expandedSections, setExpandedSections] = useState({
    otherCore: false,
    vitamins: false,
    minerals: false,
  });

  // Animation states for required fields
  const [foodNameAnim] = useState(new Animated.Value(0));

  // Validation states
  const [validationErrors, setValidationErrors] = useState({
    foodName: false,
  });

  // Initialize form data with all nutrients
  const [formData, setFormData] = useState({
    foodName: "",
    ENERC_KCAL: "",
    PROCNT: "",
    CHOCDF: "",
    FAT: "",
    // Other core nutrients
    FASAT: "",
    FATRN: "",
    FAPU: "",
    FAMS: "",
    CHOLE: "",
    FIBTG: "",
    SUGAR: "",
    // Vitamins
    VITA_RAE: "",
    VITC: "",
    VITD: "",
    TOCPHA: "",
    VITK1: "",
    THIA: "",
    RIBF: "",
    NIA: "",
    VITB6A: "",
    FOLDFE: "",
    VITB12: "",
    // Minerals
    CA: "",
    FE: "",
    MG: "",
    P: "",
    K: "",
    NA: "",
    ZN: "",
  });

  // Initialize form based on initialData
  useEffect(() => {
    if (initialData) {
      const newData = { ...formData };

      // Set food name
      newData.foodName = initialData.foodLabel || "";

      // Set all nutrients from initialData
      const allNutrients = {
        ...initialData.nutrients?.core,
        ...initialData.nutrients?.vitamins,
        ...initialData.nutrients?.minerals,
      };

      Object.keys(allNutrients).forEach((key) => {
        if (
          newData.hasOwnProperty(key) &&
          allNutrients[key]?.quantity !== undefined
        ) {
          // Convert to string for the input
          newData[key] = allNutrients[key].quantity.toString();
        }
      });

      setFormData(newData);
    }
  }, [initialData]);

  // Animation function for required fields
  const triggerErrorAnimation = (anim) => {
    // Reset animation
    anim.setValue(0);

    // Create pulse animation
    Animated.sequence([
      Animated.timing(anim, {
        toValue: 1,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true, // Change to true for better performance
      }),
      Animated.timing(anim, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true, // Change to true for better performance
      }),
    ]).start();
  };

  // Handle input changes and clear validation errors
  const handleInputChange = (field, value) => {
    // For numeric fields, remove non-numeric characters except decimal point
    if (field !== "foodName") {
      // Allow only numbers and decimal point
      value = value.replace(/[^0-9.]/g, "");

      // Prevent multiple decimal points
      const decimalCount = value.split(".").length - 1;
      if (decimalCount > 1) {
        return; // Don't update state for invalid input
      }
    }

    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: false }));
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    // Reset validation
    const newErrors = {
      foodName: !formData.foodName.trim(),
    };

    setValidationErrors(newErrors);

    // Trigger animations for invalid fields
    if (newErrors.foodName) {
      triggerErrorAnimation(foodNameAnim);
    }

    // Check if form is valid
    if (newErrors.foodName) {
      return;
    }

    // Convert form data to nutrient values object
    const nutrientValues = {};
    Object.keys(formData).forEach((key) => {
      if (key !== "foodName") {
        // Convert to number, but keep as 0 if empty or invalid
        const value = formData[key] ? parseFloat(formData[key]) : 0;
        nutrientValues[key] = value;
      }
    });

    // Generate nutrient structure - THIS IS CRUCIAL
    const nutrients = generateNutrientStructure(nutrientValues);

    // Log the generated nutrients
    // console.log("Generated nutrients:", JSON.stringify(nutrients, null, 2));

    // Prepare food data
    const foodData = {
      foodLabel: formData.foodName,
      nutrients, // The generated nutrient structure
      foodCategory: "Quick Add",
      isQuickAdd: true,
      numberOfServings: 1,
    };

    // For editing, include the ID
    if (initialData?.id) {
      foodData.id = initialData.id;
    }

    // Log the complete food data
    // console.log("Submitting food data:", JSON.stringify(foodData, null, 2));

    onSubmit(foodData);
  };

  // Render nutrient input field with icon and description
  const renderNutrientInput = (
    key,
    label,
    unit,
    isGridItem = false,
    isRequired = false,
    anim = null
  ) => {
    const iconInfo = NUTRIENT_ICONS[key] || {
      icon: "nutrition",
      color: "#9E9E9E",
    };
    const description = NUTRIENT_DESCRIPTIONS[key] || "";
    const hasError = validationErrors[key];

    // Create animated styles for invalid fields
    let animatedStyle = {};
    if (hasError && anim) {
      animatedStyle = {
        borderColor: anim.interpolate({
          inputRange: [0, 1],
          outputRange: [theme.colors.primary, theme.colors.error],
        }),
        borderWidth: anim.interpolate({
          inputRange: [0, 1],
          outputRange: [scaleSize(1), scaleSize(3)],
        }),
      };
    }

    return (
      <View
        style={[styles.inputGroup, isGridItem && styles.gridItem]}
        key={key}
      >
        <View style={styles.iconLabelContainer}>
          <MaterialCommunityIcons
            name={iconInfo.icon}
            size={scaleSize(20)}
            color={iconInfo.color}
          />
          <Text
            style={[
              styles.label,
              {
                color: theme.colors.primaryTextColor,

                marginLeft: scaleSize(5),
              },
            ]}
          >
            {/* Conditionally show unit in parentheses */}
            {key === "foodName" ? label : `${label} (${unit})`}
          </Text>
        </View>

        {description && (
          <Text
            style={[styles.description, { color: theme.colors.subTextColor }]}
          >
            {description}
          </Text>
        )}

        <AnimatedTextInput
          style={[
            styles.input,
            compact && styles.compactInput,
            {
              backgroundColor: theme.colors.surfaceVariant,
              color: theme.colors.primaryTextColor,
              borderColor: hasError ? theme.colors.error : theme.colors.primary,
              borderWidth: scaleSize(1),
            },
            animatedStyle,
          ]}
          placeholder={key === "foodName" ? "Food Name" : "0"}
          placeholderTextColor={theme.colors.subTextColor}
          value={formData[key].toString()} // Ensure it's always a string
          onChangeText={(text) => handleInputChange(key, text)}
          keyboardType={key === "foodName" ? "default" : "decimal-pad"} // Use decimal-pad for numbers
        />
      </View>
    );
  };

  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  return (
    <View style={[styles.pageContainer, compact && styles.compactContainer]}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollContent,
          compact && styles.compactScrollContent,
        ]}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={true}
      >
        {!compact && (
          <Text
            style={[styles.title, { color: theme.colors.primaryTextColor }]}
          >
            {isEditing ? "Edit Food" : "Quick Add Food"}
          </Text>
        )}

        {/* Food Name - Required */}
        {renderNutrientInput(
          "foodName",
          "Food Name",
          "",
          false,
          true,
          foodNameAnim
        )}

        {/* Basic Nutrients Section */}
        <View style={styles.sectionContainer}>
          <Text
            style={[
              styles.sectionTitle,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            Basic Nutrients
          </Text>

          <View style={styles.gridContainer}>
            <View style={styles.gridRow}>
              {/* Calories - Required */}
              <View style={styles.gridColumn}>
                {renderNutrientInput("ENERC_KCAL", "Calories", "kcal", true)}
              </View>

              {/* Protein */}
              <View style={styles.gridColumn}>
                {renderNutrientInput("PROCNT", "Protein", "g", true)}
              </View>
            </View>

            <View style={styles.gridRow}>
              {/* Carbs */}
              <View style={styles.gridColumn}>
                {renderNutrientInput("CHOCDF", "Carbs", "g", true)}
              </View>

              {/* Fat */}
              <View style={styles.gridColumn}>
                {renderNutrientInput("FAT", "Fat", "g", true)}
              </View>
            </View>
          </View>
        </View>

        {/* Other Core Nutrients */}
        <ExpandableSection
          title="Other Nutrients"
          expanded={expandedSections.otherCore}
          onToggle={() => toggleSection("otherCore")}
          theme={theme}
        >
          {["FASAT", "FATRN", "FAPU", "FAMS", "CHOLE", "FIBTG", "SUGAR"].map(
            (key) =>
              renderNutrientInput(
                key,
                NUTRIENT_LABELS[key],
                NUTRIENT_UNITS[key]
              )
          )}
        </ExpandableSection>

        {/* Vitamins */}
        <ExpandableSection
          title="Vitamins"
          expanded={expandedSections.vitamins}
          onToggle={() => toggleSection("vitamins")}
          theme={theme}
        >
          {[
            "VITA_RAE",
            "VITC",
            "VITD",
            "TOCPHA",
            "VITK1",
            "THIA",
            "RIBF",
            "NIA",
            "VITB6A",
            "FOLDFE",
            "VITB12",
          ].map((key) =>
            renderNutrientInput(key, NUTRIENT_LABELS[key], NUTRIENT_UNITS[key])
          )}
        </ExpandableSection>

        {/* Minerals */}
        <ExpandableSection
          title="Minerals"
          expanded={expandedSections.minerals}
          onToggle={() => toggleSection("minerals")}
          theme={theme}
        >
          {["CA", "FE", "MG", "P", "K", "NA", "ZN"].map((key) =>
            renderNutrientInput(key, NUTRIENT_LABELS[key], NUTRIENT_UNITS[key])
          )}
        </ExpandableSection>
      </ScrollView>

      {/* Fixed Button Container */}
      <View
        style={[
          styles.buttonContainer,
          compact && styles.compactButtons,
          { backgroundColor: theme.colors.surface },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.button,
            styles.cancelButton,
            { backgroundColor: theme.colors.errorContainer },
          ]}
          onPress={onCancel}
        >
          <Text
            style={[
              styles.buttonText,
              { color: theme.colors.onErrorContainer },
            ]}
          >
            {compact ? "Back" : "Cancel"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.colors.primary }]}
          onPress={handleSubmit}
        >
          <Text style={[styles.buttonText, { color: theme.colors.onPrimary }]}>
            {isEditing ? "Update" : "Add"}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    width: "100%",
  },
  scrollContent: {
    flexGrow: 1, // Crucial for scrolling
    padding: scaleSize(15),
    paddingBottom: scaleSize(70), // Reduced to make room for buttons
  },
  compactScrollContent: {
    flexGrow: 1, // Crucial for scrolling
    padding: scaleSize(5),
    paddingBottom: scaleSize(60), // Reduced to make room for buttons
  },
  compactContainer: {
    padding: scaleSize(10),
  },
  title: {
    fontSize: scaleSize(18),
    fontWeight: "bold",
    marginBottom: scaleSize(15),
    textAlign: "center",
  },
  sectionContainer: {
    marginBottom: scaleSize(15),
    backgroundColor: "rgba(0,0,0,0.05)",
    borderRadius: scaleSize(10),
    padding: scaleSize(10),
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: scaleSize(8),
  },
  sectionTitle: {
    fontSize: scaleSize(16),
    fontWeight: "bold",
  },
  sectionContent: {
    paddingTop: scaleSize(10),
  },
  inputGroup: {
    marginBottom: scaleSize(12),
  },
  gridItem: {
    marginBottom: scaleSize(0),
  },
  gridContainer: {
    marginTop: scaleSize(5),
  },
  gridRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: scaleSize(10),
  },
  gridColumn: {
    width: "48%",
  },
  iconLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scaleSize(4),
  },
  label: {
    fontSize: scaleSize(14),
    fontWeight: "600",
  },
  input: {
    height: scaleSize(44),
    borderRadius: scaleSize(10),
    paddingHorizontal: scaleSize(12),
    fontSize: scaleSize(15),
    borderWidth: scaleSize(1),
  },
  compactInput: {
    height: scaleSize(38),
    fontSize: scaleSize(14),
  },
  description: {
    fontSize: scaleSize(11),
    fontStyle: "italic",
    marginBottom: scaleSize(4),
    opacity: 0.8,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: scaleSize(15),
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.35)",
    position: "relative", // Change from absolute to relative
    marginTop: scaleSize(10), // Add some space between form and buttons
  },
  compactButtons: {
    padding: scaleSize(10),
    borderRadius: scaleSize(10),
  },
  button: {
    flex: 1,
    height: scaleSize(42),
    borderRadius: scaleSize(10),
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: scaleSize(6),
  },
  buttonText: {
    fontSize: scaleSize(15),
    fontWeight: "600",
  },
});

export default QuickAddForm;
