import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  TouchableWithoutFeedback,
  Keyboard,
  Animated,
  ScrollView,
} from "react-native";
import { Image } from "expo-image";
import * as Haptics from "expo-haptics";
import { Appbar, Searchbar, Snackbar, IconButton } from "react-native-paper";
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from "react-native-popup-menu";
import { Ionicons } from "@expo/vector-icons";
import foodEntryModalStyles from "./styles/foodEntryModalStyles.js";
import BarcodeScanner from "./BarcodeScanner.js";
import DigitalFoodScanner from "./DigitalFoodScanner.js";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodLog } from "../context/FoodLogContext.js";
import { useFoodMenu } from "../../FoodMenu/context/FoodMenuContext.js";
import { useModal, MODAL_TYPES } from "../../../context/ModalContext.js";
import Configs from "../../../../configs.js";
import { scaleSize } from "../../../utils/deviceUtils.js";
import { useTime } from "../../../context/TimeContext.js";
import QuickAddForm from "./QuickAddForm.js";

const FoodEntryModal = React.memo(
  ({
    isVisible, // From modal context
    closeModal, // From modal context
    initialMealSection,
    isBuildingMeal,
    onCancel,
  }) => {
    const { selectedDate } = useTime();
    const { openModal } = useModal();
    const apiUrl = Configs.NutraCompass_API_URL;
    const { user } = useAuth();
    const userId = user?.uid;
    const { theme } = useThemeContext();
    const {
      mealSections,
      activeMealSection,
      setActiveMealSection,
      setActiveFoodItem,
      saveMealToFoodLog,
      saveOrUpdateSingleFoodItemToFoodLog,
      foodHistory,
      addQuickFoodEntry,
    } = useFoodLog();
    const { customMeals, saveFoodToTempCustomMeal } = useFoodMenu();

    // Add these state variables
    const [quickAddItem, setQuickAddItem] = useState(null);

    const [searchTerm, setSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(false); // pagination state
    const [searchResults, setSearchResults] = useState([]);
    const [activeTab, setActiveTab] = useState("search"); // 'search', 'meals', 'history', 'quick-add'

    const [isBarcodeScannerVisible, setIsBarcodeScannerVisible] =
      useState(false);
    const barcodeScannedRef = useRef(null);

    const [isDigitalFoodScannerVisible, setIsDigitalFoodScannerVisible] =
      useState(false);
    // State to manage snackbar visibility
    const [isSnackbarVisible, setIsSnackbarVisible] = useState(false);
    // State to track actions that should trigger the snackbar
    const [snackbarTriggered, setSnackbarTriggered] = useState(false);
    // State used to track which food entry item's icon button should be animated
    const [selectedItem, setSelectedItem] = useState(null);

    const [selectedScale] = useState(new Animated.Value(1));
    const [menuVisible, setMenuVisible] = useState(false);

    const styles = foodEntryModalStyles();

    // useEffect(() => {
    //   console.log(
    //     "Search Results\n",
    //     JSON.stringify(searchResults.slice(0, 1), null, 1)
    //   );
    // }, [searchResults]);

    useEffect(() => {
      if (!isVisible) {
        setQuickAddItem(null);
      }
    }, [isVisible]);

    useEffect(() => {
      barcodeScannedRef.current = handleBarcodeScanned;
    }, [handleBarcodeScanned]);

    useEffect(() => {
      console.log("[FoodEntryModal] Current search results:", searchResults);
    }, [searchResults]);

    // Sync initial value with context
    useEffect(() => {
      if (initialMealSection) {
        setActiveMealSection(initialMealSection);
      }
    }, [initialMealSection]);

    // useEffect to toggle snackbar visibility when snackbarTriggered changes
    useEffect(() => {
      if (snackbarTriggered) {
        setIsSnackbarVisible(true);
        // Hide snackbar after 1000ms
        const timeout = setTimeout(() => {
          setIsSnackbarVisible(false);
          setSnackbarTriggered(false); // Reset the trigger
        }, 1000);

        // Clear timeout on component unmount or when snackbarTriggered changes again
        return () => clearTimeout(timeout);
      }
    }, [snackbarTriggered]);

    // Function to handle toggling the snackbar
    const toggleSnackbar = () => {
      setSnackbarTriggered(true);
    };

    const handleSaveQuickAdd = useCallback(
      (foodData) => {
        // Create the quick add food item
        const quickAddEntry = {
          ...foodData,
          isQuickAdd: true,
          foodCategory: "Quick Add",
          foodId: `quickadd-${Date.now()}`,
          date: new Date().toISOString(),
        };

        // Handle save based on context
        if (isBuildingMeal) {
          saveFoodToTempCustomMeal(quickAddEntry);
        } else {
          saveOrUpdateSingleFoodItemToFoodLog(
            null,
            activeMealSection.id,
            quickAddEntry
          );
        }

        // Provide feedback
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        toggleSnackbar();

        // Reset quick add state
        setQuickAddItem(null);

        // Switch back to search tab
        //setActiveTab("search");
      },
      [
        isBuildingMeal,
        activeMealSection,
        saveFoodToTempCustomMeal,
        saveOrUpdateSingleFoodItemToFoodLog,
      ]
    );

    const handleViewFoodNutrients = (selectedFood) => {
      if (!selectedFood) return;

      // Use the parent's updater function directly
      setActiveFoodItem(selectedFood); // This updates both ref and state

      // Open with parent's ref getter
      openModal(MODAL_TYPES.FOOD_NUTRIENT, {
        activeMealSection: activeMealSection,
        foodNutrientModalType: "Add Food",
        isBuildingMeal,
        toggleSnackbar,
      });

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    };

    // This handles the final scan results
    const handleBarcodeScanned = useCallback((results) => {
      // Sanitize first
      const sanitized = results ? sanitizeFoodItem(results) : null;

      console.log(
        "[FoodEntryModal] Received barcode results:",
        JSON.stringify(sanitized, null, 2)
      );

      // Update UI state
      setSearchTerm("");
      setActiveTab("search");

      // Set results
      setSearchResults(sanitized ? [sanitized] : []);
    }, []);

    // Helper functions
    const sanitizeFoodItem = (item) => {
      const { $metadata, ...rest } = item;
      return rest;
    };

    const handleNewSearch = () => {
      // Reset pagination for fresh search
      setCurrentPage(1);
      setHasMore(false);
      handleFoodSearch(1); // Explicit page 1
    };

    const handleFoodSearch = async (page = 1) => {
      Keyboard.dismiss();
      try {
        // Convert page to numeric value
        const pageNumber = Number(page);

        const searchURL = `${apiUrl}/v1/food/search?query=${encodeURIComponent(
          searchTerm
        )}&userId=${userId}&page=${pageNumber}`;

        const response = await fetch(searchURL);

        if (!response.ok) {
          const errorBody = await response.text();
          throw new Error(
            `Server responded with ${response.status}: ${errorBody}`
          );
        }

        // handle pagination metadata
        const { data: results, pagination } = await response.json();

        const validResults = Array.isArray(results) ? results : [];

        // state management
        setSearchResults((prev) => {
          const merged = [...(page === 1 ? [] : prev), ...validResults].reduce(
            (acc, item) => {
              const exists = acc.some((i) => i.foodId === item.foodId);
              return exists ? acc : [...acc, item];
            },
            []
          );
          return merged;
        });
        setCurrentPage(pagination.currentPage);
        setHasMore(pagination.hasMore);

        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } catch (error) {
        console.error("Search error:", error);
        setSearchResults([]);
        setCurrentPage(1);
        setHasMore(false);
      }
    };

    // load more handler
    const handleLoadMore = () => {
      if (hasMore) {
        const nextPage = Number(currentPage) + 1;
        handleFoodSearch(nextPage);
      }
    };

    // Function to handle selecting an item
    const handleSelectItem = (item) => {
      setSelectedItem(item);
    };

    const handleSaveFood = async (selectedFood) => {
      // Determine if it's a custom meal
      const isCustomMeal =
        selectedFood?.isCustomMeal === true ||
        (selectedFood.mealItems && Array.isArray(selectedFood.mealItems));

      // Clone and modify based on tab
      let foodToSave = { ...selectedFood };

      if (activeTab === "history") {
        const { id, mealType, date, ...cleanFood } = foodToSave;
        foodToSave = {
          ...cleanFood,
          mealType: activeMealSection?.id,
          date: selectedDate,
        };

        // Preserve ID for custom meals
        if (isCustomMeal) {
          foodToSave.id = selectedFood.id;
        }
      }
      // If not building a meal, active meal section exists, date valid, and food is custom meal
      if (
        !isBuildingMeal &&
        activeMealSection &&
        selectedDate &&
        foodToSave?.isCustomMeal
      ) {
        // Save custom meal to food log with cleaned data
        saveMealToFoodLog(activeMealSection.id, selectedDate, {
          ...foodToSave,
          mealId: foodToSave.id, // Preserved for custom meals
          mealName: foodToSave.mealName || foodToSave.foodLabel,
        });
      }
      // If not building meal, active section exists, date valid, regular food item
      else if (
        !isBuildingMeal &&
        activeMealSection &&
        selectedDate &&
        foodToSave
      ) {
        // console.log("Saving food item:", foodToSave);
        saveOrUpdateSingleFoodItemToFoodLog(
          // Force new ID for history items, preserve others
          activeTab === "history" ? null : foodToSave.id,
          activeMealSection.id,
          foodToSave
        );
      }
      // If building meal and food is custom meal
      else if (isBuildingMeal && foodToSave?.isCustomMeal) {
        saveOrUpdateCustomMeal(foodToSave);
        if (navigation.getState().routeNames.includes("Custom Meals")) {
          navigation.navigate("Custom Meals");
        }
      }
      // If building meal with regular food item
      else if (isBuildingMeal && foodToSave) {
        saveFoodToTempCustomMeal(foodToSave);
      }
      // Fallback error case
      else {
        console.error(
          "Invalid save state:",
          "\nBuilding Meal:",
          isBuildingMeal,
          "\nActive Section:",
          activeMealSection,
          "\nSelected Date:",
          selectedDate,
          "\nFood Item:",
          foodToSave
        );
        return;
      }

      // Animate the save button
      Animated.timing(selectedScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: false,
      }).start(async () => {
        try {
          // Provide feedback to the user
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          // Trigger snackbar
          toggleSnackbar();

          // Reset the scale value after a delay
          setTimeout(() => {
            setIsSnackbarVisible(false);
            selectedScale.setValue(1);
          }, 2000);
        } catch (error) {
          // Handle any errors that occur during the save operation
          console.error("Error saving food:", error);
          // Optionally, display an error message to the user
          // You can use a Snackbar, Alert, or any other UI component for this purpose
        }
      });
    };

    const handleCloseModal = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setSearchResults([]);
      setSearchTerm("");
      setCurrentPage(1); // 🚀 Reset pagination
      setHasMore(false);
      setActiveTab("search");
      onCancel?.(); // Call original cancel handler
      closeModal(); // Close modal
    };

    // Direct barcode scanner opening
    const openBarcodeScanner = () => {
      openModal(MODAL_TYPES.BARCODE_SCANNER, {
        onBarcodeScanned: (scannedData) => {
          // 1. Close scanner first
          closeModal();

          // 2. Then process results
          handleBarcodeScanned(scannedData);
        },
        onClose: () => {
          // Optional cleanup
        },
      });
    };

    const closeDigitalFoodScanner = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setIsDigitalFoodScannerVisible(false);
    };

    const handleMealSectionMenuToggle = () => setMenuVisible(!menuVisible);

    const handleMealSectionMenuSelect = (section) => {
      setActiveMealSection(section);
      setMenuVisible(false);
    };

    return (
      <View style={{ height: "100%", width: "100%" }}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <View style={styles.customHeader}>
                <TouchableOpacity
                  onPress={handleCloseModal}
                  style={styles.backButton}
                >
                  <Ionicons
                    name="arrow-back"
                    size={scaleSize(24)}
                    color={theme.colors.primaryTextColor}
                  />
                </TouchableOpacity>

                <Text style={styles.headerTitle}>Add Food</Text>

                {/* Spacer to balance the layout */}
                <View style={styles.headerSpacer} />
              </View>
            </View>
            <View style={styles.modalContent}>
              <View
                style={{
                  minHeight: scaleSize(40),
                  width: "100%",
                  gap: scaleSize(6),
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  borderRadius: scaleSize(12),
                  marginVertical: scaleSize(12),
                  paddingHorizontal: scaleSize(6),
                }}
              >
                <Searchbar
                  placeholder="Search for a food"
                  placeholderTextColor={theme.colors.primaryTextColor}
                  value={searchTerm}
                  onChangeText={setSearchTerm}
                  onIconPress={handleNewSearch}
                  onSubmitEditing={handleNewSearch}
                  mode="bar"
                  editable={activeTab === "search" ? true : false}
                  style={{
                    height: "auto",
                    width: "65%",
                    borderRadius: scaleSize(16),
                    backgroundColor: theme.colors.surface,
                    elevation: 0,
                  }}
                  inputStyle={{
                    fontSize: scaleSize(15),
                    color: theme.colors.primaryTextColor,
                    alignSelf: "center",
                  }}
                  icon={() => (
                    <TouchableOpacity
                      onPress={handleNewSearch}
                      activeOpacity={0.2}
                      style={{
                        justifyContent: "center",
                        alignItems: "center",
                        width: scaleSize(40),
                        height: scaleSize(40),
                      }}
                    >
                      <Ionicons
                        name="search-outline"
                        size={scaleSize(20)}
                        color={theme.colors.primaryTextColor}
                      />
                    </TouchableOpacity>
                  )}
                />

                {!isBuildingMeal && (
                  <View style={{ width: "30%", alignItems: "flex-end" }}>
                    <Menu
                      opened={menuVisible}
                      onBackdropPress={() => setMenuVisible(false)}
                    >
                      <MenuTrigger>
                        <TouchableOpacity
                          onPress={handleMealSectionMenuToggle}
                          style={{
                            paddingHorizontal: scaleSize(12),
                            paddingVertical: scaleSize(8),
                            borderRadius: scaleSize(8),
                            backgroundColor: theme.colors.surface,
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "space-between",
                            minWidth: scaleSize(120),
                            gap: scaleSize(4),
                          }}
                        >
                          <Text
                            style={{
                              fontSize: scaleSize(14),
                              fontWeight: "500",
                              color: theme.colors.primaryTextColor,
                              flexShrink: 1,
                            }}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >
                            {activeMealSection?.name ||
                              activeMealSection?.id ||
                              "Select Meal"}
                          </Text>
                          <Ionicons
                            name={menuVisible ? "chevron-up" : "chevron-down"}
                            size={scaleSize(14)}
                            color={theme.colors.primaryTextColor}
                          />
                        </TouchableOpacity>
                      </MenuTrigger>

                      <MenuOptions
                        customStyles={{
                          optionsContainer: {
                            backgroundColor: theme.colors.surface,
                            borderRadius: scaleSize(8),
                            padding: 0,
                            maxHeight: scaleSize(300),
                            minWidth: scaleSize(120), // Set minimum width to match trigger
                            width: "auto", // Allow it to grow with content
                            alignSelf: "flex-end", // Align to the right edge
                          },
                        }}
                      >
                        <ScrollView
                          style={{ maxHeight: scaleSize(250) }}
                          contentContainerStyle={{
                            paddingVertical: scaleSize(4),
                            minWidth: scaleSize(120), // Ensure content matches trigger width
                          }}
                        >
                          {mealSections
                            ?.filter(
                              (section) =>
                                section.id !== "Water" &&
                                section.name?.trim() !== ""
                            )
                            .map((section) => (
                              <MenuOption
                                key={section.id}
                                onSelect={() =>
                                  handleMealSectionMenuSelect(section)
                                }
                                customStyles={{
                                  optionWrapper: {
                                    minHeight: scaleSize(40),
                                    backgroundColor:
                                      activeMealSection?.id === section.id
                                        ? theme.colors.primary
                                        : "transparent",
                                    paddingHorizontal: scaleSize(12),
                                    paddingVertical: scaleSize(8),
                                    minWidth: scaleSize(120), // Match trigger width
                                  },
                                }}
                              >
                                <Text
                                  style={{
                                    color:
                                      activeMealSection?.id === section.id
                                        ? theme.colors.primaryTextColor
                                        : theme.colors.subTextColor,
                                    fontSize: scaleSize(14),
                                  }}
                                  numberOfLines={1}
                                  ellipsizeMode="tail"
                                >
                                  {section.name || section.id}
                                </Text>
                              </MenuOption>
                            ))}
                        </ScrollView>
                      </MenuOptions>
                    </Menu>
                  </View>
                )}
              </View>

              <View
                style={{
                  flexDirection: "row",
                  gap: scaleSize(8),
                  padding: scaleSize(8),
                }}
              >
                <TouchableOpacity
                  onPress={openBarcodeScanner}
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: theme.colors.surface,
                    paddingVertical: scaleSize(14),
                    paddingHorizontal: scaleSize(12),
                    borderRadius: scaleSize(16),
                    gap: scaleSize(4),
                    borderColor: theme.colors.primary,
                    borderWidth: scaleSize(1),
                  }}
                >
                  <Ionicons
                    name="scan"
                    size={scaleSize(18)}
                    color={theme.colors.primaryTextColor}
                  />
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: scaleSize(12),
                      fontWeight: "500",
                    }}
                  >
                    Scan Barcode
                  </Text>
                </TouchableOpacity>

                <View style={{ position: "relative" }}>
                  <TouchableOpacity
                    disabled={true}
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: theme.colors.surface,
                      paddingVertical: scaleSize(14),
                      paddingHorizontal: scaleSize(12),
                      borderRadius: scaleSize(16),
                      gap: scaleSize(4),
                      borderColor: theme.colors.primary,
                      borderWidth: scaleSize(1),
                      opacity: 0.6,
                    }}
                  >
                    <Ionicons
                      name="scan"
                      size={scaleSize(18)}
                      color={theme.colors.primaryTextColor}
                    />
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(12),
                        fontWeight: "500",
                      }}
                    >
                      Digital Food Scanner
                    </Text>
                  </TouchableOpacity>

                  <View
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: "rgba(0, 0, 0, 0.2)",
                      borderRadius: scaleSize(16),
                    }}
                  />
                </View>
              </View>

              <View
                style={{
                  backgroundColor: theme.colors.screenBackground,
                  alignItems: "flex-start",
                  paddingHorizontal: scaleSize(10),
                  paddingTop: scaleSize(4),
                  paddingBottom: scaleSize(8),
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: scaleSize(10),
                  }}
                >
                  <TouchableOpacity
                    onPress={() => setActiveTab("search")}
                    style={{
                      paddingVertical: scaleSize(6),
                      paddingHorizontal: scaleSize(12),
                      backgroundColor:
                        activeTab === "search"
                          ? theme.colors.primary
                          : theme.colors.surface,
                      borderRadius: scaleSize(12),
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: scaleSize(1),
                      borderColor: "rgba(204, 204, 204, 0.3)",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          activeTab === "search"
                            ? theme.colors.primaryTextColor
                            : theme.colors.primary,
                        fontSize: scaleSize(14),
                      }}
                    >
                      All
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setActiveTab("meals")}
                    style={{
                      paddingVertical: scaleSize(6),
                      paddingHorizontal: scaleSize(12),
                      backgroundColor:
                        activeTab === "meals"
                          ? theme.colors.primary
                          : theme.colors.surface,
                      borderRadius: scaleSize(12),
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: scaleSize(1),
                      borderColor: "rgba(204, 204, 204, 0.3)",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          activeTab === "meals"
                            ? theme.colors.primaryTextColor
                            : theme.colors.primary,
                        fontSize: scaleSize(14),
                      }}
                    >
                      My Meals
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setActiveTab("history")}
                    style={{
                      paddingVertical: scaleSize(6),
                      paddingHorizontal: scaleSize(12),
                      backgroundColor:
                        activeTab === "history"
                          ? theme.colors.primary
                          : theme.colors.surface,
                      borderRadius: scaleSize(12),
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: scaleSize(1),
                      borderColor: "rgba(204, 204, 204, 0.3)",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          activeTab === "history"
                            ? theme.colors.primaryTextColor
                            : theme.colors.primary,
                        fontSize: scaleSize(14),
                      }}
                    >
                      History
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setActiveTab("quick-add")}
                    style={{
                      paddingVertical: scaleSize(6),
                      paddingHorizontal: scaleSize(12),
                      backgroundColor:
                        activeTab === "quick-add"
                          ? theme.colors.primary
                          : theme.colors.surface,
                      borderRadius: scaleSize(12),
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: scaleSize(1),
                      borderColor: "rgba(204, 204, 204, 0.3)",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          activeTab === "quick-add"
                            ? theme.colors.primaryTextColor
                            : theme.colors.primary,
                        fontSize: scaleSize(14),
                      }}
                    >
                      Quick Add
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {activeTab === "quick-add" ? (
                <QuickAddForm
                  theme={theme}
                  initialData={quickAddItem}
                  onSubmit={handleSaveQuickAdd}
                  onCancel={() => {
                    setQuickAddItem(null);
                    setActiveTab("search");
                  }}
                  isEditing={!!quickAddItem}
                  compact={true}
                />
              ) : (
                <FlatList
                  styles={styles.flatlist}
                  contentContainerStyle={{
                    padding: scaleSize(10),
                  }}
                  data={
                    activeTab === "history"
                      ? foodHistory
                      : activeTab === "meals"
                      ? customMeals
                      : searchResults
                  }
                  keyExtractor={(item) => {
                    if (item.isCustomMeal) {
                      // For history meals: use mealId only
                      // For non-history meals: use id property
                      return item.date
                        ? `meal-${item.mealId}`
                        : `meal-${item.id}`;
                    }
                    // For food entries: use foodId only
                    return `food-${item.foodId}`;
                  }}
                  renderItem={({ item, index }) => (
                    <TouchableOpacity
                      key={`${item.foodId}_${index}`}
                      onPress={() => handleViewFoodNutrients(item)}
                      style={styles.foodItemContainer}
                    >
                      {item?.mealImageUrl && (
                        <View
                          style={{
                            width: scaleSize(70),
                            height: scaleSize(70),
                            borderRadius: scaleSize(8),
                            backgroundColor: theme.colors.surface,
                            alignItems: "center",
                            justifyContent: "center",
                            marginRight: scaleSize(10),
                          }}
                        >
                          <Image
                            source={{ uri: item.mealImageUrl }}
                            contentFit="cover"
                            style={{
                              width: scaleSize(70),
                              height: scaleSize(70),
                              borderRadius: scaleSize(8),
                            }}
                          />
                        </View>
                      )}
                      <View style={styles.foodInfoContainer}>
                        <Text style={styles.foodLabel}>{item.foodLabel}</Text>
                        <View
                          style={{
                            flexDirection: "row",
                            gap: scaleSize(5),
                            width: "60%",
                          }}
                        >
                          <Text style={styles.foodLabelCalories}>
                            {Math.round(
                              parseFloat(
                                item?.nutrients?.core?.ENERC_KCAL?.quantity ||
                                  item?.defaultNutrients?.ENERC_KCAL ||
                                  0
                              )
                            )}{" "}
                            cal
                            {item?.activeMeasure?.weight || item?.foodBrand
                              ? ","
                              : ""}
                          </Text>
                          <Text style={styles.foodLabelServingSize}>
                            {activeTab === "meals" || item.isCustomMeal
                              ? ""
                              : Math.round(item?.activeMeasure?.weight)}{" "}
                            {activeTab === "meals" || item.isCustomMeal
                              ? ""
                              : item?.activeMeasure?.label?.toLowerCase?.() ||
                                ""}
                            {item?.foodBrand ? "," : ""}
                          </Text>

                          <Text style={styles.foodLabelServingSize}>
                            {item?.foodBrand}
                          </Text>
                        </View>
                      </View>
                      <Animated.View
                        style={{
                          transform: [{ scale: selectedScale }],
                        }}
                      >
                        <IconButton
                          iconColor={theme.colors.primary}
                          containerColor={theme.colors.screenBackground}
                          icon={
                            selectedItem === item && isSnackbarVisible
                              ? "check"
                              : "plus"
                          }
                          color={theme.colors.primaryTextColor}
                          size={scaleSize(24)}
                          onPress={() => {
                            handleSelectItem(item);
                            handleSaveFood(item);
                          }}
                        />
                      </Animated.View>
                    </TouchableOpacity>
                  )}
                  ListFooterComponent={
                    activeTab === "search" && hasMore ? (
                      <TouchableOpacity
                        onPress={handleLoadMore}
                        style={{
                          padding: scaleSize(10),
                          alignItems: "center",
                          backgroundColor: theme.colors.surface,
                          borderRadius: scaleSize(8),
                          margin: scaleSize(10),
                        }}
                      >
                        <Text
                          style={{
                            color: theme.colors.primaryTextColor,
                            fontSize: scaleSize(16),
                          }}
                        >
                          More
                        </Text>
                      </TouchableOpacity>
                    ) : null
                  }
                />
              )}
            </View>

            {/* {isBarcodeScannerVisible && (
              <View style={styles.barcodeScannerContainer}>
                <BarcodeScanner
                  onBarcodeScanned={handleBarcodeScanned}
                  onClose={() => setIsBarcodeScannerVisible(false)}
                />
              </View>
            )} */}

            {isDigitalFoodScannerVisible && (
              <View style={styles.barcodeScannerContainer}>
                <DigitalFoodScanner
                  onClose={closeDigitalFoodScanner}
                  onPictureTaken={(imageUri) => {
                    console.log("Picture taken:", imageUri);
                    setIsDigitalFoodScannerVisible(false);
                  }}
                  onOpenGallery={() => {
                    console.log("Open Gallery clicked");
                    setIsDigitalFoodScannerVisible(false);
                  }}
                />
              </View>
            )}

            <Snackbar
              visible={isSnackbarVisible}
              onDismiss={() => setIsSnackbarVisible(false)}
              style={{ backgroundColor: theme.colors.surface }}
            >
              <Text
                style={{
                  fontSize: scaleSize(16),
                  alignSelf: "center",
                  color: theme.colors.primaryTextColor,
                }}
              >
                Food Logged!
              </Text>
            </Snackbar>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }
);

export default FoodEntryModal;
