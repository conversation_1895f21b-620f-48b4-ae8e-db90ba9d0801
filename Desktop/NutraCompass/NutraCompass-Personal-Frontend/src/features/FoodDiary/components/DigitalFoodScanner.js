import React, { useState, useRef, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
  Animated,
  Easing,
} from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera";
import * as ImagePicker from "expo-image-picker";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import digitalFoodScannerStyles from "./styles/digitalFoodScannerStyles.js";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useModal, MODAL_TYPES } from "../../../context/ModalContext.js";
import Configs from "../../../../configs.js";
import * as FileSystem from "expo-file-system";
import uuid from "react-native-uuid";

const { height: screenHeight } = Dimensions.get("window");

const DigitalFoodScanner = ({ onClose, onScanResult, onError }) => {
  const apiUrl = Configs.NutraCompass_API_URL;
  const { user } = useAuth();
  const userId = user?.uid;
  const { replaceModal } = useModal();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [facing, setFacing] = useState("back");
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef(null);
  const isMounted = useRef(true);

  const styles = digitalFoodScannerStyles();

  // Animation for loading state
  const pulseAnim = useRef(new Animated.Value(0)).current;
  const spinAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (loading) {
      // Pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Spin animation
      Animated.loop(
        Animated.timing(spinAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start();
    } else {
      pulseAnim.setValue(0);
      spinAnim.setValue(0);
    }
  }, [loading]);

  // Track component mount state
  React.useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  const handleError = useCallback(
    (errorMessage) => {
      if (!isMounted.current) return;
      setError(errorMessage);
      if (onError) onError(errorMessage);
      setTimeout(() => {
        if (isMounted.current) setError(null);
      }, 3000);
    },
    [onError]
  );

  // Convert food analysis to expected scannedItem format
  const convertToScannedItem = (aiAnalysis) => {
    const { primary_nutrition, enhanced_nutrition, gpt_analysis } = aiAnalysis;
    
    // Extract first food item from analysis or use total
    const firstFood = Object.keys(enhanced_nutrition || {})[0];
    const foodData = enhanced_nutrition?.[firstFood] || {};
    
    return {
      foodId: `ai-${uuid.v4()}`,
      foodLabel: firstFood || gpt_analysis?.food_items?.[0] || "Scanned Meal",
      foodBrand: "AI Analysis",
      activeMeasure: {
        label: "serving",
        weight: 100, // default serving size
      },
      defaultNutrients: {
        calories: primary_nutrition?.total_calories || 0,
      },
      nutrients: {
        core: {
          ENERC_KCAL: {
            quantity: primary_nutrition?.total_calories || 0,
            unit: "kcal"
          },
          PROCNT: {
            quantity: primary_nutrition?.total_protein || 0,
            unit: "g"
          },
          CHOCDF: {
            quantity: primary_nutrition?.total_carbs || 0,
            unit: "g"
          },
          FAT: {
            quantity: primary_nutrition?.total_fat || 0,
            unit: "g"
          },
        },
      },
      aiAnalysis: aiAnalysis, // Store full AI analysis for reference
    };
  };

  // Helper: call OpenRouter GPT-5-nano with image for food analysis
  const analyzeImageWithOpenRouter = async (imageUri) => {
    const { API_KEY, BASE_URL, MODEL } = Configs.openRouterConfig || {};
    if (!API_KEY) {
      throw new Error("OpenRouter API key is not configured");
    }

    // Read image as base64 and embed as data URL
    const base64 = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    const dataUrl = `data:image/jpeg;base64,${base64}`;

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${API_KEY}`,
    };

    const payload = {
      model: MODEL || "openai/gpt-5-nano",
      messages: [
        {
          role: "system",
          content:
            "You are a certified nutritionist and vision analyst. Your job is to analyze a single food image and output ONLY one JSON object that matches this exact schema and property order:

          {
          labels: string[],
          total_nutrition: { calories: number, protein_g: number, carbs_g: number, fat_g: number },
          confidence_level: ‘low’ | ‘medium’ | ‘high’,
          individual_items?: { name: string, nutrition: { calories: number, protein_g: number, carbs_g: number, fat_g: number } }[]
          }

          Strict requirements:
            •	Output must be valid JSON. No markdown, no comments, no trailing commas, no extra keys, no text before/after the JSON.
            •	Property order must be exactly: labels, total_nutrition, confidence_level, individual_items (omit individual_items if there are no items).
            •	Numbers must be non-negative. Use whole numbers for calories and one decimal place for grams (e.g., 23.4).
            •	Units: calories in kcal, protein_g/carbs_g/fat_g in grams.

          Interpretation & methodology:
            1.	Identify distinct foods in the image (e.g., “grilled chicken”, “brown rice”, “broccoli”). If uncertain, choose the most likely common food item.
            2.	Estimate portion sizes visually using common references (e.g., palm = ~85 g cooked meat; cupped hand = ~150 g cooked grains; fist = ~150 g cooked vegetables; slice of bread = ~30 g).
            3.	Map each item to typical nutritional values (assume standard USDA-like averages) and compute per-item nutrition = portion_size × typical_nutrition_per_100g (or per unit).
            4.	Sum all items to produce total_nutrition.
            5.	Set confidence_level:
            •	high: items clearly visible, common foods, portions mostly unobstructed.
            •	medium: some occlusion/ambiguity or mixed preparations.
            •	low: heavy occlusion, unusual foods, image quality issues, or minimal visual cues.
            6.	Be conservative when uncertain: prefer typical/average preparations (e.g., “grilled” vs “deep-fried” unless clearly visible).
            7.	If the image appears to be non-food or empty, return labels: [], total_nutrition all zeros, confidence_level: “low”, and omit individual_items.

          labels guidance:
            •	Include 2–8 descriptive strings mixing item names and relevant attributes (e.g., “grilled chicken”, “brown rice”, “steamed broccoli”, “plate”, “lunch”).
            •	Do not include health claims or user-directed advice.

          individual_items guidance:
            •	Include an entry per distinct edible item you can identify (skip sauces/garnishes unless clearly visible and substantial).
            •	name should be concise and generic (e.g., “grilled chicken breast”, “white rice”, “mixed salad”).
            •	nutrition must reflect your estimated portion for that item.

          Validation:
            •	Ensure total_nutrition equals the sum of individual_items (when present).
            •	Round calories to nearest whole number; macros to one decimal place.
            •	Return exactly one JSON object and nothing else.",
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text:
                "Analyze this food image and provide the JSON only. If you see multiple distinct food items (like beef, eggs, and broccoli,etc), list them in the 'individual_items' array with their estimated nutrition. Ensure numeric fields are numbers in grams where applicable. If uncertain, estimate and reflect uncertainty in confidence_level.",
            },
            { type: "image_url", image_url: { url: dataUrl } },
          ],
        },
      ],
      temperature: 0.1,
      max_tokens: 1200,
      response_format: { type: "json_object" },
    };

    const resp = await fetch(`${(BASE_URL || "https://openrouter.ai/api/v1").replace(/\/$/, "")}/chat/completions`, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });

    const raw = await resp.text();
    if (!resp.ok) {
      throw new Error(`OpenRouter error (${resp.status}): ${raw.slice(0, 200)}`);
    }

    // Parse assistant content which may contain JSON or fenced code
    let content;
    try {
      const data = JSON.parse(raw);
      content = data?.choices?.[0]?.message?.content || "";
    } catch (e) {
      throw new Error("Failed to parse OpenRouter response");
    }

    let parsed;
    try {
      parsed = JSON.parse(content);
    } catch (e) {
      // Try to extract JSON from code block
      const match = content.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      if (match) {
        try {
          parsed = JSON.parse(match[1]);
        } catch {}
      }
    }

    if (!parsed || typeof parsed !== 'object') {
      const snippet = (content || '').slice(0, 200);
      throw new Error(`Invalid AI response format: ${snippet}`);
    }

    // Normalize possible variant schemas into expected shape
    const deriveLabels = () => {
      const arr = [];
      if (Array.isArray(parsed.labels)) arr.push(...parsed.labels);
      if (Array.isArray(parsed.food_items)) arr.push(...parsed.food_items);
      if (Array.isArray(parsed.items)) arr.push(...parsed.items.map(i => i?.name || i?.label || i?.title).filter(Boolean));
      if (Array.isArray(parsed.foods)) arr.push(...parsed.foods.map(i => i?.name || i?.label || i?.title).filter(Boolean));
      if (Array.isArray(parsed.detections)) arr.push(...parsed.detections.map(i => i?.name || i?.label || i?.title).filter(Boolean));
      
      // Filter out minor condiments/seasonings from main meal title
      const minorItems = [
        'sauce', 'dressing', 'seasoning', 'salt', 'pepper', 'oil', 
        'butter', 'mayo', 'mayonnaise', 'ketchup', 'mustard', 
        'soy sauce', 'hot sauce', 'vinegar', 'syrup', 'honey',
        'sugar', 'cream', 'milk', 'cheese' // small amounts
      ];
      
      const filtered = arr.filter(item => {
        const lowercased = item.toLowerCase();
        return !minorItems.some(minor => lowercased.includes(minor));
      });
      
      // If filtering removed everything, keep the most substantial items
      if (filtered.length === 0 && arr.length > 0) {
        return arr.slice(0, 3); // Keep first 3 items as fallback
      }
      
      return Array.from(new Set(filtered.filter(Boolean)));
    };

    const total = parsed.total_nutrition || parsed.nutrition || parsed.overall_nutrition || parsed.macros || {};
    const calories =
      typeof total.calories === 'number' ? total.calories :
      typeof total.kcal === 'number' ? total.kcal :
      typeof total.energy_kcal === 'number' ? total.energy_kcal : 0;
    const protein_g =
      typeof total.protein_g === 'number' ? total.protein_g :
      typeof total.protein === 'number' ? total.protein :
      typeof total.proteinGrams === 'number' ? total.proteinGrams : 0;
    const carbs_g =
      typeof total.carbs_g === 'number' ? total.carbs_g :
      typeof total.carbs === 'number' ? total.carbs :
      typeof total.carbohydrates === 'number' ? total.carbohydrates : 0;
    const fat_g =
      typeof total.fat_g === 'number' ? total.fat_g :
      typeof total.fat === 'number' ? total.fat :
      typeof total.fatGrams === 'number' ? total.fatGrams : 0;

    // Normalize individual items if provided
    const parseItemNutrition = (n) => {
      const ic =
        typeof n?.calories === 'number' ? n.calories :
        typeof n?.kcal === 'number' ? n.kcal :
        typeof n?.energy_kcal === 'number' ? n.energy_kcal : 0;
      const ip =
        typeof n?.protein_g === 'number' ? n.protein_g :
        typeof n?.protein === 'number' ? n.protein :
        typeof n?.proteinGrams === 'number' ? n.proteinGrams : 0;
      const icb =
        typeof n?.carbs_g === 'number' ? n.carbs_g :
        typeof n?.carbs === 'number' ? n.carbs :
        typeof n?.carbohydrates === 'number' ? n.carbohydrates : 0;
      const ift =
        typeof n?.fat_g === 'number' ? n.fat_g :
        typeof n?.fat === 'number' ? n.fat :
        typeof n?.fatGrams === 'number' ? n.fatGrams : 0;
      return { calories: ic, protein_g: ip, carbs_g: icb, fat_g: ift };
    };

    let individual_items = [];
    if (Array.isArray(parsed.individual_items)) {
      individual_items = parsed.individual_items
        .map((it) => ({ name: it?.name || it?.label || it?.title, nutrition: parseItemNutrition(it?.nutrition || it) }))
        .filter((it) => typeof it.name === 'string' && it.name.trim().length > 0);
    } else if (Array.isArray(parsed.items)) {
      individual_items = parsed.items
        .map((it) => ({ name: it?.name || it?.label || it?.title, nutrition: parseItemNutrition(it?.nutrition || {}) }))
        .filter((it) => typeof it.name === 'string' && it.name.trim().length > 0);
    }

    const normalized = {
      labels: deriveLabels(),
      total_nutrition: {
        calories,
        protein_g,
        carbs_g,
        fat_g,
      },
      confidence_level: parsed.confidence_level || parsed.confidence || parsed.confidenceScore || 'medium',
      individual_items,
    };

    if (!normalized.labels.length) {
      normalized.labels = ["Scanned Meal"];
    }

    return normalized;
  };

  // Helper: create smart meal title from food items
  const createSmartMealTitle = (labels) => {
    if (!labels || labels.length === 0) return "AI Scanned Meal";

    // Categorize foods - only main foods go in title
    const proteins = [];
    const grains = [];
    const mainItems = []; // Non-categorized substantial items

    const vegetableKeywords = [
      'broccoli', 'carrot', 'spinach', 'lettuce', 'tomato', 'onion', 'pepper',
      'mushroom', 'cucumber', 'cabbage', 'corn', 'peas', 'bean', 'celery',
      'asparagus', 'zucchini', 'eggplant', 'potato', 'sweet potato', 'greens',
      'kale', 'arugula', 'radish', 'beet', 'cauliflower', 'brussels sprouts',
      'green onion', 'scallion', 'edamame', 'avocado', 'kimchi'
    ];

    const proteinKeywords = [
      'chicken', 'beef', 'pork', 'fish', 'salmon', 'tuna', 'shrimp', 'turkey',
      'egg', 'tofu', 'tempeh', 'lamb', 'duck', 'bacon', 'ham', 'sausage',
      'lobster', 'crab', 'meatball', 'steak', 'ground beef', 'ground turkey'
    ];

    const grainKeywords = [
      'rice', 'pasta', 'noodle', 'bread', 'quinoa', 'oats', 'barley',
      'spaghetti', 'fettuccine', 'ramen', 'udon', 'couscous', 'bulgur',
      'tortilla', 'wrap', 'bagel', 'toast'
    ];

    // Minor items that should NOT appear in title
    const minorKeywords = [
      'sauce', 'dressing', 'seasoning', 'salt', 'pepper', 'oil',
      'butter', 'mayo', 'mayonnaise', 'ketchup', 'mustard',
      'soy sauce', 'hot sauce', 'vinegar', 'syrup', 'honey',
      'sugar', 'cream', 'milk', 'cheese'
    ];

    // Define protein priority so meats appear before eggs in the title
    const proteinPriority = ['beef','steak','ground beef','chicken','pork','turkey','fish','salmon','tuna','shrimp','eggs','tofu'];
    const sortByPriority = (arr, priority) => (
      [...arr].sort((a,b) => {
        const ai = priority.indexOf(a.toLowerCase());
        const bi = priority.indexOf(b.toLowerCase());
        return (ai === -1 ? 999 : ai) - (bi === -1 ? 999 : bi);
      })
    );

    // Canonicalize a label to a predictable, user-friendly main label
    const canonicalizeMainLabel = (label) => {
      const l = label.toLowerCase();
      // Skip vegetables and condiments from title
      if (vegetableKeywords.some((k) => l.includes(k))) return null;
      if (minorKeywords.some((k) => l.includes(k))) return null;

      // Normalize common forms
      if (l.includes('egg')) return 'eggs';
      if (l.includes('ground beef') || l.includes('steak') || l === 'beef') return 'beef';
      if (l.includes('chicken')) return 'chicken';
      if (l.includes('turkey')) return 'turkey';
      if (l.includes('pork')) return 'pork';
      if (l.includes('tofu')) return 'tofu';
      if (l.includes('salmon')) return 'salmon';
      if (l.includes('tuna')) return 'tuna';

      // Pasta/noodles canonicalization
      if (l.includes('spaghetti')) return 'spaghetti';
      if (l.includes('noodle') || l.includes('pasta') || l.includes('fettuccine') || l.includes('ramen') || l.includes('udon')) return 'pasta';

      // Rice and grains
      if (l.includes('rice')) return 'rice';
      if (l.includes('quinoa')) return 'quinoa';
      if (l.includes('bread')) return 'bread';

      // Default to original casing with first letter uppercase
      return label.charAt(0).toUpperCase() + label.slice(1);
    };

    const addUnique = (arr, value) => {
      if (!value) return;
      const exists = arr.some((v) => v.toLowerCase() === value.toLowerCase());
      if (!exists) arr.push(value);
    };

    labels.forEach((label) => {
      const canonical = canonicalizeMainLabel(label);
      if (!canonical) return; // Filtered out
      const lower = canonical.toLowerCase();

      if (grainKeywords.some((grain) => lower.includes(grain))) {
        addUnique(grains, canonical);
      } else if (proteinKeywords.some((protein) => lower.includes(protein)) || ['beef','chicken','turkey','pork','salmon','tuna','eggs','tofu'].includes(lower)) {
        addUnique(proteins, canonical);
      } else {
        addUnique(mainItems, canonical);
      }
    });

    // Build title parts - only main substantial foods
    const titleParts = [];

    // Add grains first (like spaghetti, rice)
    if (grains.length > 0) {
      titleParts.push(...grains);
    }

    // Add proteins
    if (proteins.length > 0) {
      const proteinsSorted = sortByPriority(proteins, proteinPriority);
      titleParts.push(...proteinsSorted);
    }

    // Add other substantial main items (max 1-2)
    if (mainItems.length > 0) {
      titleParts.push(...mainItems.slice(0, 2));
    }

    // If we have no main items, fallback to first few labels
    if (titleParts.length === 0) {
      titleParts.push(...labels.slice(0, 2));
    }

    // Create natural title
    let title;
    if (titleParts.length === 1) {
      title = titleParts[0];
    } else if (titleParts.length === 2) {
      title = titleParts.join(' and ');
    } else {
      const last = titleParts.pop();
      title = titleParts.join(', ') + ' and ' + last;
    }

    // Capitalize first letter
    return title.charAt(0).toUpperCase() + title.slice(1);
  };

  // Convert GPT response into app's scannedItem shape
  const buildScannedItemFromGpt = (gpt) => {
    const total = gpt.total_nutrition || {};
    const labels = gpt.labels || [];
    const items = Array.isArray(gpt.individual_items) ? gpt.individual_items : [];

    // If multiple items detected, build a custom meal
    if (items.length > 1) {
      const mealItems = items.map((it) => ({
        foodId: `ai-${uuid.v4()}`,
        foodLabel: it.name,
        foodCategory: "AI Analysis",
        foodBrand: "",
        numberOfServings: 1,
        activeMeasure: { label: "serving", weight: 100 },
        nutrients: {
          core: {
            ENERC_KCAL: { quantity: it.nutrition?.calories || 0, unit: "kcal" },
            PROCNT: { quantity: it.nutrition?.protein_g || 0, unit: "g" },
            CHOCDF: { quantity: it.nutrition?.carbs_g || 0, unit: "g" },
            FAT: { quantity: it.nutrition?.fat_g || 0, unit: "g" },
          },
        },
      }));

      const aggregated = mealItems.reduce(
        (acc, mi) => {
          acc.calories += Number(mi.nutrients.core.ENERC_KCAL.quantity || 0);
          acc.protein_g += Number(mi.nutrients.core.PROCNT.quantity || 0);
          acc.carbs_g += Number(mi.nutrients.core.CHOCDF.quantity || 0);
          acc.fat_g += Number(mi.nutrients.core.FAT.quantity || 0);
          return acc;
        },
        { calories: 0, protein_g: 0, carbs_g: 0, fat_g: 0 }
      );

      return {
        // meal level identifiers
        id: `ai-meal-${uuid.v4()}`,
        isCustomMeal: true,
        // Create a smart, natural title without ellipses
        foodLabel: createSmartMealTitle(labels),
        foodBrand: "AI Analysis",
        numberOfServings: 1,
        mealItems,
        nutrients: {
          core: {
            ENERC_KCAL: { quantity: aggregated.calories, unit: "kcal" },
            PROCNT: { quantity: aggregated.protein_g, unit: "g" },
            CHOCDF: { quantity: aggregated.carbs_g, unit: "g" },
            FAT: { quantity: aggregated.fat_g, unit: "g" },
          },
        },
        aiAnalysis: {
          gpt_analysis: gpt,
          primary_nutrition: {
            total_calories: aggregated.calories,
            total_protein: aggregated.protein_g,
            total_carbs: aggregated.carbs_g,
            total_fat: aggregated.fat_g,
            confidence_level: gpt.confidence_level || "medium",
            food_items: items.map((i) => i.name),
          },
        },
      };
    }

    // Fallback to single item (also covers items.length === 1)
    const single = items.length === 1 ? items[0] : null;
    const singleCalories = single?.nutrition?.calories ?? total.calories ?? 0;
    const singleProtein = single?.nutrition?.protein_g ?? total.protein_g ?? 0;
    const singleCarbs = single?.nutrition?.carbs_g ?? total.carbs_g ?? 0;
    const singleFat = single?.nutrition?.fat_g ?? total.fat_g ?? 0;

    return {
      foodId: `ai-${uuid.v4()}`,
      foodLabel: single?.name || labels[0] || "Scanned Meal",
      foodBrand: "AI Analysis",
      activeMeasure: { label: "serving", weight: 100 },
      defaultNutrients: { calories: singleCalories },
      nutrients: {
        core: {
          ENERC_KCAL: { quantity: singleCalories, unit: "kcal" },
          PROCNT: { quantity: singleProtein, unit: "g" },
          CHOCDF: { quantity: singleCarbs, unit: "g" },
          FAT: { quantity: singleFat, unit: "g" },
        },
      },
      aiAnalysis: { gpt_analysis: gpt, primary_nutrition: {
        total_calories: singleCalories,
        total_protein: singleProtein,
        total_carbs: singleCarbs,
        total_fat: singleFat,
        confidence_level: gpt.confidence_level || "medium",
        food_items: single ? [single.name] : labels,
      }},
    };
  };

  // Process image fully on frontend (no backend)
  const processImageWithAI = async (imageUri) => {
    if (!imageUri) return;

    setLoading(true);
    try {
      const gpt = await analyzeImageWithOpenRouter(imageUri);
      const scannedItem = buildScannedItemFromGpt(gpt);

      console.log("[DigitalFoodScanner] AI analysis successful:", scannedItem);

      replaceModal(MODAL_TYPES.SCAN_RESULTS, {
        scannedItem,
        selectedDate: new Date(),
        onSaveSuccess: () => {},
      });
    } catch (error) {
      console.error("AI nutrition analysis failed:", error);
      handleError(error.message || "Failed to analyze food. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle camera permissions
  if (!permission) {
    return <View />;
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.permissionText}>
          We need your permission to use the camera to scan food.
        </Text>
        <TouchableOpacity
          onPress={requestPermission}
          style={styles.permissionButton}
        >
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Take a picture and process with AI
  const handleTakePicture = async () => {
    if (!cameraRef.current || loading) return;
    
    try {
      setLoading(true);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
      });
      
      console.log("Picture taken:", photo.uri);
      await processImageWithAI(photo.uri);
      
    } catch (error) {
      console.error("Error taking picture:", error);
      handleError("An error occurred while taking the picture.");
    };
  };

  // Open gallery and process selected image
  const handleOpenGallery = async () => {
    if (loading) return;
    
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
      });
  
      if (!result.canceled && result.assets[0]) {
        console.log("Selected image:", result.assets[0].uri);
        await processImageWithAI(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error selecting image from gallery:", error);
      handleError("An error occurred while selecting an image.");
    };
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === "back" ? "front" : "back"));
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <MaterialIcons name="keyboard-arrow-left" size={40} color="white" />
        </TouchableOpacity>
        <Text style={styles.title}>AI Food Scanner</Text>
        <TouchableOpacity onPress={toggleCameraFacing} style={styles.flipButton}>
          <MaterialIcons name="flip-camera-ios" size={30} color="white" />
        </TouchableOpacity>
      </View>
  
      {/* Camera */}
      <CameraView 
        style={styles.camera} 
        facing={facing}
        ref={cameraRef}
      >
        {/* Scanning indicator overlay */}
        {loading && (
          <View style={styles.scanningOverlay}>
            <Animated.View
              style={[
                styles.scanningIndicator,
                {
                  opacity: pulseAnim,
                  transform: [
                    {
                      rotate: spinAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg'],
                      }),
                    },
                  ],
                },
              ]}
            >
              <MaterialIcons name="auto-awesome" size={50} color="white" />
            </Animated.View>
            <Text style={styles.scanningText}>Analyzing nutrition...</Text>
          </View>
        )}
  
        {/* Hooks for visual targeting */}
        <View style={styles.cornerHooksContainer}>
          <View style={[styles.cornerHook, styles.topLeftHook]} />
          <View style={[styles.cornerHook, styles.topRightHook]} />
          <View style={[styles.cornerHook, styles.bottomLeftHook]} />
          <View style={[styles.cornerHook, styles.bottomRightHook]} />
        </View>
      </CameraView>
  
      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          onPress={handleOpenGallery}
          style={[styles.footerButton, loading && styles.disabledButton]}
          disabled={loading}
        >
          <Ionicons name="images" size={28} color={loading ? "gray" : "white"} />
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={handleTakePicture}
          style={[styles.captureButton, loading && styles.disabledButton]}
          disabled={loading}
        >
          <View style={styles.captureButtonInner}>
            <Ionicons name="camera" size={32} color={loading ? "gray" : "white"} />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={toggleCameraFacing}
          style={[styles.footerButton, loading && styles.disabledButton]}
          disabled={loading}
        >
          <Ionicons name="camera-reverse" size={28} color={loading ? "gray" : "white"} />
        </TouchableOpacity>
      </View>
  
      {/* Error Display */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
    </View>
  );
};

export default DigitalFoodScanner;
