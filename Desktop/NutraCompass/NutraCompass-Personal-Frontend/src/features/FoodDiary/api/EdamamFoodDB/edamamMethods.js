// Import Edamam Food DB Api Config
import Configs from "../../../../../configs.js";

const API_ID = Configs.edamamConfig.APP_ID;
const API_KEY = Configs.edamamConfig.APP_KEY;
const PARSER_BASE_URL = Configs.edamamConfig.PARSER_BASE_URL;
const NUTRIENTS_BASE_URL = Configs.edamamConfig.NUTRIENTS_BASE_URL;

export async function searchFoodByQuery(searchTerm) {
  const url = `${PARSER_BASE_URL}?ingr=${searchTerm}&app_id=${API_ID}&app_key=${API_KEY}`;

  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error("Network response not ok");
    const data = await response.json();
    const hints = data.hints || [];

    // Pre-filter duplicates and invalid items
    const uniqueHints = hints.filter(
      (hint, index, self) =>
        hint.food?.foodId &&
        self.findIndex((h) => h.food?.foodId === hint.food.foodId) === index
    );

    return uniqueHints;
  } catch (error) {
    console.error("Fetch Edamam Hints error:", error);
    return [];
  }
}

export async function processHints(hints) {
  if (!hints || !Array.isArray(hints)) {
    console.error("Invalid hints data:", hints);
    return [];
  }

  try {
    const processedData = await Promise.all(
      hints.map(async (foodItem) => {
        try {
          if (!foodItem?.food || !Array.isArray(foodItem.measures)) {
            console.log("Skipping invalid food item structure");
            return null;
          }

          const ingredientsParam = {
            ingredients: [
              {
                quantity: 1,
                measureURI: foodItem.measures[0]?.uri || "",
                foodId: foodItem.food.foodId || "",
              },
            ],
          };

          const nutrients = await searchForFoodItemNutrients(ingredientsParam);

          return {
            foodId: foodItem.food?.foodId,
            foodLabel: foodItem.food?.label,
            foodCategory: foodItem.food?.category,
            foodBrand: foodItem.food?.brand,
            numberOfServings: 1,
            activeMeasure: foodItem.measures[0],
            measures: foodItem?.measures,
            defaultNutrients: {
              ENERC_KCAL: foodItem.food?.nutrients?.ENERC_KCAL,
            },
            nutrients: {
              ...processCoreNutrients(nutrients),
              vitamins: processVitamins(nutrients),
              minerals: processMinerals(nutrients),
            },
          };
        } catch (error) {
          console.error(
            "Failed to process food item:",
            foodItem?.food?.label,
            error
          );
          return null;
        }
      })
    );

    // Filter out nulls and invalid entries
    const validItems = processedData.filter(
      (item) => item?.foodId && item?.nutrients?.ENERC_KCAL
    );

    // Remove duplicates (fallback protection)
    const uniqueResults = Array.from(
      new Map(validItems.map((item) => [item.foodId, item])).values()
    );

    return uniqueResults;
  } catch (error) {
    console.error("Critical error processing Edamam Hints:", error);
    return [];
  }
}

// Search Edamam Food Database API for a specific food item through its barcode
export async function searchFoodByBarcode(code) {
  // console.log("Code: " + code);
  if (!code || code === undefined || code === null) return;

  const url = `${PARSER_BASE_URL}?upc=${code}&app_id=${API_ID}&app_key=${API_KEY}`;
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const data = await response.json();
    console.log("Food Search Edamam API Call Triggered.");
    let processedData = [];
    if (
      // typeof data.hints.food === "object" &&
      // Array.isArray(data.hints.measures)
      data.hints
    ) {
      processedData = await Promise.all(
        data.hints.map(async (foodItem) => {
          if (
            typeof foodItem.food === "object" &&
            Array.isArray(foodItem.measures)
          ) {
            const ingredientsParam = {
              ingredients: [
                {
                  quantity: 1,
                  measureURI: foodItem.measures[0]?.uri || "",
                  foodId: foodItem.food?.foodId || "",
                },
              ],
            };

            const nutrients = await searchForFoodItemNutrients(
              ingredientsParam
            );

            // Use helper methods here
            const processedDataSchema = {
              foodId: foodItem.food?.foodId,
              foodLabel: foodItem.food?.label,
              foodCategory: foodItem.food?.category,
              foodBrand: foodItem.food?.brand,
              numberOfServings: 1,
              activeMeasure: foodItem.measures[0],
              measures: foodItem?.measures,
              defaultNutrients: {
                ENERC_KCAL: foodItem.food?.nutrients?.ENERC_KCAL,
              },
              nutrients: {
                ...processCoreNutrients(nutrients),
                vitamins: processVitamins(nutrients),
                minerals: processMinerals(nutrients),
              },
            };

            return processedDataSchema;
          }
        })
      );
    }

    // Remove duplicates based on foodId
    const uniqueResultsMap = new Map();
    processedData.forEach((item) => {
      if (!uniqueResultsMap.has(item.foodId)) {
        uniqueResultsMap.set(item.foodId, item);
      }
    });
    processedData = Array.from(uniqueResultsMap.values());

    console.log("Processing finished.");
    return processedData;
  } catch (error) {
    console.log("There was a problem with the fetch operation:", error);
  }
}

/**
 * ingredients param object format
{
  "ingredients": [
    {
      "quantity": 0,
      "measureURI": "string",
      "qualifiers": [
        "string"
      ],
      "foodId": "string"
    }
  ]
}
 */

// Get a food item's nutrient information
export async function searchForFoodItemNutrients(ingredients) {
  // console.log("Ingredient Param: ", JSON.stringify(ingredients, null, 2));
  const url = `${NUTRIENTS_BASE_URL}?ingredients=${encodeURIComponent(
    JSON.stringify(ingredients)
  )}&app_id=${API_ID}&app_key=${API_KEY}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(ingredients),
    });
    // console.log("Response: " + JSON.stringify(response));
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const data = await response.json();
    console.log("Nutrient Search Edamam API Call Triggered.");
    return data;
    // Process the data as needed, e.g., display the food items
  } catch (error) {
    console.error("There was a problem with the fetch operation:", error);
  }
}

// Get nutrient information for a specific food item
export async function getNutrientsForFoodItem(foodItem, newServingSize) {
  const ingredientsParam = {
    ingredients: [
      {
        quantity: 1,
        measureURI: newServingSize
          ? newServingSize?.uri || foodItem.activeMeasure?.uri
          : foodItem.activeMeasure?.uri || foodItem.measures[0]?.uri,
        foodId: foodItem.foodId || "",
      },
    ],
  };

  try {
    const nutrients = await searchForFoodItemNutrients(ingredientsParam);

    const processedDataSchema = newServingSize
      ? {
          ...foodItem,
          activeMeasure: newServingSize,
          nutrients: {
            ...processCoreNutrients(nutrients),
            vitamins: processVitamins(nutrients),
            minerals: processMinerals(nutrients),
          },
        }
      : {
          ...foodItem,
          nutrients: {
            ...processCoreNutrients(nutrients),
            vitamins: processVitamins(nutrients),
            minerals: processMinerals(nutrients),
          },
        };

    return processedDataSchema;
  } catch (error) {
    console.error("There was a problem fetching nutrient data:", error);
    return null;
  }
}

// HELPER METHODS

function processNutrientDataWithDefaults(nutrientData, keysToFilter) {
  if (!nutrientData) {
    console.log("Nutrient data is null, empty, or undefined.");
    return {};
  }

  const defaultQuantity = "--"; // You can adjust the default value accordingly

  const nutrients = {};

  keysToFilter.forEach((key) => {
    const value = nutrientData?.totalNutrients?.[key];
    const totalDailyValue = nutrientData?.totalDaily?.[key];

    const quantity = parseFloat(value?.quantity);
    const totalDailyQuantity = parseFloat(totalDailyValue?.quantity) || 0;

    nutrients[key] = {
      label: getNutrientLabel(key),
      quantity: !isNaN(quantity) ? quantity.toFixed(2) : defaultQuantity,
      unit: value?.unit || "",
    };

    if (nutrients[key] && totalDailyValue) {
      nutrients[key].totalDaily = {
        quantity: !isNaN(totalDailyQuantity)
          ? totalDailyQuantity.toFixed(2)
          : defaultQuantity,
      };
    }
  });

  return nutrients;
}

// Helper function to process new nutrient data
export function processCoreNutrients(nutrientData) {
  const coreNutrientKeys = [
    "ENERC_KCAL",
    "CHOCDF",
    "PROCNT",
    "FAT",
    "FASAT",
    "FATRN",
    "FAPU",
    "FAMS",
    "CHOLE",
    "FIBTG",
    "SUGAR",
  ];
  return processNutrientDataWithDefaults(nutrientData, coreNutrientKeys);
}

export function processVitamins(nutrientData) {
  const vitaminKeys = [
    "VITA_RAE",
    "VITC",
    "VITD",
    "TOCPHA",
    "VITK1",
    "THIA",
    "RIBF",
    "NIA",
    "VITB6A",
    "FOLDFE",
    "VITB12",
  ];
  return processNutrientDataWithDefaults(nutrientData, vitaminKeys);
}

export function processMinerals(nutrientData) {
  const mineralKeys = ["CA", "FE", "MG", "P", "K", "NA", "ZN"];
  return processNutrientDataWithDefaults(nutrientData, mineralKeys);
}

// Helper function to process nutrient data for activeFoodItem
function processActiveFoodItemNutrientData(nutrient, numberOfServings) {
  if (!nutrient) {
    console.log("Nutrient data is null, empty, or undefined.");
    return {};
  }

  const quantity = parseFloat(nutrient.quantity);

  const processedNutrient = {
    label: nutrient.label,
    quantity: !isNaN(quantity) ? (quantity * numberOfServings).toFixed(2) : "-",
    unit: nutrient?.unit || "",
  };

  // If the nutrient has totalDaily, process it
  if (nutrient.totalDaily) {
    const totalDailyQuantity = parseFloat(nutrient.totalDaily.quantity);
    processedNutrient.totalDaily = {
      quantity: !isNaN(totalDailyQuantity)
        ? (totalDailyQuantity * numberOfServings).toFixed(2)
        : "-",
    };
  }

  return processedNutrient;
}

// Function to process activeFoodItem for number of servings update
export function processActiveFoodItemNumberOfServingsUpdate(
  activeFoodItem,
  numberOfServings
) {
  if (!activeFoodItem) {
    console.log("Active food item is null, empty, or undefined.");
    return {};
  }

  if (isNaN(numberOfServings)) {
    numberOfServings = 0;
  }

  const nutrients = activeFoodItem.nutrients;
  const processedNutrients = {};

  // Iterate over the keys in activeFoodItem.nutrients
  for (const key in nutrients) {
    if (nutrients.hasOwnProperty(key)) {
      if (key === "vitamins" || key === "minerals") {
        // If the nutrient is "vitamins" or "minerals", process each item inside
        processedNutrients[key] = {};
        for (const nestedKey in nutrients[key]) {
          if (nutrients[key].hasOwnProperty(nestedKey)) {
            processedNutrients[key][nestedKey] =
              processActiveFoodItemNutrientData(
                nutrients[key][nestedKey],
                numberOfServings
              );
          }
        }
      } else {
        // Process individual nutrient
        processedNutrients[key] = processActiveFoodItemNutrientData(
          nutrients[key],
          numberOfServings
        );
      }
    }
  }

  return processedNutrients;
}

const getNutrientLabel = (nutrientKey) => {
  switch (nutrientKey) {
    case "ENERC_KCAL":
      return "Calories";
    case "CHOCDF":
      return "Total Carbohydrate";
    case "PROCNT":
      return "Protein";
    case "FAT":
      return "Total Fat";
    case "FASAT":
      return "Saturated Fat";
    case "FATRN":
      return "Trans Fat";
    case "FAPU":
      return "Polyunsaturated Fat";
    case "FAMS":
      return "Monounsaturated Fat";
    case "CHOLE":
      return "Cholesterol";
    case "FIBTG":
      return "Dietary Fiber";
    case "SUGAR":
      return "Total Sugar";
    case "VITA_RAE":
      return "Vitamin A";
    case "VITC":
      return "Vitamin C";
    case "VITD":
      return "Vitamin D";
    case "TOCPHA":
      return "Vitamin E";
    case "VITK1":
      return "Vitamin K1";
    case "THIA":
      return "Thiamin (Vitamin B1)";
    case "RIBF":
      return "Riboflavin (Vitamin B2)";
    case "NIA":
      return "Niacin (Vitamin B3)";
    case "VITB6A":
      return "Vitamin B6";
    case "FOLDFE":
      return "Folate (Vitamin B9)";
    case "VITB12":
      return "Vitamin B12";
    case "CA":
      return "Calcium";
    case "FE":
      return "Iron";
    case "MG":
      return "Magnesium";
    case "P":
      return "Phosphorus";
    case "K":
      return "Potassium";
    case "NA":
      return "Sodium";
    case "ZN":
      return "Zinc";
    default:
      return "Unknown Nutrient";
  }
};
