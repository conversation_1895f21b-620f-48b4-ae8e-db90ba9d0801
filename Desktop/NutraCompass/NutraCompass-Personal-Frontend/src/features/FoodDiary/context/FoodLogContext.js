import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  useRef,
  useCallback,
} from "react";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import dayjs from "dayjs";
import minMax from "dayjs/plugin/minMax";
import uuid from "react-native-uuid";
import isEqual from "lodash.isequal";

// Context Imports
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useDataLoading } from "../../../context/DataLoadingContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { useStepsLog } from "../../StepsLog/context/StepsLogContext.js";
import { usePushNotification } from "../../../context/PushNotificationContext.js";
import Configs from "../../../../configs.js";

dayjs.extend(minMax);

/* ================================= */
/* ========== SCHEMAS ============== */
/* ================================= */
/*
Food Entry Schema:
{
  id: string,
  type: 'food' | 'meal-header' | 'meal-item',
  date: string (YYYY-MM-DD),
  mealType: string (meal section ID),
  foodLabel: string,
  nutrients: {
    core: {
      ENERC_KCAL: { quantity: number },
      CHOCDF: { quantity: number },
      PROCNT: { quantity: number },
      FAT: { quantity: number }
    }
  },
  numberOfServings: number,
  // For meal items:
  mealHeaderId?: string,
  mealItemIndex?: number,
  // For meal headers:
  mealName?: string,
  mealImageUrl?: string
}

Meal Section Schema:
{
  id: string,
  name: string,
  order: number
}

Meal Group Schema:
{
  type: 'meal-group',
  id: string,
  header: MealHeader,
  items: Array<MealItem>,
  timestamp: Date
}

Context Value Schema:
{
  sections: Array<SectionData>,
  activeMealSection: string | null,
  foodHistory: Array<FoodHistoryItem>,
  mealSections: Array<MealSection>,
  foodEntries: { [date: string]: { [mealType: string]: Array<FoodEntry> } },
  // Methods
  loadFoodEntries: Function,
  saveMealToFoodLog: Function,
  deleteFoodEntry: Function,
  // Calculated values
  totalDailyCaloriesAndMacrosConsumed: { calories, carbs, protein, fat },
  calorieData: { ... },
  macroData: { ... },
  // Water tracking
  totalDailyWaterConsumed: number,
  totalDailyWaterConsumedPercentage: number
}
*/
/* ================================= */
/* ========= END SCHEMAS =========== */
/* ================================= */

// Constants
const CACHE_TTL = 60 * 60 * 1000; // 1 hour
const FoodLogContext = createContext();
const getMealSectionsCacheKey = (userId) => `@meal_sections_cache_${userId}`;

// Default meal sections
const DEFAULT_SECTIONS = [
  { id: "Breakfast", name: "Breakfast" },
  { id: "Lunch", name: "Lunch" },
  { id: "Dinner", name: "Dinner" },
  { id: "Snacks", name: "Snacks" },
  { id: "Water", name: "Water" },
];

// Helper functions
const parseFirestoreDate = (dateInput) => {
  // Handle Firestore Timestamp objects
  if (
    dateInput &&
    typeof dateInput === "object" &&
    "_seconds" in dateInput &&
    "_nanoseconds" in dateInput
  ) {
    return new Date(
      dateInput._seconds * 1000 + Math.floor(dateInput._nanoseconds / 1000000)
    );
  }

  // Handle non-string values
  if (dateInput instanceof Date) return dateInput;
  if (typeof dateInput === "number") return new Date(dateInput);
  if (!dateInput || typeof dateInput !== "string") return new Date(0);

  // If it's already in ISO format
  if (dateInput.includes("T")) return new Date(dateInput);

  try {
    // Replace non-breaking space with regular space
    const normalizedString = dateInput.replace(/ /g, " ");

    // Parse custom format: "June 21, 2025 at 11:10:09 PM UTC-7"
    const months = {
      January: 0,
      February: 1,
      March: 2,
      April: 3,
      May: 4,
      June: 5,
      July: 6,
      August: 7,
      September: 8,
      October: 9,
      November: 10,
      December: 11,
    };

    const match = normalizedString.match(
      /(\w+) (\d+), (\d{4}) at (\d+):(\d+):(\d+) (AM|PM) UTC([+-]\d+)/
    );

    if (match) {
      const [, monthStr, day, year, hour, minute, second, period, tzOffset] =
        match;
      const month = months[monthStr];

      if (month === undefined) {
        console.warn(`Invalid month: ${monthStr} in date string: ${dateInput}`);
        return new Date(0);
      }

      let hour24 = parseInt(hour);
      if (period === "PM" && hour24 < 12) hour24 += 12;
      if (period === "AM" && hour24 === 12) hour24 = 0;

      // Adjust for timezone offset
      const offsetHours = parseInt(tzOffset);
      const date = new Date(
        Date.UTC(
          parseInt(year),
          month,
          parseInt(day),
          hour24 - offsetHours,
          parseInt(minute),
          parseInt(second)
        )
      );

      return date;
    } else {
      // Try ISO format again in case it's slightly different
      const isoDate = new Date(dateInput);
      if (!isNaN(isoDate.getTime())) return isoDate;

      console.warn("No regex match for date string:", dateInput);
      return new Date(0);
    }
  } catch (error) {
    console.error("Error parsing date:", error, dateInput);
    return new Date(0);
  }
};

const formatTimestamp = (timestamp) => {
  try {
    const date = parseFirestoreDate(timestamp);
    if (isNaN(date.getTime())) return "";

    // Format time with hour, minute, and AM/PM indicator
    const timeString = date.toLocaleTimeString([], {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    // Split time and period (AM/PM) and join with newline
    return timeString
      .replace(/^0+/, "") // Remove leading zeros
      .replace(/(.+)\s(AM|PM)/i, "$1\n$2"); // Move AM/PM to new line
  } catch (e) {
    return "";
  }
};

const validateTimestamp = (timestamp) => {
  const date = parseFirestoreDate(timestamp);
  return date.getTime() > 0 ? timestamp : new Date().toISOString();
};

const isDateInRange = (date, range) => {
  // Add null safety checks
  if (!range || !range.start || !range.end) {
    console.warn("Invalid date range provided:", range);
    return false;
  }

  try {
    const targetDate = dayjs(date);
    const startDate = dayjs(range.start);
    const endDate = dayjs(range.end);

    // Check if date is >= start AND <= end
    return (
      targetDate.isSame(startDate, "day") ||
      targetDate.isSame(endDate, "day") ||
      (targetDate.isAfter(startDate) && targetDate.isBefore(endDate))
    );
  } catch (error) {
    console.error("Error comparing dates with Day.js:", error, {
      date,
      range,
    });
    return false;
  }
};

const get6MonthRange = (date) => {
  const start = dayjs(date).subtract(3, "months").format("YYYY-MM-DD");
  const end = dayjs(date).add(3, "months").format("YYYY-MM-DD");
  return { start, end };
};

// Deduplication to keep only latest versions in food history
const removeDuplicates = (items) => {
  const latestMeals = new Map(); // mealId → latest meal
  const latestFoods = new Map(); // foodId → latest food

  // Process in reverse chronological order
  for (const item of items) {
    if (item.isCustomMeal) {
      // For meals: keep only the latest version by mealId
      if (!latestMeals.has(item.mealId)) {
        latestMeals.set(item.mealId, item);
      }
    } else {
      // For foods: keep only the latest version by foodId
      if (!latestFoods.has(item.foodId)) {
        latestFoods.set(item.foodId, item);
      }
    }
  }

  // Return deduplicated items in original order
  return [...latestMeals.values(), ...latestFoods.values()];
};

export function useFoodLog() {
  // Custom hook for consuming context easily within components
  return useContext(FoodLogContext);
}

// Context Provider
export function FoodLogProvider({ children }) {
  /* ================================= */
  /* ========== STATE & REFS ======== */
  /* ================================= */
  // Context hooks
  const { initialLoadComplete } = useDataLoading();
  const { user } = useAuth();
  const { selectedDate } = useTime();
  const { syncMealNames } = usePushNotification();
  const { getNutritionalGoals } = useUserSettings();
  const { calorieGoal, waterGoal, macroGoals } = getNutritionalGoals();
  const { caloriesBurned } = useStepsLog();

  // Derived values
  const userId = user?.uid;
  const apiUrl = Configs.NutraCompass_API_URL;

  // Active item state
  const [activeMealSection, setActiveMealSection] = useState(null);
  const [activeFoodItem, setActiveFoodState] = useState({});
  const activeFoodItemRef = useRef(null);

  // Data state
  const [dataVersion, setDataVersion] = useState(0);
  const versionRef = useRef(0);
  const [mealSections, setMealSections] = useState([]);
  const [foodEntries, setFoodEntries] = useState({});
  const [currentRange, setCurrentRange] = useState(null);
  const [sections, setSections] = useState([]);
  const [refreshSectionList, setRefreshSectionList] = useState(false);
  const [selectedMealGroup, setSelectedMealGroup] = useState(null);

  // Refs
  const prevMealNamesRef = useRef({});

  /* ================================= */
  /* ========== EFFECTS ============= */
  /* ================================= */

  // Persist version to storage
  useEffect(() => {
    const loadSavedVersion = async () => {
      const savedVersion = await AsyncStorage.getItem(
        `foodDiaryVersion_${userId}`
      );
      if (savedVersion) {
        updateVersion(Number(savedVersion));
      }
    };
    loadSavedVersion();
  }, [userId]);

  // Meal sections effect
  useEffect(() => {
    const syncMealNamesWithNotifications = async () => {
      if (!initialLoadComplete || mealSections.length === 0) return;

      try {
        const currentMap = mealSections.reduce(
          (acc, meal) => ({
            ...acc,
            [meal.id]: meal.name,
          }),
          {}
        );

        const changes = Object.entries(currentMap).filter(
          ([id, name]) => prevMealNamesRef.current[id] !== name
        );

        if (changes.length > 0) {
          await syncMealNames(Object.fromEntries(changes));
          prevMealNamesRef.current = currentMap;
        }
      } catch (error) {
        console.error("Meal name sync failed:", error);
      }
    };

    syncMealNamesWithNotifications();
  }, [mealSections, initialLoadComplete]); // Only runs when sections change AND initial load done

  // Refresh sections when date or entries change
  useEffect(() => {
    setRefreshSectionList(true);
    updateSections();
  }, [selectedDate, mealSections, foodEntries]);

  // useEffect(() => {
  //   console.log(
  //     "Current foodEntries structure:",
  //     Object.keys(foodEntries).map((date) => ({
  //       date,
  //       mealTypes: Object.keys(foodEntries[date] || {}),
  //     }))
  //   );
  // }, [foodEntries]);

  //   useEffect(() => {
  //   console.log("Food history updated:", foodHistory);
  // }, [foodHistory]);

  /* ================================= */
  /* ======== CORE FUNCTIONS ======== */
  /* ================================= */

  // Update version in storage
  const updateVersion = (newVersion) => {
    setDataVersion(newVersion);
    versionRef.current = newVersion;
    AsyncStorage.setItem(`foodDiaryVersion_${userId}`, String(newVersion));
  };

  const setActiveFoodItem = useCallback((item) => {
    const safeItem = {
      ...item,
      isCustomMeal: Boolean(item?.isCustomMeal),
    };
    activeFoodItemRef.current = safeItem;
    setActiveFoodState(safeItem);
  }, []);

  const getActiveFoodItem = useCallback(() => activeFoodItemRef.current, []);

  // Function to extract required details from food entries (used for logging)
  const extractEntryDetails = (foodEntries) => {
    const simplifiedEntries = {};
    for (const mealType in foodEntries) {
      simplifiedEntries[mealType] = foodEntries[mealType].map((entry) => ({
        id: entry.id,
        mealType: entry.mealType,
        date: entry.date,
        foodLabel: entry.foodLabel,
        type: entry.type,
        mealHeaderId: entry?.mealHeaderId || null,
      }));
    }
    return simplifiedEntries;
  };

  // Used to format the actual sections state that is used for the Food Diary UI
  const updateSections = useCallback(async () => {
    try {
      // console.log("[SECTIONS] Starting section update");
      // console.log("[SECTIONS] Selected date:", selectedDate);
      // console.log("[SECTIONS] Current range:", currentRange);

      if (currentRange && !isDateInRange(selectedDate, currentRange)) {
        // console.log("[SECTIONS] Date not in range, loading entries");
        setRefreshSectionList(true);
        await loadFoodEntries(mealSections);
      }

      // Get entries for selected date
      const entriesForDate = foodEntries[selectedDate] || {};
      // console.log("[SECTIONS] Entries for date:", Object.keys(entriesForDate));

      // Create a map for quick meal-header lookup
      const sectionDataMap = new Map();

      // Initialize section data structure
      mealSections.forEach((section) => {
        sectionDataMap.set(section.id, {
          id: section.id,
          name: section.name,
          data: [],
          mealGroups: [],
        });
      });

      // Helper for safe date parsing
      const getEntryDate = (entry) => {
        try {
          // Prefer clientTimestamp if available, then timestamp, then loggedAt
          const rawDate =
            entry.clientTimestamp || entry.timestamp || entry.loggedAt;
          const parsed = parseFirestoreDate(rawDate);

          // Debugging: Log original and parsed values
          // console.log(`[DATE] Entry ${entry.id}:`, {
          //   type: entry.type,
          //   clientTS: entry.clientTimestamp,
          //   serverTS: entry.timestamp,
          //   loggedAt: entry.loggedAt,
          //   parsed: parsed.toISOString(),
          // });

          return parsed;
        } catch (e) {
          console.error("Date parse error:", e, entry);
          return new Date(0);
        }
      };

      // FLATTENED ARRAY FOR TIMELINE SORTING
      const allEntries = [];

      // console.log("[SECTIONS] Processing all entries for timeline sorting");
      Object.entries(entriesForDate).forEach(([mealType, entries]) => {
        // console.log(
        //   `[SECTIONS] Processing ${mealType} with ${entries.length} entries`
        // );
        const sectionData = sectionDataMap.get(mealType) || {
          id: mealType,
          name: mealType,
          data: [],
          mealGroups: [],
        };

        // Create maps for headers and groups
        const sectionHeaderMap = new Map();
        const sectionMealGroups = new Map();
        const processedItemIds = new Set();

        // FIRST PASS: Create headers and groups
        entries.forEach((entry) => {
          if (entry.type === "meal-header") {
            // console.log(`[SECTIONS] Found header in ${mealType}: ${entry.id}`);
            sectionHeaderMap.set(entry.id, entry);
            sectionMealGroups.set(entry.id, {
              type: "meal-group",
              id: entry.id,
              header: {
                ...entry,
                mealName: entry.mealName || "Custom Meal",
                mealImageUrl: entry.mealImageUrl || null,
                isTemporary: false,
              },
              items: [],
              timestamp: getEntryDate(entry),
              mealType: mealType, // Track meal type for reassignment
            });
            // Add header to flattened array
            allEntries.push({
              ...sectionMealGroups.get(entry.id),
              mealType,
            });
          }
        });

        // SECOND PASS: Assign items to groups
        entries.forEach((entry) => {
          if (entry.type === "meal-header") return;
          if (processedItemIds.has(entry.id)) return;

          if (entry.type === "meal-item") {
            if (
              entry.mealHeaderId &&
              sectionHeaderMap.has(entry.mealHeaderId)
            ) {
              const group = sectionMealGroups.get(entry.mealHeaderId);
              group.items.push(entry);
              processedItemIds.add(entry.id);
              // console.log(
              //   `[SECTIONS] Assigned ${entry.id} to group ${entry.mealHeaderId} in ${mealType}`
              // );
            } else {
              // console.log(
              //   `[SECTIONS] Orphaned item ${entry.id} in ${mealType}`
              // );
              const orphanKey = `orphan-group-${Date.now()}-${Math.random()
                .toString(36)
                .substr(2, 9)}`;
              if (!sectionMealGroups.has(orphanKey)) {
                const newGroup = {
                  type: "meal-group",
                  id: orphanKey,
                  header: {
                    id: orphanKey,
                    type: "meal-header",
                    mealName: "Custom Meal",
                    mealImageUrl: null,
                    isTemporary: true,
                    timestamp: entry.timestamp || new Date(),
                  },
                  items: [entry],
                  timestamp: getEntryDate(entry),
                  mealType: mealType, // Track meal type for reassignment
                };
                sectionMealGroups.set(orphanKey, newGroup);
                processedItemIds.add(entry.id);
                // Add orphan group to flattened array
                allEntries.push({ ...newGroup, mealType });
              } else {
                sectionMealGroups.get(orphanKey).items.push(entry);
                processedItemIds.add(entry.id);
              }
            }
          } else {
            // Regular food entry - add directly to flattened array
            allEntries.push({
              ...entry,
              type: "food",
              timestamp: getEntryDate(entry),
              mealType,
            });
          }
        });

        // Add meal groups to section mealGroups array
        sectionMealGroups.forEach((group) => {
          sectionData.mealGroups.push(group);
        });

        sectionDataMap.set(mealType, sectionData);
      });

      // SORT ALL ENTRIES BY TIMESTAMP (EARLIEST FIRST)
      // console.log("[SECTIONS] Sorting all entries by timestamp");
      allEntries.sort((a, b) => {
        const dateA = getEntryDate(a);
        const dateB = getEntryDate(b);

        // Handle invalid dates by placing them at the end
        if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
        if (isNaN(dateA.getTime())) return 1;
        if (isNaN(dateB.getTime())) return -1;

        return dateA - dateB;
      });

      // REASSIGN SORTED ENTRIES BACK TO SECTIONS
      // console.log("[SECTIONS] Reassigning sorted entries to sections");
      allEntries.forEach((entry) => {
        const section = sectionDataMap.get(entry.mealType);
        if (section) {
          if (entry.type === "meal-group") {
            // Add meal group to section data
            section.data.push(entry);
          } else {
            // Add individual food item to section data
            section.data.push(entry);
          }
        }
      });

      // Convert to array format
      const newSections = Array.from(sectionDataMap.values());
      // console.log(
      //   "[SECTIONS] Final sections:",
      //   newSections.map((s) => s.id)
      // );

      setSections(newSections);
      setRefreshSectionList(false);
      // console.log("[SECTIONS] Update complete");
    } catch (error) {
      console.error("[SECTIONS] Section update failed:", error);
    }
  }, [foodEntries, selectedDate, mealSections, currentRange, loadFoodEntries]);

  // Click handler for meal headers
  const handleMealHeaderPress = useCallback((mealGroup) => {
    setSelectedMealGroup(mealGroup);
    // setModalVisible(true);
  }, []);

  /* ================================= */
  /* ====== MEAL SECTION API ======== */
  /* ================================= */

  const loadMealSectionCustomizations = async (forceRefresh = false) => {
    const cacheKey = getMealSectionsCacheKey(userId);
    const DEFAULT_SECTIONS = [
      /* Your default meal sections here */
    ];

    try {
      if (!forceRefresh) {
        const cachedData = await AsyncStorage.getItem(cacheKey);
        if (cachedData) {
          const { data, timestamp } = JSON.parse(cachedData);
          const isCacheValid =
            Array.isArray(data) &&
            data.length > 0 &&
            data.every(
              (item) =>
                item?.id &&
                typeof item.id === "string" &&
                item.id !== "undefined" &&
                (item.id.startsWith("Meal") || item.id === "Water") &&
                typeof item.name === "string"
            );

          if (isCacheValid) {
            const hasChanges = !isEqual(data, mealSections);
            if (hasChanges) {
              console.log("[CACHE] Applying cached meal sections");
              setMealSections(data);
            }
            return data;
          } else {
            console.log("[CACHE] Discarding invalid/empty cache");
            await AsyncStorage.removeItem(cacheKey);
          }
        }
      }

      console.log("[NETWORK] Fetching fresh meal sections");
      const response = await axios.get(
        `${apiUrl}/v1/food/diary/${userId}/meal-sections`
      );

      // Validate and normalize response
      const newData = (response.data || [])
        .filter(
          (item) =>
            item?.id &&
            typeof item?.name === "string" &&
            item.id !== "undefined"
        )
        .concat(
          DEFAULT_SECTIONS.filter(
            (d) => !response.data?.some((r) => r.id === d.id)
          )
        );

      if (newData.length === 0) {
        console.warn("[NETWORK] Empty response, using defaults");
        setMealSections(DEFAULT_SECTIONS);
        return DEFAULT_SECTIONS;
      }

      console.log(
        "[STATE] Setting fresh sections:",
        newData.map((i) => `${i.id}:${i.name}`).join(", ")
      );

      setMealSections(newData);
      await AsyncStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: newData,
          timestamp: Date.now(),
        })
      );

      return newData;
    } catch (error) {
      console.error("[ERROR] Load failed:", error);

      const fallback =
        mealSections.length > 0 ? mealSections : DEFAULT_SECTIONS;
      console.log(
        "[FALLBACK] Using:",
        fallback.map((i) => i.id)
      );

      if (mealSections.length === 0) {
        setMealSections(fallback);
      }

      return fallback;
    }
  };

  const updateMealSectionNames = async (mealSectionUpdates) => {
    const cacheKey = getMealSectionsCacheKey(userId);
    const previousSections = [...mealSections];

    try {
      // 1. Server update (critical operation)
      const response = await axios.put(
        `${apiUrl}/v1/food/diary/${userId}/meal-sections/update-names`,
        mealSectionUpdates
      );

      // 2. Validate and process server response
      const newMealSections = (response.data.mealSections || [])
        .filter(
          (item) =>
            item?.id &&
            typeof item.id === "string" &&
            item.id !== "undefined" &&
            item.id.startsWith("Meal")
        )
        .concat(previousSections.filter((s) => s.id === "Water")) // Preserve Water
        .filter((v, i, a) => a.findIndex((t) => t.id === v.id) === i); // Deduplicate

      // 3. Update state with validated data
      setMealSections(newMealSections);

      // 4. Update cache with cleaned data
      await AsyncStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: newMealSections,
          timestamp: Date.now(),
        })
      );
    } catch (error) {
      if (!error.response || error.response.status >= 500) {
        console.log("Rolling back meal sections");
        setMealSections(previousSections);
        await AsyncStorage.setItem(
          cacheKey,
          JSON.stringify({
            data: previousSections,
            timestamp: Date.now(),
          })
        );
      }
      throw error;
    }
  };

  /* ================================= */
  /* ====== FOOD ENTRIES API ======== */
  /* ================================= */

  // Load food entries for all meal sections
  const loadFoodEntries = async (preloadedSections) => {
    try {
      console.log("[LOAD] Starting food entries load");
      const requestedRange = get6MonthRange(selectedDate);
      let dataSource = "none";
      let networkCallMade = false;
      console.log("[LOAD] Requested range:", requestedRange);

      // Determine if we should fetch data
      const shouldFetch =
        !currentRange ||
        !isDateInRange(selectedDate, currentRange) ||
        !dataVersion;

      if (shouldFetch) {
        console.log("[LOAD] Fetching data from server");

        networkCallMade = true;
        let response;
        let attempt = 0;
        const maxAttempts = 3;
        let lastError = null;

        while (attempt < maxAttempts) {
          attempt++;
          try {
            response = await axios.get(
              `${apiUrl}/v1/food/diary/${userId}/entries`,
              {
                params: {
                  date: selectedDate,
                  t: Date.now(),
                },
                headers: { "X-Data-Version": dataVersion || "0" },
                timeout: 10000, // 10 second timeout
              }
            );
            break; // Break out of retry loop on success
          } catch (error) {
            lastError = error;

            // Don't retry for 4xx client errors
            if (error.response?.status >= 400 && error.response?.status < 500) {
              throw error;
            }

            // Exponential backoff: 1s, 2s, 4s
            if (attempt < maxAttempts) {
              const delay = Math.pow(2, attempt) * 1000;
              console.warn(
                `Request failed (attempt ${attempt}/${maxAttempts}), retrying in ${delay}ms...`
              );
              await new Promise((resolve) => setTimeout(resolve, delay));
            }
          }
        }

        // If all attempts failed
        if (!response) {
          throw lastError || new Error("All fetch attempts failed");
        }

        // Handle 304 Not Modified
        if (response.status === 304) {
          const serverVersion = response.headers["x-data-version"];
          if (serverVersion) {
            updateVersion(Number(serverVersion));
          }
          return {
            fromCache: true,
            dataSource: "client",
            networkCallMade: true,
          };
        }

        // Validate version exists
        if (!response.data.version) {
          console.error("Received invalid data version", response.data);
          throw new Error("Server response missing version information");
        }

        // Process fresh data
        console.log("[LOAD] Processing fresh data");
        const backendData = response.data?.data || {};
        console.log("[LOAD] Backend data keys:", Object.keys(backendData));

        const receivedRange = response.data?.range || requestedRange;
        dataSource = response.data.source || "database";

        console.log(`Loaded from ${dataSource}`, {
          entries: Object.keys(backendData).length,
          version: response.data.version,
        });

        // Update state with new data
        updateVersion(response.data.version);

        // Transform backend data to correct structure using your helper method
        const transformedEntries = transformRangeToEntries(
          backendData,
          preloadedSections
        );

        // Add this post-processing:
        Object.keys(transformedEntries).forEach((date) => {
          Object.keys(transformedEntries[date]).forEach((mealType) => {
            transformedEntries[date][mealType].forEach((entry) => {
              if (entry.type === "meal-item" && !entry.mealHeaderId) {
                // Add reference to header if missing
                const header = transformedEntries[date][mealType].find(
                  (e) =>
                    e.type === "meal-header" &&
                    e.mealId === entry.inACustomMeal?.mealId
                );
                if (header) {
                  entry.mealHeaderId = header.id;
                }
              }
            });
          });
        });
        // console.log("[LOAD] Transformed entries:", transformedEntries);

        setFoodEntries((prev) => deepMergeEntries(prev, transformedEntries));
        setCurrentRange((prev) => mergeRanges(prev, receivedRange));

        return {
          fromCache: false,
          dataSource,
          networkCallMade: true,
        };
      }

      // Version check for in-range dates
      try {
        // Track HEAD request as network call
        networkCallMade = true;

        const checkResponse = await axios.head(
          `${apiUrl}/v1/food/diary/${userId}/entries`,
          {
            params: { date: selectedDate },
            headers: { "X-Data-Version": dataVersion },
          }
        );

        // If version changed, force refetch
        if (checkResponse.status !== 304) {
          console.log("Version mismatch detected, forcing refetch");
          setCurrentRange(null);
          return loadFoodEntries(preloadedSections);
        }

        // Version matches - no additional network call needed
        networkCallMade = false;
      } catch (error) {
        if (error.response?.status === 304) {
          networkCallMade = false; // 304 doesn't count as data network call
        } else {
          console.error("Version check failed", error);
        }
      }

      // Data is current and available locally
      return {
        fromCache: false,
        dataSource: "memory",
        networkCallMade, // Reflects if HEAD request was made
      };
    } catch (error) {
      // Handle 304 in error case
      if (error.response?.status === 304) {
        const source =
          error.response.headers["x-cache-hit"]?.toLowerCase() || "client";
        return {
          fromCache: true,
          dataSource: source,
          networkCallMade: true, // Network call was made
        };
      }

      // Error handling
      console.error("[LOAD] Failed to load food entries:", error);
      return {
        fromCache: false,
        dataSource: "error",
        networkCallMade: !!error.response,
        error: error.message,
      };
    }
  };

  // Build food history from foodEntries state
  const buildFoodHistory = (entries) => {
    if (!entries || typeof entries !== "object") return [];

    const allEntries = [];
    const mealHeaderMap = new Map(); // Store meal headers by id
    const mealItemsMap = new Map(); // Store meal items by header ID

    // First pass: collect all entries and organize meal components
    Object.keys(entries).forEach((date) => {
      Object.keys(entries[date]).forEach((mealType) => {
        entries[date][mealType].forEach((entry) => {
          // Skip Water and Quick Add entries
          if (entry.foodCategory === "Water") return;
          if (
            entry.foodCategory === "Quick Add" &&
            !entry.inACustomMeal?.isPartOfMeal
          )
            return;

          // Track meal headers
          if (entry.type === "meal-header") {
            mealHeaderMap.set(entry.id, {
              ...entry,
              date,
              mealType,
              isFromHistory: true, // Mark as history
            });
          }
          // Track meal items
          else if (entry.type === "meal-item" && entry.mealHeaderId) {
            if (!mealItemsMap.has(entry.mealHeaderId)) {
              mealItemsMap.set(entry.mealHeaderId, []);
            }
            mealItemsMap.get(entry.mealHeaderId).push({
              ...entry,
              isFromHistory: true, // Mark as history
            });
          }
          // Track standalone entries
          else {
            allEntries.push({
              ...entry,
              date,
              mealType,
              isFromHistory: true, // Mark as history
            });
          }
        });
      });
    });

    // Reconstruct custom meals
    const reconstructedMeals = [];
    for (const [headerId, header] of mealHeaderMap) {
      const items = mealItemsMap.get(headerId) || [];

      // Only reconstruct if we have items
      if (items.length > 0) {
        reconstructedMeals.push({
          id: header.mealId,
          mealId: header.mealId,
          headerId: header.id,
          foodLabel: header.mealName,
          mealName: header.mealName,
          mealImageUrl: header.mealImageUrl,
          mealItems: items,
          nutrients: calculateTotalNutrients(items),
          isCustomMeal: true,
          type: "meal",
          date: header.date,
          mealType: header.mealType,
          timestamp: header.timestamp,
          clientTimestamp: header.clientTimestamp,
          isFromHistory: true, // Mark as history
        });
      }
    }

    // Combine all entries
    let combinedHistory = [...reconstructedMeals, ...allEntries];

    // Sort by timestamp descending
    combinedHistory.sort((a, b) => {
      const aTime = a.clientTimestamp || a.timestamp;
      const bTime = b.clientTimestamp || b.timestamp;
      return new Date(bTime) - new Date(aTime);
    });

    // Apply deduplication
    combinedHistory = removeDuplicates(combinedHistory);

    // Limit to 20 most recent items
    return combinedHistory.slice(0, 20);
  };

  // Helper function to calculate total nutrients for a meal
  const calculateTotalNutrients = (mealItems) => {
    const total = {
      core: {},
      minerals: {},
      vitamins: {},
    };

    mealItems.forEach((item) => {
      // Nutrients are ALREADY multiplied by servings in the item
      // So we should NOT multiply again here

      // Helper to sum nutrients in a category
      const sumCategory = (category) => {
        if (!item.nutrients?.[category]) return;

        Object.entries(item.nutrients[category]).forEach(([key, nutrient]) => {
          if (!total[category][key]) {
            // Initialize with a copy of the nutrient
            total[category][key] = { ...nutrient };
          } else {
            // Simply add the quantity (already includes servings)
            total[category][key].quantity += nutrient.quantity;
          }
        });
      };

      // Process all nutrient categories
      sumCategory("core");
      sumCategory("minerals");
      sumCategory("vitamins");
    });

    return total;
  };

  const transformRangeToEntries = (rangeData, sections) => {
    const safeData = rangeData || {};
    const seenIds = new Set();
    const transformed = {};

    // Create a special entry for the selected date
    transformed[selectedDate] = {};

    for (const date in safeData) {
      // Initialize date if not exists
      if (!transformed[date]) {
        transformed[date] = {};
      }

      for (const section of sections) {
        const mealType = section.id;
        const entries = safeData[date]?.[mealType] || [];

        // Initialize meal type
        if (!transformed[date][mealType]) {
          transformed[date][mealType] = [];
        }

        for (const entry of entries) {
          if (!entry?.id) continue;
          if (seenIds.has(entry.id)) continue;

          seenIds.add(entry.id);

          transformed[date][mealType].push({
            ...entry,
            date,
            mealType,
          });
        }
      }
    }

    return transformed;
  };

  // Helper method for deep merging food entries
  const deepMergeEntries = (prevEntries, newEntries) => {
    // Create a clone of previous entries
    const merged = { ...prevEntries };

    // Iterate through all dates in newEntries
    for (const date in newEntries) {
      // Initialize date in merged if it doesn't exist
      if (!merged[date]) {
        merged[date] = {};
      }

      // Iterate through all meal types for this date
      for (const mealType in newEntries[date]) {
        const existing = merged[date][mealType] || [];
        const incoming = newEntries[date][mealType] || [];

        // Merge while preventing duplicates by ID
        const mergedEntries = [
          ...existing,
          ...incoming.filter(
            (newEntry) =>
              !existing.some(
                (existingEntry) => existingEntry.id === newEntry.id
              )
          ),
        ];

        // Update the merged structure
        merged[date][mealType] = mergedEntries;
      }
    }

    return merged;
  };

  // const minDate = (a, b) => (dayjs(a).isBefore(dayjs(b)) ? a : b);
  // const maxDate = (a, b) => (dayjs(a).isAfter(dayjs(b)) ? a : b);
  const minDate = (a, b) => (a < b ? a : b);
  const maxDate = (a, b) => (a > b ? a : b);

  // Range merger with validation
  const mergeRanges = (prev, received) => {
    // Enhanced validation with fallbacks
    if (!received || !received.start || !received.end) {
      console.warn("Invalid received range:", received);
      return prev || get6MonthRange(selectedDate); // Fallback to default range
    }

    // If prev is invalid, use received range
    if (!prev || !prev.start || !prev.end) {
      return received;
    }

    return {
      start: minDate(prev.start, received.start),
      end: maxDate(prev.end, received.end),
    };
  };

  // Save handler for meal group edits
  const saveMealGroupChanges = useCallback(
    async (updatedMealGroup) => {
      try {
        // Prepare payload
        const payload = {
          mealGroupId: updatedMealGroup.header.id,
          newSection:
            updatedMealGroup.newSection || updatedMealGroup.header.mealType,
          servings: updatedMealGroup.numberOfServings || 1,
          mealItems: updatedMealGroup.items.map((item) => item.id),
        };

        // Make API call
        const response = await axios.put(
          `${apiUrl}/v1/food/diary/${userId}/meal-group`,
          payload
        );

        const { updatedHeader, updatedItems } = response.data;

        // Update local state
        setFoodEntries((prev) => {
          const newEntries = { ...prev };
          const oldSection = updatedMealGroup.header.mealType;
          const newSection = updatedHeader.mealType;

          // Remove from old section
          if (newEntries[selectedDate]?.[oldSection]) {
            newEntries[selectedDate][oldSection] = newEntries[selectedDate][
              oldSection
            ].filter(
              (e) =>
                e.id !== updatedHeader.id &&
                !updatedItems.some((i) => i.id === e.id)
            );
          }

          // Add to new section
          if (!newEntries[selectedDate]) newEntries[selectedDate] = {};
          if (!newEntries[selectedDate][newSection]) {
            newEntries[selectedDate][newSection] = [];
          }

          // Update header in new section
          const existingHeader = newEntries[selectedDate][newSection].find(
            (e) => e.id === updatedHeader.id
          );

          if (existingHeader) {
            Object.assign(existingHeader, {
              mealType: newSection,
              numberOfServings: updatedHeader.numberOfServings,
            });
          } else {
            newEntries[selectedDate][newSection].push({
              ...updatedMealGroup.header,
              mealType: newSection,
              numberOfServings: updatedHeader.numberOfServings,
            });
          }

          // Update items in new section
          updatedItems.forEach((updatedItem) => {
            const existingItem = newEntries[selectedDate][newSection].find(
              (e) => e.id === updatedItem.id
            );

            if (existingItem) {
              existingItem.mealType = newSection;
            } else {
              const originalItem = updatedMealGroup.items.find(
                (item) => item.id === updatedItem.id
              );
              if (originalItem) {
                newEntries[selectedDate][newSection].push({
                  ...originalItem,
                  mealType: newSection,
                });
              }
            }
          });

          return newEntries;
        });

        setModalVisible(false);
        showToast("Meal updated successfully");
      } catch (error) {
        console.error("Failed to update meal group:", error);
        showToast("Failed to update meal", "error");
      }
    },
    [selectedDate]
  );

  const saveMealToFoodLog = async (mealType, selectedDate, selectedMeal) => {
    try {
      // Debugging: Log the date before sending to backend
      console.log("[saveMealToFoodLog] Saving meal with date:", selectedDate);

      //  payload to satisfy backend requirements
      const payload = {
        mealType,
        selectedDate, // Ensure this is the correct date
        mealItems: Array.isArray(selectedMeal?.mealItems)
          ? selectedMeal.mealItems
          : [],
        mealId: selectedMeal?.id || selectedMeal?.mealId || uuid.v4(),
        mealName:
          selectedMeal?.foodLabel || selectedMeal?.mealName || "Custom Meal",
        mealImage:
          selectedMeal?.mealImageUrl || selectedMeal?.mealImage || "",
      };

      // Guard against empty/invalid mealItems which would cause 500 on backend
      if (!payload.mealItems.length) {
        console.error(
          "[saveMealToFoodLog] Aborting save: mealItems is empty or invalid",
          { selectedMeal }
        );
        throw new Error("No meal items to save");
      }

      console.log("[saveMealToFoodLog] Payload summary:", {
        mealType: payload.mealType,
        selectedDate: payload.selectedDate,
        mealItemsCount: payload.mealItems.length,
        mealId: payload.mealId,
        mealName: payload.mealName,
      });

      // Simple retry on 429 (too many requests)
      const executeSave = async (attempt = 1) => {
        try {
          return await axios.post(
            `${apiUrl}/v1/food/diary/${userId}/save-meal`,
            payload
          );
        } catch (err) {
          const status = err?.response?.status;
          if (status === 429 && attempt < 3) {
            const backoff = 500 * attempt; // 0.5s, 1s
            console.warn(`[saveMealToFoodLog] 429 received, retrying in ${backoff}ms (attempt ${attempt + 1})`);
            await new Promise((res) => setTimeout(res, backoff));
            return executeSave(attempt + 1);
          }
          throw err;
        }
      };

      const response = await executeSave();

      setDataVersion(response.data.version);

      const entryItems = response.data.entryItems || [];

      // Debugging: Log the returned entries
      console.log("[saveMealToFoodLog] Response entries:", entryItems);

      // Collect all new entries to add
      const newEntries = entryItems.filter(
        (entry) =>
          !foodEntries[selectedDate]?.[mealType]?.some((e) => e.id === entry.id)
      );

      if (newEntries.length === 0) {
        console.log("[saveMealToFoodLog] No new entries to add");
        return;
      }

      // Debugging: Log the new entries being added
      console.log("[saveMealToFoodLog] Adding new entries:", newEntries);

      setFoodEntries((prev) => {
        const updated = { ...prev };

        // Ensure the selectedDate bucket exists
        if (!updated[selectedDate]) {
          console.log(
            `[saveMealToFoodLog] Creating new date bucket: ${selectedDate}`
          );
          updated[selectedDate] = {};
        }

        // Ensure the mealType array exists
        if (!updated[selectedDate][mealType]) {
          console.log(
            `[saveMealToFoodLog] Creating new mealType bucket: ${mealType}`
          );
          updated[selectedDate][mealType] = [];
        }

        // Add new entries to the end
        updated[selectedDate][mealType] = [
          ...updated[selectedDate][mealType],
          ...newEntries,
        ];

        console.log(
          `[saveMealToFoodLog] Added ${newEntries.length} entries to ${selectedDate}/${mealType}`
        );
        return updated;
      });
    } catch (error) {
      console.error(
        "Error saving meal to food log:",
        error?.response?.data || error.message,
        "status:",
        error?.response?.status || "unknown"
      );
    }
  };

  const saveOrUpdateSingleFoodItemToFoodLog = async (
    entryId,
    mealType,
    updatedEntry
  ) => {
    try {
      // Validate required fields
      if (!updatedEntry || !updatedEntry.foodId) {
        console.error("Missing required entry details: 'foodId'");
        throw new Error("Missing required entry details.");
      }

      // Determine if this is a historical entry
      const isHistoricalEntry = updatedEntry.isFromHistory || false;

      // Always treat historical entries as new creations
      const isNewEntry = !entryId || isHistoricalEntry;

      // Clear ID for new/historical entries
      if (isNewEntry) {
        entryId = null;
        updatedEntry = { ...updatedEntry, id: undefined };
      }

      // Find original entry to preserve grouping information (only for non-historical updates)
      let originalEntry = null;
      let originalLocation = null;

      if (entryId && !isHistoricalEntry) {
        for (const date in foodEntries) {
          for (const mealTypeKey in foodEntries[date]) {
            const found = foodEntries[date][mealTypeKey].find(
              (e) => e.id === entryId
            );
            if (found) {
              originalEntry = found;
              originalLocation = { date, mealType: mealTypeKey };
              break;
            }
          }
          if (originalEntry) break;
        }
      }

      // Add current timestamp for new entries
      if (isNewEntry) {
        updatedEntry = {
          ...updatedEntry,
          clientTimestamp: new Date().toISOString(),
        };
      }

      // Prepare payload with additional context
      const payload = {
        mealType,
        updatedEntry,
        selectedDate: selectedDate,
        originalHeaderId: null,
        originalDate: null,
        originalMealType: null,
      };

      // Check if we're moving a meal item to a new section (only for non-historical)
      const isMovingMealItem =
        !isHistoricalEntry &&
        originalEntry?.type === "meal-item" &&
        mealType !== originalLocation?.mealType;

      // Add original header context only for meal items being moved
      if (isMovingMealItem) {
        payload.originalHeaderId = originalEntry.mealHeaderId;
        payload.originalDate = originalLocation.date;
        payload.originalMealType = originalLocation.mealType;

        // Modify the entry to detach from group
        payload.updatedEntry = {
          ...updatedEntry,
          mealHeaderId: null, // Detach from group
          type: "food", // Convert to standalone food entry
        };
      } else if (!isHistoricalEntry && originalEntry?.mealHeaderId) {
        // Preserve mealHeaderId for non-move updates
        payload.updatedEntry.mealHeaderId = originalEntry.mealHeaderId;
      }

      // Always use POST for both create and update
      const url = `${apiUrl}/v1/food/diary/${userId}/entries${
        entryId ? `/${entryId}` : ""
      }`;

      const response = await axios.post(url, payload); // Always use POST
      setDataVersion(response.data.version);

      // Get the saved entry from response
      const savedEntry = response.data.entry;
      const savedId = savedEntry.id;

      // Update UI after successful server response
      setFoodEntries((prev) => {
        const updated = { ...prev };
        const targetDate = selectedDate;

        // Create target location if it doesn't exist
        if (!updated[targetDate]) updated[targetDate] = {};
        if (!updated[targetDate][mealType]) updated[targetDate][mealType] = [];

        const targetEntries = updated[targetDate][mealType];

        // 1. Remove existing entry from ALL locations if updating
        if (!isNewEntry) {
          for (const dateKey in updated) {
            for (const mealKey in updated[dateKey]) {
              updated[dateKey][mealKey] = updated[dateKey][mealKey].filter(
                (e) => e.id !== entryId
              );
            }
          }
        }

        // 2. Remove entry from original location if moving (non-historical)
        if (!isHistoricalEntry && originalLocation && isMovingMealItem) {
          const { date, mealType: originalMealType } = originalLocation;

          if (updated[date]?.[originalMealType]) {
            updated[date] = { ...updated[date] };
            updated[date][originalMealType] = updated[date][
              originalMealType
            ].filter((e) => e.id !== entryId);

            // Remove abandoned header if backend deleted it
            if (response.data.deletedHeaderId) {
              updated[date][originalMealType] = updated[date][
                originalMealType
              ].filter(
                (entry) =>
                  !(
                    entry.type === "meal-header" &&
                    entry.id === response.data.deletedHeaderId
                  )
              );
            }
          }
        }

        // 3. Add/update entry in target location
        // Check if entry already exists in target location (shouldn't after removal)
        const existingIndex = targetEntries.findIndex((e) => e.id === savedId);

        if (existingIndex !== -1) {
          // Update existing entry
          updated[targetDate][mealType] = targetEntries.map((item, index) =>
            index === existingIndex
              ? { ...savedEntry, isFromHistory: false }
              : item
          );
        } else {
          // Add new entry
          updated[targetDate][mealType] = [
            ...targetEntries,
            { ...savedEntry, isFromHistory: false },
          ];
        }

        return updated;
      });
    } catch (error) {
      console.error("Error saving or updating food entry:", error);
      // Show error to user
      Alert.alert(
        "Save Failed",
        error.response?.data?.message || "Could not save food entry"
      );
    }
  };

  // Function to handle deleting a single food entry
  const deleteFoodEntry = async (mealType, entryId) => {
    try {
      // First, get the current entry from state to check if it's part of a meal group
      let currentEntry = null;
      let foundLocation = null;

      for (const date in foodEntries) {
        for (const mealTypeKey in foodEntries[date]) {
          const entry = foodEntries[date][mealTypeKey].find(
            (e) => e.id === entryId
          );
          if (entry) {
            currentEntry = entry;
            foundLocation = { date, mealType: mealTypeKey };
            break;
          }
        }
        if (currentEntry) break;
      }

      if (!currentEntry) {
        console.warn("Entry not found for deletion:", entryId);
        return;
      }

      const response = await axios.delete(
        `${apiUrl}/v1/food/diary/${userId}/entries/${entryId}`
      );

      setDataVersion(response.data.version);

      // Optimistic UI update with meal header cleanup
      setFoodEntries((prev) => {
        const updated = { ...prev };
        let headerIdToRemove = null;

        // Remove the deleted entry from its location
        if (
          foundLocation &&
          updated[foundLocation.date]?.[foundLocation.mealType]
        ) {
          updated[foundLocation.date] = { ...updated[foundLocation.date] };
          updated[foundLocation.date][foundLocation.mealType] = updated[
            foundLocation.date
          ][foundLocation.mealType].filter((e) => e.id !== entryId);

          // Check if we need to remove any orphaned headers
          if (currentEntry?.mealHeaderId) {
            // Find the header in the same date section
            const header = updated[foundLocation.date][
              foundLocation.mealType
            ]?.find(
              (e) =>
                e.id === currentEntry.mealHeaderId && e.type === "meal-header"
            );

            if (header) {
              // Check if header has any remaining items in the same date
              const hasItems = updated[foundLocation.date][
                foundLocation.mealType
              ]?.some(
                (e) =>
                  e.mealHeaderId === currentEntry.mealHeaderId &&
                  e.id !== entryId
              );

              if (!hasItems) {
                // Remove the orphaned header from all sections in this date
                for (const mealTypeKey in updated[foundLocation.date]) {
                  updated[foundLocation.date][mealTypeKey] = updated[
                    foundLocation.date
                  ][mealTypeKey].filter(
                    (e) => e.id !== currentEntry.mealHeaderId
                  );
                }
                headerIdToRemove = currentEntry.mealHeaderId;
              }
            }
          }
        }

        return updated;
      });
    } catch (error) {
      console.error("Error deleting food entry:", error);
    }
  };

  // Function to handle deleting all entries from a specific meal section
  const deleteMealSectionEntries = async (mealType) => {
    try {
      const response = await axios.delete(
        `${apiUrl}/v1/food/diary/${userId}/entries/meal-section/${mealType}/date/${selectedDate}`
      );

      setDataVersion(response.data.version);

      // Update local state with orphaned header cleanup
      setFoodEntries((prevEntries) => {
        const updatedEntries = { ...prevEntries };
        const headersToRemove = new Set();

        // Only process the selected date
        if (updatedEntries[selectedDate]?.[mealType]) {
          // Collect headers that might be orphaned
          updatedEntries[selectedDate][mealType].forEach((entry) => {
            if (entry.type === "meal-item" && entry.mealHeaderId) {
              headersToRemove.add(entry.mealHeaderId);
            }
          });

          // Remove all entries for this meal type on the selected date
          updatedEntries[selectedDate][mealType] = [];
        }

        // Check and remove orphaned headers in the same date
        headersToRemove.forEach((headerId) => {
          // Check if header still exists and has no items in the same date
          let hasItems = false;
          for (const mealTypeKey in updatedEntries[selectedDate]) {
            if (
              updatedEntries[selectedDate][mealTypeKey].some(
                (e) => e.mealHeaderId === headerId
              )
            ) {
              hasItems = true;
              break;
            }
          }

          if (!hasItems) {
            // Remove the orphaned header from all meal types in this date
            for (const mealTypeKey in updatedEntries[selectedDate]) {
              updatedEntries[selectedDate][mealTypeKey] = updatedEntries[
                selectedDate
              ][mealTypeKey].filter((e) => e.id !== headerId);
            }
          }
        });

        return updatedEntries;
      });

      console.log("Entries deleted successfully.");
    } catch (error) {
      console.error("Failed to delete entries:", error);
    }
  };

  const copyEntriesBetweenMealSections = async (
    sourceMealType,
    destinationMealType,
    sourceDate,
    destinationDate
  ) => {
    try {
      // Get source entries from correct date/meal structure
      const sourceDateEntries = foodEntries[sourceDate] || {};
      const entries = sourceDateEntries[sourceMealType] || [];

      if (entries.length > 0) {
        const response = await axios.post(
          `${apiUrl}/v1/food/diary/${userId}/copy-entries`,
          {
            entries,
            destinationMealType,
            destinationDate,
          }
        );

        setDataVersion(response.data.version);
        const newEntries = response.data.entries || [];

        setFoodEntries((prevEntries) => {
          const updatedEntries = { ...prevEntries };

          newEntries.forEach((entry) => {
            const date = entry.date;
            const mealType = entry.mealType;

            // Initialize date if needed
            if (!updatedEntries[date]) {
              updatedEntries[date] = {};
            }

            // Initialize meal type if needed
            if (!updatedEntries[date][mealType]) {
              updatedEntries[date][mealType] = [];
            }

            // Add entry if not already present
            if (
              !updatedEntries[date][mealType].some((e) => e.id === entry.id)
            ) {
              updatedEntries[date][mealType].push(entry);
            }
          });

          return updatedEntries;
        });
      }
    } catch (error) {
      console.error("Error copying entries:", error);
    }
  };

  const addQuickFoodEntry = async (mealType, foodDetails) => {
    if (!mealType || !foodDetails) return;

    // Reformat the foodDetails to match the structure expected by saveOrUpdateSingleFoodItemToFoodLog
    const formattedEntry = {
      foodLabel: foodDetails.foodLabel,
      nutrients: foodDetails.nutrients,
      foodCategory: "Quick Add",
      isQuickAdd: true,
      numberOfServings: foodDetails.numberOfServings || 1,
    };

    // Include ID if editing
    if (foodDetails.id) {
      formattedEntry.id = foodDetails.id;
    }

    const payload = {
      mealType: mealType,
      entryDetails: formattedEntry,
      selectedDate: selectedDate,
    };

    try {
      const response = await axios.post(
        `${apiUrl}/v1/food/diary/${userId}/quick-entry`,
        payload
      );

      setDataVersion(response.data.version);
      const newEntry = response.data.entry;

      setFoodEntries((prevEntries) => {
        const updatedEntries = { ...prevEntries };
        const date = selectedDate;

        // Initialize date if needed
        if (!updatedEntries[date]) {
          updatedEntries[date] = {};
        }

        // Initialize meal type if needed
        if (!updatedEntries[date][mealType]) {
          updatedEntries[date][mealType] = [];
        }

        const currentEntries = updatedEntries[date][mealType];

        if (foodDetails.id) {
          // Update existing entry
          updatedEntries[date][mealType] = currentEntries.map((entry) =>
            entry.id === foodDetails.id ? { ...entry, ...newEntry } : entry
          );
        } else {
          // Add new entry
          updatedEntries[date][mealType] = [...currentEntries, newEntry];
        }

        return updatedEntries;
      });

      console.log("Quick add successful:", response.data);
    } catch (error) {
      console.error("Failed to submit quick add:", error);
    }
  };

  /* ================================= */
  /* ======== WATER TRACKING ======== */
  /* ================================= */

  // Helper function to convert water volume to goal units
  const convertToGoalUnit = (volume, unit, goalUnit) => {
    if (unit === goalUnit) return volume;

    if (goalUnit === "fl oz") {
      // Convert to fluid ounces
      return unit === "ml" ? volume * 0.033814 : volume;
    } else {
      // Convert to milliliters (default)
      return unit === "fl oz" ? volume * 29.5735 : volume;
    }
  };

  const addWaterEntry = async (volume, unit, existingId) => {
    try {
      const response = await axios.post(
        `${apiUrl}/v1/food/diary/${userId}/water-log`,
        {
          volume: volume,
          unit: unit,
          date: selectedDate,
          id: existingId,
        }
      );

      setDataVersion(response.data.version);
      const newEntry = response.data.entry;

      setFoodEntries((prevEntries) => {
        const updatedEntries = { ...prevEntries };
        const date = selectedDate;
        const mealType = "Water"; // Always water type

        // Initialize date if needed
        if (!updatedEntries[date]) {
          updatedEntries[date] = {};
        }

        // Initialize meal type if needed
        if (!updatedEntries[date][mealType]) {
          updatedEntries[date][mealType] = [];
        }

        const currentEntries = updatedEntries[date][mealType];

        if (existingId) {
          // Update existing entry
          updatedEntries[date][mealType] = currentEntries.map((entry) =>
            entry.id === existingId ? { ...entry, ...newEntry } : entry
          );
        } else {
          // Add new entry
          updatedEntries[date][mealType] = [...currentEntries, newEntry];
        }

        return updatedEntries;
      });

      console.log("Adding water entry successful:", response.data);
    } catch (error) {
      console.error("Failed to log water intake:", error);
    }
  };

  /* ================================= */
  /* ======== NUTRITION CALC ======== */
  /* ================================= */

  // Recursively calculate nutrients from food entries
  const calculateTotalCaloriesAndMacros = (entries) => {
    // console.log("[NUTRITION] Calculating for entries:", entries.length);

    // Track processed entries to prevent double counting
    const processedIds = new Set();
    let totalCalories = 0;
    let totalCarbs = 0;
    let totalProtein = 0;
    let totalFat = 0;

    const processEntry = (entry) => {
      if (processedIds.has(entry.id)) {
        // console.log(`[NUTRITION] Skipping ${entry.id} (already processed)`);
        return;
      }

      processedIds.add(entry.id);
      // console.log(`[NUTRITION] Processing ${entry.id} (${entry.type})`);

      if (entry.type === "meal-group") {
        // console.log(`[NUTRITION] Found meal-group ${entry.id}`);

        // Process group items WITHOUT item-level servings
        const groupBaseTotals = entry.items.reduce(
          (totals, item) => {
            if (!item?.nutrients?.core) return totals;

            const { ENERC_KCAL, CHOCDF, PROCNT, FAT } = item.nutrients.core;

            // Add BASE nutrients without item servings
            totals.calories += Math.abs(Number(ENERC_KCAL?.quantity)) || 0;
            totals.carbs += Math.abs(Number(CHOCDF?.quantity)) || 0;
            totals.protein += Math.abs(Number(PROCNT?.quantity)) || 0;
            totals.fat += Math.abs(Number(FAT?.quantity)) || 0;

            return totals;
          },
          { calories: 0, carbs: 0, protein: 0, fat: 0 }
        );

        // Apply ONLY group-level servings
        const groupServings = entry.header.numberOfServings || 1;

        totalCalories += groupBaseTotals.calories * groupServings;
        totalCarbs += groupBaseTotals.carbs * groupServings;
        totalProtein += groupBaseTotals.protein * groupServings;
        totalFat += groupBaseTotals.fat * groupServings;

        // console.log(
        //   `[NUTRITION] Meal group ${entry.id}: ` +
        //     `Base=${groupBaseTotals.calories} ` +
        //     `×${groupServings} servings = ${
        //       groupBaseTotals.calories * groupServings
        //     } cal`
        // );
      } else if (entry.type === "meal-header") {
        // console.log(`[NUTRITION] Skipping meal-header ${entry.id}`);
      } else {
        // Handle standalone food entries
        if (entry?.nutrients?.core) {
          const { ENERC_KCAL, CHOCDF, PROCNT, FAT } = entry.nutrients.core;
          const servings = entry.numberOfServings || 1;

          const calories =
            Math.abs(Number(ENERC_KCAL?.quantity)) || 0 * servings;
          const carbs = Math.abs(Number(CHOCDF?.quantity)) || 0 * servings;
          const protein = Math.abs(Number(PROCNT?.quantity)) || 0 * servings;
          const fat = Math.abs(Number(FAT?.quantity)) || 0 * servings;

          // console.log(
          //   `[NUTRITION] Adding nutrients for ${entry.id}: ` +
          //     `Calories=${calories} (Base=${ENERC_KCAL?.quantity} ×${servings})`
          // );

          totalCalories += calories;
          totalCarbs += carbs;
          totalProtein += protein;
          totalFat += fat;
        }
      }
    };

    // Process all entries
    entries.forEach(processEntry);

    // console.log(
    //   `[NUTRITION] Calculation complete: ` +
    //     `Calories=${totalCalories}, Carbs=${totalCarbs}, Protein=${totalProtein}, Fat=${totalFat}`
    // );

    return {
      calories: totalCalories,
      carbs: totalCarbs,
      protein: totalProtein,
      fat: totalFat,
    };
  };

  // Find meal name by ID (supports sections and headers)
  const findMealNameById = (mealId) => {
    // Check regular meal sections first
    const mealSection = mealSections.find((section) => section.id === mealId);
    if (mealSection) return mealSection.name;

    // Then check meal headers in all entries
    for (const date in foodEntries) {
      for (const mealType in foodEntries[date]) {
        const header = foodEntries[date][mealType].find(
          (entry) => entry.id === mealId && entry.type === "meal-header"
        );
        if (header) return header.mealName;
      }
    }

    return "";
  };

  /* ================================= */
  /* ====== CALCULATION HOOKS ======= */
  /* ================================= */

  // Calculate total water consumed for selected date
  const totalDailyWaterConsumed = useMemo(() => {
    // Get water entries for selected date
    const dateEntries = foodEntries[selectedDate] || {};
    const waterEntries = dateEntries["Water"] || [];

    // Convert all entries to goal units and sum
    return waterEntries.reduce((total, entry) => {
      const convertedVolume = convertToGoalUnit(
        entry.volume,
        entry.unit,
        waterGoal?.unit || "ml"
      );
      return total + (Number(convertedVolume) || 0);
    }, 0);
  }, [foodEntries, selectedDate, waterGoal?.unit]);

  // Calculate water consumption percentage
  const totalDailyWaterConsumedPercentage = useMemo(() => {
    if (!waterGoal?.amount || waterGoal.amount <= 0) return 0;

    const rawPercentage = (totalDailyWaterConsumed / waterGoal.amount) * 100;
    return Math.min(Math.max(rawPercentage, 0), 100); // Clamp between 0-100
  }, [totalDailyWaterConsumed, waterGoal]);

  // Filter entries by meal type for selected date
  const filteredEntriesByMeal = useMemo(() => {
    const dateEntries = foodEntries[selectedDate] || {};

    return mealSections.reduce((acc, section) => {
      const entries = dateEntries[section.id] || [];
      // Exclude meal headers and water entries from nutrition calculations
      acc[section.id] = entries.filter(
        (entry) =>
          entry.type !== "meal-header" && entry.foodCategory !== "Water"
      );
      return acc;
    }, {});
  }, [mealSections, foodEntries, selectedDate]);

  // Calculate total daily nutrition consumption
  const totalDailyCaloriesAndMacrosConsumed = useMemo(() => {
    // console.log("[NUTRITION] Recalculating daily totals");

    // Flatten all relevant entries for the day
    const allEntries = sections.flatMap((section) => section.data);

    // Log all entry IDs for debugging
    // console.log(
    //   "[NUTRITION] All entries for calculation:",
    //   allEntries.map((e) => `${e.id} (${e.type})`)
    // );

    return calculateTotalCaloriesAndMacros(allEntries);
  }, [sections]);

  // Calculate calorie progress data
  const calorieData = useMemo(() => {
    const totalCalories = totalDailyCaloriesAndMacrosConsumed.calories || 0;
    const goal = calorieGoal || 1; // Prevent division by zero

    // Calculate progress percentages
    const foodConsumed = totalCalories / goal;
    const netProgress = (totalCalories - (caloriesBurned || 0)) / goal;
    const burnedProgress = (caloriesBurned || 0) / goal;

    // Calculate calories remaining (now allows negative values)
    const remaining = goal - totalCalories + (caloriesBurned || 0);

    return {
      calorieGoal: goal,
      totalCalories,
      caloriesRemaining: remaining, // Now can be negative
      isCalorieDeficit: remaining >= 0, // Flag for UI styling
      foodConsumedPercentage: isNaN(foodConsumed)
        ? 0
        : Math.min(foodConsumed, 1),
      netCalorieProgressPercentage: isNaN(netProgress)
        ? 0
        : Math.min(netProgress, 1),
      caloriesBurnedPercentage: isNaN(burnedProgress)
        ? 0
        : Math.min(burnedProgress, 1),
    };
  }, [totalDailyCaloriesAndMacrosConsumed, calorieGoal, caloriesBurned]);

  // Calculate macro nutrient progress data
  const macroData = useMemo(() => {
    const { carbs, protein, fat } = totalDailyCaloriesAndMacrosConsumed;

    // Return zero data if no calories consumed
    if (totalDailyCaloriesAndMacrosConsumed.calories <= 0) {
      return {
        carbsData: {
          percentage: 0,
          consumedGrams: 0,
          consumedColor: "#FFA500",
          remainingColor: "#FFD580",
          label: "Carbs",
          totalGramsGoal: macroGoals?.carb?.dailyGrams || 0,
          iconName: "corn",
        },
        proteinData: {
          percentage: 0,
          consumedGrams: 0,
          consumedColor: "#32CD32",
          remainingColor: "#8EDC8E",
          label: "Protein",
          totalGramsGoal: macroGoals?.protein?.dailyGrams || 0,
          iconName: "food-steak",
        },
        fatData: {
          percentage: 0,
          consumedGrams: 0,
          consumedColor: "#FF0000",
          remainingColor: "#FF9999",
          label: "Fat",
          totalGramsGoal: macroGoals?.fat?.dailyGrams || 0,
          iconName: "sausage",
        },
      };
    }

    // Calculate percentages with fallbacks
    const safeCarbsGoal = macroGoals?.carb?.dailyGrams || 1;
    const safeProteinGoal = macroGoals?.protein?.dailyGrams || 1;
    const safeFatGoal = macroGoals?.fat?.dailyGrams || 1;

    const carbsPercentage = carbs / safeCarbsGoal;
    const proteinPercentage = protein / safeProteinGoal;
    const fatPercentage = fat / safeFatGoal;

    return {
      carbsData: {
        percentage: isNaN(carbsPercentage) ? 0 : carbsPercentage,
        consumedColor: "#FFA500",
        remainingColor: "#FFD580",
        label: "Carbs",
        totalGramsGoal: macroGoals?.carb?.dailyGrams || 0,
        consumedGrams: carbs || 0,
        iconName: "corn",
      },
      proteinData: {
        percentage: isNaN(proteinPercentage) ? 0 : proteinPercentage,
        consumedColor: "#32CD32",
        remainingColor: "#8EDC8E",
        label: "Protein",
        totalGramsGoal: macroGoals?.protein?.dailyGrams || 0,
        consumedGrams: protein || 0,
        iconName: "food-steak",
      },
      fatData: {
        percentage: isNaN(fatPercentage) ? 0 : fatPercentage,
        consumedColor: "#FF0000",
        remainingColor: "#FF9999",
        label: "Fat",
        totalGramsGoal: macroGoals?.fat?.dailyGrams || 0,
        consumedGrams: fat || 0,
        iconName: "sausage",
      },
    };
  }, [
    totalDailyCaloriesAndMacrosConsumed,
    macroGoals,
    calorieGoal,
    caloriesBurned,
  ]);

  // Food History built using foodEntries state.
  const foodHistory = useMemo(() => {
    return buildFoodHistory(foodEntries);
  }, [foodEntries]);

  /* ================================= */
  /* ======== CONTEXT VALUE ========= */
  /* ================================= */
  const contextValue = useMemo(
    () => ({
      sections,
      setSections,
      refreshSectionList,
      setRefreshSectionList,
      updateSections,
      activeMealSection,
      setActiveMealSection,
      getActiveFoodItem,
      setActiveFoodItem,
      loadMealSectionCustomizations,
      foodHistory,
      loadFoodEntries,
      mealSections,
      setMealSections,
      foodEntries,
      setFoodEntries,
      updateMealSectionNames,
      saveMealToFoodLog,
      deleteFoodEntry,
      deleteMealSectionEntries,
      saveOrUpdateSingleFoodItemToFoodLog,
      copyEntriesBetweenMealSections,
      calculateTotalCaloriesAndMacros,
      filteredEntriesByMeal,
      totalDailyCaloriesAndMacrosConsumed,
      calorieData,
      macroData,
      addQuickFoodEntry,
      addWaterEntry,
      totalDailyWaterConsumed,
      totalDailyWaterConsumedPercentage,
      findMealNameById,
      formatTimestamp,
    }),
    [
      sections,
      setSections,
      refreshSectionList,
      setRefreshSectionList,
      updateSections,
      activeMealSection,
      setActiveMealSection,
      getActiveFoodItem,
      setActiveFoodItem,
      loadMealSectionCustomizations,
      foodHistory,
      loadFoodEntries,
      mealSections,
      setMealSections,
      foodEntries,
      setFoodEntries,
      updateMealSectionNames,
      saveMealToFoodLog,
      deleteFoodEntry,
      deleteMealSectionEntries,
      saveOrUpdateSingleFoodItemToFoodLog,
      copyEntriesBetweenMealSections,
      calculateTotalCaloriesAndMacros,
      filteredEntriesByMeal,
      totalDailyCaloriesAndMacrosConsumed,
      calorieData,
      macroData,
      addQuickFoodEntry,
      addWaterEntry,
      totalDailyWaterConsumed,
      totalDailyWaterConsumedPercentage,
      findMealNameById,
      formatTimestamp,
    ]
  );

  return (
    <FoodLogContext.Provider value={contextValue}>
      {children}
    </FoodLogContext.Provider>
  );
}
