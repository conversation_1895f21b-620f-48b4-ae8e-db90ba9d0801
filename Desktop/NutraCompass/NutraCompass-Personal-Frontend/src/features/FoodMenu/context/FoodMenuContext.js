import React, {
  useState,
  useEffect,
  useMemo,
  createContext,
  useContext,
  useCallback,
} from "react";
import axios from "axios"; // Preferred HTTP client for browser-based applications
import { useAuth } from "../../../authentication/context/AuthContext.js"; // Importing context for user authentication
import uuid from "react-native-uuid"; // UUID generator for client-side unique identifiers
import Configs from "../../../../configs.js";
import * as ImageManipulator from "expo-image-manipulator";
import * as FileSystem from "expo-file-system";

const CUSTOM_MEAL_PICTURE_CONFIG = {
  resize: { width: 1000, height: 1000 },
  compress: 0.75,
  format: ImageManipulator.SaveFormat.JPEG,
};

const FoodMenuContext = createContext();

export function useFoodMenu() {
  return useContext(FoodMenuContext);
}

export function FoodMenuProvider({ children }) {
  const { user } = useAuth();
  const userId = user?.uid;
  const apiUrl = Configs.NutraCompass_API_URL; // Base URL for API, should move to environment variables for production

  const [customMeals, setCustomMeals] = useState([]);
  const [tempCustomMeal, setTempCustomMeal] = useState({
    mealItems: [],
    foodLabel: "",
    mealImageUrl: "",
  });

  // useEffect(() => {
  //   const loadData = async () => {
  //     await loadCustomMeals();
  //   };

  //   if (user) {
  //     loadData();
  //   }
  // }, [user]);

  const transformMeal = (meal) => ({
    ...meal,
    mealImageUrl: meal.mealImageUrl
      ? `${meal.mealImageUrl}?timestamp=${new Date().getTime()}`
      : meal.mealImageUrl,
  });

  const deduplicateMeals = (meals) => {
    const seen = new Map();
    const result = [];

    // Process in reverse to keep the latest version
    for (let i = meals.length - 1; i >= 0; i--) {
      const meal = meals[i];
      if (!seen.has(meal.id)) {
        seen.set(meal.id, true);
        result.unshift(meal); // Add to beginning to maintain order
      }
    }

    return result;
  };

  const loadCustomMeals = useCallback(async () => {
    try {
      const response = await axios.get(
        `${apiUrl}/v1/food/menu/${userId}/custom-meals?t=${Date.now()}`
      );

      // Add deduplication step
      const deduplicated = deduplicateMeals(response.data || []);
      const updatedCustomMeals = deduplicated.map(transformMeal);

      setCustomMeals(updatedCustomMeals);
    } catch (error) {
      console.error("Error loading custom meals:", error);
    }
  }, [apiUrl, userId]);

  const saveOrUpdateCustomMeal = useCallback(
    async (customMeal) => {
      let tempFilesToCleanup = [];
      const isUpdate = !!customMeal.id;
      const tempId = `temp-${Date.now()}`;

      try {
        // Optimistic update
        setCustomMeals((prev) => {
          const newMeal = transformMeal({
            ...customMeal,
            id: customMeal.id || tempId,
            isOptimistic: true,
          });

          // For updates, filter out ALL versions
          return isUpdate
            ? [...prev.filter((meal) => meal.id !== customMeal.id), newMeal]
            : [...prev, newMeal];
        });

        const url = `${apiUrl}/v1/food/menu/${userId}/custom-meals`;
        const formData = new FormData();

        // Always include the ID field if it exists
        if (customMeal.id) {
          formData.append("id", customMeal.id);
        }

        // Serialize and append mealItems as a JSON string if it exists
        if (customMeal.mealItems) {
          formData.append("mealItems", JSON.stringify(customMeal.mealItems));
        }

        // Serialize and append nutrients as a JSON string if it exists
        if (
          customMeal.nutrients &&
          Object.keys(customMeal.nutrients).length > 0
        ) {
          formData.append("nutrients", JSON.stringify(customMeal.nutrients));
        }

        // Handle image processing and inclusion
        if (customMeal.mealImageUrl) {
          if (
            customMeal.mealImageUrl.startsWith("file://") ||
            customMeal.mealImageUrl.startsWith("content://")
          ) {
            // Process and upload new image
            const processedImage = await processAndPrepareImage(
              customMeal.mealImageUrl
            );
            tempFilesToCleanup.push(...processedImage.tempFiles);
            formData.append("mealImage", {
              uri: processedImage.uri,
              type: processedImage.type,
              name: processedImage.name,
            });
          } else if (customMeal.mealImageUrl.startsWith("http")) {
            // Preserve existing remote image URL
            formData.append("existingImageUrl", customMeal.mealImageUrl);
          }
        }

        // Append other regular fields
        Object.keys(customMeal).forEach((key) => {
          if (
            key !== "id" &&
            key !== "mealItems" &&
            key !== "nutrients" &&
            key !== "mealImageUrl"
          ) {
            formData.append(key, customMeal[key]);
          }
        });

        const response = await axios.post(url, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });

        // 3. Update state using ONLY server-provided ID
        setCustomMeals((prev) => {
          const confirmedMeal = transformMeal(response.data);

          return [
            ...prev.filter(
              (meal) =>
                meal.id !== customMeal.id &&
                meal.id !== tempId &&
                meal.id !== response.data.id
            ),
            confirmedMeal,
          ];
        });

        clearTempCustomMeal();
        return response.data;
      } catch (error) {
        // Enhanced error recovery
        setCustomMeals((prev) =>
          prev.filter(
            (meal) =>
              meal.id !== (customMeal.id || tempId) || !meal.isOptimistic
          )
        );

        console.error("Save/update failed:", error);
        throw error;
      } finally {
        // Cleanup temporary files
        await Promise.all(
          tempFilesToCleanup.map(async (uri) => {
            try {
              if (await FileSystem.getInfoAsync(uri)) {
                await FileSystem.deleteAsync(uri);
              }
            } catch (cleanupError) {
              console.warn("Failed to cleanup file:", uri, cleanupError);
            }
          })
        );
      }
    },
    [apiUrl, userId, clearTempCustomMeal]
  );

  const deleteCustomMeal = async (mealId) => {
    let rollbackMeal = null;

    setCustomMeals((prev) => {
      rollbackMeal = prev.find((meal) => meal.id === mealId);
      return prev.filter((meal) => meal.id !== mealId);
    });

    try {
      await axios.delete(
        `${apiUrl}/v1/food/menu/${userId}/custom-meals/${mealId}`
      );
    } catch (error) {
      if (rollbackMeal) {
        setCustomMeals((prev) => [...prev, rollbackMeal]);
      }
      console.error("Error deleting custom meal:", error);
    }
  };

  // Temp Data Management
  const setTempCustomMealName = (newMealName) => {
    setTempCustomMeal({ ...tempCustomMeal, foodLabel: newMealName });
  };

  const saveFoodToTempCustomMeal = (selectedFoodItem) => {
    const itemExists = tempCustomMeal.mealItems.some(
      (item) => item.foodId === selectedFoodItem.foodId
    );
    if (itemExists) {
      setTempCustomMeal((prevMeal) => {
        const updatedMealItems = prevMeal.mealItems.map((item) =>
          item.foodId === selectedFoodItem.foodId
            ? { ...selectedFoodItem, foodId: item.foodId }
            : item
        );
        return { ...prevMeal, mealItems: updatedMealItems };
      });
    } else {
      setTempCustomMeal((prevMeal) => ({
        ...prevMeal,
        mealItems: [
          ...prevMeal.mealItems,
          { ...selectedFoodItem, foodId: selectedFoodItem.foodId || uuid.v4() },
        ],
      }));
    }
  };

  const addLoggedFoodItemsToTempCustomMeal = (foodItems) => {
    const formattedItems = foodItems.map((item) => ({
      foodId: item.foodId,
      foodLabel: item.foodLabel,
      foodCategory: item?.foodCategory || "",
      foodBrand: item?.foodBrand || "",
      numberOfServings: item.numberOfServings,
      activeMeasure: { ...item.activeMeasure },
      nutrients: { ...item.nutrients },
      measures: item.measures,
    }));

    setTempCustomMeal((prev) => ({
      ...prev,
      mealItems: [...prev.mealItems, ...formattedItems],
    }));
  };

  const deleteFoodFromTempCustomMeal = (index) => {
    setTempCustomMeal((prevMeal) => {
      const updatedMealItems = [...prevMeal.mealItems];
      updatedMealItems.splice(index, 1);
      return { ...prevMeal, mealItems: updatedMealItems };
    });
  };

  const clearTempCustomMeal = () => {
    setTempCustomMeal({ mealItems: [], foodLabel: "", mealImageUrl: "" });
  };

  const processAndPrepareImage = async (imageUri) => {
    const tempFiles = [];

    try {
      // 1. Convert content:// URI to file:// if needed
      let processedUri = imageUri;
      if (imageUri.startsWith("content://")) {
        const tempPath = `${FileSystem.cacheDirectory}${uuid.v4()}.jpg`;
        await FileSystem.copyAsync({ from: imageUri, to: tempPath });
        processedUri = tempPath;
        tempFiles.push(tempPath);
      }

      // 2. Process image with ImageManipulator
      const manipResult = await ImageManipulator.manipulateAsync(
        processedUri,
        [
          {
            resize: CUSTOM_MEAL_PICTURE_CONFIG.resize,
          },
        ],
        {
          compress: CUSTOM_MEAL_PICTURE_CONFIG.compress,
          format: CUSTOM_MEAL_PICTURE_CONFIG.format,
        }
      );

      tempFiles.push(manipResult.uri);

      return {
        uri: manipResult.uri,
        name: `mealImage.jpg`,
        type: "image/jpeg",
        tempFiles, // Return files to track for cleanup
      };
    } catch (error) {
      // Cleanup if processing fails
      await Promise.all(
        tempFiles.map((file) => FileSystem.deleteAsync(file).catch(() => {}))
      );
      console.error("Image processing failed:", error);
      throw error;
    }
  };

  const contextValue = useMemo(
    () => ({
      customMeals,
      setCustomMeals,
      tempCustomMeal,
      setTempCustomMeal,
      loadCustomMeals,
      saveOrUpdateCustomMeal,
      deleteCustomMeal,
      setTempCustomMealName,
      saveFoodToTempCustomMeal,
      deleteFoodFromTempCustomMeal,
      clearTempCustomMeal,
      addLoggedFoodItemsToTempCustomMeal,
    }),
    [
      customMeals,
      setCustomMeals,
      tempCustomMeal,
      setTempCustomMeal,
      loadCustomMeals,
      saveOrUpdateCustomMeal,
      deleteCustomMeal,
      setTempCustomMealName,
      saveFoodToTempCustomMeal,
      deleteFoodFromTempCustomMeal,
      clearTempCustomMeal,
      addLoggedFoodItemsToTempCustomMeal,
    ]
  );

  return (
    <FoodMenuContext.Provider value={contextValue}>
      {children}
    </FoodMenuContext.Provider>
  );
}
