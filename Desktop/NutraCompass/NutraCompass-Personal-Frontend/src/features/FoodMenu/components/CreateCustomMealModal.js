// CreateCustomMealModal.js
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  FlatList,
  Alert,
} from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { Button, TextInput, TouchableRipple } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { Ionicons, Feather } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodMenu } from "../context/FoodMenuContext.js";
import * as ImagePicker from "expo-image-picker";
import { CustomImageUploadAlert } from "../../../components/CustomImageUploadAlert.js";
import { useFoodLog } from "../../FoodDiary/context/FoodLogContext.js";
import { useModal, MODAL_TYPES } from "../../../context/ModalContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";
export default function CreateCustomMealModal({
  isVisible, // From modal context
  closeModal, // From modal context
  onClose,
}) {
  const { openModal } = useModal();
  const { theme } = useThemeContext();
  const {
    deleteFoodFromTempCustomMeal,
    tempCustomMeal,
    setTempCustomMeal,
    setTempCustomMealName,
  } = useFoodMenu();
  const { setActiveFoodItem, getActiveFoodItem } = useFoodLog();

  const [activeCustomMeal, setActiveCustomMeal] = useState(null);
  const [showCustomImageUploadAlert, setShowCustomImageUploadAlert] =
    useState(false);

  useEffect(() => {
    // Update custom meal total nutrients when tempCustomMeal changes
    updateActiveCustomMeal();
  }, [tempCustomMeal]);

  const updateActiveCustomMeal = () => {
    if (tempCustomMeal.mealItems.length > 0) {
      // Initialize active custom meal object
      const activeCustomMealObject = {
        id: tempCustomMeal?.id || "",
        foodLabel: tempCustomMeal?.foodLabel,
        foodCategory: "Custom Meal",
        isCustomMeal: true,
        numberOfServings: 1,
        mealImageUrl: tempCustomMeal?.mealImageUrl || "",
        mealItems: [],
        nutrients: { core: {}, vitamins: {}, minerals: {} },
      };

      // Process each meal item
      tempCustomMeal.mealItems.forEach((item) => {
        // Add meal item to collection
        activeCustomMealObject.mealItems.push({
          foodId: item.foodId,
          foodLabel: item.foodLabel,
          foodCategory: item?.foodCategory || "",
          foodBrand: item?.foodBrand || "",
          numberOfServings: item.numberOfServings,
          activeMeasure: { ...item.activeMeasure },
          nutrients: { ...item.nutrients },
          measures: item.measures,
        });

        // Process nutrients using centralized function
        processNutrients(activeCustomMealObject, item);
      });

      // Set the final processed meal object
      setActiveCustomMeal(activeCustomMealObject);
    } else {
      setActiveCustomMeal(null);
    }
  };

  const _roundToTwo = (num) => {
    return Math.round((Number(num) + Number.EPSILON) * 100) / 100;
  };

  const _safeParse = (value) => {
    if (value === null || value === undefined) return null;
    const num = parseFloat(value);
    return isNaN(num) ? null : _roundToTwo(num);
  };

  const _processValue = (value, processor) => {
    const parsed = _safeParse(value);
    return parsed !== null ? processor(parsed) : null;
  };

  const processNutrients = (activeCustomMealObject, item) => {
    // Iterate over nutrient categories
    ["core", "vitamins", "minerals"].forEach((categoryKey) => {
      if (!item.nutrients[categoryKey]) return;

      // Initialize category if needed
      activeCustomMealObject.nutrients[categoryKey] =
        activeCustomMealObject.nutrients[categoryKey] || {};

      // Process each nutrient in category
      Object.entries(item.nutrients[categoryKey]).forEach(
        ([nutrientKey, nutrient]) => {
          const current =
            activeCustomMealObject.nutrients[categoryKey][nutrientKey];
          const baseQuantity = _safeParse(nutrient.quantity) || 0;
          const baseDaily =
            _processValue(nutrient.totalDailyQuantity, (v) => v) || 0;

          if (current) {
            // Update existing nutrient with safe values
            const currentQty = _safeParse(current.quantity) || 0;
            const currentDaily = _safeParse(current.totalDailyQuantity) || 0;

            // Calculate new values
            const newQuantity = _roundToTwo(currentQty + baseQuantity);
            const newDaily =
              baseQuantity === 0 ? 0 : _roundToTwo(currentDaily + baseDaily);

            // Apply updates
            current.quantity = newQuantity;
            current.totalDailyQuantity = newDaily;
          } else {
            // Create new nutrient entry with processed values
            activeCustomMealObject.nutrients[categoryKey][nutrientKey] = {
              label: nutrient.label,
              quantity: baseQuantity,
              totalDailyQuantity: baseQuantity === 0 ? 0 : baseDaily,
              unit: nutrient.unit,
              totalDailyUnit: nutrient.totalDailyUnit,
            };
          }
        }
      );
    });

    return activeCustomMealObject;
  };

  const handleAddFoodToMeal = () => {
    openModal(MODAL_TYPES.FOOD_ENTRY, {
      isBuildingMeal: true,
      onCancel: () => {
        return;
      },
    });
  };

  const handleOpenFoodNutrientModal = (activeItem) => {
    if (!activeItem || Object.keys(activeItem).length === 0) {
      console.error("Invalid food item provided to nutrient modal");
      return;
    }

    // Use context method to set active item
    setActiveFoodItem(activeItem);

    openModal(MODAL_TYPES.FOOD_NUTRIENT, {
      foodNutrientModalType: getFoodNutrientModalTitle(),
      isBuildingMeal: true,
    });
  };

  const handleDeleteFoodFromTempMeal = (index) => {
    // Delete the selected food item from the temp meal
    deleteFoodFromTempCustomMeal(index);
  };

  const takePhoto = async () => {
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    // Using the "assets" array to access the selected image
    if (!result.canceled && result.assets) {
      const imageUri = result.assets[0].uri; // Accessing the first selected image URI
      setTempCustomMeal((currentMeal) => ({
        ...currentMeal,
        mealImageUrl: imageUri,
      }));
    }

    setShowCustomImageUploadAlert(false);
  };

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    // Using the "assets" array to access the selected image
    if (!result.canceled && result.assets) {
      const imageUri = result.assets[0].uri; // Accessing the first selected image URI
      setTempCustomMeal((currentMeal) => ({
        ...currentMeal,
        mealImageUrl: imageUri,
      }));
    }

    setShowCustomImageUploadAlert(false);
  };

  const handleDeleteImageConfirmation = () => {
    // Check if there is an image to delete
    if (tempCustomMeal.mealImageUrl) {
      Alert.alert(
        "Delete Image",
        "Are you sure you want to delete this image?",
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          {
            text: "Delete",
            onPress: () => handleDeleteImage(),
            style: "destructive",
          },
        ]
      );
    }
  };

  const handleDeleteImage = () => {
    // Delete the image from tempCustomMeal state
    setTempCustomMeal((currentMeal) => ({
      ...currentMeal,
      mealImageUrl: "",
    }));

    setShowCustomImageUploadAlert(false);
  };

  const handleNextButtonPress = () => {
    if (!tempCustomMeal.foodLabel || tempCustomMeal.mealItems.length === 0) {
      // Check if tempCustomMeal.mealName is not provided
      // Display an alert with the required message
      Alert.alert(
        "Enter Details",
        "Please input a meal name and a meal item.",
        [{ text: "OK", onPress: () => console.log("OK pressed") }] // Add an "OK" button to close the alert
      );
    } else {
      // Show the total nutrient information when the "Next" button is clicked
      handleOpenFoodNutrientModal(activeCustomMeal);
    }
  };

  const handlePickImage = pickImage;
  const handleTakePhoto = takePhoto;

  const getFoodNutrientModalTitle = () => {
    const currentItem = getActiveFoodItem(); // Use the context getter
    if (currentItem?.isCustomMeal === true && currentItem?.id) {
      return "Edit Meal";
    } else if (currentItem?.isCustomMeal === true) {
      return "Create Meal";
    } else if (currentItem?.foodId) {
      return "Edit Meal Item";
    }
    return "Food Nutrients"; // Default fallback
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
      }}
    >
      <TouchableOpacity
        style={{ flex: 1 }}
        onPress={Keyboard.dismiss}
        activeOpacity={1}
      >
        {/* Header */}
        <LinearGradient
          style={{
            height: "12%",
            justifyContent: "flex-end",
            borderBottomLeftRadius: scaleSize(24),
            borderBottomRightRadius: scaleSize(24),
            borderColor: theme.colors.sectionBorderColor,
            borderLeftWidth: scaleSize(1),
            borderRightWidth: scaleSize(1),
            borderBottomWidth: scaleSize(1),
            backgroundColor: theme.colors.screenBackground,
            elevation: 4,
          }}
          colors={[
            `${theme.colors.primary}99`, // Adding "99" for 0.99 opacity
            `${theme.colors.secondary}99`, // Adding "99" for 0.99 opacity
          ]}
          start={{ x: 0, y: 1.5 }} // Top left corner
          end={{ x: 1, y: 2 }} // Bottom right corner
        >
          <View>
            <View
              style={{ paddingHorizontal: "5%", paddingVertical: scaleSize(5) }}
            >
              <View
                style={{
                  height: "100%",
                  justifyContent: "center",
                  alignItems: "flex-end",
                  flexDirection: "row",
                }}
              >
                <TouchableOpacity
                  style={{
                    justifyContent: "center",
                    alignItems: "center",
                    position: "absolute",
                    left: 0,
                  }}
                  onPress={() => {
                    onClose?.();
                    closeModal();
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                >
                  <Feather
                    name="x"
                    color={theme.colors.primaryTextColor}
                    size={scaleSize(28)}
                  />
                </TouchableOpacity>

                <Text
                  style={{
                    fontSize: scaleSize(28),
                    fontWeight: "500",
                    color: theme.colors.primaryTextColor,
                  }}
                >
                  {tempCustomMeal?.id ? "Edit Meal" : "Create Meal"}
                </Text>
              </View>
            </View>
          </View>
        </LinearGradient>
        {/* Body */}
        <View
          style={{
            backgroundColor: theme.colors.screenBackground,
            flex: 1,
            padding: scaleSize(10),
            gap: scaleSize(8),
          }}
        >
          <TextInput
            mode="outlined"
            label="Meal Name (Required)"
            value={tempCustomMeal?.foodLabel}
            onChangeText={(mealName) => setTempCustomMealName(mealName)}
            outlineStyle={{ borderRadius: scaleSize(12) }}
            style={{ fontSize: scaleSize(16), paddingTop: scaleSize(12) }}
          />

          <TouchableRipple
            onPress={() => setShowCustomImageUploadAlert(true)}
            onLongPress={handleDeleteImageConfirmation}
            style={{
              width: "100%",
              alignItems: "center",
              marginVertical: scaleSize(10),
              borderLeftWidth: scaleSize(1),
              borderRightWidth: scaleSize(1),
              borderColor: theme.colors.cardBorderColor,
              backgroundColor: theme.colors.surface,
            }}
          >
            {tempCustomMeal?.mealImageUrl ? (
              <Image
                source={{ uri: tempCustomMeal?.mealImageUrl }}
                style={{
                  width: scaleSize(150),
                  height: scaleSize(150),
                  borderRadius: scaleSize(10),
                  borderWidth: scaleSize(1),
                }}
              />
            ) : (
              <View
                style={{
                  width: "100%",
                  height: scaleSize(150),
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text
                  style={{
                    color: theme.colors.primary,
                    fontSize: scaleSize(16),
                  }}
                >
                  Upload an image for your custom meal
                </Text>
              </View>
            )}
          </TouchableRipple>

          <View
            style={{
              flex: 1,
              backgroundColor: theme.colors.surface,
              borderRadius: scaleSize(12),
              borderWidth: scaleSize(1),
              borderColor: theme.colors.cardBorderColor,
            }}
          >
            {/* Button to open FoodEntryModal */}
            <TouchableOpacity
              onPress={handleAddFoodToMeal}
              style={{
                borderBottomWidth: scaleSize(1),
                borderColor: theme.colors.cardBorderColor,
                borderRadius: 0,
                padding: scaleSize(4),
                width: "100%",
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "transparent",
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  paddingVertical: scaleSize(6),
                }}
              >
                <Ionicons
                  name="add"
                  size={scaleSize(20)}
                  color={theme.colors.primary}
                  style={{ marginRight: scaleSize(8) }}
                />
                <Text
                  style={{
                    color: theme.colors.primary,
                    fontSize: scaleSize(14),
                    fontWeight: "500",
                  }}
                >
                  ADD MEAL ITEM
                </Text>
              </View>
            </TouchableOpacity>
            {/** Display Food Items In Custom Meal */}
            <FlatList
              style={{ flex: 1 }}
              data={tempCustomMeal.mealItems} // Use the mealItems from tempCustomMeal
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item, index }) => (
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    padding: scaleSize(10),
                    borderBottomWidth: scaleSize(1),
                    borderBottomColor: theme.colors.cardBorderColor,
                  }}
                  onPress={() => {
                    if (item.foodCategory === "Quick Add") {
                      console.log(
                        "Item is a Quick Add, handle this entry differently."
                      );
                    } else {
                      handleOpenFoodNutrientModal(item); // Pass item directly instead of activeItem
                    }
                  }}
                >
                  <View style={{ gap: scaleSize(5) }}>
                    <Text
                      style={{
                        fontSize: scaleSize(16),
                        color: theme.colors.primaryTextColor,
                        maxWidth: "90%",
                      }}
                    >
                      {item.foodCategory === "Quick Add" && "Quick Add - "}
                      {item.foodLabel}
                    </Text>
                    {item.foodCategory === "Quick Add" ? (
                      <Text
                        style={{
                          fontSize: scaleSize(14),
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {Math.round(item.nutrients.core.ENERC_KCAL?.quantity)}{" "}
                        cal
                      </Text>
                    ) : (
                      <Text
                        style={{
                          fontSize: scaleSize(14),
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {Math.round(item.nutrients.core.ENERC_KCAL.quantity)}{" "}
                        cal, {Math.round(item.numberOfServings)}{" "}
                        {item.activeMeasure.label}
                      </Text>
                    )}
                  </View>

                  <TouchableOpacity
                    style={{
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    onPress={() => handleDeleteFoodFromTempMeal(index)}
                  >
                    <Feather
                      style={{ padding: scaleSize(10) }}
                      name="x"
                      size={scaleSize(18)}
                      color={theme.colors.primaryTextColor}
                    />
                  </TouchableOpacity>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
        <View
          style={{
            height: "10%",
            padding: scaleSize(10),
            flexDirection: "row",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          <TouchableOpacity
            style={{
              width: "30%",
              backgroundColor: theme.colors.primary,
              alignItems: "center",
              padding: scaleSize(12),
              borderRadius: scaleSize(12),
            }}
            onPress={handleNextButtonPress}
          >
            <Text
              style={{
                color: theme.colors.onPrimary,
                fontSize: scaleSize(14),
              }}
            >
              NEXT
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
      <CustomImageUploadAlert
        isVisible={showCustomImageUploadAlert}
        onClose={() => setShowCustomImageUploadAlert(false)}
        onPickImage={handlePickImage}
        onTakePhoto={handleTakePhoto}
        onRemoveImage={handleDeleteImageConfirmation}
        hasImage={!!tempCustomMeal?.mealImageUrl}
      />
    </View>
  );
}
