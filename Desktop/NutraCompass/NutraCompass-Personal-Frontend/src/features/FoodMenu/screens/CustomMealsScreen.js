import React, { useState, useMemo, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Keyboard,
  TextInput,
} from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import {
  Searchbar,
  IconButton,
  Dialog,
  Portal,
  Button,
} from "react-native-paper";
import * as Haptics from "expo-haptics";
import { Feather, Ionicons } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFoodMenu } from "../context/FoodMenuContext.js";
import { useModal, MODAL_TYPES } from "../../../context/ModalContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

export default function CustomMealsScreen({ navigation }) {
  const { openModal } = useModal();
  const { theme } = useThemeContext();
  const {
    customMeals,
    deleteCustomMeal,
    setTempCustomMeal,
    clearTempCustomMeal,
  } = useFoodMenu();
  const [searchQuery, setSearchQuery] = useState("");
  const onChangeSearch = (query) => setSearchQuery(query.toLowerCase());

  // Use useMemo to avoid unnecessary recalculations
  const sortedMeals = useMemo(() => {
    if (!searchQuery) {
      return customMeals;
    }
    return [...customMeals].sort((a, b) => {
      const nameA = a.foodLabel.toLowerCase();
      const nameB = b.foodLabel.toLowerCase();
      const query = searchQuery.toLowerCase();

      const matchA = nameA.includes(query);
      const matchB = nameB.includes(query);

      if (matchA && !matchB) {
        return -1;
      } else if (!matchA && matchB) {
        return 1;
      } else {
        return 0;
      }
    });
  }, [customMeals, searchQuery]);

  const [showDeleteCustomMealDialog, setShowDeleteCustomMealDialog] =
    useState(false);
  const [customMealToDelete, setCustomMealToDelete] = useState("");

  const handleDeleteCustomMealConfirm = async () => {
    try {
      await deleteCustomMeal(customMealToDelete);
      setShowDeleteCustomMealDialog(false);
      setCustomMealToDelete("");
    } catch (error) {
      console.log("Error deleting custom meal.");
    }
  };

  const handleOpenDeleteCustomMealDialog = (mealId) => {
    setCustomMealToDelete(mealId);
    setShowDeleteCustomMealDialog(true);
  };

  const handleDeleteCustomMealCancel = () => {
    setShowDeleteCustomMealDialog(false);
    setCustomMealToDelete("");
  };

  const renderMealItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        setTempCustomMeal(item);
        openModal(MODAL_TYPES.CREATE_CUSTOM_MEAL, {
          onClose: () => {
            clearTempCustomMeal();
          },
        });
      }}
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: theme.colors.surface,
        borderWidth: scaleSize(1),
        borderColor: theme.colors.cardBorderColor,
        padding: scaleSize(4),
        marginVertical: scaleSize(5),
        borderRadius: scaleSize(60),
      }}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: scaleSize(10),
        }}
      >
        <View
          style={{
            width: scaleSize(100), // Increased from 60 to 80
            height: scaleSize(100), // Increased from 60 to 80
            borderRadius: scaleSize(100) / 2, // Half of width/height for perfect circle
            backgroundColor: theme.colors.surface,
            alignItems: "center",
            justifyContent: "center",
            overflow: "hidden", // Ensures image stays within circular bounds
          }}
        >
          {item?.mealImageUrl ? (
            <Image
              source={{ uri: item.mealImageUrl }}
              contentFit="cover"
              style={{
                width: "100%", // Fill container width
                height: "100%", // Fill container height
              }}
            />
          ) : (
            <Ionicons
              name="image-outline"
              size={scaleSize(32)} // Increased icon size proportionally
              color={theme.colors.subTextColor}
            />
          )}
        </View>

        <View style={{ gap: scaleSize(2) }}>
          <Text
            style={{
              fontSize: scaleSize(14),
              color: theme.colors.primaryTextColor,
            }}
          >
            {item.foodLabel}
          </Text>
          <Text
            style={{
              fontSize: scaleSize(12),
              color: theme.colors.subTextColor,
            }}
          >
            {Math.round(
              parseFloat(item?.nutrients?.core?.ENERC_KCAL?.quantity)
            )}{" "}
            cal
          </Text>
        </View>
      </View>

      <IconButton
        onPress={() => handleOpenDeleteCustomMealDialog(item.id)}
        icon="trash-can-outline"
        iconColor={"red"}
        size={scaleSize(18)}
      />
    </TouchableOpacity>
  );

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={Keyboard.dismiss}
      style={{
        flex: 1,
        paddingBottom: "20%",
        backgroundColor: theme.colors.screenBackground,
        paddingHorizontal: scaleSize(8),
      }}
    >
      <LinearGradient
        style={{
          height: "8%",
          justifyContent: "center",
          backgroundColor: theme.colors.surface,
          borderRadius: scaleSize(10),
          paddingVertical: scaleSize(4),
          paddingHorizontal: scaleSize(4),
        }}
        colors={[`${theme.colors.surface}99`, `${theme.colors.surface}99`]}
        start={{ x: 0, y: 1.5 }}
        end={{ x: 1, y: 2 }}
      >
        <View>
          <View style={{ paddingHorizontal: "4%" }}>
            <View
              style={{
                height: "100%",
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "row",
              }}
            >
              <TouchableOpacity
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                  position: "absolute",
                  left: 0,
                }}
                onPress={() => {
                  navigation.navigate("Food Options");
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <Feather
                  name="chevron-left"
                  color={theme.colors.primaryTextColor}
                  size={scaleSize(24)}
                />
              </TouchableOpacity>

              <Text
                style={{
                  fontSize: scaleSize(20),
                  fontWeight: "500",
                  color: theme.colors.primaryTextColor,
                }}
              >
                Custom Meals
              </Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      <View style={{ flex: 1 }}>
        <View
          style={{
            flexDirection: "row",
            padding: scaleSize(8),
            gap: scaleSize(8),
          }}
        >
          <Searchbar
            placeholder="Search Meals"
            value={searchQuery}
            onChangeText={onChangeSearch}
            onSubmitEditing={onChangeSearch}
            placeholderTextColor={theme.colors.subTextColor}
            icon={() => (
              <TouchableOpacity
                disabled={true}
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                  width: scaleSize(40),
                  height: scaleSize(40),
                }}
              >
                <Ionicons
                  name="search-outline"
                  size={scaleSize(20)}
                  color={theme.colors.primaryTextColor}
                />
              </TouchableOpacity>
            )}
            style={{
              flex: 2,
              height: scaleSize(40),
              borderRadius: scaleSize(60),
              backgroundColor: theme.colors.surface,
              elevation: 0, // Disable shadow/elevation
            }}
            inputStyle={{
              fontSize: scaleSize(14),
              color: theme.colors.primaryTextColor,
              alignSelf: "center", // Center text vertically
            }}
          />

          <View
            style={{
              flex: 1,
              flexDirection: "row",
              alignItems: "center",
              gap: scaleSize(4),
            }}
          >
            <TouchableOpacity
              onPress={() => {
                openModal(MODAL_TYPES.CREATE_CUSTOM_MEAL, {
                  onClose: () => {
                    clearTempCustomMeal();
                  },
                });
              }}
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: theme.colors.primary,
                padding: scaleSize(8),
                borderRadius: scaleSize(60),
              }}
            >
              <Ionicons
                name="add"
                size={scaleSize(18)}
                color={theme.colors.primaryTextColor}
              />
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(12),
                  fontWeight: "500",
                }}
              >
                Create Meal
              </Text>
            </TouchableOpacity>

            <Ionicons
              name="filter-outline"
              size={scaleSize(24)}
              color={theme.colors.primaryTextColor}
            />
          </View>
        </View>

        <View style={{ flex: 1, padding: scaleSize(4) }}>
          <FlatList
            data={sortedMeals}
            renderItem={renderMealItem}
            keyExtractor={(item) => `${item.id}-${item.updatedAt || ""}`}
            style={{ flex: 1 }}
            contentContainerStyle={{ paddingBottom: scaleSize(150) }}
          />
        </View>
      </View>

      <Portal>
        <Dialog
          visible={showDeleteCustomMealDialog}
          onDismiss={handleDeleteCustomMealCancel}
        >
          <Dialog.Title
            style={{
              fontSize: scaleSize(16),
              color: theme.colors.primaryTextColor,
            }}
          >
            Delete Custom Meal
          </Dialog.Title>
          <Dialog.Content>
            <Text
              style={{
                fontSize: scaleSize(14),
                color: theme.colors.primaryTextColor,
              }}
            >
              Are you sure you want to delete this custom meal?
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                gap: scaleSize(10), // Add spacing between buttons
              }}
            >
              <TouchableOpacity
                onPress={handleDeleteCustomMealCancel}
                style={{
                  paddingHorizontal: scaleSize(12),
                  paddingVertical: scaleSize(8),
                  borderRadius: 4, // Rounded corners
                  backgroundColor: "transparent", // Or your theme's surface color
                  alignItems: "center", // Center horizontally
                  justifyContent: "center", // Center vertically
                }}
              >
                <Text
                  style={{
                    fontSize: scaleSize(14),
                    color: theme.colors.primaryTextColor,
                  }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleDeleteCustomMealConfirm}
                style={{
                  paddingHorizontal: scaleSize(12),
                  paddingVertical: scaleSize(8),
                  borderRadius: 4,
                  backgroundColor: "transparent",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text style={{ fontSize: scaleSize(14), color: "red" }}>
                  Delete
                </Text>
              </TouchableOpacity>
            </View>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </TouchableOpacity>
  );
}
