// NutritionProgramContext.js
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
import axios from "axios";
import * as ImageManipulator from "expo-image-manipulator";
import { useAuth } from "../../../authentication/context/AuthContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import Configs from "../../../../configs.js";

const PROGRESS_PICTURE_CONFIG = {
  resize: { width: 1000, height: 1000 },
  compress: 0.75,
  format: ImageManipulator.SaveFormat.JPEG,
};

const NutritionProgramContext = createContext();

export function useNutritionProgram() {
  return useContext(NutritionProgramContext);
}

export function NutritionProgramProvider({ children }) {
  const { user } = useAuth();
  const { getNutritionalGoals } = useUserSettings();
  const { calorieGoal, macroGoals } = getNutritionalGoals();
  const { getSelectedDateAsDate } = useTime();
  const userId = user?.uid;

  const apiUrl = Configs.NutraCompass_API_URL;

  const [activeNutritionalProgram, setActiveNutritionalProgram] =
    useState(null);
  const [inactiveNutritionalPrograms, setInactiveNutritionalPrograms] =
    useState([]);
  const [generatedProgram, setGeneratedProgram] = useState(null);
  const [generatedNutritionalGoals, setGeneratedNutritionalGoals] =
    useState(null);
  const [actionState, setActionState] = useState(null);
  const [error, setError] = useState(null);
  const [
    isNutritionalGoalsAlignmentNeeded,
    setIsNutritionalGoalsAlignmentNeeded,
  ] = useState(false);
  // Program lifecycle flags
  const [programStarted, setProgramStarted] = useState(false);
  // Track whether we have already shown a “Congratulations” message
  const [hasShownCongrats, setHasShownCongrats] = useState(false);

  // =========================================================================
  // 1) CHECK IF TODAY'S DATE IS WITHIN PROGRAM TIMELINE
  // =========================================================================
  // useEffect(() => {
  //   if (activeNutritionalProgram) {
  //     const now = getSelectedDateAsDate();
  //     const programStartDate = new Date(activeNutritionalProgram.startDate);
  //     const programEndDate = new Date(activeNutritionalProgram.endDate);

  //     // If now is past the end date, automatically deactivate the program
  //     if (now > programEndDate) {
  //       handleProgramDeactivation();
  //     } else {
  //       setProgramStarted(now >= programStartDate);
  //     }
  //   }
  // }, [activeNutritionalProgram, getSelectedDateAsDate]);
  useEffect(() => {
    if (activeNutritionalProgram) {
      // Always use the REAL current date for expiration checks
      const realCurrentDate = new Date();
      const programEndDate = new Date(activeNutritionalProgram.endDate);

      // Only use selectedDate for program start checks
      const selectedDateObj = getSelectedDateAsDate();
      const programStartDate = new Date(activeNutritionalProgram.startDate);

      // 1. Are we past the end date in REAL TIME?
      if (realCurrentDate > programEndDate) {
        // 2. Check if every checkpoint has submittedWeight
        const allCheckpointsSubmitted =
          activeNutritionalProgram.checkpoints?.every(
            (cp) => cp.submittedWeight != null
          ) || false;

        // 3. If all are submitted & we haven't shown congrats yet, do so
        if (allCheckpointsSubmitted && !hasShownCongrats) {
          setHasShownCongrats(true);

          // Optional: Wait a few seconds, then deactivate
          setTimeout(() => {
            deactivateNutritionalProgram(activeNutritionalProgram.programId);
          }, 3000);
        } else {
          // If not all submitted (or we've shown congrats), just deactivate
          deactivateNutritionalProgram(activeNutritionalProgram.programId);
        }
      } else {
        // Program hasn't ended yet—check if it's started for SELECTED DATE
        setProgramStarted(selectedDateObj >= programStartDate);
      }
    }
  }, [
    activeNutritionalProgram,
    getSelectedDateAsDate,
    hasShownCongrats,
    setProgramStarted,
    setHasShownCongrats,
    deactivateNutritionalProgram,
  ]);

  // =========================================================================
  // 1) CHECK FOR ALIGNMENT BETWEEN ACTIVE PROGRAM AND CURRENT USER GOALS
  // =========================================================================
  useEffect(() => {
    if (
      !activeNutritionalProgram ||
      !activeNutritionalProgram.nutritionalGoals ||
      !activeNutritionalProgram.nutritionalGoals.macroGoals
    ) {
      setIsNutritionalGoalsAlignmentNeeded(false);
      return;
    }

    const currentDate = getSelectedDateAsDate();
    const programStartDate = new Date(activeNutritionalProgram.startDate);
    const programEndDate = new Date(activeNutritionalProgram.endDate);

    const isWithinProgramTimeline =
      currentDate >= programStartDate && currentDate <= programEndDate;

    const goalsAreDifferent =
      calorieGoal !== activeNutritionalProgram.nutritionalGoals.calorieGoal ||
      Math.round(macroGoals.protein.dailyPercentage * 100) !==
        Math.round(
          activeNutritionalProgram.nutritionalGoals.macroGoals.protein
            .dailyPercentage * 100
        ) ||
      Math.round(macroGoals.carb.dailyPercentage * 100) !==
        Math.round(
          activeNutritionalProgram.nutritionalGoals.macroGoals.carb
            .dailyPercentage * 100
        ) ||
      Math.round(macroGoals.fat.dailyPercentage * 100) !==
        Math.round(
          activeNutritionalProgram.nutritionalGoals.macroGoals.fat
            .dailyPercentage * 100
        );

    setIsNutritionalGoalsAlignmentNeeded(
      isWithinProgramTimeline && goalsAreDifferent
    );
  }, [
    activeNutritionalProgram,
    calorieGoal,
    macroGoals,
    getSelectedDateAsDate,
  ]);

  // =========================================================================
  // 2) LOADING MESSAGES
  // =========================================================================
  const getLoadingMessage = () => {
    switch (actionState) {
      case "loading":
        return "Loading Program...";
      case "deactivating":
        return "Deactivating Program...";
      case "deleting":
        return "Deleting Program...";
      default:
        return "Processing...";
    }
  };

  // =========================================================================
  // 3) FETCH FUNCTIONS (ACTIVE & INACTIVE PROGRAMS)
  // =========================================================================
  // Load ACTIVE program
  const loadActiveNutritionalProgram = async (userId) => {
    if (!userId) return;
    setActionState("loading");
    try {
      const response = await axios.get(
        `${apiUrl}/v1/active-nutritional-program`,
        {
          params: { userId },
        }
      );
      setActiveNutritionalProgram(response.data);
    } catch (err) {
      console.error("Error fetching active program:", err);
      setError("Failed to load active program.");
    } finally {
      setActionState(null);
    }
  };

  // Load INACTIVE programs
  const loadInactiveNutritionalPrograms = async (userId) => {
    if (!userId) return;
    setActionState("loading");
    try {
      const response = await axios.get(
        `${apiUrl}/v1/inactive-nutritional-programs`,
        {
          params: { userId },
        }
      );
      setInactiveNutritionalPrograms(response.data);
    } catch (err) {
      console.error("Error fetching inactive programs:", err);
      setError("Failed to load inactive programs.");
    } finally {
      setActionState(null);
    }
  };

  // =========================================================================
  // 4) ACTIVATE / DEACTIVATE / DELETE
  // =========================================================================
  const activateNutritionalProgram = async (program) => {
    if (!userId || !program) return;
    try {
      setActionState("loading");
      const response = await axios.post(
        `${apiUrl}/v1/activate-nutritional-program`,
        {
          userId,
          program,
        }
      );
      setActiveNutritionalProgram(response.data.activatedProgram);
      return response.data.success;
    } catch (err) {
      console.error("Error activating nutritional program:", err);
      setError("Failed to activate nutritional program.");
    } finally {
      setActionState(null);
    }
  };

  const deactivateNutritionalProgram = async (programId) => {
    if (!userId || !programId) return;
    try {
      setActionState("deactivating");
      const response = await axios.post(
        `${apiUrl}/v1/deactivate-nutritional-program`,
        {
          userId,
          programId,
        }
      );
      // Move the just-deactivated program into the inactive list
      setInactiveNutritionalPrograms((prev) => [
        ...prev,
        activeNutritionalProgram,
      ]);
      // Then remove the program from local state/context
      setActiveNutritionalProgram(null);
      // Reset your flags
      setProgramStarted(false);
      setHasShownCongrats(false);

      return response.data.success;
    } catch (err) {
      console.error("Error deactivating nutritional program:", err);
      setError("Failed to deactivate nutritional program.");
    } finally {
      setActionState(null);
    }
  };

  // Updated deleteNutritionalProgram method
  const deleteNutritionalProgram = async (programId) => {
    if (!userId || !programId) return;
    try {
      setActionState("deleting");
      const response = await axios.delete(
        `${apiUrl}/v1/users/${userId}/nutritional-programs/${programId}`
      );

      if (response.status === 200) {
        // If the active program was the one just deleted, clear it
        if (activeNutritionalProgram?.programId === programId) {
          setActiveNutritionalProgram(null);
        }
        // Remove it from the inactive programs array
        setInactiveNutritionalPrograms((prev) =>
          prev.filter((p) => p.programId !== programId)
        );
        return true;
      }
    } catch (error) {
      console.error("Error deleting program:", error);
      setError("Failed to delete nutritional program.");
    } finally {
      setActionState(null);
    }
  };

  // New method: deleteNutritionalProgramsBatch
  const deleteNutritionalProgramsBatch = async (programIds) => {
    if (!userId || !Array.isArray(programIds) || programIds.length === 0)
      return;
    try {
      setActionState("deleting");
      const response = await axios.delete(
        `${apiUrl}/v1/users/${userId}/nutritional-programs`,
        {
          data: { programIds },
        }
      );

      if (response.status === 200) {
        // Remove the deleted programs from the inactive programs array
        setInactiveNutritionalPrograms((prev) =>
          prev.filter((p) => !programIds.includes(p.programId))
        );
        return true;
      }
    } catch (error) {
      console.error("Error batch deleting programs:", error);
      setError("Failed to batch delete nutritional programs.");
    } finally {
      setActionState(null);
    }
  };

  // =========================================================================
  // 5) CREATE / CALCULATE / ETC.
  // =========================================================================
  // Calculate daily nutritional goals
  const calculateNutritionalGoals = async (
    sex,
    heightFeet,
    heightInches,
    weight,
    weightUnit,
    age,
    activityLevel,
    goal
  ) => {
    if (
      !sex ||
      !heightFeet ||
      !heightInches ||
      !weight ||
      !weightUnit ||
      !age ||
      !activityLevel ||
      !goal
    ) {
      console.error("Missing or invalid input fields");
      return;
    }

    // Convert weight to lbs for backend
    const weightInLbs =
      weightUnit === "kg" ? parseFloat(weight) * 2.20462 : parseFloat(weight);

    try {
      const res = await axios.post(`${apiUrl}/v1/calculate-nutritional-goals`, {
        sex,
        heightFeet,
        heightInches,
        weight: weightInLbs, // Send converted weight
        age,
        activityLevel,
        goal,
      });
      setGeneratedNutritionalGoals(res.data.nutritionalGoals);
    } catch (err) {
      console.error("Error calculating nutritional goals:", err);
      setError("Failed to calculate nutritional goals.");
    }
  };

  // Create a new basic program
  const createBasicNutritionalProgram = async (
    sex,
    heightFeet,
    heightInches,
    weight,
    weightUnit,
    age,
    activityLevel,
    goal,
    goalWeight,
    goalWeightUnit,
    startDate,
    endDate
  ) => {
    if (
      !sex ||
      !heightFeet ||
      !heightInches ||
      !weight ||
      !weightUnit ||
      !age ||
      !activityLevel ||
      !goal ||
      !goalWeight ||
      !goalWeightUnit ||
      !startDate ||
      !endDate
    ) {
      console.error(
        "Missing or invalid input fields for createBasicNutritionalProgram"
      );
      setError("Please fill in all required fields, including program name.");
      return;
    }

    const toLbs = (value, unit) => {
      const numericValue = parseFloat(value);
      if (isNaN(numericValue)) return 0; // Handle invalid numbers
      return unit === "kg" ? numericValue * 2.20462 : numericValue;
    };

    const currentWeightInLbs = toLbs(weight, weightUnit);
    const goalWeightInLbs = toLbs(goalWeight, weightUnit); // Use weightUnit for both

    try {
      setActionState("loading");
      // For example, "5" is a string for heightFeet. Joi will parse it.
      const response = await axios.post(
        `${apiUrl}/v1/generate-nutritional-program`,
        {
          programType: "Basic",
          sex,
          heightFeet,
          heightInches,
          weight: currentWeightInLbs, // Send converted
          age,
          activityLevel,
          goal,
          goalWeight: goalWeightInLbs, // Send converted
          startDate,
          endDate,
        }
      );

      setGeneratedProgram(response.data.program);
    } catch (err) {
      console.error("Error creating nutritional program:", err);
      setError("Failed to create nutritional program.");
    } finally {
      setActionState(null);
    }
  };

  // =========================================================================
  // 6) SUBMIT WEIGHT / UPLOAD PICTURE
  // =========================================================================
  const submitWeight = async (checkpointId, newWeight) => {
    if (!userId || !checkpointId || newWeight == null) return;
    try {
      const response = await axios.post(
        `${apiUrl}/v1/active-nutritional-program/checkpoint/${checkpointId}/log-weight`,
        {
          userId,
          userLoggedWeight: newWeight,
        }
      );
      if (response.data?.success && response.data?.checkpoint) {
        const updatedCheckpoint = response.data.checkpoint;
        // Update local state
        setActiveNutritionalProgram((prev) => {
          if (!prev || !prev.checkpoints) return prev;
          const updatedCheckpoints = prev.checkpoints.map((cp) =>
            cp.week === checkpointId ? { ...cp, ...updatedCheckpoint } : cp
          );
          return { ...prev, checkpoints: updatedCheckpoints };
        });
        return updatedCheckpoint;
      }
    } catch (err) {
      console.error("Error updating weight:", err);
    }
  };

  const uploadProgressPicture = async (uri, checkpointId) => {
    if (!userId || !uri || !checkpointId) return null;
    try {
      // 1. Process image first
      const processedImage = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: PROGRESS_PICTURE_CONFIG.resize }],
        {
          compress: PROGRESS_PICTURE_CONFIG.compress,
          format: PROGRESS_PICTURE_CONFIG.format,
        }
      );

      // 2. Create FormData
      const formData = new FormData();
      formData.append("progressPicture", {
        uri: processedImage.uri,
        name: `progressPicture_${checkpointId}.jpg`,
        type: "image/jpeg",
      });
      formData.append("userId", userId);

      // 4. Upload to backend
      const res = await axios.post(
        `${apiUrl}/v1/active-nutritional-program/checkpoint/${checkpointId}/upload-progress-picture`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );

      setActiveNutritionalProgram((prev) => ({
        ...prev,
        checkpoints: prev.checkpoints.map((cp) =>
          cp.week === checkpointId
            ? {
                ...cp,
                progressPictureUrl: res.data.progressPictureUrl, // Clean URL
              }
            : cp
        ),
      }));

      return res.data.progressPictureUrl;
    } catch (err) {
      console.error("Error uploading progress picture:", err);
      return null;
    }
  };

  const removeProgressPicture = async (checkpointId) => {
    if (!userId || !checkpointId) return null;
    try {
      // 1. Delete from backend
      await axios.delete(
        `${apiUrl}/v1/active-nutritional-program/checkpoint/${checkpointId}/delete-progress-picture`,
        {
          data: { userId }, // or params, depending on your API design
        }
      );

      // 2. Immediate UI update
      setActiveNutritionalProgram((prev) => ({
        ...prev,
        checkpoints: prev.checkpoints.map((cp) =>
          cp.week === checkpointId ? { ...cp, progressPictureUrl: "" } : cp
        ),
      }));
    } catch (err) {
      console.error("Error deleting progress picture:", err);
      throw err;
    }
  };

  // =========================================================================
  // 7) COMBINED "allPrograms"
  // =========================================================================
  /**
   * Now we can create one array that merges the active program (if any) with
   * the array of inactive programs, so the caller can just map over 'allPrograms.'
   */
  const allPrograms = useMemo(() => {
    if (activeNutritionalProgram) {
      return [activeNutritionalProgram, ...inactiveNutritionalPrograms];
    }
    return [...inactiveNutritionalPrograms];
  }, [activeNutritionalProgram, inactiveNutritionalPrograms]);

  // =========================================================================
  // 8) CONTEXT VALUE
  // =========================================================================
  const contextValue = useMemo(
    () => ({
      // Loaders
      loadActiveNutritionalProgram,
      loadInactiveNutritionalPrograms,
      // Data
      activeNutritionalProgram,
      inactiveNutritionalPrograms,
      allPrograms, // here is our merged array
      // Actions
      activateNutritionalProgram,
      deactivateNutritionalProgram,
      deleteNutritionalProgram,
      deleteNutritionalProgramsBatch,
      calculateNutritionalGoals,
      createBasicNutritionalProgram,
      submitWeight,
      uploadProgressPicture,
      removeProgressPicture,
      // Generated data
      generatedProgram,
      generatedNutritionalGoals,
      // States
      actionState,
      error,
      programStarted,
      hasShownCongrats,
      // Helpers
      getLoadingMessage,
      isNutritionalGoalsAlignmentNeeded,
      setIsNutritionalGoalsAlignmentNeeded,
    }),
    [
      activeNutritionalProgram,
      inactiveNutritionalPrograms,
      loadActiveNutritionalProgram,
      loadInactiveNutritionalPrograms,
      activateNutritionalProgram,
      deactivateNutritionalProgram,
      deleteNutritionalProgram,
      deleteNutritionalProgramsBatch,
      calculateNutritionalGoals,
      createBasicNutritionalProgram,
      submitWeight,
      uploadProgressPicture,
      removeProgressPicture,
      generatedProgram,
      generatedNutritionalGoals,
      actionState,
      error,
      programStarted,
      hasShownCongrats,
      getLoadingMessage,
      isNutritionalGoalsAlignmentNeeded,
      setIsNutritionalGoalsAlignmentNeeded,
    ]
  );

  return (
    <NutritionProgramContext.Provider value={contextValue}>
      {children}
    </NutritionProgramContext.Provider>
  );
}
