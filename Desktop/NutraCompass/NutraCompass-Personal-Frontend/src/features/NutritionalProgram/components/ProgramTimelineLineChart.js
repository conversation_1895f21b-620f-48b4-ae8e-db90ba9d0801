// ProgramTimelineLineChart.js
import React from "react";
import { Dimensions, View, Text, StyleSheet } from "react-native";
import { LineChart } from "react-native-chart-kit";
import Svg, { Circle } from "react-native-svg";
import { useUserSettings } from "../../Settings/context/UserSettingsContext";

const ProgramTimelineLineChart = ({ activeNutritionalProgram, theme }) => {
  const width = Dimensions.get("window").width;
  const { getUserMeasurementSettings } = useUserSettings();
  const { bodyWeightUnit } = getUserMeasurementSettings(); // Get user's preferred unit

  // Helper function to convert and format weight
  const convertWeight = (weightInLbs) => {
    if (bodyWeightUnit === "kg") {
      return weightInLbs / 2.20462; // Convert lbs to kg
    }
    return weightInLbs; // Keep as lbs
  };

  // Prepare chart data with converted weights
  const chartData = {
    labels: activeNutritionalProgram.checkpoints.map((cp) => {
      const d = new Date(cp.date);
      return d.toLocaleDateString("en-US", {
        timeZone: "UTC",
        month: "short",
        day: "numeric",
      });
    }),
    datasets: [
      {
        data: activeNutritionalProgram.checkpoints.map((cp) =>
          convertWeight(cp.expectedWeight)
        ),
        color: () => theme.colors.subTextColor || "#4CAF50", // Line color
        strokeWidth: 2,
      },
    ],
  };

  // Chart configuration with dynamic units
  const chartConfig = {
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 1,
    color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
    labelColor: (opacity = 1) =>
      theme.colors.primaryTextColor || `rgba(170, 170, 170, ${opacity})`,
    propsForLabels: {
      fontSize: 12,
      fontWeight: "500",
    },
    propsForBackgroundLines: {
      stroke: theme.colors.subTextColor || "#e0e0e0",
    },
  };

  // Dot colors based on whether the user has logged weight for the checkpoint
  const loggedColor = theme.colors.chartDotFill || "#4CAF50"; // Green
  const notLoggedColor = theme.colors.subTextColor || "#808080"; // Gray

  return (
    <View>
      <LineChart
        data={chartData}
        width={width * 2} // Increased width for horizontal scrolling
        height={220}
        yAxisSuffix={` ${bodyWeightUnit}`} // Dynamic unit suffix
        chartConfig={chartConfig}
        yLabelsOffset={10}
        bezier
        style={{ borderRadius: 16 }}
        verticalLabelRotation={0}
        // Customize dots: green if weight logged, gray if not
        renderDotContent={({ x, y, index }) => {
          const cp = activeNutritionalProgram.checkpoints[index];
          const dotColor = cp.userLoggedWeight ? loggedColor : notLoggedColor;
          const dotRadius = cp.userLoggedWeight ? "7" : "6";
          return (
            <Svg key={index}>
              <Circle cx={x} cy={y} r={dotRadius} fill={dotColor} />
            </Svg>
          );
        }}
      />

      {/* Legend */}
      <View style={styles.legendContainer}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: loggedColor }]} />
          <Text
            style={[
              styles.legendText,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            Weight Logged
          </Text>
        </View>
        <View style={styles.legendItem}>
          <View
            style={[styles.legendDot, { backgroundColor: notLoggedColor }]}
          />
          <Text
            style={[
              styles.legendText,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            Weight Not Logged
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  legendContainer: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    marginTop: 12,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    marginLeft: 6,
    fontSize: 14,
  },
});

export default ProgramTimelineLineChart;
