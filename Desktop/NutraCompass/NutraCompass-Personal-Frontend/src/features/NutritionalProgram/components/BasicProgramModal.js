// BasicProgramModal.js
import React, { useEffect, useState, useMemo, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
} from "react-native";
import { Image } from "expo-image";
import { LineChart } from "react-native-chart-kit";
import { Card, Button, TextInput, Snackbar } from "react-native-paper";
import { Calendar } from "react-native-calendars";
import * as Haptics from "expo-haptics";
import Modal from "react-native-modal";
import Feather from "react-native-vector-icons/Feather";
import dailyNutritionGoalsCalculationModalStyles from "../../FoodDiary/components/styles/dailyNutritionGoalsCalculationModalStyles.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const ResultsSlideContent = React.memo(
  ({
    mode,
    goal,
    sex,
    heightFeet,
    heightInches,
    weight,
    weightUnit,
    age,
    bodyFatPercentageRange,
    goalWeight,
    goalWeightUnit,
    endDate,
    startDate,
    activityLevel,
    theme,
    generatedNutritionalGoals,
    generatedProgram,
    programName,
    setProgramName, // New prop to update programName
  }) => {
    const styles = dailyNutritionGoalsCalculationModalStyles();

    // Helper function for weight conversion
    const convertWeight = (weightInLbs, targetUnit = weightUnit) => {
      if (targetUnit === "kg") {
        return weightInLbs / 2.20462;
      }
      return weightInLbs;
    };

    // Calculate Net Change in Bodyweight
    const netChange =
      convertWeight(goalWeight, goalWeightUnit) - convertWeight(weight);

    // Format net change based on user's unit preference
    const formatNetChange = (change) => {
      return `${Math.abs(change).toFixed(1)} ${weightUnit}`;
    };

    const netChangeText =
      netChange < 0
        ? `Lose ${formatNetChange(netChange)}`
        : netChange > 0
        ? `Gain ${formatNetChange(netChange)}`
        : "Maintain Current Weight";

    return (
      <ScrollView
        style={{ height: "100%", width: "100%" }}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: scaleSize(200),
        }}
      >
        <TouchableOpacity activeOpacity={1} style={{ flexGrow: 1 }}>
          {/* Program Name Input Field */}
          <View
            style={{
              paddingHorizontal: scaleSize(16),
              marginBottom: scaleSize(20),
            }}
          >
            <TextInput
              mode="outlined"
              placeholder="ENTER PROGRAM NAME"
              placeholderTextColor={theme.colors.subTextColor}
              value={programName}
              onChangeText={(text) => setProgramName(text)}
              style={{
                backgroundColor: theme.colors.surface,
                color: theme.colors.primaryTextColor,
                textAlign: "center",
              }}
              outlineColor={theme.colors.primary}
              activeOutlineColor={theme.colors.primary}
            />
          </View>

          {/* Avatar and Floating Cards Section */}
          <View style={{ alignItems: "center", gap: scaleSize(8) }}>
            {/* Floating Cards Around the Avatar */}
            <View
              style={{
                flexDirection: "row",
                flexWrap: "wrap",
                justifyContent: "center",
                width: "100%",
                gap: scaleSize(8),
              }}
            >
              {/* Existing Floating Cards */}
              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: scaleSize(3),
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: scaleSize(2),
                  },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Goal:{" "}
                  {goal === "SetGoalWeight" ? "Achieve Goal Weight" : goal}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: scaleSize(3),
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: scaleSize(2),
                  },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Sex: {sex || "Not specified"}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: scaleSize(3),
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: scaleSize(2),
                  },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Height: {heightFeet} ft {heightInches} in
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: scaleSize(3),
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: scaleSize(2),
                  },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Weight: {weight} {weightUnit}
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: scaleSize(3),
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: scaleSize(2),
                  },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Age: {age} years
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: scaleSize(3),
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: scaleSize(2),
                  },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Body Fat: {bodyFatPercentageRange || "Not specified"}%
                </Text>
              </View>

              <View
                style={{
                  paddingHorizontal: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  backgroundColor: theme.colors.surface,
                  borderRadius: scaleSize(8),
                  elevation: scaleSize(3),
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: scaleSize(2),
                  },
                  shadowOpacity: 0.2,
                  shadowRadius: scaleSize(4),
                  minWidth: scaleSize(100),
                }}
              >
                <Text style={[styles.cardText, { fontSize: scaleSize(11) }]}>
                  Activity Level:{" "}
                  {activityLevel
                    ? activityLevel.replace(/\s*\([^)]*\)/g, "") // Removes text between parentheses
                    : "Not specified"}
                </Text>
              </View>
            </View>
          </View>

          {/* Nutritional Goals Section When There Is No Goal Weight, Start Date, nor End Date */}
          {generatedNutritionalGoals &&
            !goalWeight &&
            !goalWeightUnit &&
            !startDate &&
            !endDate && (
              <View
                style={{
                  paddingHorizontal: scaleSize(16),
                  marginTop: scaleSize(16),
                }}
              >
                <Card style={{ marginBottom: scaleSize(20) }}>
                  <Card.Title title="Your Nutritional Goals" />
                  <Card.Content style={{ gap: scaleSize(6) }}>
                    {/* Daily Calories */}
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        backgroundColor: theme.colors.screenBackground,
                        padding: scaleSize(10),
                        borderRadius: scaleSize(8),
                      }}
                    >
                      <Text style={[styles.cardText]}>Calories</Text>
                      <Text
                        style={[
                          styles.inputValue,
                          { color: theme.colors.primary },
                        ]}
                      >
                        {generatedNutritionalGoals?.dailyCalories.toLocaleString() ||
                          "Not calculated yet"}
                      </Text>
                    </View>

                    {/* Carbs */}
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        backgroundColor: theme.colors.screenBackground,
                        padding: scaleSize(10),
                        borderRadius: scaleSize(8),
                      }}
                    >
                      <Text style={[styles.cardText]}>Carbs</Text>
                      <Text
                        style={[
                          styles.inputValue,
                          { color: theme.colors.primary },
                        ]}
                      >
                        {`${generatedNutritionalGoals?.carbGrams} grams` ||
                          "Not calculated yet"}{" "}
                        (
                        {(
                          generatedNutritionalGoals?.carbPercentage * 100
                        ).toFixed(0)}
                        %)
                      </Text>
                    </View>

                    {/* Protein */}
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        backgroundColor: theme.colors.screenBackground,
                        padding: scaleSize(10),
                        borderRadius: scaleSize(8),
                      }}
                    >
                      <Text style={[styles.cardText]}>Protein</Text>
                      <Text
                        style={[
                          styles.inputValue,
                          { color: theme.colors.primary },
                        ]}
                      >
                        {`${generatedNutritionalGoals?.proteinGrams} grams` ||
                          "Not calculated yet"}{" "}
                        (
                        {(
                          generatedNutritionalGoals?.proteinPercentage * 100
                        ).toFixed(0)}
                        %)
                      </Text>
                    </View>

                    {/* Fats */}
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        backgroundColor: theme.colors.screenBackground,
                        padding: scaleSize(10),
                        borderRadius: scaleSize(8),
                      }}
                    >
                      <Text style={[styles.cardText]}>Fats</Text>
                      <Text
                        style={[
                          styles.inputValue,
                          { color: theme.colors.primary },
                        ]}
                      >
                        {`${generatedNutritionalGoals?.fatGrams} grams` ||
                          "Not calculated yet"}{" "}
                        (
                        {(
                          generatedNutritionalGoals?.fatPercentage * 100
                        ).toFixed(0)}
                        %)
                      </Text>
                    </View>
                  </Card.Content>
                </Card>
              </View>
            )}

          {/* Program Section When There Is GoalWeight, StartDate, EndDate */}
          {generatedProgram &&
            goalWeight &&
            goalWeightUnit &&
            startDate &&
            endDate && (
              <View style={{ marginTop: scaleSize(16) }}>
                <View
                  style={{
                    padding: scaleSize(12),
                    gap: scaleSize(12),
                  }}
                >
                  {/* Program Start Date, End Date, Total Time, Bodyweight, Goal Weight, Net Change */}
                  <Card>
                    <Card.Title title="Program Timeline" />
                    <Card.Content
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      {/* Left Column: Start Date, End Date, Program Duration */}
                      <View style={{ flex: 1 }}>
                        {/* Start Date */}
                        <View
                          style={{
                            flexDirection: "row",
                            marginBottom: scaleSize(5),
                          }}
                        >
                          <Text
                            style={{
                              fontSize: scaleSize(12),
                              fontWeight: "bold",
                              color: theme.colors.primaryTextColor,
                            }}
                          >
                            Start Date:{" "}
                          </Text>
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontSize: scaleSize(12),
                            }}
                          >
                            {new Intl.DateTimeFormat("en-US", {
                              month: "long",
                              day: "numeric",
                              year: "numeric",
                            }).format(
                              // Apply timezone correction for startDate
                              new Date(
                                new Date(startDate)
                                  .toUTCString()
                                  .split(" GMT")[0]
                              )
                            )}
                          </Text>
                        </View>

                        {/* End Date */}
                        <View
                          style={{
                            flexDirection: "row",
                            marginBottom: scaleSize(5),
                          }}
                        >
                          <Text
                            style={{
                              fontSize: scaleSize(12),
                              fontWeight: "bold",
                              color: theme.colors.primaryTextColor,
                            }}
                          >
                            End Date:{" "}
                          </Text>
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontSize: scaleSize(12),
                            }}
                          >
                            {new Intl.DateTimeFormat("en-US", {
                              month: "long",
                              day: "numeric",
                              year: "numeric",
                            }).format(
                              // Apply timezone correction for endDate
                              new Date(
                                new Date(endDate).toUTCString().split(" GMT")[0]
                              )
                            )}
                          </Text>
                        </View>

                        {/* Total Program Duration */}
                        <View style={{ flexDirection: "row" }}>
                          <Text
                            style={{
                              fontSize: scaleSize(12),
                              fontWeight: "bold",
                              color: theme.colors.primaryTextColor,
                            }}
                          >
                            Program Duration:{" "}
                          </Text>
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontSize: scaleSize(12),
                            }}
                          >
                            {Math.ceil(
                              (new Date(
                                new Date(endDate).toUTCString().split(" GMT")[0]
                              ) -
                                new Date(
                                  new Date(startDate)
                                    .toUTCString()
                                    .split(" GMT")[0]
                                )) /
                                (1000 * 60 * 60 * 24)
                            )}{" "}
                            days
                          </Text>
                        </View>
                      </View>

                      {/* Right Column: Current Weight, Goal Weight, Net Change */}
                      <View style={{ flex: 3 / 4 }}>
                        {/* Bodyweight */}
                        <View
                          style={{
                            flexDirection: "row",
                            marginBottom: scaleSize(5),
                          }}
                        >
                          <Text
                            style={{
                              fontSize: scaleSize(12),
                              fontWeight: "bold",
                              color: theme.colors.primaryTextColor,
                            }}
                          >
                            Current Weight:{" "}
                          </Text>
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontSize: scaleSize(12),
                            }}
                          >
                            {weight} {weightUnit}
                          </Text>
                        </View>

                        {/* Goal Weight */}
                        <View
                          style={{
                            flexDirection: "row",
                            marginBottom: scaleSize(5),
                          }}
                        >
                          <Text
                            style={{
                              fontSize: scaleSize(12),
                              fontWeight: "bold",
                              color: theme.colors.primaryTextColor,
                            }}
                          >
                            Goal Weight:{" "}
                          </Text>
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontSize: scaleSize(12),
                            }}
                          >
                            {goalWeight} {goalWeightUnit}
                          </Text>
                        </View>

                        {/* Net Change in Bodyweight */}
                        <View style={{ flexDirection: "row" }}>
                          <Text
                            style={{
                              fontSize: scaleSize(12),
                              fontWeight: "bold",
                              color: theme.colors.primaryTextColor,
                            }}
                          >
                            Net Change:{" "}
                          </Text>
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontSize: scaleSize(12),
                            }}
                          >
                            {netChangeText}
                          </Text>
                        </View>
                      </View>
                    </Card.Content>
                  </Card>

                  {/* Nutritional Goals */}
                  <Card>
                    <Card.Title title="Your Nutritional Goals" />
                    <Card.Content style={{ gap: scaleSize(6) }}>
                      {/* Daily Calories */}
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                          alignItems: "center",
                          backgroundColor: theme.colors.screenBackground,
                          padding: scaleSize(10),
                          borderRadius: scaleSize(8),
                        }}
                      >
                        <Text style={[styles.cardText]}>Calories</Text>
                        <Text
                          style={[
                            styles.inputValue,
                            { color: theme.colors.primary },
                          ]}
                        >
                          {generatedProgram?.nutritionalGoals?.calorieGoal?.toLocaleString() ||
                            "Not calculated yet"}
                        </Text>
                      </View>

                      {/* Carbs */}
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                          alignItems: "center",
                          backgroundColor: theme.colors.screenBackground,
                          padding: scaleSize(10),
                          borderRadius: scaleSize(8),
                        }}
                      >
                        <Text style={[styles.cardText]}>Carbs</Text>
                        <Text
                          style={[
                            styles.inputValue,
                            { color: theme.colors.primary },
                          ]}
                        >
                          {`${generatedProgram?.nutritionalGoals?.macroGoals?.carb?.dailyGrams} grams` ||
                            "Not calculated yet"}{" "}
                          (
                          {(
                            generatedProgram?.nutritionalGoals?.macroGoals?.carb
                              ?.dailyPercentage * 100
                          ).toFixed(0)}
                          %)
                        </Text>
                      </View>

                      {/* Protein */}
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                          alignItems: "center",
                          backgroundColor: theme.colors.screenBackground,
                          padding: scaleSize(10),
                          borderRadius: scaleSize(8),
                        }}
                      >
                        <Text style={[styles.cardText]}>Protein</Text>
                        <Text
                          style={[
                            styles.inputValue,
                            { color: theme.colors.primary },
                          ]}
                        >
                          {`${generatedProgram?.nutritionalGoals?.macroGoals?.protein?.dailyGrams} grams` ||
                            "Not calculated yet"}{" "}
                          (
                          {(
                            generatedProgram?.nutritionalGoals?.macroGoals
                              ?.protein?.dailyPercentage * 100
                          ).toFixed(0)}
                          %)
                        </Text>
                      </View>

                      {/* Fats */}
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                          alignItems: "center",
                          backgroundColor: theme.colors.screenBackground,
                          padding: scaleSize(10),
                          borderRadius: scaleSize(8),
                        }}
                      >
                        <Text style={[styles.cardText]}>Fats</Text>
                        <Text
                          style={[
                            styles.inputValue,
                            { color: theme.colors.primary },
                          ]}
                        >
                          {`${generatedProgram?.nutritionalGoals?.macroGoals?.fat?.dailyGrams} grams` ||
                            "Not calculated yet"}{" "}
                          (
                          {(
                            generatedProgram?.nutritionalGoals?.macroGoals?.fat
                              ?.dailyPercentage * 100
                          ).toFixed(0)}
                          %)
                        </Text>
                      </View>
                    </Card.Content>
                  </Card>
                </View>

                {/* Chart Section */}
                <Card
                  style={{
                    flex: 1,
                    marginHorizontal: scaleSize(12),
                    borderRadius: scaleSize(16),
                    elevation: scaleSize(5), // Adds shadow on Android
                    shadowColor: "#000", // Adds shadow on iOS
                    shadowOffset: {
                      width: 0,
                      height: scaleSize(2),
                    }, // Shadow position
                    shadowOpacity: 0.25, // Shadow opacity
                    shadowRadius: scaleSize(3.84), // Shadow blur
                  }}
                >
                  <Card.Title title="Projected Body Weight Progress to Goal" />

                  {/* Legend Section */}
                  <View
                    style={{
                      alignSelf: "flex-end",
                      flexDirection: "row",
                      alignItems: "center",
                      paddingHorizontal: scaleSize(16),
                      paddingBottom: scaleSize(12),
                    }}
                  >
                    <View
                      style={{
                        width: scaleSize(8),
                        height: scaleSize(8),
                        borderRadius: scaleSize(6),
                        backgroundColor: theme.colors.primary,
                        marginRight: scaleSize(8),
                      }}
                    />
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(12),
                      }}
                    >
                      Weigh-In Checkpoint
                    </Text>
                  </View>

                  <ScrollView
                    horizontal
                    contentContainerStyle={{
                      flexGrow: 1,
                      paddingLeft: scaleSize(12),
                    }}
                  >
                    <TouchableWithoutFeedback>
                      <View>
                        {generatedProgram?.checkpoints?.length > 0 && (
                          <LineChart
                            data={{
                              labels: generatedProgram?.checkpoints.map(
                                (checkpoint) => {
                                  const correctedDate = new Date(
                                    new Date(checkpoint.date)
                                      .toUTCString()
                                      .split(" GMT")[0]
                                  );
                                  return new Intl.DateTimeFormat("en-US", {
                                    month: "short",
                                    day: "numeric",
                                  }).format(correctedDate);
                                }
                              ),
                              datasets: [
                                {
                                  data: generatedProgram?.checkpoints.map(
                                    (checkpoint) =>
                                      // Convert weight to user's preferred unit
                                      convertWeight(
                                        parseFloat(checkpoint.expectedWeight)
                                      )
                                  ),
                                  color: () =>
                                    mode === "dark"
                                      ? `rgba(255, 255, 255, 0.5)`
                                      : `rgba(0, 0, 0, 0.5)`,
                                  strokeWidth: scaleSize(2),
                                },
                              ],
                            }}
                            width={Math.max(
                              scaleSize(300),
                              generatedProgram?.checkpoints.length *
                                scaleSize(80)
                            )}
                            height={scaleSize(250)}
                            yAxisSuffix={` ${weightUnit}`} // Dynamic unit suffix
                            chartConfig={{
                              backgroundGradientFrom: theme.colors.surface,
                              backgroundGradientTo: theme.colors.surface,
                              backgroundGradientFromOpacity: 0.0,
                              backgroundGradientToOpacity: 0.0,
                              decimalPlaces: 1,
                              color: (opacity = 1) =>
                                `rgba(255, 255, 255, ${opacity})`,
                              labelColor: (opacity = 1) =>
                                `rgba(255, 255, 255, ${opacity})`,
                              propsForDots: {
                                r: scaleSize(4),
                                strokeWidth: scaleSize(2),
                                stroke: theme.colors.primary,
                                fill: theme.colors.primary,
                              },
                              propsForBackgroundLines: {
                                stroke:
                                  mode === "dark"
                                    ? "rgba(255, 255, 255, 0.2)"
                                    : "rgba(0, 0, 0, 0.2)",
                                strokeDasharray: "",
                              },
                              propsForVerticalLabels: {
                                fontSize: scaleSize(11),
                                fontWeight: "bold",
                                fill: theme.colors.primaryTextColor,
                              },
                              propsForHorizontalLabels: {
                                fontSize: scaleSize(10),
                                fontWeight: "bold",
                                fill: theme.colors.primaryTextColor,
                              },
                            }}
                            withVerticalLines={false}
                            withHorizontalLines={true}
                            verticalLabelRotation={0}
                          />
                        )}
                      </View>
                    </TouchableWithoutFeedback>
                  </ScrollView>
                </Card>
              </View>
            )}
        </TouchableOpacity>
      </ScrollView>
    );
  }
);

const BasicProgramModal = ({ isVisible, closeModal }) => {
  const styles = dailyNutritionGoalsCalculationModalStyles();
  const { theme, mode } = useThemeContext();
  const {
    createBasicNutritionalProgram,
    generatedProgram,
    generatedNutritionalGoals,
    activateNutritionalProgram,
    activeNutritionalProgram,
  } = useNutritionProgram();
  const { setCalorieAndMacroGoals, getUserProfile } = useUserSettings();
  const { getSelectedDateAsDate } = useTime();
  const userProfile = getUserProfile();

  // Parse bodyWeight string to extract value and unit
  const parseBodyWeight = (bodyWeight) => {
    if (!bodyWeight) return { value: "", unit: "lbs" };

    const parts = bodyWeight.trim().split(/\s+/);
    if (parts.length < 2) return { value: "", unit: "lbs" };

    const value = parts[0];
    const unit = parts[1].toLowerCase();

    return {
      value,
      unit: unit.includes("kg") ? "kg" : "lbs", // Default to lbs if not kg
    };
  };

  const { value: initialWeight, unit: initialWeightUnit } = parseBodyWeight(
    userProfile?.bodyWeight
  );
  const numericAge = userProfile?.age ? String(userProfile.age) : ""; // Convert age to string

  const [formState, setFormState] = useState({
    programName: "",
    goal: "Maintain",
    sex: userProfile?.sex || "",
    heightFeet: "",
    heightInches: "",
    weight: initialWeight,
    weightUnit: initialWeightUnit,
    age: numericAge,
    bodyFatPercentageRange: null,
    goalWeight: "",
    goalWeightUnit: "",
    endDate: "",
    startDate: "",
    activityLevel: "",
  });

  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [currentSlide, setCurrentSlide] = useState(0);
  const [heightFeetFocused, setHeightFeetFocused] = useState(false);
  const [heightInchesFocused, setHeightInchesFocused] = useState(false);
  const [weightFocused, setWeightFocused] = useState(false);
  const [ageFocused, setAgeFocused] = useState(false);
  const [goalWeightFocused, setGoalWeightFocused] = useState(false);
  const [startCalendarVisible, setStartCalendarVisible] = useState(false);
  const [endCalendarVisible, setEndCalendarVisible] = useState(false);

  const minStartDate = getSelectedDateAsDate().toISOString().split("T")[0];

  let minEndDate = getSelectedDateAsDate();
  minEndDate.setDate(minEndDate.getDate() + 14);
  minEndDate = minEndDate.toISOString().split("T")[0];

  useEffect(() => {
    if (!isVisible) {
      resetFormFields();
      setSnackbarMessage("");
      setSnackbarVisible(false);
      setCurrentSlide(0); // Reset to the first slide
    }
  }, [isVisible]);

  const resetFormFields = () => {
    setFormState({
      programName: "",
      goal: "SetGoalWeight",
      sex: "",
      heightFeet: "",
      heightInches: "",
      weight: initialWeight,
      weightUnit: initialWeightUnit,
      age: numericAge,
      bodyFatPercentageRange: null,
      goalWeight: "",
      goalWeightUnit: "",
      endDate: "",
      startDate: "",
      activityLevel: "",
    });
  };

  const updateFormState = (key, value) => {
    setFormState((prevState) => {
      if (prevState[key] === value) return prevState;
      return { ...prevState, [key]: value };
    });
  };

  const handleOptionSelect = (key, value) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    updateFormState(key, value);
  };

  const handleSlideChange = (newSlide) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (newSlide >= 0 && newSlide < slides.length) {
      setHeightFeetFocused(false);
      setHeightInchesFocused(false);
      setWeightFocused(false);
      setAgeFocused(false);
      setGoalWeightFocused(false); // Reset focus
      setCurrentSlide(newSlide);
      Keyboard.dismiss();
    } else {
      console.error("Invalid slide index:", newSlide);
    }
  };

  const handleSetAsMyNutritionalProgram = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      // Validate programName is not empty
      if (!formState.programName.trim()) {
        setSnackbarMessage("Please enter a name for your program.");
        setSnackbarVisible(true);
        return;
      }

      if (activeNutritionalProgram) {
        setSnackbarMessage(
          "You can only have one active nutritional program at a time!"
        );
        setSnackbarVisible(true);
        return;
      }

      if (generatedProgram) {
        // Ensure programName is included in generatedProgram
        if (!generatedProgram.programName) {
          generatedProgram.programName = formState.programName.trim();
        }

        // Validate programName is not empty
        if (!generatedProgram.programName) {
          setSnackbarMessage("Please enter a valid program name.");
          setSnackbarVisible(true);
          return;
        }

        const response = await activateNutritionalProgram(generatedProgram);

        if (response) {
          setSnackbarMessage("Nutritional Program successfully activated!");
          setSnackbarVisible(true);
          closeModal(); // Close modal upon successful activation
        } else {
          throw new Error("Failed to activate nutritional program.");
        }
      } else {
        setSnackbarMessage("The generated program is missing required data.");
        setSnackbarVisible(true);
      }
    } catch (error) {
      console.error("Error activating nutritional program:", error);
      setSnackbarMessage("Failed to activate nutritional program.");
      setSnackbarVisible(true);
    }
  };

  const handleUpdateNutritionalGoals = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      if (
        generatedNutritionalGoals.dailyCalories &&
        generatedNutritionalGoals.proteinPercentage &&
        generatedNutritionalGoals.carbPercentage &&
        generatedNutritionalGoals.fatPercentage
      ) {
        setCalorieAndMacroGoals({
          calorieGoal: generatedNutritionalGoals.dailyCalories,
          proteinPercentage: generatedNutritionalGoals.proteinPercentage * 100,
          carbPercentage: generatedNutritionalGoals.carbPercentage * 100,
          fatPercentage: generatedNutritionalGoals.fatPercentage * 100,
        });
        setSnackbarMessage("Nutritional goals updated successfully!");
        setSnackbarVisible(true);
      }
    } catch (error) {
      console.error("Error updating nutritional goals:", error);
      setSnackbarMessage("Failed to update nutritional goals.");
      setSnackbarVisible(true);
    }
  };

  const handleGenerateNutritionalProgram = useCallback(async () => {
    try {
      // Basic checks
      if (
        !formState.sex ||
        !formState.heightFeet ||
        !formState.heightInches ||
        !formState.weight ||
        !formState.weightUnit ||
        !formState.age ||
        !formState.activityLevel ||
        !formState.goal ||
        !formState.goalWeight ||
        !formState.goalWeightUnit ||
        !formState.startDate ||
        !formState.endDate
      ) {
        console.error("Missing or invalid input fields for creating a program");
        setSnackbarMessage("Please fill in all required fields.");
        setSnackbarVisible(true);
        return;
      }

      // Convert formState start/end dates to YYYY-MM-DD
      // (Assuming formState.startDate / endDate might be ISO or a Date object)
      const formatDate = (dateInput) => {
        // If it's a string like "2025-01-13T00:00:00.000Z" (ISO),
        // or a Date object, this ensures "YYYY-MM-DD"
        const dateObj = new Date(dateInput);
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, "0");
        const day = String(dateObj.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`; // e.g. "2025-01-13"
      };

      const startDateFormatted = formatDate(formState.startDate);
      const endDateFormatted = formatDate(formState.endDate);

      await createBasicNutritionalProgram(
        formState.sex,
        formState.heightFeet,
        formState.heightInches,
        formState.weight,
        formState.weightUnit,

        formState.age,
        formState.activityLevel,
        formState.goal,
        formState.goalWeight,
        formState.goalWeightUnit,
        startDateFormatted,
        endDateFormatted
      );
    } catch (error) {
      console.error("Error creating nutritional program:", error);
      setSnackbarMessage("Failed to create nutritional program.");
      setSnackbarVisible(true);
    }
  }, [
    formState.sex,
    formState.heightFeet,
    formState.heightInches,
    formState.weight,
    formState.weightUnit,
    formState.age,
    formState.activityLevel,
    formState.goal,
    formState.goalWeight,
    formState.goalWeightUnit,
    formState.startDate,
    formState.endDate,
  ]);

  const bodyTypes = [
    {
      id: 1,
      value: 8,
      description: "Very Lean\n5-10% body fat",
      image: require("../../../../assets/BodyFatImages/1.png"),
    },
    {
      id: 2,
      value: 13,
      description: "Lean\n11-15% body fat",
      image: require("../../../../assets/BodyFatImages/2.png"),
    },
    {
      id: 3,
      value: 18,
      description: "Moderately Lean\n16-20% body fat",
      image: require("../../../../assets/BodyFatImages/3.png"),
    },
    {
      id: 4,
      value: 23,
      description: "Average\n21-25% body fat",
      image: require("../../../../assets/BodyFatImages/4.png"),
    },
    {
      id: 5,
      value: 28,
      description: "Above Average\n26-30% body fat",
      image: require("../../../../assets/BodyFatImages/5.png"),
    },
    {
      id: 6,
      value: 33,
      description: "Overweight\n31-35% body fat",
      image: require("../../../../assets/BodyFatImages/6.png"),
    },
    {
      id: 7,
      value: 40,
      description: "Obese\n36%+ body fat",
      image: require("../../../../assets/BodyFatImages/7.png"),
    },
  ];

  // We removed the "I want to" slide from this array.
  const slides = [
    {
      title: "Basic Nutritional Program Creator",
      content: (
        <View style={{ alignItems: "center", padding: scaleSize(20) }}>
          <Image
            source={require("../../../../assets/GoalsBasedNutritionalCalculator.png")}
            style={{
              width: "90%",
              height: scaleSize(300),
              contentFit: "cover",
              borderRadius: scaleSize(30),
            }}
          />
          <Card
            style={{
              marginVertical: scaleSize(20),
              marginHorizontal: scaleSize(20),
            }}
          >
            <Card.Content>
              <Text
                style={{
                  fontSize: scaleSize(18),
                  textAlign: "center",
                  color: "gray",
                }}
              >
                Get ready to enter some key details about your body and
                lifestyle. We'll use this information to estimate your daily
                calorie needs and suggest the best macronutrient distribution to
                help you achieve your goals.
              </Text>
            </Card.Content>
          </Card>
        </View>
      ),
    },
    {
      title: "Your Sex",
      content: (
        <View>
          <TouchableOpacity onPress={() => handleOptionSelect("sex", "Male")}>
            <Card
              style={
                formState?.sex === "Male" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Male</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleOptionSelect("sex", "Female")}>
            <Card
              style={
                formState?.sex === "Female" ? styles.selectedCard : styles.card
              }
            >
              <Card.Content>
                <Text style={styles.cardText}>Female</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
        </View>
      ),
    },
    {
      title: "Your Height",
      content: (
        <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
          <View
            style={[
              {
                flex: 1,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              heightFeetFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="Feet"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.heightFeet}
              onFocus={() => setHeightFeetFocused(true)}
              onBlur={() => setHeightFeetFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^[1-8]$/.test(text)) {
                  updateFormState("heightFeet", text);
                }
              }}
            />
          </View>
          <View
            style={[
              {
                flex: 1,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              heightInchesFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="Inches"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.heightInches}
              onFocus={() => setHeightInchesFocused(true)}
              onBlur={() => setHeightInchesFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^([0-9]|1[0-1])$/.test(text)) {
                  updateFormState("heightInches", text);
                }
              }}
            />
          </View>
        </View>
      ),
    },
    {
      title: "Your Bodyweight",
      content: (
        <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
          {/* Weight Input */}
          <View
            style={[
              {
                flex: 0.7,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              weightFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder={formState?.weightUnit === "kg" ? "kg" : "lbs"}
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.weight}
              onFocus={() => setWeightFocused(true)}
              onBlur={() => setWeightFocused(false)}
              onChangeText={(text) => {
                // Allow decimals and empty value
                if (text === "" || /^\d*\.?\d*$/.test(text)) {
                  updateFormState("weight", text);
                }
              }}
            />
          </View>

          {/* Unit Selector - Controls both weights */}
          <View
            style={{
              flex: 0.3,
              flexDirection: "row",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              borderRadius: scaleSize(10),
              overflow: "hidden",
            }}
          >
            <TouchableOpacity
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor:
                  formState?.weightUnit === "lbs"
                    ? "rgba(255, 255, 255, 0.3)"
                    : "transparent",
              }}
              onPress={() => {
                updateFormState("weightUnit", "lbs");
                // Also update goal weight unit to match
                updateFormState("goalWeightUnit", "lbs");
              }}
            >
              <Text
                style={{
                  fontSize: scaleSize(20),
                  color: theme.colors.primaryTextColor,
                }}
              >
                lbs
              </Text>
            </TouchableOpacity>

            <View
              style={{
                width: 1,
                backgroundColor: "rgba(255,255,255,0.2)",
              }}
            />

            <TouchableOpacity
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor:
                  formState?.weightUnit === "kg"
                    ? "rgba(255, 255, 255, 0.3)"
                    : "transparent",
              }}
              onPress={() => {
                updateFormState("weightUnit", "kg");
                // Also update goal weight unit to match
                updateFormState("goalWeightUnit", "kg");
              }}
            >
              <Text
                style={{
                  fontSize: scaleSize(20),
                  color: theme.colors.primaryTextColor,
                }}
              >
                kg
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      ),
    },
    {
      title: "Your Age",
      content: (
        <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
          <View
            style={[
              {
                flex: 1,
                borderRadius: scaleSize(10),
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
              ageFocused && {
                backgroundColor: "rgba(255, 255, 255, 0.5)",
              },
            ]}
          >
            <TextInput
              mode="flat"
              style={{
                fontSize: scaleSize(28),
                color: theme.colors.primaryTextColor,
                backgroundColor: "transparent",
                paddingVertical: scaleSize(10),
                textAlign: "center",
              }}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              placeholder="# years old"
              placeholderTextColor={theme.colors.subTextColor}
              keyboardType="numeric"
              value={formState?.age}
              onFocus={() => setAgeFocused(true)}
              onBlur={() => setAgeFocused(false)}
              onChangeText={(text) => {
                if (text === "" || /^[0-9]{1,2}$/.test(text)) {
                  updateFormState("age", text);
                }
              }}
            />
          </View>
        </View>
      ),
    },
    {
      title: "Your Body Type",
      content: (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: scaleSize(12) }}
        >
          {bodyTypes.map((bodyType) => (
            <TouchableOpacity
              key={bodyType.id}
              onPress={() =>
                updateFormState(
                  "bodyFatPercentageRange",
                  bodyType.value.toString()
                )
              }
              style={[
                formState?.bodyFatPercentageRange ===
                  bodyType.value.toString() && {
                  backgroundColor: theme.colors.primary,
                },
                {
                  borderWidth: scaleSize(1),
                  flex: 1,
                  aspectRatio: 3 / 4,
                  borderRadius: scaleSize(10),
                  justifyContent: "center",
                  alignItems: "center",
                  margin: scaleSize(10),
                  padding: scaleSize(10),
                },
              ]}
              accessibilityLabel={`Select body type: ${bodyType.description}`}
              accessibilityRole="button"
            >
              <Image
                source={bodyType.image}
                style={{
                  width: scaleSize(175),
                  height: scaleSize(200),
                  alignSelf: "center",
                }}
                contentFit="contain"
              />
              <Text
                style={[
                  styles.cardText,
                  { paddingBottom: scaleSize(12) },
                  formState?.bodyFatPercentageRange ===
                    bodyType.value.toString() && {
                    color: theme.colors.primaryTextColor,
                  },
                ]}
              >
                {bodyType.description}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      ),
    },
    {
      title: "Your Activity Level",
      content: (
        <View
          style={{
            flexDirection: "row",
            flexWrap: "wrap",
            justifyContent: "space-between",
          }}
        >
          {[
            { value: "Sedentary (BMR x 0.2)", label: "Sedentary" },
            { value: "Lightly Active (BMR x 0.375)", label: "Lightly Active" },
            {
              value: "Moderately Active (BMR x 0.5)",
              label: "Moderately Active",
            },
            { value: "Very Active (BMR x 0.9)", label: "Very Active" },
          ].map((activity) => (
            <TouchableOpacity
              key={activity.value}
              onPress={() => updateFormState("activityLevel", activity.value)}
              style={[
                {
                  width: "48%",
                  borderRadius: scaleSize(10),
                  padding: scaleSize(10),
                  paddingVertical: scaleSize(20),
                  justifyContent: "center",
                  alignItems: "center",
                  marginVertical: scaleSize(10),
                  borderWidth: scaleSize(1),
                  backgroundColor: theme.colors.surface,
                },
                formState?.activityLevel === activity.value && {
                  backgroundColor: theme.colors.primary,
                },
              ]}
              accessibilityLabel={`Select activity level: ${activity.label}`}
              accessibilityRole="button"
            >
              <Text
                style={{
                  textAlign: "center",
                  fontSize: scaleSize(14),
                  color: theme.colors.primaryTextColor,
                  paddingHorizontal: scaleSize(5),
                }}
              >
                {activity.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      ),
    },
    {
      title: "Set Your Goal Weight and Target Date",
      content: (
        <View style={{ gap: scaleSize(20) }}>
          <Text
            style={{
              fontSize: scaleSize(14),
              textAlign: "center",
              color: "gray",
              paddingHorizontal: scaleSize(8),
            }}
          >
            By setting a specific weight goal and selecting a target date, a
            calculated change in body weight per week will be determined to help
            you achieve your goal by the specified date.
          </Text>
          <View
            style={{
              alignSelf: "center",
              width: "80%",
              alignItems: "center",
              gap: scaleSize(12),
            }}
          >
            <View style={{ width: "100%", gap: scaleSize(8) }}>
              <Text style={styles.cardText}>Goal Weight</Text>
              <View style={{ flexDirection: "row", gap: scaleSize(10) }}>
                {/* Goal Weight Input */}
                <View
                  style={[
                    {
                      flex: 0.7,
                      borderRadius: scaleSize(10),
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      justifyContent: "center",
                      alignItems: "center",
                    },
                    goalWeightFocused && {
                      backgroundColor: "rgba(255, 255, 255, 0.3)",
                    },
                  ]}
                >
                  <TextInput
                    mode="flat"
                    style={{
                      fontSize: scaleSize(16),
                      color: theme.colors.primaryTextColor,
                      backgroundColor: "transparent",
                      textAlign: "center",
                      width: "100%",
                      paddingVertical: scaleSize(8),
                    }}
                    underlineColor="transparent"
                    activeUnderlineColor="transparent"
                    placeholder={formState?.weightUnit === "kg" ? "kg" : "lbs"} // Use same unit
                    placeholderTextColor={theme.colors.subTextColor}
                    keyboardType="numeric"
                    value={formState?.goalWeight}
                    onFocus={() => setGoalWeightFocused(true)}
                    onBlur={() => setGoalWeightFocused(false)}
                    onChangeText={(text) => {
                      // Allow decimals and empty value
                      if (text === "" || /^\d*\.?\d*$/.test(text)) {
                        updateFormState("goalWeight", text);
                      }
                    }}
                  />
                </View>

                {/* Static Unit Display - Matches body weight unit */}
                <View
                  style={{
                    flex: 0.3,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                    borderRadius: scaleSize(10),
                  }}
                >
                  <Text
                    style={{
                      fontSize: scaleSize(14),
                      color: theme.colors.primaryTextColor,
                      fontWeight: "bold",
                    }}
                  >
                    {formState?.weightUnit}
                  </Text>
                </View>
              </View>
            </View>

            <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
              {/* Start Date Input */}
              <View style={{ flex: 1, gap: scaleSize(8) }}>
                <Text style={styles.cardText}>Start Date</Text>
                <TouchableOpacity
                  style={[
                    {
                      borderRadius: scaleSize(10),
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      paddingVertical: scaleSize(8),
                      paddingHorizontal: scaleSize(12),
                      justifyContent: "center",
                      alignItems: "center",
                      width: "100%",
                    },
                    startCalendarVisible && {
                      backgroundColor: "rgba(255, 255, 255, 0.3)",
                    },
                  ]}
                  onPress={() => setStartCalendarVisible(true)}
                  accessibilityLabel="Select Start Date"
                  accessibilityRole="button"
                >
                  <Text style={styles.cardText}>
                    {formState?.startDate || "Select a Start Date"}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* End Date Input */}
              <View style={{ flex: 1, gap: scaleSize(8) }}>
                <Text style={styles.cardText}>End Date</Text>
                <TouchableOpacity
                  style={[
                    {
                      borderRadius: scaleSize(10),
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      paddingVertical: scaleSize(8),
                      paddingHorizontal: scaleSize(12),
                      justifyContent: "center",
                      alignItems: "center",
                      width: "100%",
                    },
                    endCalendarVisible && {
                      backgroundColor: "rgba(255, 255, 255, 0.3)",
                    },
                  ]}
                  onPress={() => setEndCalendarVisible(true)}
                  accessibilityLabel="Select End Date"
                  accessibilityRole="button"
                >
                  <Text style={styles.cardText}>
                    {formState?.endDate || "Select an End Date"}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Calendar Modal for selecting start date */}
          <Modal
            isVisible={startCalendarVisible}
            onBackdropPress={() => setStartCalendarVisible(false)}
            backdropOpacity={0.3}
            style={{ margin: 0, justifyContent: "center" }}
          >
            <View
              style={{
                backgroundColor: theme.colors.surface,
                padding: scaleSize(20),
                borderRadius: scaleSize(10),
              }}
            >
              <Calendar
                current={formState?.startDate}
                minDate={minStartDate}
                onDayPress={(day) => {
                  updateFormState("startDate", day.dateString);
                  setStartCalendarVisible(false);
                }}
                monthFormat={"MMMM yyyy"}
                hideExtraDays={true}
                disableMonthChange={true}
                firstDay={1}
                showWeekNumbers={false}
                onPressArrowLeft={(subtractMonth) => subtractMonth()}
                onPressArrowRight={(addMonth) => addMonth()}
                disableAllTouchEventsForDisabledDays={true}
                enableSwipeMonths={true}
                style={{
                  borderWidth: scaleSize(1),
                  borderColor: theme.colors.primary,
                }}
                theme={{
                  backgroundColor: theme.colors.surface,
                  calendarBackground: theme.colors.surface,
                  textSectionTitleColor: theme.colors.primaryTextColor,
                  selectedDayBackgroundColor: theme.colors.primary,
                  selectedDayTextColor: theme.colors.primaryTextColor,
                  todayTextColor: theme.colors.primaryTextColor,
                  dayTextColor: theme.colors.primaryTextColor,
                  textDisabledColor: "gray",
                  dotColor: theme.colors.primary,
                  selectedDotColor: theme.colors.primaryTextColor,
                  arrowColor: theme.colors.secondary,
                  monthTextColor: theme.colors.primaryTextColor,
                }}
                markedDates={{
                  [formState?.startDate]: {
                    selected: true,
                    marked: true,
                    selectedColor: theme.colors.primary,
                  },
                  [minStartDate]: {
                    marked: true,
                    selectedColor: theme.colors.secondary,
                  },
                }}
              />
            </View>
          </Modal>

          {/* Calendar Modal for selecting end date */}
          <Modal
            isVisible={endCalendarVisible}
            onBackdropPress={() => setEndCalendarVisible(false)}
            backdropOpacity={0.3}
            style={{ margin: 0, justifyContent: "center" }}
          >
            <View
              style={{
                backgroundColor: theme.colors.surface,
                padding: scaleSize(20),
                borderRadius: scaleSize(10),
              }}
            >
              <Calendar
                current={formState?.endDate}
                minDate={minEndDate}
                onDayPress={(day) => {
                  updateFormState("endDate", day.dateString);
                  setEndCalendarVisible(false);
                }}
                monthFormat={"MMMM yyyy"}
                hideExtraDays={true}
                disableMonthChange={true}
                firstDay={1}
                showWeekNumbers={false}
                onPressArrowLeft={(subtractMonth) => subtractMonth()}
                onPressArrowRight={(addMonth) => addMonth()}
                disableAllTouchEventsForDisabledDays={true}
                enableSwipeMonths={true}
                style={{
                  borderWidth: scaleSize(1),
                  borderColor: theme.colors.primary,
                }}
                theme={{
                  backgroundColor: theme.colors.surface,
                  calendarBackground: theme.colors.surface,
                  textSectionTitleColor: theme.colors.primaryTextColor,
                  selectedDayBackgroundColor: theme.colors.primary,
                  selectedDayTextColor: theme.colors.primaryTextColor,
                  todayTextColor: theme.colors.primaryTextColor,
                  dayTextColor: theme.colors.primaryTextColor,
                  textDisabledColor: "gray",
                  dotColor: theme.colors.primary,
                  selectedDotColor: theme.colors.primaryTextColor,
                  arrowColor: theme.colors.secondary,
                  monthTextColor: theme.colors.primaryTextColor,
                }}
                markedDates={{
                  [formState?.endDate]: {
                    selected: true,
                    marked: true,
                    selectedColor: theme.colors.primary,
                  },
                  [formState.startDate ||
                  new Date().toISOString().split("T")[0]]: {
                    marked: true,
                    selectedColor: theme.colors.secondary,
                  },
                }}
              />
            </View>
          </Modal>
        </View>
      ),
    },
    {
      title: "Your Results",
      content: (
        <ResultsSlideContent
          mode={mode}
          goal={formState?.goal}
          sex={formState?.sex}
          heightFeet={formState?.heightFeet}
          heightInches={formState?.heightInches}
          weight={formState?.weight}
          weightUnit={formState?.weightUnit}
          age={formState?.age}
          bodyFatPercentageRange={formState?.bodyFatPercentageRange}
          goalWeight={formState?.goalWeight}
          goalWeightUnit={formState?.goalWeightUnit}
          endDate={formState?.endDate}
          startDate={formState?.startDate}
          activityLevel={formState?.activityLevel}
          theme={theme}
          generatedProgram={generatedProgram}
          generatedNutritionalGoals={generatedNutritionalGoals}
          programName={formState.programName} // Pass programName
          setProgramName={(text) => updateFormState("programName", text)} // Pass setter
        />
      ),
    },
  ];

  return (
    <Modal
      isVisible={isVisible}
      style={{ flex: 1, height: "100%", width: "100%", margin: 0 }}
      avoidKeyboard={true}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView
          style={{
            flex: 1,
            flexDirection: "column",
            backgroundColor: theme.colors.screenBackground,
            alignItems: "center",
            paddingBottom: 0,
          }}
        >
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                closeModal();
              }}
              accessibilityLabel="Close Modal"
              accessibilityRole="button"
            >
              <Feather
                name="chevron-left"
                color={theme.colors.primaryTextColor}
                size={scaleSize(38)}
              />
            </TouchableOpacity>
          </View>

          <KeyboardAvoidingView style={styles.contentContainer}>
            <View
              style={{
                flex: 1,
              }}
            >
              <View
                style={{
                  flex: 1,
                  gap:
                    currentSlide > 0 && currentSlide < slides.length - 1
                      ? scaleSize(34)
                      : scaleSize(12),
                  paddingTop:
                    currentSlide > 0 && currentSlide < slides.length - 1
                      ? "5%"
                      : 0,
                }}
              >
                <Text
                  style={{
                    ...styles.label,
                    textAlign: "center",
                    paddingTop: currentSlide === slides.length - 1 ? 0 : 0,
                  }}
                >
                  {slides[currentSlide].title}
                </Text>
                <View>
                  <View>{slides[currentSlide].content}</View>
                </View>
              </View>

              <View style={styles.navigationButtons}>
                {currentSlide > 0 && currentSlide !== slides.length - 1 && (
                  <TouchableOpacity
                    style={{
                      paddingVertical: scaleSize(12),
                      paddingHorizontal: scaleSize(16),
                      borderRadius: scaleSize(16),
                      borderWidth: scaleSize(1),
                      borderColor: theme.colors.primaryTextColor,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    onPress={() => {
                      handleSlideChange(currentSlide - 1);
                    }}
                    accessibilityLabel="Go Back"
                    accessibilityRole="button"
                  >
                    <Text
                      style={{
                        color: theme.colors.primary,
                        fontSize: scaleSize(16),
                      }}
                    >
                      Back
                    </Text>
                  </TouchableOpacity>
                )}
                <View style={{ flex: 1 }} />

                {/* If we are not on the last slide, show the Next/Calculate button */}
                {currentSlide >= 0 && currentSlide < slides.length - 1 && (
                  <>
                    {/* Because the "I want to" slide was removed,
          we only need to handle the special logic for
          "Set Your Goal Weight and Target Date" => "Calculate" */}
                    {slides[currentSlide].title ===
                      "Set Your Goal Weight and Target Date" && (
                      <TouchableOpacity
                        style={{
                          paddingVertical: scaleSize(12),
                          paddingHorizontal: scaleSize(16),
                          borderRadius: scaleSize(16),
                          borderWidth: scaleSize(1),
                          borderColor: theme.colors.primaryTextColor,
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                        onPress={() => {
                          // Simple validation
                          if (
                            !formState?.goalWeight ||
                            !formState?.startDate ||
                            !formState?.endDate
                          ) {
                            setSnackbarMessage("Please provide an answer.");
                            setSnackbarVisible(true);
                            return;
                          }

                          handleGenerateNutritionalProgram();
                          handleSlideChange(currentSlide + 1);
                        }}
                        accessibilityLabel="Calculate Nutritional Program"
                        accessibilityRole="button"
                      >
                        <Text
                          style={{
                            color: theme.colors.primary,
                            fontSize: scaleSize(16),
                          }}
                        >
                          Calculate
                        </Text>
                      </TouchableOpacity>
                    )}

                    {/* For all other slides, just go to the next one (with basic validation) */}
                    {!["Set Your Goal Weight and Target Date"].includes(
                      slides[currentSlide].title
                    ) && (
                      <TouchableOpacity
                        style={{
                          paddingVertical: scaleSize(12),
                          paddingHorizontal: scaleSize(16),
                          borderRadius: scaleSize(16),
                          borderWidth: scaleSize(1),
                          borderColor: theme.colors.primaryTextColor,
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                        onPress={() => {
                          // Simple validation
                          if (
                            (currentSlide === 1 && !formState?.sex) ||
                            (currentSlide === 2 &&
                              (!formState?.heightFeet ||
                                !formState?.heightInches)) ||
                            (currentSlide === 3 && !formState?.weight) ||
                            (currentSlide === 4 && !formState?.age) ||
                            (currentSlide === 5 &&
                              !formState?.bodyFatPercentageRange) ||
                            (currentSlide === 6 && !formState?.activityLevel) ||
                            (currentSlide === 7 &&
                              (!formState?.goalWeight ||
                                !formState?.startDate ||
                                !formState?.endDate))
                          ) {
                            setSnackbarMessage("Please provide an answer.");
                            setSnackbarVisible(true);
                          } else {
                            handleSlideChange(currentSlide + 1);
                          }
                        }}
                        accessibilityLabel="Next Slide"
                        accessibilityRole="button"
                      >
                        <Text
                          style={{
                            color: theme.colors.primary,
                            fontSize: scaleSize(16),
                          }}
                        >
                          Next
                        </Text>
                      </TouchableOpacity>
                    )}
                  </>
                )}

                {/* If we are on the last slide ("Your Results"), show final action buttons */}
                {currentSlide === slides.length - 1 && (
                  <>
                    {formState?.goalWeight &&
                      formState?.startDate &&
                      formState?.endDate && (
                        <TouchableOpacity
                          style={{
                            paddingVertical: scaleSize(12),
                            paddingHorizontal: scaleSize(16),
                            borderRadius: scaleSize(16),
                            borderWidth: scaleSize(1),
                            borderColor: theme.colors.primaryTextColor,
                            backgroundColor: theme.colors.screenBackground,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                          onPress={handleSetAsMyNutritionalProgram}
                          accessibilityLabel="Set as My Nutritional Program"
                          accessibilityRole="button"
                        >
                          <Text
                            style={{
                              color: theme.colors.primary,
                              fontSize: scaleSize(16),
                            }}
                          >
                            Set as My Nutritional Program
                          </Text>
                        </TouchableOpacity>
                      )}
                  </>
                )}
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </TouchableWithoutFeedback>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: "OK",
          onPress: () => {},
          labelStyle: {
            fontSize: scaleSize(16),
            paddingVertical: scaleSize(8), // Added vertical padding
            color: theme.colors.primary, // Use primary color
          },
          style: {
            paddingHorizontal: scaleSize(12), // Add horizontal padding
          },
        }}
        style={{
          backgroundColor: theme.colors.surface,
          paddingVertical: scaleSize(12), // Increased vertical padding
          minHeight: scaleSize(56), // Ensure minimum height
          alignItems: "center", // Center content vertically
          justifyContent: "center", // Center content horizontally
        }}
        wrapperStyle={{
          bottom: scaleSize(80), // Adjust position if needed
        }}
      >
        <Text
          style={{
            fontSize: scaleSize(16),
            color: theme.colors.primaryTextColor,
            textAlign: "center",
            lineHeight: scaleSize(24), // Add line height for vertical spacing
            paddingHorizontal: scaleSize(8), // Add horizontal padding
          }}
          numberOfLines={2} // Allow text to wrap
        >
          {snackbarMessage}
        </Text>
      </Snackbar>
    </Modal>
  );
};

export default BasicProgramModal;
