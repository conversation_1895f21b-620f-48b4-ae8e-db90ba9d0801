import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { Card } from "react-native-paper";
import Feather from "react-native-vector-icons/Feather";
import { useNavigation } from "@react-navigation/native";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { isTablet, scaleSize } from "../../../utils/deviceUtils.js";
const NutritionalProgramCard = ({ theme }) => {
  const tablet = isTablet(); // Check if the device is a tablet
  const navigation = useNavigation();
  const { activeNutritionalProgram } = useNutritionProgram();

  // Responsive sizing
  const imageSize = scaleSize(45);
  const titleFontSize = scaleSize(16);
  const subtitleFontSize = scaleSize(14);
  const cardPadding = scaleSize(8);
  const iconSize = scaleSize(24);

  return (
    <View
      style={{
        width: "100%",
        paddingHorizontal: scaleSize(6),
      }}
    >
      <Card
        style={{
          padding: cardPadding,
          borderRadius: scaleSize(8), // Larger border radius on tablet
        }}
      >
        <Card.Content>
          <TouchableOpacity
            onPress={() => navigation.navigate("Nutritional Program")}
            style={{
              flexDirection: "row",
              alignItems: "center", // Ensure vertical alignment
            }}
          >
            {/* Left content */}
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                gap: scaleSize(10), // Larger gap on tablet
              }}
            >
              <Image
                source={require("../../../../assets/NutritionalProgramIcon.png")}
                style={{
                  width: imageSize,
                  height: imageSize,
                }}
                resizeMode="contain"
              />

              <View
                style={{
                  flex: 1,
                  rowGap: scaleSize(5), // Adjust gap based on device
                }}
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    fontSize: titleFontSize,
                    fontWeight: "600",
                  }}
                >
                  Nutritional Program
                </Text>

                {activeNutritionalProgram ? (
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: subtitleFontSize,
                      lineHeight: scaleSize(18), // Better line height on tablet
                    }}
                    numberOfLines={tablet ? 2 : 1} // Allow wrapping on tablet
                  >
                    Active Program:{" "}
                    {activeNutritionalProgram?.programName ||
                      "Untitled Program"}
                  </Text>
                ) : (
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: subtitleFontSize,
                    }}
                  >
                    No active program yet
                  </Text>
                )}
              </View>
            </View>

            {/* Right icon */}
            <Feather name="chevron-right" color="gray" size={iconSize} />
          </TouchableOpacity>
        </Card.Content>
      </Card>
    </View>
  );
};

export default NutritionalProgramCard;
