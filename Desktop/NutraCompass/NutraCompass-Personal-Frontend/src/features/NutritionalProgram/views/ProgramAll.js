// ProgramAll.js

import React, { useEffect, useState, useMemo } from "react";
import {
  View,
  Text,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import ProgramCard from "./components/ProgramCard"; // Assuming ProgramCard remains separate
import { scaleSize } from "../../../utils/deviceUtils.js";

const ProgramAll = () => {
  const { theme } = useThemeContext();
  const {
    activeNutritionalProgram,
    inactiveNutritionalPrograms,
    activateNutritionalProgram,
    deactivateNutritionalProgram,
    deleteNutritionalProgram,
    deleteNutritionalProgramsBatch,
  } = useNutritionProgram();

  // Ensure inactiveNutritionalPrograms is always an array
  const inactivePrograms = Array.isArray(inactiveNutritionalPrograms)
    ? inactiveNutritionalPrograms
    : [];

  // State to track selected programs
  const [selectedProgramIds, setSelectedProgramIds] = useState(new Set());

  // State to manage loading state centrally
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);

  // State to manage the number of inactive programs displayed
  const [inactiveProgramsToShow, setInactiveProgramsToShow] = useState(3);

  /**
   * Calculates the completion percentage based solely on checkpoint inputs.
   *
   * For this calculation, the entire grade (100%) is based on whether each
   * checkpoint has an input (for example, a valid numerical 'userLoggedWeight').
   * If no checkpoints exist, 0% is returned.
   *
   * @param {Object} program - The program object containing a list of checkpoints.
   * @returns {string} - The completion percentage as a string (e.g., "75%").
   */
  const calculateCompletionPercentage = (program) => {
    if (!program.checkpoints || !Array.isArray(program.checkpoints)) {
      return "0%";
    }

    const totalCheckpoints = program.checkpoints.length;
    if (totalCheckpoints === 0) return "0%";

    let completedCheckpoints = 0;

    program.checkpoints.forEach((checkpoint) => {
      // Consider a checkpoint completed if there is a valid number input.
      if (
        checkpoint.userLoggedWeight !== undefined &&
        typeof checkpoint.userLoggedWeight === "number" &&
        !isNaN(checkpoint.userLoggedWeight)
      ) {
        completedCheckpoints += 1;
      }
    });

    const percent = (completedCheckpoints / totalCheckpoints) * 100;
    return `${percent.toFixed(0)}%`;
  };

  /** Toggle selection of a program */
  const toggleSelectProgram = (programId) => {
    if (isGlobalLoading) return; // Prevent selection during loading
    const updatedSelected = new Set(selectedProgramIds);
    if (updatedSelected.has(programId)) {
      updatedSelected.delete(programId);
    } else {
      updatedSelected.add(programId);
    }
    setSelectedProgramIds(updatedSelected);
  };

  /** ------------------- Custom Alert Implementation ------------------- */

  // Alert State Management
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertTitle, setAlertTitle] = useState("");
  const [alertMessage, setAlertMessage] = useState("");
  const [alertOnConfirm, setAlertOnConfirm] = useState(null);
  const [alertOnCancel, setAlertOnCancel] = useState(null);
  const [alertType, setAlertType] = useState("confirmation"); // 'confirmation', 'error', 'success', 'info'
  const [alertConfirmText, setAlertConfirmText] = useState("OK");
  const [alertCancelText, setAlertCancelText] = useState("Cancel");

  /** Function to Show Custom Alert */
  const showAlert = ({
    title,
    message,
    onConfirm,
    onCancel,
    type = "confirmation",
    confirmText = "OK",
    cancelText = "Cancel",
  }) => {
    setAlertTitle(title);
    setAlertMessage(message);
    setAlertOnConfirm(() => onConfirm);
    setAlertOnCancel(() => onCancel);
    setAlertType(type);
    setAlertConfirmText(confirmText);
    setAlertCancelText(cancelText);
    setAlertVisible(true);
  };

  /** Handle Deactivation with Custom Alert */
  const handleDeactivate = (programId) => {
    if (isGlobalLoading) return; // Prevent action during loading
    showAlert({
      title: "Confirm Deactivation",
      message: "Are you sure you want to deactivate the selected program?",
      onConfirm: () => {
        setIsGlobalLoading(true);
        deactivateNutritionalProgram(programId)
          .then(() => {
            setIsGlobalLoading(false);
            setSelectedProgramIds((prev) => {
              const updated = new Set(prev);
              updated.delete(programId);
              return updated;
            });
            // showAlert({
            //   title: "Success",
            //   message: "Program deactivated successfully.",
            //   type: "success",
            //   confirmText: "OK",
            // });
          })
          .catch(() => {
            showAlert({
              title: "Error",
              message: "Failed to deactivate the program. Please try again.",
              type: "error",
              confirmText: "OK",
            });
            setIsGlobalLoading(false);
          });
      },
      onCancel: () => {}, // Optional: No action on cancel
      type: "confirmation",
      confirmText: "Deactivate",
      cancelText: "Cancel",
    });
  };

  /** Handle Activation with Custom Alert */
  const handleActivate = (program) => {
    if (isGlobalLoading) return; // Prevent action during loading
    showAlert({
      title: "Confirm Activation",
      message: "Are you sure you want to activate the selected program?",
      onConfirm: () => {
        setIsGlobalLoading(true);
        activateNutritionalProgram(program)
          .then(() => {
            setIsGlobalLoading(false);
            setSelectedProgramIds((prev) => {
              const updated = new Set(prev);
              updated.delete(program.programId);
              return updated;
            });
            // showAlert({
            //   title: "Success",
            //   message: "Program activated successfully.",
            //   type: "success",
            //   confirmText: "OK",
            // });
          })
          .catch(() => {
            showAlert({
              title: "Error",
              message: "Failed to activate the program. Please try again.",
              type: "error",
              confirmText: "OK",
            });
            setIsGlobalLoading(false);
          });
      },
      onCancel: () => {},
      type: "confirmation",
      confirmText: "Activate",
      cancelText: "Cancel",
    });
  };

  /** Handle Deletion with Custom Alert */
  const handleDelete = (programIds) => {
    if (isGlobalLoading) return; // Prevent action during loading
    showAlert({
      title: "Confirm Deletion",
      message:
        "Are you sure you want to delete the selected program(s)? This action cannot be undone.",
      onConfirm: () => {
        setIsGlobalLoading(true);
        // Call the batch delete method from context
        deleteNutritionalProgramsBatch(programIds)
          .then(() => {
            setIsGlobalLoading(false);
            setSelectedProgramIds(new Set());
            // showAlert({
            //   title: "Success",
            //   message: "Selected program(s) deleted successfully.",
            //   type: "success",
            //   confirmText: "OK",
            // });
          })
          .catch(() => {
            showAlert({
              title: "Error",
              message:
                "Failed to delete selected program(s). Please try again.",
              type: "error",
              confirmText: "OK",
            });
            setIsGlobalLoading(false);
          });
      },
      onCancel: () => {},
      type: "confirmation",
      confirmText: "Delete",
      cancelText: "Cancel",
    });
  };

  /** Handle View Action with Custom Alert */
  const handleView = (program) => {
    showAlert({
      title: "View Program",
      message: `Viewing details for ${
        program.programName || "Untitled Program"
      }`,
      onConfirm: () => {
        // Implement navigation to Program Details if necessary
        // For now, simply close the alert
      },
      onCancel: () => {},
      type: "info",
      confirmText: "OK",
      cancelText: null, // No cancel button for info alerts
    });
  };

  /** Determine action buttons based on selection within a section */
  const getActionButtonsForSection = (section) => {
    const selectedIds = Array.from(selectedProgramIds).filter((id) =>
      section.data.some((prog) => prog.programId === id)
    );
    const isActiveSection = section.title === "Active Program";
    const isInactiveSection = section.title === "Inactive Programs";

    if (isActiveSection) {
      if (selectedIds.length === 1) {
        return (
          <View style={styles.headerActionsContainer}>
            <ActionButton
              actionType="deactivate"
              onPress={() =>
                handleDeactivate(selectedIds.values().next().value)
              }
              isDisabled={isGlobalLoading}
              theme={theme}
            />
          </View>
        );
      }
    }

    if (isInactiveSection) {
      if (selectedIds.length === 1) {
        const selectedProg = inactivePrograms.find(
          (prog) => prog.programId === selectedIds[0]
        );
        return (
          <View style={styles.headerActionsContainer}>
            {/* <ActionButton
              actionType="activate"
              onPress={() => handleActivate(selectedProg)}
              isDisabled={isGlobalLoading}
              theme={theme}
            /> */}
            <ActionButton
              actionType="delete"
              onPress={() => handleDelete(selectedIds)}
              isDisabled={isGlobalLoading}
              theme={theme}
            />
            {/* <ActionButton
              actionType="view"
              onPress={() => handleView(selectedProg)}
              isDisabled={isGlobalLoading}
              theme={theme}
            /> */}
          </View>
        );
      }

      if (selectedIds.length > 1) {
        return (
          <View style={styles.headerActionsContainer}>
            <ActionButton
              actionType="delete"
              onPress={() => handleDelete(Array.from(selectedIds))}
              isDisabled={isGlobalLoading}
              theme={theme}
            />
          </View>
        );
      }
    }

    return null;
  };

  /** Prepare sections for SectionList */
  const sections = [
    {
      title: "Active Program",
      data: activeNutritionalProgram ? [activeNutritionalProgram] : [],
    },
    {
      title: "Inactive Programs",
      data: inactivePrograms.slice(0, inactiveProgramsToShow),
    },
  ];

  /** Render Section Header with Action Buttons */
  const renderSectionHeader = ({ section }) => (
    <View style={styles.sectionHeader}>
      <Text
        style={[styles.headerText, { color: theme.colors.primaryTextColor }]}
      >
        {section.title}
      </Text>
      {getActionButtonsForSection(section)}
    </View>
  );

  /** Render each item in SectionList */
  const renderItem = ({ item, section }) => {
    const isSelected = selectedProgramIds.has(item.programId);
    const completionPercentage = calculateCompletionPercentage(item);

    return (
      <ProgramCard
        program={{ ...item, completionPercentage }}
        isSelected={isSelected}
        onToggleSelect={toggleSelectProgram}
        theme={theme}
      />
    );
  };

  /** Handle "More/Less" Button Press */
  const toggleMoreLess = () => {
    if (inactiveProgramsToShow === 3) {
      setInactiveProgramsToShow(inactivePrograms.length);
    } else {
      setInactiveProgramsToShow(3);
    }
  };

  /** Determine if "More/Less" Button Should Be Shown */
  const shouldShowMoreLess = inactivePrograms.length > 3;

  /** Determine Button Label */
  const moreLessLabel = inactiveProgramsToShow === 3 ? "More" : "Less";

  /** Render List Footer: "More/Less" Button */
  const renderListFooter = () => (
    <View>
      {shouldShowMoreLess && (
        <TouchableOpacity
          style={[
            styles.moreLessButton,
            { backgroundColor: theme.colors.primary },
          ]}
          onPress={toggleMoreLess}
          accessibilityLabel={`${moreLessLabel} Button`}
          accessibilityHint={`Shows ${
            moreLessLabel === "More" ? "all" : "fewer"
          } inactive programs`}
          disabled={isGlobalLoading}
        >
          <Text
            style={[
              styles.moreLessText,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            {moreLessLabel}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  /** Key Extractor */
  const keyExtractor = (item) => item.programId;

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: theme.colors.screenBackground },
      ]}
    >
      {/* Centralized Loading Indicator */}
      {/* {isGlobalLoading && (
        <View style={styles.globalLoadingOverlay} pointerEvents="auto">
          <View
            style={[
              styles.loadingContainer,
              { backgroundColor: theme.colors.primaryTextColor },
            ]}
          >
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text
              style={[
                styles.loadingText,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              Processing...
            </Text>
          </View>
        </View>
      )} */}

      {/* Custom Alert */}
      <CustomAlertComponent
        visible={alertVisible}
        title={alertTitle}
        message={alertMessage}
        onConfirm={() => {
          if (alertOnConfirm) alertOnConfirm();
          setAlertVisible(false);
        }}
        onCancel={() => {
          if (alertOnCancel) alertOnCancel();
          setAlertVisible(false);
        }}
        confirmText={alertConfirmText}
        cancelText={alertCancelText}
        type={alertType}
        theme={theme}
      />

      <SectionList
        sections={sections}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        renderSectionHeader={renderSectionHeader}
        ListFooterComponent={renderListFooter}
        stickySectionHeadersEnabled={false}
        contentContainerStyle={{
          paddingBottom: scaleSize(100),
          paddingHorizontal: scaleSize(16),
        }}
        // Disable interactions when loading
        scrollEnabled={!isGlobalLoading}
      />
    </View>
  );
};

/** ----------------------- ActionButton Component ----------------------- */

/**
 * Inline ActionButton component to replace HeaderActions.js
 * This component is defined within ProgramAll.js for simplicity.
 */
const ActionButton = ({ actionType, onPress, isDisabled, theme }) => {
  const getIcon = () => {
    switch (actionType) {
      case "activate":
        return "check-circle-outline";
      case "deactivate":
        return "minus-circle-outline";
      case "delete":
        return "delete-outline";
      case "view":
        return "eye-outline";
      default:
        return "dots-horizontal";
    }
  };

  const getLabel = () => {
    switch (actionType) {
      case "activate":
        return "Activate";
      case "deactivate":
        return "Deactivate";
      case "delete":
        return "Delete";
      case "view":
        return "View";
      default:
        return "Action";
    }
  };

  const getButtonStyle = () => {
    switch (actionType) {
      case "activate":
        return { backgroundColor: "#4CAF50" }; // Green
      case "deactivate":
        return { backgroundColor: "#FFC107" }; // Amber
      case "delete":
        return { backgroundColor: "#F44336" }; // Red
      case "view":
        return { backgroundColor: "#2196F3" }; // Blue
      default:
        return { backgroundColor: "#9E9E9E" }; // Grey
    }
  };

  return (
    <TouchableOpacity
      style={[styles.actionButton, getButtonStyle()]}
      onPress={onPress}
      disabled={isDisabled}
      accessibilityLabel={`${getLabel()} Button`}
      accessibilityHint={`Performs the ${getLabel()} action`}
    >
      <MaterialCommunityIcons
        name={getIcon()}
        size={scaleSize(16)}
        color={theme.colors.primaryTextColor}
      />
      <Text
        style={[
          styles.actionButtonText,
          { color: theme.colors.primaryTextColor },
        ]}
      >
        {getLabel()}
      </Text>
    </TouchableOpacity>
  );
};

/** ----------------------- CustomAlertComponent ----------------------- */

const CustomAlertComponent = ({
  visible,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText,
  cancelText,
  type, // 'confirmation', 'error', 'success', 'info'
  theme,
}) => {
  // Animation for alert appearance
  const scaleValue = useMemo(() => new Animated.Value(0), []);

  useEffect(() => {
    if (visible) {
      Animated.spring(scaleValue, {
        toValue: 1,
        friction: 5,
        useNativeDriver: true,
      }).start();
    } else {
      scaleValue.setValue(0);
    }
  }, [visible, scaleValue]);

  // Determine styles based on alert type
  const getTypeStyles = () => {
    switch (type) {
      case "error":
        return { backgroundColor: "#F44336" }; // Red
      case "success":
        return { backgroundColor: "#4CAF50" }; // Green
      case "warning":
        return { backgroundColor: "#FFC107" }; // Amber
      case "info":
        return { backgroundColor: "#2196F3" }; // Blue
      default:
        return { backgroundColor: theme.colors.surface };
    }
  };

  return (
    <Modal
      transparent
      animationType="none"
      visible={visible}
      onRequestClose={onCancel}
    >
      <View style={styles.modalBackground}>
        <Animated.View
          style={[
            styles.alertContainer,
            getTypeStyles(),
            { transform: [{ scale: scaleValue }] },
          ]}
        >
          {title ? (
            <Text
              style={[styles.title, { color: theme.colors.primaryTextColor }]}
            >
              {title}
            </Text>
          ) : null}
          {message ? (
            <Text
              style={[styles.message, { color: theme.colors.primaryTextColor }]}
            >
              {message}
            </Text>
          ) : null}
          <View style={styles.alertButtonContainer}>
            {cancelText && onCancel ? (
              <TouchableOpacity
                style={[styles.alertButton, styles.cancelButton]}
                onPress={onCancel}
                accessibilityLabel={`${cancelText} Button`}
                accessibilityHint="Cancels the action"
              >
                <Text
                  style={[
                    styles.alertButtonText,
                    { color: theme.colors.primaryTextColor },
                  ]}
                >
                  {cancelText}
                </Text>
              </TouchableOpacity>
            ) : null}
            {confirmText && onConfirm ? (
              <TouchableOpacity
                style={[styles.alertButton, styles.confirmButton]}
                onPress={onConfirm}
                accessibilityLabel={`${confirmText} Button`}
                accessibilityHint="Confirms the action"
              >
                <Text
                  style={[
                    styles.alertButtonText,
                    { color: theme.colors.primaryTextColor },
                  ]}
                >
                  {confirmText}
                </Text>
              </TouchableOpacity>
            ) : null}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

/** ----------------------- Styles ----------------------- */

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: scaleSize(16), // Increased padding in top-level container
    paddingHorizontal: 0, // Increased padding for left/right
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: scaleSize(12), // Increased margin for better separation
    paddingVertical: scaleSize(8), // Added vertical padding
  },
  headerText: {
    fontWeight: "bold",
    fontSize: scaleSize(16), // Increased font size for better readability
  },
  headerActionsContainer: {
    flexDirection: "row",
    gap: scaleSize(8), // Reduced gap between buttons
  },
  actionButton: {
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: scaleSize(4), // Reduced vertical padding
    paddingHorizontal: scaleSize(8), // Reduced horizontal padding
    borderRadius: 4, // Reduced border radius for smaller buttons
    gap: scaleSize(2),
  },
  actionButtonText: {
    fontSize: scaleSize(12), // Reduced font size
    fontWeight: "bold",
    marginLeft: scaleSize(4), // Reduced margin between icon and text
  },
  moreLessButton: {
    alignSelf: "center",
    borderRadius: 6,
    paddingVertical: scaleSize(8), // Adjusted padding for better touch targets
    paddingHorizontal: scaleSize(16),
    marginBottom: scaleSize(12),
  },
  moreLessText: {
    fontSize: scaleSize(14), // Slightly increased font size
    fontWeight: "bold",
  },
  globalLoadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  loadingContainer: {
    padding: scaleSize(20),
    borderRadius: 8,
    alignItems: "center",
    flexDirection: "row",
    gap: scaleSize(12),
  },
  loadingText: {
    fontSize: scaleSize(16),
    fontWeight: "bold",
  },
  /** Custom Alert Styles */
  modalBackground: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  alertContainer: {
    width: "80%",
    padding: scaleSize(20),
    borderRadius: 10,
    alignItems: "center",
  },
  title: {
    fontSize: scaleSize(18),
    fontWeight: "bold",
    marginBottom: scaleSize(10),
  },
  message: {
    fontSize: scaleSize(16),
    textAlign: "center",
    marginBottom: scaleSize(20),
  },
  alertButtonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    width: "100%",
  },
  alertButton: {
    paddingVertical: scaleSize(8), // Reduced vertical padding
    paddingHorizontal: scaleSize(16), // Adjusted horizontal padding
    borderRadius: scaleSize(5),
    marginLeft: scaleSize(10),
  },
  confirmButton: {
    backgroundColor: "#4CAF50", // Green
  },
  cancelButton: {
    backgroundColor: "#F44336", // Red
  },
  alertButtonText: {
    fontSize: scaleSize(14), // Reduced font size
    fontWeight: "bold",
  },
});

export default ProgramAll;
