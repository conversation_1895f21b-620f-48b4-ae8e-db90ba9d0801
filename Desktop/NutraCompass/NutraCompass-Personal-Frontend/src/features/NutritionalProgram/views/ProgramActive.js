// ProgramActive.js

import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  Modal,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import ProgramTimelineLineChart from "../components/ProgramTimelineLineChart.js";
import * as Haptics from "expo-haptics";

import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { scaleSize } from "../../../utils/deviceUtils.js";

const { height, width } = Dimensions.get("window");

const ProgramActive = () => {
  const { theme } = useThemeContext();
  const {
    activeNutritionalProgram,
    deactivateNutritionalProgram,
    isNutritionalGoalsAlignmentNeeded,
    programStarted,
    hasShownCongrats,
  } = useNutritionProgram();
  const { setCalorieAndMacroGoals, getUserMeasurementSettings } =
    useUserSettings();
  const { bodyWeightUnit } = getUserMeasurementSettings();

  const [showConfirmation, setShowConfirmation] = useState(false);

  // If there's no program, show "No Active Program" screen
  if (!activeNutritionalProgram) {
    return (
      <View
        style={[
          styles.noProgramContainer,
          { backgroundColor: theme.colors.screenBackground },
        ]}
      >
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={64}
          color="gray"
        />
        <Text style={styles.noProgramTitle}>No Active Program</Text>
        <Text style={styles.noProgramSubtitle}>
          You currently have no active program. Create one from the menu above!
        </Text>
      </View>
    );
  }

  // If we've triggered 'hasShownCongrats', show a quick "Congratulations" screen/message.
  // Typically, the context effect will call deactivateNutritionalProgram() soon after
  // setting this to true, but you can delay that or let user dismiss it if you'd like.
  if (hasShownCongrats) {
    return (
      <View
        style={[
          styles.congratsContainer,
          { backgroundColor: theme.colors.screenBackground },
        ]}
      >
        <MaterialCommunityIcons
          name="trophy"
          size={scaleSize(64)}
          color="#FFD700" // "Gold" color
        />
        <Text style={styles.congratsTitle}>Congratulations!</Text>
        <Text style={styles.congratsSubtitle}>
          You’ve completed your nutritional program!
        </Text>
        {/* Optionally, a button if you want the user to manually close/dismiss */}
      </View>
    );
  }

  // Otherwise, show normal Program UI

  const handleProgramDeactivation = async () => {
    try {
      setShowConfirmation(false); // Close confirmation

      const response = await deactivateNutritionalProgram(
        activeNutritionalProgram?.programId
      );
      if (response) {
        console.log("Program deactivated successfully.");
      }
    } catch (error) {
      console.error("Failed to deactivate the program:", error);
    }
  };

  const handleAlignGoals = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const { calorieGoal, macroGoals } =
        activeNutritionalProgram.nutritionalGoals;
      await setCalorieAndMacroGoals({
        calorieGoal: calorieGoal || 0,
        proteinPercentage: (macroGoals?.protein?.dailyPercentage || 0) * 100,
        carbPercentage: (macroGoals?.carb?.dailyPercentage || 0) * 100,
        fatPercentage: (macroGoals?.fat?.dailyPercentage || 0) * 100,
      });

      console.log("Aligned user goals with program!");
    } catch (error) {
      console.error("Error aligning nutritional goals:", error);
    }
  };

  const headingText = programStarted
    ? "Your Active Nutritional Program"
    : "Upcoming Program";

  const startDate = new Date(activeNutritionalProgram.startDate);
  const endDate = new Date(activeNutritionalProgram.endDate);
  const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

  const { calorieGoal, macroGoals } = activeNutritionalProgram.nutritionalGoals;

  // Helper function to convert and format weight
  const formatWeight = (weightInLbs) => {
    if (weightInLbs === null || weightInLbs === undefined) return "N/A";

    if (bodyWeightUnit === "kg") {
      const kgValue = weightInLbs / 2.20462;
      return `${kgValue.toFixed(1)} kg`;
    }
    return `${weightInLbs.toFixed(1)} lbs`;
  };

  // Calculate Net Change in Bodyweight
  const netChange =
    activeNutritionalProgram.goalWeight - activeNutritionalProgram.startWeight;

  // Format net change based on user's unit preference
  const formatNetChange = (change) => {
    if (bodyWeightUnit === "kg") {
      const kgChange = Math.abs(change) / 2.20462;
      return `${kgChange.toFixed(1)} kg`;
    }
    return `${Math.abs(change).toFixed(1)} lbs`;
  };

  const netChangeText =
    netChange < 0
      ? `Lose ${formatNetChange(netChange)}`
      : netChange > 0
      ? `Gain ${formatNetChange(netChange)}`
      : "Maintain Current Weight";

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={[
        styles.scrollViewContent,
        { backgroundColor: theme.colors.screenBackground },
      ]}
    >
      {/* Upcoming Program Notice */}
      {!programStarted && (
        <View
          style={[
            styles.upcomingProgramContainer,
            { marginTop: 0, paddingBottom: scaleSize(8) },
          ]}
        >
          <MaterialCommunityIcons
            name="clock-outline"
            size={scaleSize(24)}
            color="#FFA000"
            style={{ marginRight: scaleSize(8) }}
          />
          <Text style={styles.upcomingProgramText}>
            This program hasn’t started yet.
          </Text>
        </View>
      )}

      {/* Dashboard Grid */}
      <View style={styles.dashboardGrid}>
        {/* Program Overview Card */}
        <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.cardHeader}>
            <MaterialCommunityIcons
              name="clipboard-check-outline"
              size={scaleSize(24)}
              color={theme.colors.primary}
            />
            <Text
              style={[
                styles.cardTitle,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              Overview
            </Text>
          </View>
          <View style={styles.cardContent}>
            <Text
              style={[styles.cardLabel, { color: theme.colors.subTextColor }]}
            >
              Program Name:
            </Text>
            <Text
              style={[
                styles.cardValue,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {activeNutritionalProgram?.programName || "Untitled Program"}
            </Text>

            <Text
              style={[styles.cardLabel, { color: theme.colors.subTextColor }]}
            >
              Program Type:
            </Text>
            <Text
              style={[
                styles.cardValue,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {activeNutritionalProgram?.programType || "N/A"}
            </Text>

            <Text
              style={[styles.cardLabel, { color: theme.colors.subTextColor }]}
            >
              Duration:
            </Text>
            <Text
              style={[
                styles.cardValue,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {totalDays} days
            </Text>

            <Text
              style={[styles.cardLabel, { color: theme.colors.subTextColor }]}
            >
              Start Weight:
            </Text>
            <Text
              style={[
                styles.cardValue,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {activeNutritionalProgram?.startWeight
                ? formatWeight(activeNutritionalProgram.startWeight)
                : "N/A"}
            </Text>

            <Text
              style={[styles.cardLabel, { color: theme.colors.subTextColor }]}
            >
              Goal Weight:
            </Text>
            <Text
              style={[
                styles.cardValue,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {activeNutritionalProgram?.goalWeight
                ? formatWeight(activeNutritionalProgram.goalWeight)
                : "N/A"}
            </Text>

            <Text
              style={[styles.cardLabel, { color: theme.colors.subTextColor }]}
            >
              Net Change:
            </Text>
            <Text
              style={[
                styles.cardValue,
                {
                  color:
                    netChange < 0
                      ? "#F44336"
                      : netChange > 0
                      ? "#4CAF50"
                      : theme.colors.primaryTextColor,
                },
              ]}
            >
              {netChangeText}
            </Text>
          </View>
        </View>

        <View style={{ gap: 8 }}>
          {/* Nutritional Goals Card */}
          <View
            style={[styles.card, { backgroundColor: theme.colors.surface }]}
          >
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons
                name="nutrition"
                size={scaleSize(24)}
                color={theme.colors.primary}
              />
              <Text
                style={[
                  styles.cardTitle,
                  { color: theme.colors.primaryTextColor },
                ]}
              >
                {`Nutritional\nGoals`}
              </Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.goalsColumn}>
                <View style={styles.goalItem}>
                  <MaterialCommunityIcons
                    name="fire"
                    size={scaleSize(20)}
                    color="#FF9800"
                  />
                  <Text
                    style={[
                      styles.goalLabel,
                      { color: theme.colors.primaryTextColor },
                    ]}
                  >
                    {calorieGoal?.toLocaleString()} cal
                  </Text>
                </View>
                <View style={styles.goalItem}>
                  <MaterialCommunityIcons
                    name="corn"
                    size={scaleSize(20)}
                    color="#2196F3"
                  />
                  <Text
                    style={[
                      styles.goalLabel,
                      { color: theme.colors.primaryTextColor },
                    ]}
                  >
                    {macroGoals?.carb?.dailyGrams}g Carbs
                  </Text>
                </View>
                <View style={styles.goalItem}>
                  <MaterialCommunityIcons
                    name="food-steak"
                    size={scaleSize(20)}
                    color="#4CAF50"
                  />
                  <Text
                    style={[
                      styles.goalLabel,
                      { color: theme.colors.primaryTextColor },
                    ]}
                  >
                    {macroGoals?.protein?.dailyGrams}g Protein
                  </Text>
                </View>
                <View style={styles.goalItem}>
                  <MaterialCommunityIcons
                    name="sausage"
                    size={scaleSize(20)}
                    color="#FF5722"
                  />
                  <Text
                    style={[
                      styles.goalLabel,
                      { color: theme.colors.primaryTextColor },
                    ]}
                  >
                    {macroGoals?.fat?.dailyGrams}g Fat
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                onPress={handleAlignGoals}
                disabled={!isNutritionalGoalsAlignmentNeeded}
                style={[
                  styles.alignButton,
                  {
                    backgroundColor: isNutritionalGoalsAlignmentNeeded
                      ? theme.colors.primary
                      : theme.colors.disabledButton || "gray",
                  },
                ]}
                accessibilityLabel="Align Goals"
                accessibilityRole="button"
              >
                <Text
                  style={[
                    styles.alignButtonText,
                    { color: theme.colors.primaryTextColor },
                  ]}
                >
                  {isNutritionalGoalsAlignmentNeeded
                    ? "Align Goals"
                    : "Goals Aligned"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Action Card */}
          <View
            style={[
              styles.card,
              { backgroundColor: theme.colors.surface, width: "100%" },
            ]}
          >
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons
                name="cog"
                size={scaleSize(24)}
                color={theme.colors.primary}
              />
              <Text
                style={[
                  styles.cardTitle,
                  { color: theme.colors.primaryTextColor },
                ]}
              >
                Actions
              </Text>
            </View>
            <View style={styles.actionButtonsContainer}>
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  {
                    backgroundColor: theme.colors.deactivateButton || "#F44336",
                  },
                ]}
                onPress={() => setShowConfirmation(true)} // Show confirmation instead
                accessibilityLabel="End Program"
                accessibilityRole="button"
              >
                <MaterialCommunityIcons
                  name="close-circle-outline"
                  size={scaleSize(20)}
                  color={theme.colors.primaryTextColor}
                  style={{ marginRight: scaleSize(6) }}
                />
                <Text
                  style={[
                    styles.actionButtonText,
                    { color: theme.colors.primaryTextColor || "#fff" },
                  ]}
                >
                  End Program
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Progress Chart Card */}
        <View
          style={[
            styles.card,
            styles.chartCard,
            { backgroundColor: theme.colors.surface },
          ]}
        >
          <View style={styles.cardHeader}>
            <MaterialCommunityIcons
              name="chart-line"
              size={scaleSize(24)}
              color={theme.colors.primary}
            />
            <Text
              style={[
                styles.cardTitle,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              Body Weight Timeline & Checkpoints
            </Text>
          </View>
          <View style={styles.chartContainer}>
            <Text
              style={{
                color: theme.colors.subTextColor,
                fontSize: scaleSize(12),
                paddingBottom: scaleSize(12),
                alignSelf: "center",
                textAlign: "center",
              }}
            >
              Shows your projected body weight over time and checkpoint
              completion.
            </Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 0 }}
            >
              {/* Use the custom ProgramTimelineLineChart component */}
              <ProgramTimelineLineChart
                activeNutritionalProgram={activeNutritionalProgram}
                theme={theme}
              />
            </ScrollView>
          </View>
        </View>
      </View>

      {/* Upcoming Program Notice */}
      {!programStarted && (
        <View style={styles.upcomingProgramContainer}>
          <MaterialCommunityIcons
            name="clock-outline"
            size={scaleSize(24)}
            color="#FFA000"
            style={{ marginRight: scaleSize(8) }}
          />
          <Text style={styles.upcomingProgramText}>
            This program hasn’t started yet.
          </Text>
        </View>
      )}

      <Modal
        transparent={true}
        visible={showConfirmation}
        animationType="fade"
        onRequestClose={() => setShowConfirmation(false)}
      >
        <View
          style={{
            flex: 1,
            backgroundColor: "rgba(0,0,0,0.5)",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <View
            style={{
              backgroundColor: theme.colors.screenBackground,
              borderRadius: scaleSize(12),
              padding: scaleSize(20),
              width: "80%",
              maxWidth: 400,
            }}
          >
            <Text
              style={{
                fontSize: scaleSize(20),
                fontWeight: "bold",
                color: theme.colors.primaryTextColor,
                marginBottom: scaleSize(10),
                textAlign: "center",
              }}
            >
              End Program?
            </Text>

            <Text
              style={{
                fontSize: scaleSize(16),
                color: theme.colors.primaryTextColor,
                marginBottom: scaleSize(20),
                textAlign: "center",
              }}
            >
              Are you sure you want to end this program? This action cannot be
              undone.
            </Text>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-around",
              }}
            >
              <TouchableOpacity
                style={{
                  backgroundColor: theme.colors.surface,
                  padding: scaleSize(12),
                  borderRadius: scaleSize(8),
                  minWidth: scaleSize(100),
                  alignItems: "center",
                }}
                onPress={() => setShowConfirmation(false)}
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    fontSize: scaleSize(16),
                  }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  backgroundColor: theme.colors.deactivateButton || "#F44336",
                  padding: scaleSize(12),
                  borderRadius: scaleSize(8),
                  minWidth: scaleSize(100),
                  alignItems: "center",
                }}
                onPress={handleProgramDeactivation}
              >
                <Text
                  style={{
                    color: "#fff",
                    fontSize: scaleSize(16),
                    fontWeight: "bold",
                  }}
                >
                  End Program
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: scaleSize(110),
  },
  noProgramContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: scaleSize(32),
  },
  noProgramTitle: {
    fontWeight: "bold",
    fontSize: scaleSize(24),
    marginTop: scaleSize(16),
    textAlign: "center",
    color: "gray",
  },
  noProgramSubtitle: {
    fontSize: scaleSize(16),
    textAlign: "center",
    color: "gray",
    marginTop: scaleSize(8),
  },
  congratsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: scaleSize(32),
  },
  congratsTitle: {
    fontWeight: "bold",
    fontSize: scaleSize(24),
    marginTop: scaleSize(16),
    textAlign: "center",
    color: "gray",
  },
  congratsSubtitle: {
    fontSize: scaleSize(16),
    textAlign: "center",
    color: "gray",
    marginTop: scaleSize(8),
  },
  upcomingProgramText: {
    fontSize: scaleSize(16),
    color: "#FFA000",
    textAlign: "center",
  },
  dashboardGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    gap: scaleSize(8),
  },
  card: {
    width: (width - 48) / 2,
    borderRadius: 12,
    padding: scaleSize(16),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartCard: {
    width: width - 32,
    alignSelf: "center",
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scaleSize(12),
  },
  cardTitle: {
    fontSize: scaleSize(14),
    fontWeight: "bold",
    marginLeft: 8,
  },
  cardContent: {},
  cardLabel: {
    fontSize: scaleSize(13),
    fontWeight: "500",
    marginTop: 0,
  },
  cardValue: {
    fontSize: scaleSize(15),
    fontWeight: "600",
    marginTop: scaleSize(1),
    marginBottom: scaleSize(8),
  },
  goalsColumn: {
    flexDirection: "column",
    alignItems: "flex-start",
    marginBottom: scaleSize(8),
  },
  goalItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scaleSize(8),
  },
  goalLabel: {
    marginLeft: scaleSize(6),
    fontSize: scaleSize(15),
    fontWeight: "600",
  },
  alignButton: {
    paddingVertical: scaleSize(10),
    borderRadius: scaleSize(6),
    alignItems: "center",
  },
  alignButtonText: {
    fontWeight: "bold",
    fontSize: scaleSize(14),
  },
  chartContainer: {},
  actionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "flex-start",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scaleSize(10),
    paddingHorizontal: scaleSize(12),
    borderRadius: 6,
  },
  actionButtonText: {
    fontWeight: "bold",
    fontSize: scaleSize(14),
  },
  upcomingProgramContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: scaleSize(24),
  },
});

export default ProgramActive;
