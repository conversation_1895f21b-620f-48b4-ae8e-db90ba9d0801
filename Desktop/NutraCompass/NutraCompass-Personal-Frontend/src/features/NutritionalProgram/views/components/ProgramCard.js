// components/ProgramCard.js

import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import Checkbox from "expo-checkbox";
import PropTypes from "prop-types"; // Optional: For prop type checking
import { scaleSize } from "../../../../utils/deviceUtils.js";

/**
 * Helper: Format date as "Mon DD, YYYY"
 * Handles both plain "YYYY-MM-DD" and full ISO formats like "2025-01-12T00:00:00Z"
 */
const formatDate = (dateInput) => {
  if (!dateInput) return "N/A";
  let dateObj;

  if (typeof dateInput === "string") {
    if (dateInput.includes("T")) {
      // parse an ISO string "2025-01-12T00:00:00Z"
      dateObj = new Date(dateInput);
    } else {
      // parse a "YYYY-MM-DD" string
      const [year, month, day] = dateInput.split("-").map(Number);
      if (!year || !month || !day) return "Invalid Date";
      dateObj = new Date(year, month - 1, day);
    }
  } else if (
    typeof dateInput === "object" &&
    dateInput._seconds !== undefined &&
    dateInput._nanoseconds !== undefined
  ) {
    // Firestore Timestamp (e.g., { _seconds: 1673500800, _nanoseconds: 0 })
    dateObj = new Date(
      dateInput._seconds * 1000 + dateInput._nanoseconds / 1e6
    );
  } else if (dateInput instanceof Date) {
    // Already a Date object
    dateObj = dateInput;
  } else {
    return "Invalid Date";
  }

  // Check if dateObj is a valid date
  if (isNaN(dateObj.getTime())) {
    return "Invalid Date";
  }

  // Format as "Mon DD, YYYY" (e.g., "Jan 12, 2025")
  return dateObj.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

/**
 * Helper: Parse percentage string to number (e.g., "75%" -> 75)
 */
const parsePercentage = (percentageString) => {
  const parsed = parseInt(percentageString.replace("%", ""), 10);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Helper: Get color based on completion percentage
 */
const getCompletionColor = (percentage, theme) => {
  if (percentage >= 0 && percentage <= 69) {
    return theme.colors.primaryTextColor;
  } else if (percentage >= 70 && percentage <= 89) {
    return "#FFA500"; // e.g., orange
  } else if (percentage >= 90 && percentage <= 100) {
    return "#32CD32"; // e.g., green
  } else {
    return theme.colors.primaryTextColor;
  }
};

const ProgramCard = ({
  program,
  isSelected,
  onToggleSelect,
  isLoading = false, // Default value set here
  theme,
}) => {
  // Parse the percentage string to a number
  const percentageNumber = parsePercentage(program.completionPercentage);
  // Determine the color based on the percentage
  const percentageColor = getCompletionColor(percentageNumber, theme);

  return (
    <TouchableOpacity
      onPress={() => onToggleSelect(program.programId)}
      style={[styles.cardContainer, { backgroundColor: theme.colors.surface }]}
      activeOpacity={0.8}
      accessibilityRole="button"
      accessibilityState={{ selected: isSelected }}
      accessibilityLabel={`${isSelected ? "Deselect" : "Select"} ${
        program.programName || "Untitled Program"
      }`}
      accessibilityHint="Tap to select this program for batch actions"
    >
      {/* Select Checkbox */}
      <Checkbox
        value={isSelected}
        onValueChange={() => onToggleSelect(program.programId)}
        color={isSelected ? theme.colors.primary : undefined}
        style={styles.checkbox}
        accessibilityLabel={`${isSelected ? "Deselect" : "Select"} ${
          program.programName || "Untitled Program"
        }`}
        accessibilityHint="Toggle selection for batch actions"
      />

      {/* Program Details */}
      <View style={styles.detailsContainer}>
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          {/* Program Name */}
          <Text
            style={[
              styles.programName,
              { color: theme.colors.primaryTextColor },
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {program.programName || "Untitled Program"}
          </Text>

          {/* Completion Percentage */}
          <View style={styles.percentContainer}>
            <Text
              style={[
                styles.percentText,
                {
                  color: percentageColor, // Apply color based on % range
                },
              ]}
            >
              {program.completionPercentage}
            </Text>
          </View>
        </View>

        {/* Start and End Dates */}
        <View style={styles.datesContainer}>
          <Text style={[styles.dateText, { color: theme.colors.subTextColor }]}>
            Start: {formatDate(program.startDate)}
          </Text>
          <Text style={[styles.dateText, { color: theme.colors.subTextColor }]}>
            End: {formatDate(program.endDate)}
          </Text>
        </View>
      </View>

      {/* Loading Indicator Overlay (if isLoading is true) */}
      {isLoading && (
        <View style={[styles.loadingOverlay, { zIndex: 1 }]}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
        </View>
      )}
    </TouchableOpacity>
  );
};

ProgramCard.propTypes = {
  program: PropTypes.shape({
    programId: PropTypes.string.isRequired,
    programName: PropTypes.string,
    startDate: PropTypes.oneOfType([
      PropTypes.string, // "YYYY-MM-DD" or ISO
      PropTypes.object, // Firestore Timestamp
      PropTypes.instanceOf(Date), // Already a Date object
    ]),
    endDate: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.object,
      PropTypes.instanceOf(Date),
    ]),
    completionPercentage: PropTypes.string.isRequired,
    deactivatedAt: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.object,
      PropTypes.instanceOf(Date),
    ]),
  }).isRequired,
  isSelected: PropTypes.bool.isRequired,
  onToggleSelect: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  theme: PropTypes.shape({
    colors: PropTypes.shape({
      surface: PropTypes.string,
      primary: PropTypes.string,
      primaryTextColor: PropTypes.string,
      subTextColor: PropTypes.string,
    }),
  }).isRequired,
};

export default ProgramCard;

const styles = StyleSheet.create({
  cardContainer: {
    borderRadius: 8,
    padding: scaleSize(12),
    position: "relative",
    marginBottom: scaleSize(12),
    flexDirection: "row",
    alignItems: "center",
  },
  checkbox: {
    marginRight: scaleSize(12),
    transform: [{ scaleX: scaleSize(1.2) }, { scaleY: scaleSize(1.2) }],
  },
  detailsContainer: {
    flex: 1,
    justifyContent: "center",
    gap: scaleSize(8),
    paddingLeft: scaleSize(8),
  },
  programName: {
    fontSize: scaleSize(14),
    fontWeight: "bold",
    marginBottom: scaleSize(4),
  },
  datesContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  dateText: {
    fontSize: scaleSize(12),
  },
  percentContainer: {
    backgroundColor: "rgba(0,0,0,0.1)",
    borderRadius: 4,
    paddingHorizontal: scaleSize(6),
    paddingVertical: scaleSize(4),
  },
  percentText: {
    fontSize: scaleSize(12),
    fontWeight: "bold",
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(255,255,255,0.7)",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
  },
});
