import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Keyboard,
  Modal,
  FlatList,
  Dimensions,
  ScrollView,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";
import { useTime } from "../../../context/TimeContext.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import ProgressPicture from "../components/ProgressPicture.js";
import isEqual from "lodash.isequal";
import { scaleSize } from "../../../utils/deviceUtils.js";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
const { height } = Dimensions.get("window");

const ProgramCheckpoints = () => {
  const { theme } = useThemeContext();
  const { activeNutritionalProgram, submitWeight } = useNutritionProgram();
  const { getSelectedDateAsDate } = useTime();
  const { getUserMeasurementSettings } = useUserSettings();
  // Get user's preferred weight unit
  const { bodyWeightUnit } = getUserMeasurementSettings();

  const [programStarted, setProgramStarted] = useState(false);
  const [selectedCheckpoint, setSelectedCheckpoint] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [items, setItems] = useState([]);
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);
  const [inputWeight, setInputWeight] = useState("");

  // Helper functions for weight conversion
  const convertLbsToUserUnit = (lbs) => {
    if (!lbs) return "";
    const numericValue = parseFloat(lbs);
    if (isNaN(numericValue)) return "";

    if (bodyWeightUnit === "kg") {
      return (numericValue / 2.20462).toFixed(1);
    }
    return numericValue.toFixed(1);
  };

  const convertUserUnitToLbs = (value) => {
    if (!value) return "";
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) return "";

    if (bodyWeightUnit === "kg") {
      return (numericValue * 2.20462).toFixed(1);
    }
    return numericValue.toFixed(1);
  };

  // Helper to format dates
  const formatDate = (dateInput) => {
    if (!dateInput) return "";
    const dateObj = new Date(dateInput);
    return dateObj.toLocaleDateString("en-US", {
      timeZone: "UTC",
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  // Check if the program has started
  useEffect(() => {
    if (activeNutritionalProgram?.startDate) {
      const now = getSelectedDateAsDate();
      const programStartDate = new Date(activeNutritionalProgram.startDate);
      setProgramStarted(now >= programStartDate);
    }
  }, [activeNutritionalProgram, getSelectedDateAsDate]);

  // Ref to store previous checkpoints for deep equality checking
  const prevCheckpointsRef = useRef();

  // Initialize dropdown items and set the initial selected value
  useEffect(() => {
    // If items have already been set, assume we've already processed and return early.
    if (items.length > 0) {
      return;
    }

    if (activeNutritionalProgram?.checkpoints?.length) {
      // Check for previous checkpoint data
      if (prevCheckpointsRef.current) {
        const equal = isEqual(
          prevCheckpointsRef.current,
          activeNutritionalProgram.checkpoints
        );
        if (equal) {
          console.log("Checkpoints are equal, returning early");
          return;
        }
      }

      // Update the ref with the new checkpoints
      prevCheckpointsRef.current = activeNutritionalProgram.checkpoints;

      // Prepare items for the modal selector
      const checkpointItems = activeNutritionalProgram.checkpoints.map(
        (checkpoint) => ({
          id: checkpoint.week, // Assuming week is unique
          week: checkpoint.week,
          date: checkpoint.date,
          label: `Week ${checkpoint.week} (${formatDate(checkpoint.date)})`,
        })
      );
      setItems(checkpointItems);

      // Preserve or initialize the selected checkpoint
      if (selectedCheckpoint) {
        const updatedCheckpoint = activeNutritionalProgram.checkpoints.find(
          (cp) => cp.week === selectedCheckpoint.week
        );
        if (updatedCheckpoint) {
          setSelectedCheckpoint(updatedCheckpoint);
          setInputWeight(
            updatedCheckpoint.userLoggedWeight
              ? convertLbsToUserUnit(
                  updatedCheckpoint.userLoggedWeight.toString()
                )
              : ""
          );
        } else {
          setSelectedCheckpoint(activeNutritionalProgram.checkpoints[0]);
          setInputWeight(
            activeNutritionalProgram.checkpoints[0].userLoggedWeight
              ? convertLbsToUserUnit(
                  activeNutritionalProgram.checkpoints[0].userLoggedWeight.toString()
                )
              : ""
          );
        }
      } else {
        setSelectedCheckpoint(activeNutritionalProgram.checkpoints[0]);
        setInputWeight(
          activeNutritionalProgram.checkpoints[0].userLoggedWeight
            ? convertLbsToUserUnit(
                activeNutritionalProgram.checkpoints[0].userLoggedWeight.toString()
              )
            : ""
        );
      }
    }
  }, [activeNutritionalProgram?.checkpoints, items]);

  // Handle weight submission
  const handleWeightSubmit = () => {
    if (!selectedCheckpoint) return;

    Keyboard.dismiss();

    // Convert input to lbs for backend
    const lbsValue = convertUserUnitToLbs(inputWeight);

    // Validate the input weight
    if (!lbsValue || isNaN(lbsValue)) {
      alert("Please enter a valid number for weight.");
      return;
    }

    if (lbsValue === selectedCheckpoint.userLoggedWeight?.toString()) {
      // No change in weight
      return;
    }

    submitWeight(selectedCheckpoint.week, lbsValue)
      .then((updatedCheckpoint) => {
        setSelectedCheckpoint(updatedCheckpoint);
        setInputWeight(
          updatedCheckpoint.userLoggedWeight
            ? convertLbsToUserUnit(
                updatedCheckpoint.userLoggedWeight.toString()
              )
            : ""
        );
      })
      .catch((error) => {
        console.error("Error submitting weight: ", error);
        alert("Failed to submit weight. Please try again.");
      });
  };

  // Handle weight input change
  const handleWeightChange = (text) => {
    setInputWeight(text);
  };

  // Determine completion state
  const isCompleted =
    selectedCheckpoint?.userLoggedWeight && selectedCheckpoint?.feedback;

  // Check whether the checkpoint is in the past or future
  const currentDate = new Date().toISOString().split("T")[0];
  const checkpointDate = selectedCheckpoint
    ? new Date(selectedCheckpoint.date).toISOString().split("T")[0]
    : null;
  const isPast = checkpointDate ? currentDate >= checkpointDate : false;
  const isFuture = checkpointDate ? currentDate < checkpointDate : false;

  if (!activeNutritionalProgram) {
    return (
      <View
        style={[
          styles.noProgramContainer,
          { backgroundColor: theme.colors.screenBackground },
        ]}
      >
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={scaleSize(64)}
          color="gray"
        />
        <Text style={styles.noProgramTitle}>No Active Program</Text>
        <Text style={styles.noProgramSubtitle}>
          You currently have no active program. Create one from the menu above!
        </Text>
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: theme.colors.screenBackground },
      ]}
    >
      <Text style={[styles.header, { color: theme.colors.primaryTextColor }]}>
        Weigh-In Checkpoints
      </Text>

      {/* Checkpoint Selector Button */}
      <TouchableOpacity
        style={[
          styles.selectorButton,
          {
            backgroundColor: theme.colors.surface,
            borderBottomWidth: scaleSize(1),
            borderColor: theme.colors.primary,
          },
        ]}
        onPress={() => setModalVisible(true)}
        accessibilityLabel="Select Checkpoint"
        accessibilityRole="button"
      >
        <Text
          style={{
            color: theme.colors.primaryTextColor,
            fontSize: scaleSize(16),
          }}
        >
          {selectedCheckpoint
            ? `Week ${selectedCheckpoint.week} (${formatDate(
                selectedCheckpoint.date
              )})`
            : "Select a checkpoint"}
        </Text>
        <MaterialCommunityIcons
          name="chevron-down"
          size={scaleSize(24)}
          color={theme.colors.primaryTextColor}
        />
      </TouchableOpacity>

      {/* ScrollView for Checkpoint Content */}
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        {/* Grade & Completed Indicator */}
        <View style={[styles.section, { backgroundColor: "transparent" }]}>
          <View style={styles.row}>
            <Text
              style={[styles.label, { color: theme.colors.primaryTextColor }]}
            >
              Grade:
            </Text>
            <Text
              style={[
                styles.gradeValue,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              {selectedCheckpoint?.grade || "N/A"}
            </Text>
          </View>
          <View style={[styles.row, { marginTop: scaleSize(12) }]}>
            {isCompleted && (
              <View style={styles.completedIndicator}>
                <MaterialCommunityIcons
                  name="check-circle"
                  size={scaleSize(24)}
                  color="green"
                />
                <Text style={styles.completedText}>Completed</Text>
              </View>
            )}
            {selectedCheckpoint?.feedback && (
              <TouchableOpacity
                style={[
                  styles.section,
                  {
                    backgroundColor: theme.colors.surface,
                    paddingVertical: scaleSize(12),
                    borderWidth: scaleSize(1),
                    borderColor: theme.colors.shadow,
                  },
                ]}
                onPress={() => setFeedbackModalVisible(true)}
                accessibilityLabel="View Feedback"
                accessibilityRole="button"
              >
                <Text
                  style={{
                    color: theme.colors.primaryTextColor,
                    fontSize: scaleSize(14),
                    fontWeight: "500",
                  }}
                >
                  Open Feedback
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Date */}
        <View style={[styles.section, { backgroundColor: "transparent" }]}>
          <View style={styles.row}>
            <Text
              style={[styles.label, { color: theme.colors.primaryTextColor }]}
            >
              Date:
            </Text>
            <Text
              style={[styles.value, { color: theme.colors.primaryTextColor }]}
            >
              {formatDate(selectedCheckpoint?.date) || ""}
            </Text>
          </View>
        </View>

        {/* Expected Weight */}
        <View style={[styles.section, { backgroundColor: "transparent" }]}>
          <View style={styles.row}>
            <Text
              style={[styles.label, { color: theme.colors.primaryTextColor }]}
            >
              Expected Weight:
            </Text>
            <Text
              style={[styles.value, { color: theme.colors.primaryTextColor }]}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(20),
                  fontWeight: "600",
                }}
              >
                {selectedCheckpoint?.expectedWeight
                  ? convertLbsToUserUnit(selectedCheckpoint.expectedWeight)
                  : ""}
              </Text>{" "}
              {bodyWeightUnit}
            </Text>
          </View>
        </View>

        {/* Weight Input */}
        <View style={[styles.section, { backgroundColor: "transparent" }]}>
          <TextInput
            keyboardType="numeric"
            editable={!isFuture}
            style={[
              styles.input,
              {
                borderColor: theme.colors.shadow,
                color: theme.colors.primaryTextColor,
              },
            ]}
            placeholder={`Log Weight`}
            placeholderTextColor={theme.colors.subTextColor}
            value={inputWeight}
            onChangeText={handleWeightChange}
            onSubmitEditing={handleWeightSubmit}
            returnKeyType="done"
            accessibilityLabel="Log Weight Input"
          />
        </View>

        {/* Progress Picture */}
        <View
          style={[
            styles.section,
            {
              backgroundColor: "transparent",
              borderTopWidth: scaleSize(1),
              borderTopColor: theme.colors.shadow,
            },
          ]}
        >
          <Text
            style={[
              styles.label,
              {
                color: theme.colors.primaryTextColor,
                alignSelf: "center",
                paddingBottom: scaleSize(12),
              },
            ]}
          >
            Progress Picture
          </Text>
          <View style={styles.progressPictureContainer}>
            <ProgressPicture
              size={height * 0.4}
              checkpointId={selectedCheckpoint?.week}
              isDisabled={isFuture}
            />
          </View>
        </View>
      </ScrollView>

      {/* Modal for Selecting Checkpoint */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalBackdrop}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <Text
              style={[
                styles.modalHeader,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              Select a Checkpoint
            </Text>
            <FlatList
              data={items}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={{ paddingBottom: scaleSize(24) }}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.modalItem}
                  onPress={() => {
                    const checkpoint =
                      activeNutritionalProgram.checkpoints.find(
                        (cp) => cp.week === item.week
                      );
                    setSelectedCheckpoint(checkpoint);
                    setInputWeight(
                      checkpoint.userLoggedWeight
                        ? checkpoint.userLoggedWeight.toString()
                        : ""
                    );
                    setModalVisible(false);
                  }}
                  accessibilityLabel={`Select Week ${item.week}`}
                  accessibilityRole="button"
                >
                  <Text
                    style={{
                      color: theme.colors.primaryTextColor,
                      fontSize: scaleSize(16),
                    }}
                  >
                    {item.label}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Feedback Expansion Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={feedbackModalVisible}
        onRequestClose={() => setFeedbackModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalBackdrop}
          activeOpacity={1}
          onPressOut={() => setFeedbackModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <Text
              style={[
                styles.modalHeader,
                { color: theme.colors.primaryTextColor },
              ]}
            >
              Feedback
            </Text>
            <ScrollView>
              <Text
                style={[
                  styles.feedbackText,
                  { color: theme.colors.primaryTextColor },
                ]}
              >
                {selectedCheckpoint?.feedback}
              </Text>
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  noProgramContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: scaleSize(32),
  },
  noProgramTitle: {
    fontWeight: "bold",
    fontSize: scaleSize(24),
    marginTop: scaleSize(16),
    textAlign: "center",
    color: "gray",
  },
  noProgramSubtitle: {
    fontSize: scaleSize(16),
    textAlign: "center",
    color: "gray",
    marginTop: scaleSize(8),
  },
  header: {
    fontWeight: "bold",
    fontSize: scaleSize(18),
    marginTop: scaleSize(16),
    marginBottom: scaleSize(8),
    textAlign: "center",
  },
  selectorButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: scaleSize(16),
    marginHorizontal: scaleSize(16),
    borderRadius: 10,
    marginBottom: scaleSize(16),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dropdown: {
    marginHorizontal: scaleSize(16),
    borderColor: "#ccc",
    borderRadius: 10,
    paddingHorizontal: scaleSize(10),
  },
  scrollViewContent: {
    paddingHorizontal: 16,
    paddingBottom: scaleSize(100),
  },
  section: {
    padding: scaleSize(16),
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  label: {
    fontSize: scaleSize(16),
    fontWeight: "600",
  },
  value: {
    fontSize: scaleSize(16),
  },
  gradeValue: {
    fontSize: scaleSize(28),
    fontWeight: "bold",
  },
  input: {
    borderWidth: 1,
    padding: scaleSize(24),
    borderRadius: 8,
    fontSize: scaleSize(22),
    textAlign: "center",
    width: "50%",
    alignSelf: "center",
  },
  completedIndicator: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: scaleSize(8),
  },
  completedText: {
    marginLeft: scaleSize(4),
    color: "green",
    fontWeight: "bold",
    fontSize: scaleSize(16),
  },
  progressPictureContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    height: height * 0.4,
    width: "100%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: scaleSize(16),
  },
  modalHeader: {
    fontSize: scaleSize(18),
    fontWeight: "bold",
    marginBottom: scaleSize(16),
    textAlign: "center",
  },
  modalItem: {
    paddingVertical: scaleSize(12),
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  feedbackText: {
    fontSize: scaleSize(16),
    lineHeight: scaleSize(24),
  },
});

export default ProgramCheckpoints;
