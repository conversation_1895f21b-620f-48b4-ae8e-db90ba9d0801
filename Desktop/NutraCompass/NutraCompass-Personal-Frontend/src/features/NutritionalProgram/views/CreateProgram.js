// CreateProgram.js
import React, { useEffect, useState } from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import Animated, { useSharedValue } from "react-native-reanimated";
import * as Haptics from "expo-haptics";
import { Snackbar } from "react-native-paper";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useFeatureFlags } from "../../../context/FeatureFlagsContext.js";
import { useUpgrade } from "../../../hooks/useUpgrade.js";
import CategoriesGrid from "./components/CategoriesGrid.js";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { scaleSize } from "../../../utils/deviceUtils.js";

const CreateProgram = ({
  setIsBasicProgramModalVisible,
  setIsFoodTrackerProgramModalVisible,
  setIsAIMealPlansProgramModalVisible,
  setIsVoiceAssistedProgramModalVisible,
  handleSubscribePremium, // Function to handle subscription
}) => {
  const { theme } = useThemeContext();
  const { features } = useFeatureFlags();
  const { loading, upgradeError } = useUpgrade();

  // Snackbar state
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarVisible, setSnackbarVisible] = useState(false);

  // Listen for upgrade errors
  useEffect(() => {
    if (upgradeError) {
      setSnackbarMessage(upgradeError);
      setSnackbarVisible(true);
    }
  }, [upgradeError]);

  // Reanimated shared value to track scroll (passed to CategoriesGrid)
  const scrollY = useSharedValue(0);

  /**
   * Handle user tapping on a category in the grid
   */
  const handleCategorySelect = (category) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Open the corresponding modal based on the selected category
    switch (category.label) {
      case "Basic":
        setIsBasicProgramModalVisible(true);
        break;
      case "AI Meal Plans":
        setIsAIMealPlansProgramModalVisible(true);
        break;
      case "Voice-Assisted Creation":
        setIsVoiceAssistedProgramModalVisible(true);
        break;
      case "Food Tracker":
        setIsFoodTrackerProgramModalVisible(true);
        break;
      default:
        console.warn("Unhandled category:", category.label);
        break;
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: theme.colors.screenBackground },
      ]}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text
          style={[styles.headerTitle, { color: theme.colors.primaryTextColor }]}
        >
          Select a Program
        </Text>
      </View>

      {/* Premium Subscription Advertisement */}
      {/* <View style={styles.premiumAdContainer}>
        <LinearGradient
          colors={[theme.colors.secondary, theme.colors.primary]}
          style={styles.premiumAdBackground}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.premiumAdContent}>
            <MaterialCommunityIcons
              name="crown"
              size={24}
              color={theme.colors.surface}
              style={{ marginRight: 8 }}
            />
            <View style={styles.premiumAdTextContainer}>
              <Text
                style={[styles.premiumAdTitle, { color: theme.colors.surface }]}
              >
                Unlock Premium Features
              </Text>
              <Text
                style={[
                  styles.premiumAdSubtitle,
                  { color: theme.colors.surface },
                ]}
              >
                Subscribe to the Premium Tier to access all exclusive programs.
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={[
              styles.subscribeButton,
              { backgroundColor: theme.colors.surface },
            ]}
            onPress={handleSubscribePremium}
            accessibilityLabel="Subscribe to Premium"
            accessibilityHint="Unlock all premium programs by subscribing"
          >
            <Text
              style={[
                styles.subscribeButtonText,
                { color: theme.colors.primary },
              ]}
            >
              Subscribe
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      </View> */}

      {/* Categories Grid */}
      <CategoriesGrid
        theme={theme}
        features={features}
        onSelectCategory={handleCategorySelect}
        scrollY={scrollY.value}
      />

      {/* Snackbar for Error Messages */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={{ backgroundColor: theme.colors.surface }}
      >
        <Text style={{ color: theme.colors.primaryTextColor }}>
          {snackbarMessage}
        </Text>
      </Snackbar>
    </View>
  );
};

export default CreateProgram;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: scaleSize(18),
    fontWeight: "bold",
  },
  premiumAdContainer: {
    marginHorizontal: 42,
    marginBottom: 16,
    borderRadius: 12,
    overflow: "hidden",
  },
  premiumAdBackground: {
    padding: 16,
    borderRadius: 12,
  },
  premiumAdContent: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  premiumAdTextContainer: {
    flex: 1,
  },
  premiumAdTitle: {
    fontSize: 16,
    fontWeight: "bold",
  },
  premiumAdSubtitle: {
    fontSize: 12,
  },
  subscribeButton: {
    alignSelf: "flex-start",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  subscribeButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
});
