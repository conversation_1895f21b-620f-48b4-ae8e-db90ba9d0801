// NutritionalProgramScreen.js
import React, { useState, useMemo } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Chip } from "react-native-paper";
import { scaleSize } from "../../../utils/deviceUtils.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { useNutritionProgram } from "../context/NutritionProgramContext.js";

// Import your sub-screens:
import CreateProgram from "../views/CreateProgram.js";
import ProgramActive from "../views/ProgramActive.js";
import ProgramAll from "../views/ProgramAll.js"; // For managing all programs
import ProgramCheckpoints from "../views/ProgramCheckpoints.js";
// import ProgramTimeline from "../views/ProgramTimeline.js";
// import ProgramGoals from "../views/ProgramGoals.js";
import ProgramMealPlan from "../views/ProgramMealPlan.js"; // Placeholder for future AI meal planning

import BasicProgramModal from "../components/BasicProgramModal.js";
import AICreateMealPlanModal from "../components/AICreateMealPlanModal.js";
import VoiceAssistedProgramCreationModal from "../components/VoiceAssistedProgramCreationModal.js";

const menuItems = [
  { key: "CreateProgram", label: "Create Program" },
  { key: "ActiveProgram", label: "Active Program" },
  { key: "Checkpoints", label: "Checkpoints" },
  // { key: "Timeline", label: "Timeline" },
  // { key: "Goals", label: "Goals" },
  // { key: "MealPlan", label: "Meal Plan" },
  { key: "AllPrograms", label: "All Programs" },
];

const NutritionalProgramScreen = () => {
  const navigation = useNavigation();
  const { theme } = useThemeContext();
  const { activeNutritionalProgram, actionState, getLoadingMessage } =
    useNutritionProgram();

  // Default selected menu item
  const [selectedMenuItem, setSelectedMenuItem] = useState("CreateProgram");

  // Modal visibility states
  const [isBasicProgramModalVisible, setIsBasicProgramModalVisible] =
    useState(false);
  const [
    isFoodTrackerProgramModalVisible,
    setIsFoodTrackerProgramModalVisible,
  ] = useState(false);
  const [
    isAIMealPlansProgramModalVisible,
    setIsAIMealPlansProgramModalVisible,
  ] = useState(false);
  const [
    isVoiceAssistedProgramModalVisible,
    setIsVoiceAssistedProgramModalVisible,
  ] = useState(false);

  // Memoize the sub-screen components so they are created only once
  const memoizedScreens = useMemo(() => {
    return {
      CreateProgram: (
        <CreateProgram
          setIsBasicProgramModalVisible={setIsBasicProgramModalVisible}
          setIsFoodTrackerProgramModalVisible={
            setIsFoodTrackerProgramModalVisible
          }
          setIsAIMealPlansProgramModalVisible={
            setIsAIMealPlansProgramModalVisible
          }
          setIsVoiceAssistedProgramModalVisible={
            setIsVoiceAssistedProgramModalVisible
          }
        />
      ),
      ActiveProgram: <ProgramActive />,
      Checkpoints: <ProgramCheckpoints />,
      MealPlan: <ProgramMealPlan />,
      AllPrograms: <ProgramAll />,
    };
  }, []); // Empty dependency array means these screens will be created once per mount of NutritionalProgramScreen

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.screenBackground }}>
      {/* HEADER with Back Button & Centered Title */}
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          paddingBottom: 8,
          paddingHorizontal: 12,
        }}
      >
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ marginRight: 8 }}
        >
          <MaterialCommunityIcons
            name="chevron-left"
            size={scaleSize(32)}
            color={theme.colors.primaryTextColor}
          />
        </TouchableOpacity>

        <View style={{ flex: 1, alignItems: "center" }}>
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontSize: scaleSize(20),
              fontWeight: "bold",
              textAlign: "center",
            }}
          >
            Nutritional Program
          </Text>
        </View>
        <View style={{ width: 40 }} />
      </View>

      {/* FIXED-HEIGHT Horizontal Menu */}
      <View
        style={{
          backgroundColor: theme.colors.screenBackground,
          flexDirection: "row",
          paddingHorizontal: scaleSize(8),
          paddingVertical: scaleSize(8),
        }}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {menuItems.map((item) => (
            <Chip
              key={item.key}
              style={{ marginRight: scaleSize(8) }}
              selected={selectedMenuItem === item.key}
              onPress={() => setSelectedMenuItem(item.key)}
              mode="flat"
              textStyle={{
                color: theme.colors.primaryTextColor,
                fontSize: scaleSize(14),
                paddingVertical: scaleSize(12),
              }}
            >
              {item.label}
            </Chip>
          ))}
        </ScrollView>
      </View>

      {/* MAIN CONTENT AREA */}
      <View style={{ flex: 1 }}>{memoizedScreens[selectedMenuItem]}</View>

      {/* Modals */}
      <BasicProgramModal
        isVisible={isBasicProgramModalVisible}
        closeModal={() => setIsBasicProgramModalVisible(false)}
      />
      <AICreateMealPlanModal
        isVisible={isAIMealPlansProgramModalVisible}
        closeModal={() => setIsAIMealPlansProgramModalVisible(false)}
      />
      <VoiceAssistedProgramCreationModal
        isVisible={isVoiceAssistedProgramModalVisible}
        closeModal={() => setIsVoiceAssistedProgramModalVisible(false)}
      />
    </View>
  );
};

export default NutritionalProgramScreen;
