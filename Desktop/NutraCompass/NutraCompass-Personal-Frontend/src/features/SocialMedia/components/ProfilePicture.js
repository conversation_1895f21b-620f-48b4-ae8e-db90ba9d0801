import React, { useState } from "react";
import { View, TouchableOpacity, Text, Alert, Linking } from "react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { useUserSettings } from "../../Settings/context/UserSettingsContext.js";
import { useThemeContext } from "../../../context/ThemeContext.js";
import { CustomImageUploadAlert } from "../../../components/CustomImageUploadAlert";

export default function ProfilePicture({ size = 150 }) {
  const { uploadProfilePicture, removeProfilePicture, getUserProfile } =
    useUserSettings();
  const { theme } = useThemeContext();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const userProfile = getUserProfile();
  const profilePictureUrl = userProfile.pictureUrl;

  const handlePickImage = async () => {
    setIsModalVisible(true);
  };

  const handleImagePick = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      Alert.alert(
        "Photo Access Required",
        "Photo library access is required to select images for your profile and custom meals. Please enable access in settings.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Open Settings", onPress: () => Linking.openSettings() },
        ]
      );
      return;
    }

    const pickerResult = await ImagePicker.launchImageLibraryAsync();
    if (pickerResult.canceled) {
      console.log("User canceled image picker");
      return;
    }

    if (pickerResult.assets && pickerResult.assets.length > 0) {
      const image = pickerResult.assets[0];
      try {
        await uploadProfilePicture({ uri: image.uri });
        setIsModalVisible(false); // Close modal after successful upload
      } catch (error) {
        console.error("Error uploading profile picture:", error);
        Alert.alert("Upload failed", "Unable to upload profile picture.");
      }
    }
  };

  const handleTakePhoto = async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
    if (!permissionResult.granted) {
      Alert.alert(
        "Camera Access Required",
        "Camera access is required to take photos for your profile and custom meals. Please enable access in settings.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Open Settings", onPress: () => Linking.openSettings() },
        ]
      );
      return;
    }

    const pickerResult = await ImagePicker.launchCameraAsync();
    if (pickerResult.canceled) {
      console.log("User canceled camera picker");
      return;
    }

    if (pickerResult.assets && pickerResult.assets.length > 0) {
      const image = pickerResult.assets[0];
      try {
        await uploadProfilePicture({ uri: image.uri });
        setIsModalVisible(false); // Close modal after successful upload
      } catch (error) {
        console.error("Error uploading profile picture:", error);
        Alert.alert("Upload failed", "Unable to upload profile picture.");
      }
    }
  };

  const handleRemovePhoto = async () => {
    Alert.alert(
      "Remove Profile Picture",
      "Are you sure you want to remove your profile picture?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          onPress: async () => {
            try {
              await removeProfilePicture();
              setIsModalVisible(false); // Close modal after successful removal
            } catch (error) {
              console.error("Error removing profile picture:", error);
              Alert.alert(
                "Removal failed",
                "Unable to remove profile picture."
              );
            }
          },
          style: "destructive",
        },
      ]
    );
  };

  return (
    <View>
      <TouchableOpacity
        style={{
          height: size,
          width: size,
          borderRadius: size / 2,
          overflow: "hidden",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#cccccc",
          position: "relative", // Added for positioning context
        }}
        onPress={handlePickImage}
      >
        {profilePictureUrl ? (
          <Image
            source={{ uri: profilePictureUrl }}
            style={{
              position: "absolute", // Fill container completely
              width: "100%",
              height: "100%",
            }}
            contentFit="cover"
            contentPosition="center"
          />
        ) : (
          <View
            style={{
              flex: 1,
              width: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text>Upload Photo</Text>
          </View>
        )}
      </TouchableOpacity>
      <CustomImageUploadAlert
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onPickImage={handleImagePick}
        onTakePhoto={handleTakePhoto}
        onRemoveImage={handleRemovePhoto}
        hasImage={!!profilePictureUrl}
      />
    </View>
  );
}
