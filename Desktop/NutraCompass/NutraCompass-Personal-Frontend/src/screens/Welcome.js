import React, { useState } from "react";
import {
  Text,
  View,
  Dimensions,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { FontAwesome, Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image"; // use expo-image for better image performance
import Carousel from "react-native-reanimated-carousel"; // use the reanimated carousel
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import welcomeScreenStyles from "./styles/welcomeScreenStyles.js";
import { useThemeContext } from "../context/ThemeContext.js";
import { useAuth } from "../authentication/context/AuthContext.js";
import LoginModal from "../authentication/components/LoginModal.js";
import { scaleSize } from "../utils/deviceUtils.js";
const { height: screenHeight, width: screenWidth } = Dimensions.get("window");

function WelcomeScreen({ navigation }) {
  const styles = welcomeScreenStyles();
  const { theme } = useThemeContext();
  const { registration, signInWithGoogle, signInWithApple } = useAuth();

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoginModalVisible, setIsLoginModalVisible] = useState(false);

  const carouselHeight = screenHeight * 0.4; // Adjust carousel height to 40% of screen
  const carouselWidth = screenWidth; // Set the width to 100% of the screen width

  const images = [
    require("../../assets/welcome/welcome4.png"),
    require("../../assets/welcome/welcome1.png"),
    require("../../assets/welcome/welcome3.png"),
  ];

  const handleSlideChange = (index) => {
    setCurrentIndex(index);
  };

  const handleAppleSignIn = () => {
    signInWithApple();
    setIsLoginModalVisible(false); // Close modal after sign-in
  };

  const handleGoogleSignIn = () => {
    signInWithGoogle();
    setIsLoginModalVisible(false); // Close modal after sign-in
  };

  const handleEmailLogin = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    navigation.navigate("Sign In");
    // Navigate to email login screen or perform login
    setIsLoginModalVisible(false); // Close modal after sign-in
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={["black", "white"]}
        style={{ flex: 1 }}
        start={{ x: 0, y: 0.6 }}
        end={{ x: 0, y: 0.2 }}
      >
        <View style={styles.contentContainer}>
          <Image
            source={require("../../assets/brandmark-design-logo.png")}
            style={{
              width: scaleSize(360),
              height: scaleSize(100),
              alignSelf: "center",
              marginBottom: scaleSize(20),
            }}
          />

          {/* Carousel Section */}
          <View style={{ flex: 0.6, justifyContent: "center" }}>
            {/* Adjusted flex */}
            <View
              style={{
                alignSelf: "center",
                flexDirection: "row",
                marginVertical: scaleSize(10),
              }}
            >
              {images.map((_, index) => (
                <View
                  key={index}
                  style={{
                    height: screenWidth * 0.025,
                    width: screenWidth * 0.025,
                    borderRadius: scaleSize(30),
                    backgroundColor:
                      index === currentIndex
                        ? theme.colors.primary
                        : "rgba(255, 255, 255, 0.5)",
                    marginHorizontal: scaleSize(5),
                  }}
                />
              ))}
            </View>
            <Carousel
              height={carouselHeight}
              width={carouselWidth}
              data={images}
              renderItem={({ item }) => (
                <View style={{ alignSelf: "center", flex: 1 }}>
                  <Image
                    contentFit="contain"
                    source={item}
                    style={{
                      width: carouselWidth,
                      height: carouselHeight,
                    }}
                  />
                </View>
              )}
              onSnapToItem={handleSlideChange}
            />
          </View>

          {/* Buttons Section */}
          <View
            style={{
              flex: 0.4, // Adjusted flex to reserve space for buttons
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
            }}
          >
            <TouchableOpacity
              onPress={() => {
                navigation.navigate("Sign Up");
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              }}
              style={{
                backgroundColor: "white",
                borderRadius: scaleSize(8),
                paddingVertical: scaleSize(12),
                marginVertical: scaleSize(15),
                width: "90%",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: "black",
                  fontSize: scaleSize(18),
                  fontWeight: "bold",
                }}
              >
                Start now
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                borderRadius: scaleSize(24),
                paddingVertical: scaleSize(10),
                marginVertical: scaleSize(15),
                width: "90%",
                alignItems: "center",
              }}
              onPress={() => {
                setIsLoginModalVisible(true); // Open the login modal
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              }}
            >
              <Text
                style={{
                  color: theme.colors.subTextColor,
                  fontSize: scaleSize(16),
                }}
              >
                Already have an account?
                <Text style={{ color: "white", fontSize: scaleSize(16) }}>
                  {" "}
                  Log In
                </Text>
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Login Modal */}
        <LoginModal
          isVisible={isLoginModalVisible}
          onClose={() => setIsLoginModalVisible(false)}
          handleAppleSignIn={handleAppleSignIn}
          handleGoogleSignIn={handleGoogleSignIn}
          handleEmailLogin={handleEmailLogin}
        />
      </LinearGradient>
    </SafeAreaView>
  );
}

export default WelcomeScreen;
