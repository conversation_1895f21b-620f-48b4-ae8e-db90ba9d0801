import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import {
  View,
  SectionList,
  ActivityIndicator,
  Animated,
  useWindowDimensions,
} from "react-native";

// Context hooks
import { useTime } from "../context/TimeContext.js";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";
import { useFoodMenu } from "../features/FoodMenu/context/FoodMenuContext.js";
import { useThemeContext } from "../context/ThemeContext.js";
import { useModal, MODAL_TYPES } from "../context/ModalContext.js";

// Utils
import { scaleSize, screenDimensions } from "../utils/deviceUtils";

// Components
import DateSelector from "../components/DateSelector.js";
import TimelineSection from "../features/FoodDiary/components/TimelineStyledDiary/TimelineSection.js";
import TimelineEntry from "../features/FoodDiary/components/TimelineStyledDiary/TimelineEntry.js";
import MealGroupHeader from "../features/FoodDiary/components/TimelineStyledDiary/MealGroupHeader.js";
import SectionFooter from "../features/FoodDiary/components/TimelineStyledDiary/SectionFooter.js";
import TimelineBackground from "../features/FoodDiary/components/TimelineStyledDiary/TimelineBackground.js";
import MealSectionToggleMenu from "../features/FoodDiary/components/TimelineStyledDiary/MealSectionMenu/MealSectionToggleMenu.js";
import CopyMealSectionFromDateModal from "../features/FoodDiary/components/MealSectionMenuOptions/CopyMealSectionFromDateModal.js";
import CarouselRenderItemComponent from "../features/FoodDiary/components/CarouselRenderItemComponent.js";
import CarouselWithIndicators from "../features/FoodDiary/components/CarouselWithIndicators.js";

// Get responsive screen dimensions
const { height: screenHeight, width: screenWidth } = screenDimensions();
export default function FoodDiaryScreen() {
  const { theme } = useThemeContext();
  const { selectedDate } = useTime();
  const { openModal } = useModal();

  // Food log context
  const {
    sections,
    mealSections,
    foodEntries,
    refreshSectionList,
    setRefreshSectionList,
    updateSections,
    activeMealSection,
    setActiveMealSection,
    setActiveFoodItem,
    calculateTotalCaloriesAndMacros,
    deleteMealSectionEntries,
    copyEntriesBetweenMealSections,
    findMealNameById,
    calorieData,
    caloriesBurned,
    totalDailyWaterConsumedPercentage,
    macroData,
  } = useFoodLog();

  // Food menu context
  const { clearTempCustomMeal, addLoggedFoodItemsToTempCustomMeal } =
    useFoodMenu();

  // Window dimensions
  const { height: screenHeight } = useWindowDimensions();

  // Refs
  const scrollY = useRef(new Animated.Value(0)).current;

  // State
  const [collapsedSections, setCollapsedSections] = useState([]);
  const [isCopyMealSectionModalVisible, setIsCopyMealSectionModalVisible] =
    useState(false);
  const [copyMealSectionModalType, setCopyMealSectionModalType] = useState("");

  // Responsive scaling
  const scaled = useMemo(
    () => ({
      paddingXS: scaleSize(2),
      paddingS: scaleSize(4),
      paddingM: scaleSize(6),
      paddingL: scaleSize(8),
      paddingXL: scaleSize(12),
      iconXS: scaleSize(16),
      iconS: scaleSize(20),
      iconM: scaleSize(24),
      iconL: scaleSize(28),
      borderRadiusS: scaleSize(4),
      borderRadiusM: scaleSize(8),
      borderRadiusL: scaleSize(12),
      shadowRadius: scaleSize(5),
      gapXS: scaleSize(4),
      gapS: scaleSize(6),
      gapM: scaleSize(8),
      gapL: scaleSize(12),
      fontS: scaleSize(12),
      fontM: scaleSize(14),
      fontL: scaleSize(16),
      fontXL: scaleSize(18),
    }),
    []
  );

  // ========================================================
  // EFFECTS
  // ========================================================

  // // Refresh sections when date or entries change
  // useEffect(() => {
  //   setRefreshSectionList(true);
  //   updateSections();
  // }, [selectedDate, mealSections, foodEntries]);

  // ========================================================
  // ACTION HANDLERS (Stable references)
  // ========================================================

  // Handle date confirmation for copy operations
  const handleDateConfirm = useCallback(
    (currentMealType, targetMealType, targetDate) => {
      if (copyMealSectionModalType === "To Date") {
        copyEntriesBetweenMealSections(
          currentMealType,
          targetMealType,
          selectedDate,
          targetDate
        );
      } else if (copyMealSectionModalType === "From Date") {
        copyEntriesBetweenMealSections(
          targetMealType,
          currentMealType,
          targetDate,
          selectedDate
        );
      }

      setIsCopyMealSectionModalVisible(false);
      setCopyMealSectionModalType("");
    },
    [copyMealSectionModalType, selectedDate]
  );

  // Open copy meal section modal
  const handleOpenCopyMealSectionModal = useCallback(
    (mealType, modalType) => {
      setCopyMealSectionModalType(modalType);
      setActiveMealSection(mealType);
      setIsCopyMealSectionModalVisible(true);
    },
    [setActiveMealSection]
  );

  // Toggle section collapse
  const toggleSection = useCallback((sectionId) => {
    setCollapsedSections((prev) =>
      prev.includes(sectionId)
        ? prev.filter((id) => id !== sectionId)
        : [...prev, sectionId]
    );
  }, []);

  // ========================================================
  // MODAL HANDLERS (Stable references)
  // ========================================================

  // Water log modal
  const handleOpenWaterLogEntryModal = useCallback(
    (activeItem) => {
      openModal(MODAL_TYPES.WATER_LOG_ENTRY, {
        activeItem: activeItem || null,
        onClose: () => setActiveFoodItem && setActiveFoodItem(null),
      });
    },
    [openModal, setActiveFoodItem]
  );

  // Quick add modal
  const openQuickAddModal = useCallback(
    (mealType, activeItem = null) => {
      // Validate mealType is string
      if (typeof mealType !== "string") {
        console.error("Invalid mealType:", mealType);
        mealType = "Meal 1"; // Fallback
      }

      // Validate activeItem
      if (activeItem && activeItem.type !== "food") {
        console.warn("Invalid activeItem type:", activeItem?.type);
        activeItem = null;
      }

      setActiveMealSection && setActiveMealSection(mealType);

      if (activeItem) {
        setActiveFoodItem && setActiveFoodItem(activeItem);
      } else {
        setActiveFoodItem && setActiveFoodItem(null);
      }

      openModal(MODAL_TYPES.QUICK_ADD, {
        mealType,
        activeItem,
      });
    },
    [setActiveMealSection, setActiveFoodItem, openModal]
  );

  // Food entry modal
  const handleOpenFoodEntryModal = useCallback(
    (mealType) => {
      openModal(MODAL_TYPES.FOOD_ENTRY, {
        initialMealSection: mealType,
        isBuildingMeal: false,
        onCancel: () => setActiveMealSection && setActiveMealSection(null),
      });
    },
    [openModal, selectedDate, setActiveMealSection]
  );

  // Food nutrient modal
  const handleOpenFoodNutrientModal = useCallback(
    (activeItem) => {
      setActiveFoodItem && setActiveFoodItem(activeItem);
      openModal(MODAL_TYPES.FOOD_NUTRIENT, {
        activeMealSection: {
          id: activeItem.mealType,
          name: findMealNameById(activeItem.mealType),
        },
        foodNutrientModalType: "Edit Entry",
        isBuildingMeal: false,
      });
    },
    [setActiveFoodItem, openModal, findMealNameById, selectedDate]
  );

  // ========================================================
  // DATA PROCESSING
  // ========================================================

  // Process sections to include default times and filter out unnamed sections
  const processedSections = useMemo(() => {
    const defaultTimes = {
      Breakfast: "08:00 AM",
      Lunch: "12:00 PM",
      Dinner: "06:00 PM",
      Snacks: "03:00 PM",
      Water: "All Day",
    };

    return sections
      .filter((section) => section.name && section.name.trim() !== "") // Filter out sections without names
      .map((section) => ({
        ...section,
        time: defaultTimes[section.name] || "12:00 PM",
      }));
  }, [sections]);

  // ========================================================
  // RENDER FUNCTIONS
  // ========================================================

  // Render section header
  const renderSectionHeader = useCallback(
    ({ section }) => {
      const totals = calculateTotalCaloriesAndMacros(section.data);
      const isCollapsed = collapsedSections.includes(section.id);

      // console.log(
      //   `[SECTION HEADER] Rendering ${section.id}: ` +
      //     `Calories=${totals.calories} ` +
      //     `(${section.data.length} entries)`
      // );

      return (
        <TimelineSection
          section={section}
          totalCalories={totals.calories}
          isCollapsed={isCollapsed}
          toggleSection={toggleSection}
          scaled={scaled}
        />
      );
    },
    [collapsedSections, toggleSection, calculateTotalCaloriesAndMacros]
  );

  // Render list item
  const renderItem = useCallback(
    ({ item, section, index }) => {
      // Added index parameter
      if (collapsedSections.includes(section.id)) return null;

      // console.log("Rendering item:", {
      //   id: item.id,
      //   type: item.type,
      //   label: item.foodLabel || item.name,
      // });

      // Calculate alignment based on index (alternating)
      const isLeftAligned = index % 2 === 0;

      // Handle meal groups (custom meals)
      if (item.type === "meal-group") {
        const totals = calculateTotalCaloriesAndMacros(item.items);

        return (
          <View>
            {/* Pass alignment to MealGroupHeader */}
            <MealGroupHeader
              header={{
                ...item.header,
                totalCalories: totals.calories,
              }}
              scaled={scaled}
              theme={theme}
              isGrouped={true}
              isLeftAligned={isLeftAligned}
            />

            {/* Pass alignment to TimelineEntry */}
            {item.items.map((entry) => (
              <TimelineEntry
                key={entry.id}
                item={entry}
                scaled={scaled}
                handleOpenFoodNutrientModal={handleOpenFoodNutrientModal}
                openQuickAddModal={openQuickAddModal}
                handleOpenWaterLogEntryModal={handleOpenWaterLogEntryModal}
                section={section}
                isGrouped={true}
                mealGroupItems={item.items}
                isLeftAligned={isLeftAligned} // Same alignment for all items in group
              />
            ))}
          </View>
        );
      }

      // Handle standalone entries
      return (
        <TimelineEntry
          item={item}
          scaled={scaled}
          handleOpenFoodNutrientModal={handleOpenFoodNutrientModal}
          openQuickAddModal={openQuickAddModal}
          handleOpenWaterLogEntryModal={handleOpenWaterLogEntryModal}
          section={section}
          isGrouped={false}
          isLeftAligned={isLeftAligned} // Pass alignment
        />
      );
    },
    [collapsedSections, calculateTotalCaloriesAndMacros]
  );

  // Render section footer
  const renderSectionFooter = useCallback(
    ({ section }) => {
      if (collapsedSections.includes(section.id)) return null;

      return (
        <SectionFooter
          section={section}
          scaled={scaled}
          theme={theme}
          handleOpenFoodEntryModal={handleOpenFoodEntryModal}
          handleOpenWaterLogEntryModal={handleOpenWaterLogEntryModal}
          // Pass ALL needed props directly to SectionFooter
          openQuickAddModal={openQuickAddModal}
          deleteMealSectionEntries={deleteMealSectionEntries}
          handleOpenCopyMealSectionModal={handleOpenCopyMealSectionModal}
          addLoggedFoodItemsToTempCustomMeal={
            addLoggedFoodItemsToTempCustomMeal
          }
          openModal={openModal}
          clearTempCustomMeal={clearTempCustomMeal}
          MODAL_TYPES={MODAL_TYPES}
        />
      );
    },
    [
      collapsedSections,
      handleOpenFoodEntryModal,
      handleOpenWaterLogEntryModal,
      openQuickAddModal,
      deleteMealSectionEntries,
      handleOpenCopyMealSectionModal,
      addLoggedFoodItemsToTempCustomMeal,
      openModal,
      clearTempCustomMeal,
    ]
  );

  // Responsive carousel dimensions
  const carouselContainerHeight = screenHeight * 0;
  const carouselHeight = screenHeight * 0.23;

  const carouselSlides = useMemo(
    () => [
      {
        type: "CaloriesAndWaterProgressSection",
        calorieData,
        caloriesBurned: caloriesBurned,
        waterProgressBarPercentage: totalDailyWaterConsumedPercentage / 100,
      },
      {
        type: "MacroProgressSection",
        macroData,
      },
    ],
    [calorieData, caloriesBurned, totalDailyWaterConsumedPercentage, macroData]
  );

  // Calculate the total number of items to render initially, including headers and footers
  // const totalItemsToRender = useMemo(() => {
  //   return sections.reduce((total, section) => {
  //     // Count each meal group as 1 item plus its items
  //     const sectionItemCount = section.data.reduce((count, item) => {
  //       if (item.items) {
  //         // Group counts as 1 item + its children
  //         return count + 1 + item.items.length;
  //       }
  //       return count + 1;
  //     }, 0);

  //     return total + sectionItemCount + 2; // +2 for header and footer
  //   }, 0);
  // }, [sections]);

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.screenBackground }}>
      {/* Background component */}
      <TimelineBackground scrollY={scrollY} screenHeight={screenHeight} />

      {/* Date Selector */}
      <DateSelector />

      {/* Carousel */}
      {/* <View
        style={{
          height: carouselContainerHeight,
          justifyContent: "center",
        }}
      >
        <CarouselWithIndicators
          carouselHeight={carouselHeight}
          carouselWidth={screenWidth}
          carouselSlides={carouselSlides}
          renderItem={CarouselRenderItemComponent}
          theme={theme}
        />
      </View> */}

      {/* Diary entries list */}
      <View style={{ flex: 1 }}>
        <SectionList
          sections={processedSections}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          renderSectionHeader={renderSectionHeader}
          renderSectionFooter={renderSectionFooter}
          contentContainerStyle={{
            paddingTop: scaleSize(16),
            paddingBottom: scaleSize(32),
          }}
          stickySectionHeadersEnabled={false}
          // onRefresh={updateSections}
          refreshing={refreshSectionList}
          style={{ flex: 1 }}
          ListFooterComponent={<View style={{ height: scaleSize(100) }} />}
        />
      </View>

      {/* Loading overlay */}
      {refreshSectionList && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0,0,0,0.2)",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 10,
          }}
        >
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      )}

      {/* Copy meal section modal */}
      <CopyMealSectionFromDateModal
        isVisible={isCopyMealSectionModalVisible}
        onClose={() => {
          setIsCopyMealSectionModalVisible(false);
          setCopyMealSectionModalType("");
        }}
        onDateConfirm={handleDateConfirm}
        activeMealSection={activeMealSection}
        modalType={copyMealSectionModalType}
      />
    </View>
  );
}
