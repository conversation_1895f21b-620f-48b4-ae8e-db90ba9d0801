import React, { useState, useEffect, useRef } from "react";
import { View, Animated, Easing, Dimensions, StyleSheet } from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { useThemeContext } from "../context/ThemeContext.js";
import { useNavigation } from "@react-navigation/native";
import { useAuth } from "../authentication/context/AuthContext.js";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";
import { useFoodLog } from "../features/FoodDiary/context/FoodLogContext.js";
import { useNutritionProgram } from "../features/NutritionalProgram/context/NutritionProgramContext.js";
import { useFoodMenu } from "../features/FoodMenu/context/FoodMenuContext.js";
import { useTutorial } from "../context/TutorialContext.js";
import { useDataLoading } from "../context/DataLoadingContext.js";
import { scalePercentage, scaleSize, isTablet } from "../utils/deviceUtils";

const { height: screenHeight, width: screenWidth } = Dimensions.get("window");

const AppLoadingScreen = () => {
  // Context hooks
  const {
    initialLoadComplete,
    setInitialLoadComplete,
    loadingStates,
    retryableLoad,
  } = useDataLoading();
  const { isCompleted } = useTutorial();
  const { user, token, loading } = useAuth();
  const { fetchUserSettings } = useUserSettings();
  const { loadMealSectionCustomizations, loadFoodEntries } = useFoodLog();
  const {
    loadActiveNutritionalProgram,
    loadInactiveNutritionalPrograms,
    actionState,
  } = useNutritionProgram();
  const { loadCustomMeals } = useFoodMenu();

  const { theme } = useThemeContext();
  const navigation = useNavigation();

  // Animation refs
  const progressValue = useRef(new Animated.Value(0)).current;
  const animationRef = useRef(null);
  const spinValue = useRef(new Animated.Value(0)).current;

  // 3D rotation animation setup
  useEffect(() => {
    const spinAnimation = Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 3000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
    spinAnimation.start();
    return () => spinAnimation.stop();
  }, []);

  // Data loading sequence
  useEffect(() => {
    const MAX_RETRIES = 3;
    const MIN_LOAD_DURATION = 3000; // Minimum 3 second loading experience

    const loadCriticalData = async () => {
      if (!loading && user && token) {
        try {
          const loadStartTime = Date.now();

          // Start progress animation
          animationRef.current = Animated.timing(progressValue, {
            toValue: 1,
            duration: MIN_LOAD_DURATION,
            easing: Easing.linear,
            useNativeDriver: false,
          });
          animationRef.current.start();

          // Sequential loading with dependencies
          // 1. Load core user configuration first
          await retryableLoad(fetchUserSettings, "userSettings");

          // 2. Load meal sections FIRST and capture return value
          const loadedSections = await retryableLoad(
            () => loadMealSectionCustomizations(),
            "mealSections"
          );

          // 3. Pass sections to dependent loads such as food diary entries
          await retryableLoad(
            () => loadFoodEntries(loadedSections),
            "foodEntries"
          );

          // 4. Load secondary data
          await Promise.allSettled([
            retryableLoad(
              () => loadActiveNutritionalProgram(user.uid),
              "nutritionProgram"
            ),
            retryableLoad(
              () => loadInactiveNutritionalPrograms(user.uid),
              "inactivePrograms"
            ),
            retryableLoad(loadCustomMeals, "customMeals"),
          ]);

          // Ensure minimum loading duration
          const elapsed = Date.now() - loadStartTime;
          const remaining = Math.max(MIN_LOAD_DURATION - elapsed, 0);
          await new Promise((resolve) => {
            Animated.timing(progressValue, {
              toValue: 1,
              duration: remaining,
              easing: Easing.linear,
              useNativeDriver: false,
            }).start(resolve);
          });

          setInitialLoadComplete(true);
        } catch (error) {
          console.error("Critical loading error:", error);
          setInitialLoadComplete(true); // Fail-safe to prevent blocking
        }
      }
    };

    loadCriticalData();
  }, [loading, user, token]);

  // Navigation handler
  useEffect(() => {
    const allDataLoaded = Object.values(loadingStates).every((state) => !state);

    if (initialLoadComplete && allDataLoaded && actionState !== "loading") {
      // Use tutorial context state directly
      navigation.navigate(isCompleted ? "Main" : "TutorialInitial");
    }
  }, [initialLoadComplete, loadingStates, actionState, isCompleted]);

  // Animation interpolations
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  const scale = spinValue.interpolate({
    inputRange: [0, 0.25, 0.5, 0.75, 1],
    outputRange: [1, 0.98, 0.96, 0.98, 1],
  });

  return (
    <LinearGradient
      colors={[theme.backgroundPrimary, theme.backgroundSecondary]}
      style={styles.container}
      start={{ x: 0, y: 0.8 }}
      end={{ x: 0, y: 0.2 }}
    >
      {/* 3D Compass Animation */}
      <View style={styles.perspectiveContainer}>
        <Animated.View
          style={[
            styles.compassContainer,
            {
              transform: [{ rotateY: spin }, { scale }, { perspective: 1000 }],
            },
          ]}
        >
          <Image
            source={require("../../assets/NutraCompass_Compass.png")}
            contentFit="contain"
            style={styles.compassImage}
          />
        </Animated.View>

        {/* Animated Shadow */}
        <Animated.View
          style={[
            styles.shadowContainer,
            { transform: [{ rotateY: spin }, { perspective: 1000 }] },
          ]}
        >
          <Image
            source={require("../../assets/NutraCompass_Shadow.png")}
            contentFit="contain"
            style={styles.shadowImage}
          />
        </Animated.View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                width: progressValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ["0%", "100%"],
                }),
              },
            ]}
          />
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  perspectiveContainer: {
    height: scalePercentage("55%"),
    alignItems: "center",
    transform: [{ perspective: 1000 }], // Parent perspective
  },
  compassContainer: {
    width: screenWidth * 0.95,
    height: screenWidth * 0.95,
    marginBottom: screenHeight * 0.02,
  },
  compassImage: {
    width: "100%",
    height: "100%",
  },
  shadowContainer: {
    width: screenWidth * 0.55, // Original shadow width
    height: screenWidth * 0.5 * 0.2, // Original shadow height
    marginTop: -screenWidth * 0.15,
  },
  shadowImage: {
    width: "100%",
    height: "100%",
  },
  progressContainer: {
    position: "absolute",
    bottom: 40,
    width: "80%",
    height: 4,
    backgroundColor: "#e0e0e0",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#2ecc71", // Use your theme color here
    borderRadius: 2,
  },
});

export default AppLoadingScreen;
