import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import useWorkoutDiaryScreenStyles from "./styles/workoutDiaryScreenStyles";
import TabModal from "../features/WorkoutDiary/components/TabModal"; // Ensure correct path
import CreateEditContent from "../features/WorkoutDiary/CreatePlan/CreateEditContent"; // Ensure correct path
import WorkoutPlanScreen from "../features/WorkoutDiary/WorkoutPlan/WorkutPlanScreen";

const WorkoutDiary = () => {
  const styles = useWorkoutDiaryScreenStyles();
  const [modalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState(null);

  const menuItems = [
    { title: "History", iconName: "history" },
    { title: "Create/Edit", iconName: "file-edit" },
    { title: "Workout Plans", iconName: "calendar-heart" },
    { title: "Preset Plans", iconName: "format-list-checkbox" },
    { title: "Notes", iconName: "notebook" },
    { title: "Exercises", iconName: "run" },
  ];

  const handleTabPress = (title) => {
    setActiveTab(title);
    setModalVisible(true);
  };

  const renderContent = () => {
    switch (activeTab) {
      case "Create/Edit":
        return <CreateEditContent onClose={() => setModalVisible(false)} />;

      case "Workout Plans":
        return <WorkoutPlanScreen onClose={() => setModalVisible(false)} />;
      // Add cases for other tabs
      default:
        return <Text>No Content Available</Text>;
    }
  };

  const overlayStyles = StyleSheet.create({
    overlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    text: {
      color: "white",
      fontSize: 20,
      marginBottom: 10, // Space above the icon
    },
    icon: {
      fontSize: 50,
      color: "white",
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.gridContainer}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.gridItem}
            onPress={() => handleTabPress(item.title)}
          >
            <Icon name={item.iconName} style={styles.icon} />
            <Text style={styles.text}>{item.title}</Text>
          </TouchableOpacity>
        ))}
      </View>
      <TabModal
        isVisible={modalVisible}
        onClose={() => setModalVisible(false)}
        renderContent={renderContent}
      />
    </View>
  );
};

export default WorkoutDiary;
