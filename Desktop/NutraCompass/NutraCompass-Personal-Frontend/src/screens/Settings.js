import React from "react";
import {
  Text,
  View,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import settingsScreenStyles from "./styles/settingsScreenStyles.js";
import { useThemeContext } from "../context/ThemeContext.js";
import { scaleSize } from "../utils/deviceUtils.js";

function SettingsScreen() {
  const styles = settingsScreenStyles();
  const { theme } = useThemeContext();
  const navigation = useNavigation();

  const handleNavigateToProfile = () => {
    navigation.navigate("ProfileSettings");
  };

  const handleNavigateToNotifications = () => {
    navigation.navigate("NotificationsSettings");
  };

  const handleNavigateToNutrition = () => {
    navigation.navigate("NutritionSettings");
  };

  const handleNavigateToMeasurement = () => {
    navigation.navigate("MeasurementSettings");
  };

  const handleNavigateToPermissions = () => {
    navigation.navigate("PermissionSettings");
  };

  const handleNavigateToAccount = () => {
    navigation.navigate("AccountSettings");
  };

  const handleNavigateToTutorial = () => {
    navigation.navigate("Tutorial");
  };

  const settingsOptions = [
    {
      name: "Profile",
      icon: "account-circle-outline",
      onPress: handleNavigateToProfile,
    },
    {
      name: "Nutrition Settings",
      icon: "food",
      onPress: handleNavigateToNutrition,
    },
    {
      name: "Measurement Units",
      icon: "swap-horizontal",
      onPress: handleNavigateToMeasurement,
    },
    {
      name: "Notifications",
      icon: "bell-outline",
      onPress: handleNavigateToNotifications,
    },
    {
      name: "Permissions",
      icon: "shield-key-outline",
      onPress: handleNavigateToPermissions,
    },
    {
      name: "Account",
      icon: "account-settings-outline",
      onPress: handleNavigateToAccount,
    },
    {
      name: "Tutorial",
      icon: "map-legend",
      onPress: handleNavigateToTutorial,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Text
        style={{
          alignSelf: "center",
          color: theme.colors.primaryTextColor,
          fontSize: scaleSize(18),
          fontWeight: "700",
          paddingBottom: scaleSize(18),
        }}
      >
        Settings
      </Text>

      <ScrollView contentContainerStyle={{ paddingBottom: scaleSize(50) }}>
        {settingsOptions.map((option, index) => (
          <TouchableOpacity
            key={index}
            onPress={option.onPress}
            style={[
              styles.rowContainer,
              index < settingsOptions.length - 1 && styles.rowBorder,
            ]}
          >
            <View style={styles.rowLeft}>
              <MaterialCommunityIcons
                name={option.icon}
                size={scaleSize(24)}
                color={theme.colors.primaryTextColor}
                style={styles.rowIcon}
              />
              <Text style={styles.rowText}>{option.name}</Text>
            </View>
            <MaterialCommunityIcons
              name="chevron-right"
              size={scaleSize(24)}
              color={theme.colors.subTextColor}
            />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

export default SettingsScreen;
