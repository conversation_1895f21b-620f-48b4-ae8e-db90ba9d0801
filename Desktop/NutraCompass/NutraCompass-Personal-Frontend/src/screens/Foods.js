import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Card, Button, Icon } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { Feather, Ionicons } from "@expo/vector-icons";
import { useThemeContext } from "../context/ThemeContext.js";
import { useFoodMenu } from "../features/FoodMenu/context/FoodMenuContext.js";
import { useModal, MODAL_TYPES } from "../context/ModalContext.js";
import { scaleSize } from "../utils/deviceUtils.js";
export default function FoodsScreen({ navigation }) {
  const { openModal } = useModal();
  const { theme } = useThemeContext();
  const { customMeals, clearTempCustomMeal } = useFoodMenu();

  return (
    <View
      style={{
        flex: 1,
        paddingBottom: "20%",
        backgroundColor: theme.colors.screenBackground,
      }}
    >
      {/** Header */}
      <LinearGradient
        style={{
          height: "12%",
          justifyContent: "flex-end",
          borderBottomLeftRadius: scaleSize(24),
          borderBottomRightRadius: scaleSize(24),
          borderColor: theme.colors.sectionBorderColor,
          borderLeftWidth: scaleSize(1),
          borderRightWidth: scaleSize(1),
          borderBottomWidth: scaleSize(1),
          backgroundColor: theme.colors.screenBackground,
          elevation: 4,
        }}
        colors={[
          `${theme.colors.primary}99`, // Adding "99" for 0.99 opacity
          `${theme.colors.secondary}99`, // Adding "99" for 0.99 opacity
        ]}
        start={{ x: 0, y: 1.5 }} // Top left corner
        end={{ x: 1, y: 2 }} // Bottom right corner
      >
        <View>
          <View
            style={{ paddingHorizontal: "5%", paddingVertical: scaleSize(5) }}
          >
            <View
              style={{
                height: "100%",
                justifyContent: "space-between",
                alignItems: "flex-end",
                flexDirection: "row",
              }}
            >
              <Text
                style={{
                  fontSize: scaleSize(28),
                  fontWeight: "500",
                  color: theme.colors.primaryTextColor,
                }}
              >
                Pantry
              </Text>

              <TouchableOpacity
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                  position: "absolute",
                  right: 0,
                }}
                onPress={() => {
                  navigation.navigate("Food Options");
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <Feather
                  name="search"
                  color={theme.colors.primaryTextColor}
                  size={scaleSize(28)}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>

      {/** Body */}
      <ScrollView style={{ flex: 1, padding: scaleSize(20) }}>
        <Card style={{ padding: scaleSize(8) }}>
          <Card.Content>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate("Custom Meals");
              }}
              style={{ flexDirection: "row" }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "flex-start",
                  gap: scaleSize(15),
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    gap: scaleSize(10),
                    alignItems: "center",
                  }}
                >
                  <Icon
                    source="silverware-fork-knife"
                    color={theme.colors.primary}
                    size={scaleSize(32)}
                  />
                  <View style={{ gap: scaleSize(5) }}>
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(18),
                        fontWeight: 600,
                      }}
                    >
                      Custom Meals
                    </Text>
                    <Text
                      style={{
                        fontSize: scaleSize(14),
                        color: theme.colors.primaryTextColor,
                      }}
                    >
                      {customMeals.length} Meals
                    </Text>
                  </View>
                </View>
                <TouchableOpacity
                  onPress={() => {
                    openModal(MODAL_TYPES.CREATE_CUSTOM_MEAL, {
                      onClose: () => {
                        clearTempCustomMeal();
                      },
                    });
                  }}
                  style={{
                    backgroundColor: theme.colors.primary,
                    borderRadius: scaleSize(8),
                    paddingVertical: scaleSize(10),
                    paddingHorizontal: scaleSize(16),
                    justifyContent: "center",
                    alignItems: "center",
                    elevation: 2,
                    shadowColor: "#000",
                    shadowOffset: { width: 0, height: scaleSize(2) },
                    shadowOpacity: 0.25,
                    shadowRadius: scaleSize(4),
                    marginVertical: scaleSize(8),
                    minWidth: scaleSize(150),
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Ionicons
                      name="add"
                      size={scaleSize(20)}
                      color={theme.colors.primaryTextColor}
                      style={{ marginRight: scaleSize(8) }}
                    />
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(14),
                        fontWeight: "bold",
                      }}
                    >
                      CREATE MEAL
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View style={{ flex: 1, alignItems: "flex-end" }}>
                <Feather
                  name="chevron-right"
                  color={theme.colors.primaryTextColor}
                  size={scaleSize(28)}
                />
              </View>
            </TouchableOpacity>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}
