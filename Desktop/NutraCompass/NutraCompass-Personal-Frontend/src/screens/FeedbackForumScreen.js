import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  TouchableOpacity,
  Animated,
  Easing,
} from "react-native";
import { useAuth } from "../authentication/context/AuthContext";
import { useThemeContext } from "../context/ThemeContext";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext";
import { scaleSize } from "../utils/deviceUtils";
import Configs from "../../configs";
import axios from "axios";
import * as Haptics from "expo-haptics";
import { MaterialCommunityIcons } from "@expo/vector-icons";

const CHAR_LIMIT = 500;
const apiUrl = Configs.NutraCompass_API_URL;

const FeedbackForumScreen = () => {
  const { user } = useAuth();
  const { getUserProfile } = useUserSettings();
  const { firstName, lastName } = getUserProfile();
  const { theme } = useThemeContext();
  const styles = feedbackForumStyles(theme);
  const textInputRef = useRef(null);

  const [message, setMessage] = useState("");
  const [buttonState, setButtonState] = useState("idle"); // 'idle', 'loading', 'success', 'error'
  const [statusMessage, setStatusMessage] = useState("");

  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  // Auto-focus keyboard on screen open
  useEffect(() => {
    const timer = setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Integrated sendFeedbackToNotion method
  const sendFeedbackToNotion = async (feedbackData) => {
    try {
      const response = await axios.post(`${apiUrl}/v1/feedback`, feedbackData);
      return response.data;
    } catch (error) {
      let errorMessage = "Failed to submit feedback";
      if (error.response) {
        errorMessage =
          error.response.data.error ||
          error.response.data.message ||
          `Server error: ${error.response.status}`;
      } else if (error.request) {
        errorMessage = "No response from server";
      } else {
        errorMessage = error.message;
      }
      throw new Error(errorMessage);
    }
  };

  const animateButton = (toValue, duration = 300) => {
    return new Promise((resolve) => {
      Animated.timing(scaleAnim, {
        toValue,
        duration,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start(() => resolve());
    });
  };

  const rotateButton = () => {
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  };

  const handleSubmit = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    Keyboard.dismiss();

    if (!message.trim()) {
      setStatusMessage("Message cannot be empty");
      setButtonState("error");
      setTimeout(() => setButtonState("idle"), 2000);
      return;
    }

    setButtonState("loading");
    rotateButton();

    try {
      await sendFeedbackToNotion({
        userId: user.uid,
        name: `${firstName} ${lastName}`,
        email: user.email,
        message: message.trim(),
        timestamp: new Date().toISOString(),
      });

      // Animate to success state
      await animateButton(0.8);
      setButtonState("success");
      setStatusMessage("Feedback submitted!");
      setMessage("");
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Reset after success
      setTimeout(() => {
        animateButton(1).then(() => setButtonState("idle"));
      }, 2000);
    } catch (error) {
      // Animate to error state
      await animateButton(0.8);
      setButtonState("error");
      setStatusMessage(error.message || "Failed to submit feedback");
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

      // Reset after error
      setTimeout(() => {
        animateButton(1).then(() => setButtonState("idle"));
      }, 3000);
    } finally {
      rotateAnim.setValue(0);
    }
  };

  // Animation values
  const rotateInterpolation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  const buttonScale = scaleAnim.interpolate({
    inputRange: [0.5, 1],
    outputRange: [0.5, 1],
  });

  const buttonStyles = {
    transform: [
      { scale: buttonScale },
      ...(buttonState === "loading" ? [{ rotate: rotateInterpolation }] : []),
    ],
    opacity: buttonState === "idle" ? 1 : 0.9,
  };

  const getButtonContent = () => {
    switch (buttonState) {
      case "loading":
        return (
          <MaterialCommunityIcons
            name="loading"
            size={scaleSize(26)}
            color={theme.colors.primaryTextColor}
          />
        );
      case "success":
        return (
          <MaterialCommunityIcons
            name="check-circle"
            size={scaleSize(26)}
            color={theme.colors.primaryTextColor}
          />
        );
      case "error":
        return (
          <MaterialCommunityIcons
            name="alert-circle"
            size={scaleSize(26)}
            color={theme.colors.primaryTextColor}
          />
        );
      default:
        return (
          <Text
            style={[
              styles.buttonText,
              { color: theme.colors.primaryTextColor },
            ]}
          >
            Submit Feedback
          </Text>
        );
    }
  };

  const getButtonBackground = () => {
    switch (buttonState) {
      case "success":
        return theme.colors.success || "#4CAF50";
      case "error":
        return theme.colors.error || "#F44336";
      case "loading":
        return theme.colors.primary;
      default:
        return theme.colors.primary;
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={styles.container}>
        <Text style={styles.title}>Suggestion Box</Text>

        <TextInput
          ref={textInputRef}
          multiline
          style={styles.input}
          placeholder="Your feedback (max 500 characters)"
          placeholderTextColor={theme.colors.subTextColor}
          value={message}
          onChangeText={setMessage}
          maxLength={CHAR_LIMIT}
          editable={buttonState === "idle"}
          autoFocus={true}
          blurOnSubmit={false}
          returnKeyType="done"
          onSubmitEditing={Keyboard.dismiss}
        />

        <Text style={styles.counter}>
          {message.length}/{CHAR_LIMIT}
        </Text>

        <View style={styles.buttonContainer}>
          <Animated.View
            style={[styles.statusContainer, { opacity: opacityAnim }]}
          >
            {buttonState !== "idle" && (
              <Text
                style={[
                  styles.statusText,
                  {
                    color:
                      buttonState === "error"
                        ? theme.colors.error
                        : theme.colors.primaryTextColor,
                  },
                ]}
              >
                {statusMessage}
              </Text>
            )}
          </Animated.View>

          <TouchableOpacity
            onPress={handleSubmit}
            disabled={buttonState !== "idle" || !message.trim()}
            style={[
              styles.button,
              {
                backgroundColor: getButtonBackground(),
                opacity: !message.trim() ? 0.5 : 1,
              },
            ]}
          >
            <Animated.View style={buttonStyles}>
              {getButtonContent()}
            </Animated.View>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const feedbackForumStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: scaleSize(20),
      backgroundColor: theme.colors.screenBackground,
    },
    title: {
      fontSize: scaleSize(24),
      fontWeight: "bold",
      marginBottom: scaleSize(20),
      color: theme.colors.primaryTextColor,
      textAlign: "center",
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.sectionBorderColor,
      borderRadius: theme.dimensions.cardBorderRadius,
      padding: scaleSize(15),
      minHeight: scaleSize(150),
      marginBottom: scaleSize(10),
      textAlignVertical: "top",
      color: theme.colors.primaryTextColor,
      backgroundColor: theme.colors.surface,
      ...Platform.select({
        ios: {
          shadowColor: theme.colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    counter: {
      alignSelf: "flex-end",
      color: theme.colors.subTextColor,
      marginBottom: scaleSize(20),
      fontSize: scaleSize(14),
    },
    buttonContainer: {
      alignItems: "center",
      marginBottom: scaleSize(20),
    },
    button: {
      width: "100%",
      height: scaleSize(50),
      borderRadius: theme.dimensions.buttonBorderRadius,
      justifyContent: "center",
      alignItems: "center",
      ...Platform.select({
        ios: {
          shadowColor: theme.colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
        },
        android: {
          elevation: 4,
        },
      }),
    },
    buttonText: {
      fontSize: scaleSize(16),
      fontWeight: "bold",
    },
    statusContainer: {
      marginBottom: scaleSize(10),
      minHeight: scaleSize(20),
    },
    statusText: {
      fontSize: scaleSize(14),
      textAlign: "center",
    },
  });

export default FeedbackForumScreen;
