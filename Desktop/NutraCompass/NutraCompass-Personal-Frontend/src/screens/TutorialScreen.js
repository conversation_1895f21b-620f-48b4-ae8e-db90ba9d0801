import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import Onboarding from "react-native-onboarding-swiper";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useTutorial } from "../context/TutorialContext.js";
import { useThemeContext } from "../context/ThemeContext.js";
import { scaleSize } from "../utils/deviceUtils.js";
import { Video, ResizeMode, Audio } from "expo-av";

const { width, height } = Dimensions.get("window");

const TutorialScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { markCompleted } = useTutorial();
  const { theme } = useThemeContext();
  const isInitial = route.params?.isInitial ?? false;
  const [currentPage, setCurrentPage] = useState(0);

  // Per-video state management
  const videoRefs = useRef([]);
  const [videoStates, setVideoStates] = useState([]);

  const videoSources = useRef([
    require("../../assets/videos/tutorials/CustomMeals.mp4"),
    require("../../assets/videos/tutorials/FoodDiaryLogging.mp4"),
    require("../../assets/videos/tutorials/FoodDiaryCalendar.mp4"),
    require("../../assets/videos/tutorials/UsingBarcodeScanner.mp4"),
    require("../../assets/videos/tutorials/NutritionalProgram.mp4"),
    require("../../assets/videos/tutorials/WaterConsumptionAndStepTracking.mp4"),
  ]).current;

  // Initialize video states
  useEffect(() => {
    setVideoStates(
      videoSources.map(() => ({
        isLoading: true,
        showControls: false,
        isPlaying: true,
      }))
    );
    videoRefs.current = videoSources.map(() => React.createRef());

    Audio.setAudioModeAsync({
      playsInSilentModeIOS: true,
      allowsRecordingIOS: false,
      staysActiveInBackground: false,
      shouldDuckAndroid: false,
      playThroughEarpieceAndroid: false,
    });

    return () => {
      // Clean up all videos on unmount
      videoRefs.current.forEach((ref) => {
        if (ref.current) {
          ref.current.unloadAsync();
        }
      });
    };
  }, []);

  // Handle page changes
  const handlePageChange = (index) => {
    // Pause current video
    const currentRef = videoRefs.current[currentPage]?.current;
    if (currentRef && videoStates[currentPage]?.isPlaying) {
      currentRef.pauseAsync();
    }

    setCurrentPage(index);

    // Play new video after short delay
    setTimeout(() => {
      const newRef = videoRefs.current[index]?.current;
      if (newRef && !videoStates[index]?.isPlaying) {
        newRef.playAsync().then(() => {
          updateVideoState(index, { isPlaying: true });
        });
      }
    }, 100);
  };

  // Update state for specific video
  const updateVideoState = (index, newState) => {
    setVideoStates((prev) => {
      const newStates = [...prev];
      newStates[index] = { ...newStates[index], ...newState };
      return newStates;
    });
  };

  // Handle video ready
  const handleReady = (index) => {
    updateVideoState(index, { isLoading: false });
  };

  // Handle playback status updates
  const handlePlaybackStatusUpdate = (index, playbackStatus) => {
    if (playbackStatus.isLoaded) {
      updateVideoState(index, { isPlaying: playbackStatus.isPlaying });
    }
  };

  // Handle completion of tutorial
  const handleCompletion = async () => {
    // Stop all videos
    videoRefs.current.forEach((ref) => {
      if (ref.current) {
        ref.current.stopAsync();
      }
    });

    if (isInitial) {
      markCompleted();
      navigation.replace("Main");
    } else {
      navigation.goBack();
    }
  };

  // Toggle playback for specific video
  const togglePlayback = async (index) => {
    const ref = videoRefs.current[index]?.current;
    if (!ref) return;

    try {
      if (videoStates[index]?.isPlaying) {
        await ref.pauseAsync();
      } else {
        await ref.playAsync();
      }
    } catch (error) {
      console.log("Playback toggle error:", error);
    }
  };

  // Replay specific video
  const replayVideo = async (index) => {
    const ref = videoRefs.current[index]?.current;
    if (ref) {
      try {
        await ref.setPositionAsync(0);
        await ref.playAsync();
      } catch (error) {
        console.log("Replay error:", error);
      }
    }
  };

  // Toggle controls for specific video
  const toggleControls = (index) => {
    updateVideoState(index, {
      showControls: !videoStates[index]?.showControls,
    });
  };

  // Custom Buttons with theme styling
  const DoneButton = ({ ...props }) => (
    <TouchableOpacity
      style={[styles.doneButton, { backgroundColor: theme.colors.primary }]}
      {...props}
    >
      <Text
        style={[styles.buttonText, { color: theme.colors.primaryTextColor }]}
      >
        {isInitial ? "Get Started" : "Finish"}
      </Text>
    </TouchableOpacity>
  );

  const SkipButton = ({ ...props }) => (
    <TouchableOpacity style={styles.skipButton} {...props}>
      <Text style={[styles.skipText, { color: theme.colors.subTextColor }]}>
        Skip
      </Text>
    </TouchableOpacity>
  );

  const NextButton = ({ ...props }) => (
    <TouchableOpacity {...props}>
      <Text style={[styles.nextText, { color: theme.colors.primaryTextColor }]}>
        Next
      </Text>
    </TouchableOpacity>
  );

  const Dot = ({ selected }) => (
    <View
      style={[
        styles.dot,
        {
          backgroundColor: selected
            ? theme.colors.primary
            : theme.colors.subTextColor,
        },
      ]}
    />
  );

  // Action-oriented titles based on video content
  const tutorialPages = [
    {
      title: "Create Custom Meals",
      subtitle: "Combine foods to build your own meals and save them",
    },
    {
      title: "Log Food in Your Diary",
      subtitle: "Quickly add foods and track your daily nutrition",
    },
    {
      title: "Review Past Diaries",
      subtitle: "Navigate through calendar to see previous entries",
    },
    {
      title: "Scan Food Barcodes",
      subtitle: "Instantly log packaged foods with your camera",
    },
    {
      title: "Join Nutrition Programs",
      subtitle: "Enroll in guided programs for your health goals",
    },
    {
      title: "Track Water & Activity",
      subtitle: "Set goals and log your hydration and steps",
    },
  ];

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: theme.colors.screenBackground },
      ]}
    >
      <Onboarding
        pages={tutorialPages.map((page, index) => ({
          backgroundColor: "transparent",
          image: (
            <View
              style={{
                ...styles.videoContainer,
                backgroundColor: theme.colors.screenBackground || "#000",
              }}
            >
              {/* Video component for each slide */}
              <Video
                ref={videoRefs.current[index]}
                source={videoSources[index]}
                style={styles.video}
                resizeMode={ResizeMode.CONTAIN}
                useNativeControls={false}
                isLooping
                isMuted
                onReadyForDisplay={() => handleReady(index)}
                onLoadStart={() => updateVideoState(index, { isLoading: true })}
                onError={(error) => {
                  console.log("Video Error", error);
                  updateVideoState(index, { isLoading: false });
                }}
                onPlaybackStatusUpdate={(status) =>
                  handlePlaybackStatusUpdate(index, status)
                }
              />

              {videoStates[index]?.isLoading && (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator
                    size="large"
                    color={theme.colors.primary}
                  />
                  <Text style={{ color: "white", marginTop: 10 }}>
                    Loading video...
                  </Text>
                </View>
              )}

              <TouchableOpacity
                style={styles.controlOverlay}
                onPress={() => toggleControls(index)}
                activeOpacity={0.9}
              >
                {videoStates[index]?.showControls &&
                  !videoStates[index]?.isLoading && (
                    <View style={styles.controlsContainer}>
                      <TouchableOpacity
                        onPress={() => replayVideo(index)}
                        style={styles.controlButton}
                      >
                        <Text
                          style={[
                            styles.controlText,
                            { color: theme.colors.primaryTextColor },
                          ]}
                        >
                          ↺
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => togglePlayback(index)}
                        style={styles.controlButton}
                      >
                        <Text
                          style={[
                            styles.controlText,
                            { color: theme.colors.primaryTextColor },
                          ]}
                        >
                          {videoStates[index]?.isPlaying ? "❚❚" : "▶"}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
              </TouchableOpacity>
            </View>
          ),
          title: page.title,
          subtitle: page.subtitle,
          titleStyles: [styles.title, { color: theme.colors.primaryTextColor }],
          subTitleStyles: [
            styles.subtitle,
            { color: theme.colors.subTextColor },
          ],
        }))}
        onDone={handleCompletion}
        onSkip={handleCompletion}
        onChange={handlePageChange}
        DoneButtonComponent={DoneButton}
        SkipButtonComponent={SkipButton}
        NextButtonComponent={NextButton}
        DotComponent={Dot}
        bottomBarHeight={scaleSize(80)}
        bottomBarColor={theme.colors.surface}
        containerStyles={styles.onboardingContainer}
        imageContainerStyles={styles.pageImageContainer}
        titleStyles={styles.titleContainer}
        subTitleStyles={styles.subtitleContainer}
        showSkip={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  videoContainer: {
    flex: 1,
    width: width * 0.9,
    height: height * 0.4,
    marginBottom: scaleSize(20),
    borderRadius: 12,
    overflow: "hidden",
    position: "relative",
  },
  video: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  onboardingContainer: {
    flex: 1,
    paddingHorizontal: scaleSize(20),
    paddingBottom: scaleSize(40),
    justifyContent: "space-between",
  },
  titleContainer: {
    justifyContent: "flex-end",
    paddingHorizontal: scaleSize(20),
  },
  title: {
    fontSize: scaleSize(32),
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: scaleSize(16),
  },
  subtitleContainer: {
    flex: 0.35,
    justifyContent: "flex-start",
    paddingHorizontal: scaleSize(30),
  },
  subtitle: {
    fontSize: scaleSize(18),
    textAlign: "center",
    lineHeight: scaleSize(24),
  },
  doneButton: {
    borderRadius: scaleSize(24),
    paddingHorizontal: 32,
    paddingVertical: 12,
    marginRight: 20,
  },
  buttonText: {
    fontSize: scaleSize(16),
    fontWeight: "600",
  },
  skipButton: {
    marginLeft: scaleSize(20),
    padding: scaleSize(10),
  },
  skipText: {
    fontSize: scaleSize(16),
  },
  nextText: {
    fontSize: scaleSize(16),
    marginRight: scaleSize(20),
    fontWeight: "600",
  },
  dot: {
    width: scaleSize(8),
    height: scaleSize(8),
    borderRadius: scaleSize(4),
    marginHorizontal: scaleSize(4),
  },
  pageImageContainer: {
    flex: 1,
    justifyContent: "center",
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.7)",
  },
  controlOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "flex-end",
    alignItems: "center",
    paddingBottom: 20,
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.7)",
    borderRadius: 25,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  controlButton: {
    marginHorizontal: 30,
    padding: 10,
  },
  controlText: {
    fontSize: 30,
    fontWeight: "bold",
  },
});

export default TutorialScreen;
