import React, { useState } from "react";
import {
  Text,
  View,
  TouchableOpacity,
  Alert,
  Keyboard,
  Platform,
  SafeAreaView,
  TouchableWithoutFeedback,
} from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import Feather from "react-native-vector-icons/Feather";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../context/ThemeContext.js";
import { TextInput, Button } from "react-native-paper";
import signinScreenStyles from "./styles/signinScreenStyles.js";
import { useAuth } from "../authentication/context/AuthContext.js";
import ResetPasswordModal from "../authentication/components/ResetPasswordModal.js";
import { scaleSize } from "../utils/deviceUtils.js";
// Validation utilities
const validateEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
const validatePassword = (password) => {
  if (password.length < 8) {
    return "Password must be at least 8 characters";
  }
  if (!/[A-Z]/.test(password)) {
    return "Password must contain at least one uppercase letter";
  }
  if (!/[a-z]/.test(password)) {
    return "Password must contain at least one lowercase letter";
  }
  if (!/[0-9]/.test(password)) {
    return "Password must contain at least one number";
  }
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return "Password must contain at least one special character";
  }
  return null;
};

function SignInScreen({ navigation }) {
  const styles = signinScreenStyles(); // Use the imported styles
  const { theme } = useThemeContext();
  const { signIn } = useAuth();

  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] =
    useState(false);
  const [emailIconColor, setEmailIconColor] = useState("transparent");
  const [passwordIconColor, setPasswordIconColor] = useState("transparent");
  const [isSecureTextEntry, setIsSecureTextEntry] = useState(true);
  const [errors, setErrors] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  const [value, setValue] = useState({
    email: "",
    password: "",
  });

  // Validate the form whenever inputs change
  const handleInputChange = (field, text) => {
    setValue((prev) => ({ ...prev, [field]: text }));

    const newErrors = { ...errors };

    // if (field === "email") {
    //   if (!validateEmail(text)) {
    //     newErrors.email = "Invalid email address.";
    //   } else {
    //     delete newErrors.email;
    //   }
    // }

    // if (field === "password") {
    //   const passwordError = validatePassword(text); // Get the actual error message
    //   if (passwordError) {
    //     newErrors.password = passwordError; // Use the exact message from validation
    //   } else {
    //     delete newErrors.password;
    //   }
    // }

    // setErrors(newErrors);
    // setIsFormValid(Object.keys(newErrors).length === 0);
  };

  const handleSignIn = async () => {
    // if (!isFormValid) {
    //   Alert.alert("Invalid Input", "Please enter a valid email and password.");
    //   return;
    // }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Keyboard.dismiss(); // Unfocus the input fields
    setIsLoggingIn(true); // Disable the login button and show "Signing In..."

    try {
      await signIn("<EMAIL>", "Am123456?");
      setValue({ email: "", password: "" }); // Reset input fields after successful login
    } catch (error) {
      // Differentiate error messages
      if (error.message.includes("auth")) {
        console.log("Login Failed", "The email or password is incorrect.");
      } else {
        console.log(
          "Login Failed",
          "An error occurred during validation. Please try again later."
        );
      }
    } finally {
      setIsLoggingIn(false); // Re-enable the login button
      setIsFormValid(false); // Disable login until corrected
    }
  };

  const toggleResetPasswordModal = () => {
    setIsResetPasswordModalVisible(!isResetPasswordModalVisible);
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
      }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <LinearGradient
          colors={["black", "white"]}
          style={{
            flex: 1,
          }}
          start={{ x: 0, y: 0.6 }}
          end={{ x: 0, y: 0.2 }}
          pointerEvents="box-none"
        >
          <View
            style={{
              height: scaleSize(85),
              minWidth: "100%",
              justifyContent: "center",
            }}
          >
            <TouchableOpacity
              style={{ alignSelf: "flex-start", padding: scaleSize(15) }}
              onPress={() => {
                navigation.navigate("Welcome");
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Feather
                name="chevron-left"
                color={"black"}
                size={scaleSize(38)}
              />
            </TouchableOpacity>
          </View>

          <View style={{ flex: 1 }}>
            <Image
              source={require("../../assets/brandmark-design-logo.png")}
              style={styles.logo}
            />
            <View
              style={{
                ...styles.contentContainer,
                gap: scaleSize(10),
                paddingTop: "15%",
              }}
            >
              <View
                style={{
                  justifyContent: "center",
                  width: "100%",
                  gap: scaleSize(20),
                }}
              >
                <TextInput
                  placeholder="EMAIL"
                  placeholderTextColor={"white"}
                  value={value.email}
                  style={{
                    backgroundColor: "transparent",
                    paddingLeft: scaleSize(12),
                    fontSize: scaleSize(16),
                  }}
                  textColor="white"
                  outlineStyle={{ borderRadius: scaleSize(14) }}
                  onChangeText={(text) => handleInputChange("email", text)}
                  autoCapitalize="none"
                  mode="flat"
                  onFocus={() => {
                    setEmailIconColor("white");
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  onBlur={() => setEmailIconColor("transparent")}
                  left={
                    <TextInput.Icon
                      icon="account"
                      color={"lightgray"}
                      style={{ backgroundColor: emailIconColor }}
                      size={scaleSize(24)}
                    />
                  }
                />
                {errors.email && (
                  <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                    {errors.email}
                  </Text>
                )}

                <TextInput
                  placeholder="PASSWORD"
                  placeholderTextColor={"white"}
                  value={value.password}
                  style={{
                    backgroundColor: "transparent",
                    paddingLeft: scaleSize(12),
                    fontSize: scaleSize(16),
                  }}
                  textColor="white"
                  outlineStyle={{ borderRadius: scaleSize(14) }}
                  onChangeText={(text) => handleInputChange("password", text)}
                  secureTextEntry={isSecureTextEntry}
                  autoCapitalize="none"
                  mode="flat"
                  onFocus={() => {
                    setPasswordIconColor("white");
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  onBlur={() => setPasswordIconColor("transparent")}
                  left={
                    <TextInput.Icon
                      icon="lock"
                      color={"lightgray"}
                      style={{ backgroundColor: passwordIconColor }}
                      size={scaleSize(24)}
                    />
                  }
                  right={
                    <TextInput.Icon
                      icon="eye"
                      color={"lightgray"}
                      onPress={() => setIsSecureTextEntry(!isSecureTextEntry)}
                      size={scaleSize(24)}
                    />
                  }
                />
                {errors.password && (
                  <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                    {errors.password}
                  </Text>
                )}
              </View>

              <TouchableOpacity
                onPress={handleSignIn}
                style={{
                  backgroundColor: "white",
                  borderRadius: scaleSize(8),
                  paddingVertical: scaleSize(8),
                  marginVertical: scaleSize(15),
                  width: "90%",
                  justifyContent: "center",
                  alignItems: "center",
                  opacity: !isFormValid || isLoggingIn ? 0.6 : 1,
                }}
              >
                <Text
                  style={{
                    color: "black",
                    fontSize: scaleSize(18),
                    fontWeight: "bold",
                  }}
                >
                  {isLoggingIn ? "LOGGING IN..." : "LOG IN"}
                </Text>
              </TouchableOpacity>

              <Text
                style={{
                  color: "gray",
                  marginTop: scaleSize(5),
                  marginBottom: scaleSize(15),
                  fontSize: scaleSize(14),
                }}
                onPress={() => {
                  toggleResetPasswordModal();
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                Forgot your password?
              </Text>
              <Text
                style={{
                  color: "white",
                  fontSize: scaleSize(16),
                  marginTop: scaleSize(20),
                  textAlign: "center",
                  marginBottom: scaleSize(20),
                }}
              >
                Don't Have an account?{" "}
                <Text
                  style={{ color: "white" }}
                  onPress={() => {
                    navigation.navigate("Sign Up");
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                >
                  SIGN UP
                </Text>
              </Text>
            </View>
          </View>
        </LinearGradient>
      </TouchableWithoutFeedback>
      <ResetPasswordModal
        visible={isResetPasswordModalVisible}
        onClose={() => setIsResetPasswordModalVisible(false)}
      />
    </SafeAreaView>
  );
}

export default SignInScreen;
