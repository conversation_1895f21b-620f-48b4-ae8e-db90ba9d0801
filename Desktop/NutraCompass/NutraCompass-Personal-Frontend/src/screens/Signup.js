import React, { useEffect, useState, useRef } from "react";
// import { Asset } from "expo-asset";
// const logo = Asset.fromModule(
//   require("../../assets/brandmark-design-logo.png")
// );
// logo.downloadAsync();
import Icon from "@expo/vector-icons/MaterialCommunityIcons";
import {
  Text,
  View,
  Alert,
  TouchableWithoutFeedback,
  TouchableOpacity,
  Keyboard,
  SafeAreaView,
  Dimensions,
  Platform,
} from "react-native";
import { Image } from "expo-image";
// Firebase API method imports
import { useAuth } from "../authentication/context/AuthContext.js";
import { useTime } from "../context/TimeContext.js";
import { MaterialIcons, FontAwesome } from "@expo/vector-icons";
import Feather from "react-native-vector-icons/Feather";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../context/ThemeContext.js";
import AboutYouSection from "../authentication/components/AboutYouSection.js";
import ActivityLevelSection from "../authentication/components/ActivityLevelSection.js";
import SetAGoalSection from "../authentication/components/SetAGoalSection.js";
import AccountDetailsSection from "../authentication/components/AccountDetailsSection.js";
import BodyTypeSection from "../authentication/components/BodyTypeSection.js";
import { LinearGradient } from "expo-linear-gradient";
import { scaleSize } from "../utils/deviceUtils.js";
import * as AppleAuthentication from "expo-apple-authentication";

const screenWidth = Dimensions.get("window").width;

const SectionBarComponent = ({ currentSection }) => {
  const { theme } = useThemeContext();

  // Render nothing if currentSection is greater than or equal to 5
  if (currentSection >= 5) {
    return null;
  }

  return (
    <View
      style={{
        marginVertical: scaleSize(10),
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "row",
        gap: scaleSize(10),
      }}
    >
      <View
        style={{
          borderRadius: scaleSize(16),
          width: scaleSize(50),
          height: scaleSize(8),
          backgroundColor:
            currentSection >= 1
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />

      <View
        style={{
          borderRadius: scaleSize(16),
          width: scaleSize(50),
          height: scaleSize(8),
          backgroundColor:
            currentSection >= 2
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />

      <View
        style={{
          borderRadius: scaleSize(16),
          width: scaleSize(50),
          height: scaleSize(8),
          backgroundColor:
            currentSection >= 3
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />

      <View
        style={{
          borderRadius: scaleSize(16),
          width: scaleSize(50),
          height: scaleSize(8),
          backgroundColor:
            currentSection >= 4
              ? theme.colors.primary
              : "rgba(169, 169, 169, 0.7)",
        }}
      />
    </View>
  );
};

function SignUpScreen({ navigation }) {
  const { theme, mode } = useThemeContext();
  const {
    registration,
    signInWithApple,
    signInWithGoogle,
    checkEmailExists,
    sendVerificationCode,
    verifyCode,
    resendCode,
  } = useAuth();
  const { deviceTimezone, deviceRegion } = useTime();

  const [currentSection, setCurrentSection] = useState(1);
  const [value, setValue] = useState({
    sex: "",
    birthday: "",
    age: null,
    height: { inches: null, centimeters: null, unit: "in" },
    weight: "",
    activityLevel: "",
    maintenanceCalories: null,
    weightTrendGoal: null,
    customEnergyTarget: null,
    bodyFatPercentageRange: null,
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    error: "",
  });

  const emptyState = () => {
    setValue({
      sex: "",
      birthday: "",
      age: null,
      height: { inches: null, centimeters: null, unit: "in" },
      weight: "",
      activityLevel: "",
      maintenanceCalories: null,
      weightTrendGoal: null,
      customEnergyTarget: null,
      bodyFatPercentageRange: null,
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      error: "",
    });
  };

  const registrationDataRef = useRef(null);

  // Function to capture registration data
  const captureRegistrationData = (data) => {
    registrationDataRef.current = data;
  };

  const handleLoginNavigation = () => {
    navigation.navigate("Sign In"); // Replace with your actual login screen name
  };

  const handleNextSection = () => {
    setCurrentSection(currentSection + 1);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    //console.log("Values: ", JSON.stringify(value, null, 1));
  };

  const handleAppleSignup = async () => {
    try {
      // Calculate settings from form data (same as Google flow)
      const weightUnit = determineWeightUnit(value.weight);
      const parsedWeight = convertWeightToKg(value.weight, weightUnit);
      const firstCalorieGoal = calculateFirstCalorieGoal(value, parsedWeight);

      // Create settings object from form values
      const defaultSettings = createDefaultUserSettings(
        value,
        firstCalorieGoal
      );

      // Execute Apple sign-up with form data
      await signInWithApple(defaultSettings);
    } catch (error) {
      console.error("Apple sign-in error:", error);
    }
  };

  const handleGoogleSignUp = async () => {
    try {
      // Calculate settings from form data
      const weightUnit = determineWeightUnit(value.weight);
      const parsedWeight = convertWeightToKg(value.weight, weightUnit);
      const firstCalorieGoal = calculateFirstCalorieGoal(value, parsedWeight);

      // Create settings object from form values
      const defaultSettings = createDefaultUserSettings(
        value,
        firstCalorieGoal
      );

      // Execute Google sign-up with form data
      await signInWithGoogle(defaultSettings);
    } catch (error) {
      console.error("Google sign-in error:", error);
    }
  };

  /**
   * Handles the sign-up process, including sending the verification code.
   */
  const handleSignUp = async () => {
    if (!isFormValid(value)) {
      return; // Exit early if the form is invalid
    }

    try {
      // Send the verification code to the user's email
      await sendVerificationCode(value.email, value.firstName);
      return true; // Indicate success
    } catch (error) {
      console.error("Error during signup:", error);
      Alert.alert("Signup Error", "Unable to proceed with signup.");
      return false; // Indicate failure
    }
  };

  /**
   * Verifies the code entered by the user and completes the registration process.
   */
  const handleVerifyCode = async (email, code) => {
    if (!email || !code) return;

    try {
      const isVerified = await verifyCode(email, code);

      if (isVerified) {
        // Use captured data from ref
        const data = registrationDataRef.current;

        if (!data) {
          throw new Error("Registration data is missing");
        }

        // Calculate settings
        const weightUnit = determineWeightUnit(data.weight);

        const parsedWeight = convertWeightToKg(data.weight, weightUnit);

        const firstCalorieGoal = calculateFirstCalorieGoal(data, parsedWeight);

        const defaultSettings = createDefaultUserSettings(
          data,
          firstCalorieGoal
        );

        // Perform registration
        await registration(data.email, data.password, defaultSettings);

        // Reset state
        emptyState();
        return true;
      } else {
        Alert.alert("Verification Failed", "Invalid code. Please try again.");
        return false;
      }
    } catch (error) {
      console.error("Verification Error:", error);
      Alert.alert(
        "Verification Failed",
        "An error occurred during verification."
      );
      return false;
    }
  };

  /**
   * Handles resending the verification code if the user didn't receive it.
   */
  const handleResendCode = async (email) => {
    try {
      await resendCode(email, value.firstName);
      //Alert.alert("Verification Code Resent", "Please check your email.");
    } catch (error) {
      console.error("Resend Code Error:", error);
      Alert.alert("Error", "Unable to resend verification code.");
    }
  };

  /**
   * Validates the sign-up form inputs.
   */
  const isFormValid = (value) => {
    if (!value.firstName || !value.lastName) {
      Alert.alert("Please enter your first name and last name.");
      return false;
    }
    if (!value.userName) {
      Alert.alert("Please enter your user name.");
      return false;
    }
    if (!value.email || !value.password) {
      Alert.alert("Email and password are mandatory.");
      return false;
    }
    if (value.password !== value.confirmPassword) {
      Alert.alert("Password and Confirm Password don't match.");
      return false;
    }
    return true;
  };

  // Helper to safely parse numbers
  const safeParseFloat = (value) => {
    if (value === null || value === undefined) {
      console.warn("[safeParseFloat] Received null/undefined, returning 0");
      return 0;
    }

    if (typeof value === "number") {
      return value;
    }

    if (typeof value === "string") {
      // Remove non-numeric characters except decimal point
      const cleaned = value.replace(/[^0-9.]/g, "");
      const parsed = parseFloat(cleaned);

      if (isNaN(parsed)) {
        console.warn(
          `[safeParseFloat] Failed to parse string: "${value}", returning 0`
        );
        return 0;
      }

      return parsed;
    }

    console.warn(
      `[safeParseFloat] Unsupported type: ${typeof value}, value:`,
      value
    );
    return 0;
  };

  /**
   * Determines the weight unit (lbs or kg) from the weight input.
   */
  const determineWeightUnit = (weight) => {
    if (!weight) return null;

    if (typeof weight === "string") {
      const weightParts = weight.trim().split(" ");
      return weightParts.length === 2 ? weightParts[1].toLowerCase() : null;
    }

    // Handle cases where weight might be a number
    return null;
  };

  /**
   * Converts weight to kilograms based on the provided unit.
   */
  const convertWeightToKg = (weight, unit) => {
    // Extract numeric value from string like "150 lbs"
    const numericValue =
      typeof weight === "string"
        ? parseFloat(weight.replace(/[^0-9.]/g, ""))
        : weight;

    if (unit === "lbs") {
      return numericValue * 0.45359237; // lbs to kg
    }
    return numericValue; // already in kg
  };

  /**
   * Calculates the first daily calorie goal for the user.
   */
  const calculateFirstCalorieGoal = (value, weightInKg) => {
    try {
      // Try custom targets first
      if (value?.customEnergyTarget) {
        const customTarget = safeParseFloat(value.customEnergyTarget);

        return customTarget;
      }

      // Then maintenance calories
      if (value?.maintenanceCalories) {
        const maintenance = safeParseFloat(value.maintenanceCalories);

        return maintenance;
      }

      // Safely parse all values
      const heightInCm =
        value.height?.centimeters ||
        (value.height?.inches ? value.height.inches * 2.54 : 0);
      const age = safeParseFloat(value.age);
      const height = safeParseFloat(heightInCm);

      // Validate all required fields exist
      const hasRequiredFields =
        value?.activityLevel &&
        weightInKg > 0 &&
        value?.sex &&
        age > 0 &&
        height > 0;

      if (hasRequiredFields) {
        const calories = calculateDailyCalories(
          height,
          weightInKg,
          value.sex,
          age,
          value.activityLevel,
          value?.weightTrendGoal,
          value?.bodyFatPercentageRange
        );

        return calories;
      } else {
        console.warn(
          "[calculateFirstCalorieGoal] Missing required fields for calculation"
        );
      }
    } catch (error) {
      console.error("[calculateFirstCalorieGoal] Error in calculation:", error);
    }

    return 2000; // Fallback if all else fails
  };
  const createDefaultUserSettings = (value, calorieGoal) => {
    // Validate inputs
    if (!value || typeof value !== "object") {
      console.error(
        "Invalid value parameter in createDefaultUserSettings",
        value
      );
      value = {}; // Use empty object as fallback
    }

    // Ensure calorieGoal is a valid number
    calorieGoal = Math.max(
      1000,
      Math.min(
        5000,
        typeof calorieGoal === "number" && !isNaN(calorieGoal)
          ? Math.round(calorieGoal)
          : 2000
      )
    );

    // Safe calculation functions
    const safeMacroCalculation = (calories, percentage, type) => {
      const dailyCalories = calories * percentage;
      return {
        dailyPercentage: percentage,
        dailyCalories: Math.round(dailyCalories),
        dailyGrams: Math.round(dailyCalories / (type === "fat" ? 9 : 4)),
      };
    };

    return {
      profile: {
        firstName: value?.firstName || "",
        lastName: value?.lastName || "",
        userName: value?.userName || "",
        userNameLower: (value?.userName || "").toLowerCase(),
        email: value?.email || "",
        birthday: value?.birthday || "",
        age: value?.age ? safeParseFloat(value.age) : 0,
        sex: value?.sex || "male",
        bodyWeight: value?.weight || "0 lbs",
        height: {
          inches: value?.height?.inches || 0,
          centimeters: value?.height?.centimeters || 0,
          unit: value?.height?.unit || "in",
        },
        pictureUrl: "",
      },
      location: {
        timezone: deviceTimezone,
        region: deviceRegion,
      },
      appAppearance: {
        theme: "Default",
        isDark: true,
      },
      nutritionalGoals: {
        calorieGoal,
        waterGoal: { amount: 64, unit: "fl oz" },
        macroGoals: {
          carb: safeMacroCalculation(calorieGoal, 0.4, "carb"),
          protein: safeMacroCalculation(calorieGoal, 0.3, "protein"),
          fat: safeMacroCalculation(calorieGoal, 0.3, "fat"),
        },
      },
      physicalFitnessGoals: {
        stepsGoal: 10000,
        distanceGoal: 4.73,
        distanceUnit: "mi",
      },
      measurementSettings: {
        waterUnit: "fl oz", // Default: Fluid Ounces
        distanceUnit: "mi", // Default: Miles
        bodyWeightUnit: "lbs", // Default: Pounds
        // foodUnit: "g", // Default: Grams
      },
    };
  };

  // HELPER METHODS FOR DETERMINING DEFAULT USER SETTINGS VALUES

  const calculateDailyCalories = (
    height,
    weight,
    sex,
    age,
    activityLevel,
    weightTrendGoal,
    bodyFat
  ) => {
    // BMR Calculation (Mifflin-St Jeor)
    let bmr;
    if (sex === "Male") {
      bmr = 10 * weight + 6.25 * height - 5 * age + 5;
    } else {
      bmr = 10 * weight + 6.25 * height - 5 * age - 161;
    }

    // Activity Multiplier
    const activityMultipliers = {
      Sedentary: 1.2,
      "Lightly Active": 1.375,
      "Moderately Active": 1.55,
      "Very Active": 1.725,
      "Extremely Active": 1.9,
    };

    const multiplier = activityMultipliers[activityLevel] || 1.2;

    let calories = bmr * multiplier;

    // Weight Trend Adjustment
    if (weightTrendGoal === "Lose Weight") {
      calories -= 500; // Deficit
    } else if (weightTrendGoal === "Gain Weight") {
      calories += 500; // Surplus
    }

    // Body Fat Adjustment
    if (bodyFat) {
      const bodyFatFactor = bodyFat < 20 ? 1.1 : 0.9;
      calories *= bodyFatFactor;
    }

    // Ensure it's a whole number
    calories = Math.round(calories);

    return calories;
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: theme.colors.screenBackground,
        }}
      >
        <LinearGradient
          //colors={["#2498E3", "#4AAE56"]}
          colors={["black", "white"]}
          style={{
            flex: 1,
          }}
          start={{ x: 0, y: 0.6 }}
          end={{ x: 0, y: 0.2 }}
          pointerEvents="box-none"
        >
          <View
            style={{
              height: scaleSize(85),
              minWidth: "100%",
              justifyContent: "center",
            }}
          >
            <TouchableOpacity
              style={{
                alignSelf: "flex-start",
                padding: scaleSize(15),
                zIndex: 2,
              }}
              onPress={() => {
                currentSection == 1
                  ? navigation.navigate("Welcome")
                  : setCurrentSection(currentSection - 1);

                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Feather
                name="chevron-left"
                color={"black"}
                size={scaleSize(38)}
              />
            </TouchableOpacity>
          </View>
          {/* <Image source={logo} style={styles.logo} /> */}

          <View
            style={{
              marginTop: 0,
              paddingBottom: scaleSize(20),
              paddingHorizontal: scaleSize(20),
              flex: 1,
              alignItems: "center",
              flexDirection: "column",
              backgroundColor: "transparent",
            }}
          >
            <SectionBarComponent currentSection={currentSection} />
            {currentSection === 1 && (
              <AboutYouSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 2 && (
              <ActivityLevelSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 3 && (
              <SetAGoalSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 4 && (
              <BodyTypeSection
                value={value}
                setValue={setValue}
                onNext={handleNextSection}
              />
            )}

            {currentSection === 5 && (
              <View
                style={{
                  alignItems: "center",
                  paddingTop: "20%",
                  paddingHorizontal: scaleSize(24),
                  width: "100%",
                }}
              >
                {/* Header Section */}
                <View style={{ alignItems: "center" }}>
                  <Text
                    style={{
                      fontSize: scaleSize(28),
                      fontWeight: "800",
                      textAlign: "center",
                      color: "black",
                      lineHeight: scaleSize(36),
                      marginBottom: scaleSize(8),
                    }}
                  >
                    Start Your Journey
                  </Text>
                  <Text
                    style={{
                      fontSize: scaleSize(16),
                      textAlign: "center",
                      color: theme.colors.secondaryTextColor,
                      marginTop: scaleSize(12),
                    }}
                  >
                    Create an account to save your progress{"\n"}and access
                    exclusive features
                  </Text>
                </View>

                {/* Auth Buttons Container */}
                <View
                  style={{
                    gap: scaleSize(16),
                    marginVertical: scaleSize(24),
                    width: "100%",
                  }}
                >
                  {/* Apple Sign-In Button - CORRECTLY IMPLEMENTED */}
                  {Platform.OS === "ios" && (
                    <>
                      <AppleAuthentication.AppleAuthenticationButton
                        buttonType={
                          AppleAuthentication.AppleAuthenticationButtonType
                            .SIGN_UP
                        }
                        buttonStyle={
                          mode === "dark"
                            ? AppleAuthentication.AppleAuthenticationButtonStyle
                                .WHITE
                            : AppleAuthentication.AppleAuthenticationButtonStyle
                                .WHITE_OUTLINE
                        }
                        cornerRadius={scaleSize(10)}
                        style={{
                          width: "100%",
                          height: scaleSize(52),
                        }}
                        onPress={handleAppleSignup}
                      />

                      {/* Divider for Apple */}
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          gap: scaleSize(12),
                        }}
                      >
                        <View
                          style={{
                            flex: 1,
                            height: scaleSize(1),
                            backgroundColor: "rgba(255,255,255,0.2)",
                          }}
                        />
                        <View
                          style={{
                            flex: 1,
                            height: scaleSize(1),
                            backgroundColor: "rgba(255,255,255,0.2)",
                          }}
                        />
                      </View>
                    </>
                  )}

                  {/* Google Sign-In Button */}
                  <TouchableOpacity
                    style={{
                      flexDirection: "row",
                      backgroundColor: theme.colors.surface,
                      padding: scaleSize(12),
                      borderRadius: scaleSize(10),
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: scaleSize(1),
                      borderColor: theme.colors.primaryTextColor,
                      height: scaleSize(52),
                    }}
                    onPress={handleGoogleSignUp}
                  >
                    <Image
                      source={require("../../assets/google.png")}
                      style={{
                        width: scaleSize(20),
                        height: scaleSize(20),
                        marginRight: scaleSize(10),
                      }}
                    />
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(16),
                      }}
                    >
                      Continue with Google
                    </Text>
                  </TouchableOpacity>

                  {/* Divider */}
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      gap: scaleSize(12),
                    }}
                  >
                    <View
                      style={{
                        flex: 1,
                        height: scaleSize(1),
                        backgroundColor: "rgba(255,255,255,0.2)",
                      }}
                    />
                    <View
                      style={{
                        flex: 1,
                        height: scaleSize(1),
                        backgroundColor: "rgba(255,255,255,0.2)",
                      }}
                    />
                  </View>

                  {/* Email Sign-In Button */}
                  <TouchableOpacity
                    style={{
                      flexDirection: "row",
                      backgroundColor: theme.colors.surface,
                      padding: scaleSize(12),
                      borderRadius: scaleSize(10),
                      alignItems: "center",
                      justifyContent: "center",
                      borderWidth: scaleSize(1),
                      borderColor: theme.colors.primaryTextColor,
                      height: scaleSize(52),
                    }}
                    onPress={handleNextSection}
                  >
                    <FontAwesome
                      name="envelope"
                      size={scaleSize(20)}
                      color={theme.colors.primaryTextColor}
                      style={{ marginRight: scaleSize(10) }}
                    />
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(16),
                      }}
                    >
                      Continue with Email
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* Footer Section */}
                <View
                  style={{
                    marginBottom: scaleSize(20),
                    alignItems: "center",
                  }}
                >
                  <TouchableOpacity onPress={handleLoginNavigation}>
                    <Text
                      style={{
                        color: theme.colors.subTextColor,
                        fontSize: scaleSize(16),
                        textAlign: "center",
                      }}
                    >
                      Already have an account?{"\n"}
                      <Text
                        style={{
                          color: theme.colors.primary,
                          fontWeight: "600",
                          fontSize: scaleSize(16),
                        }}
                      >
                        Sign In
                      </Text>
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {currentSection === 6 && (
              <AccountDetailsSection
                captureRegistrationData={captureRegistrationData}
                registrationDataRef={registrationDataRef}
                value={value}
                setValue={setValue}
                handleSignUp={handleSignUp}
                checkEmailExists={checkEmailExists}
                handleVerifyCode={handleVerifyCode}
                handleResendCode={handleResendCode}
              />
            )}
          </View>
        </LinearGradient>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
}

export default SignUpScreen;
