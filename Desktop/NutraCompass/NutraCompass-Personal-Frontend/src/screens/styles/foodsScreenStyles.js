import { StyleSheet } from "react-native";
import { useTheme } from "react-native-paper";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";
const foodsScreenStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    safeAreaView: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
      paddingBottom: "25%",
    },
    scrollViewContainer: {
      flex: 1,
      padding: scaleSize(5),
      paddingBottom: scaleSize(10),
      backgroundColor: "transparent",
    },
    scrollViewContainerContent: {
      flexGrow: 1,
    },
    createMealButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: scaleSize(8),
      paddingVertical: scaleSize(10),
      paddingHorizontal: scaleSize(16),
      justifyContent: "center",
      alignItems: "center",
      elevation: 2,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: scaleSize(2) },
      shadowOpacity: 0.25,
      shadowRadius: scaleSize(4),
      marginVertical: scaleSize(8),
      minWidth: scaleSize(150),
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    buttonIcon: {
      marginRight: scaleSize(8),
    },
    buttonText: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(14),
      fontWeight: "bold",
    },
  });
};

export default foodsScreenStyles;
