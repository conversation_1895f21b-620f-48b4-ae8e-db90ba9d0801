import { StyleSheet } from "react-native";
import { useTheme } from "react-native-paper";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";
const signupScreenStyles = () => {
  const paperTheme = useTheme();
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    gradientContainer: {
      flex: 1,
    },
    contentContainer: {
      marginTop: 0,
      paddingBottom: scaleSize(20),
      paddingHorizontal: scaleSize(20),
      flex: 1,
      alignItems: "center",
      flexDirection: "column",
      backgroundColor: "transparent",
    },
    logo: {
      position: "absolute",
      top: scaleSize(5),
      alignSelf: "center",
      width: scaleSize(195),
      height: scaleSize(54),
    },
    title: {
      fontSize: scaleSize(28),
      fontWeight: "bold",
      color: paperTheme.colors.primary,
      marginVertical: scaleSize(16),
    },
    link: {
      color: paperTheme.colors.accent,
      textAlign: "center",
    },
    inputContainer: {
      width: "100%",
    },
    input: {
      backgroundColor: "transparent", // Grayish background for text inputs
      paddingHorizontal: scaleSize(16),
      color: paperTheme.colors.text, // Text color from the theme
    },
    selectInput: {
      borderWidth: 1,
      borderColor: "rgba(169, 169, 169, 0.7)",
      borderRadius: scaleSize(14),
      padding: scaleSize(10),
      marginBottom: scaleSize(10), // Adjust as needed
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },

    icon: {
      padding: scaleSize(10),
    },
    datePickerText: {
      color: "white",
      fontSize: scaleSize(18),
      fontWeight: "bold",
      margin: scaleSize(5),
    },
    button: {
      backgroundColor: paperTheme.colors.primary,
      borderRadius: scaleSize(24), // Increased border radius for buttons
      paddingVertical: scaleSize(10), // Adjusted padding for buttons
      marginVertical: scaleSize(10),
      width: scaleSize(150), // Adjusted button width
    },
  });
};

export default signupScreenStyles;
