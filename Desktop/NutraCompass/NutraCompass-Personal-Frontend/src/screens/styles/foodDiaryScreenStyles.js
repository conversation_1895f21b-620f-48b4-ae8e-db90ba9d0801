import { StyleSheet } from "react-native";
import { useTheme } from "react-native-paper";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils";

const foodDiaryScreenStyles = () => {
  const paperTheme = useTheme();
  const { theme } = useThemeContext();

  // Create scaled size constants
  const scaled = {
    paddingXS: scaleSize(2),
    paddingS: scaleSize(4),
    paddingM: scaleSize(6),
    paddingL: scaleSize(8),
    paddingXL: scaleSize(12),
    paddingXXL: scaleSize(16),
    marginXS: scaleSize(2),
    marginS: scaleSize(4),
    marginM: scaleSize(8),
    marginL: scaleSize(12),
    marginXL: scaleSize(16),
    borderRadiusS: scaleSize(4),
    borderRadiusM: scaleSize(8),
    borderRadiusL: scaleSize(12),
    fontSizeXS: scaleSize(10),
    fontSizeS: scaleSize(12),
    fontSizeM: scaleSize(14),
    fontSizeL: scaleSize(16),
    fontSizeXL: scaleSize(18),
    fontSizeXXL: scaleSize(20),
    sectionHeight: scaleSize(22),
    carouselHeight: scaleSize(30),
    safeAreaPaddingBottom: scaleSize(100),
  };

  return StyleSheet.create({
    safeAreaView: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
      paddingBottom: scaled.safeAreaPaddingBottom,
    },
    scrollViewContainer: {
      flex: 1,
      padding: scaled.paddingS,
      paddingBottom: scaled.paddingXL,
      backgroundColor: theme.colors.screenBackground,
    },
    scrollViewContainerContent: {
      flexGrow: 1,
    },
    headerSection: {
      marginBottom: scaled.marginS,
      borderRadius: scaled.borderRadiusM,
      backgroundColor: theme.colors.screenBackground,
      elevation: 2,
    },
    headerDateContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingBottom: scaled.paddingS,
    },
    date: {
      paddingLeft: scaled.paddingXL,
      fontSize: scaled.fontSizeXL,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    calendarModal: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.cardBackgroundColor,
    },
    calendarWrapper: {
      width: "85%",
      backgroundColor: theme.colors.screenBackground,
    },
    cancelDateButton: {
      padding: scaled.paddingXL,
    },
    cancelDateButtonText: {
      color: theme.colors.primaryTextColor,
      textAlign: "center",
      fontSize: scaled.fontSizeM,
    },
    calendarModalButton: {
      flexDirection: "row",
      alignItems: "center",
    },
    totalDayCalories: {
      fontSize: scaled.fontSizeL,
      paddingTop: scaled.paddingXL,
      color: theme.colors.primaryTextColor,
    },
    totalDayCaloriesProgressSectionText: {
      fontSize: scaled.fontSizeM,
      paddingTop: scaled.paddingXL,
      color: theme.colors.primaryTextColor,
    },
    section: {
      marginBottom: scaled.marginL,
      borderRadius: scaled.borderRadiusM,
      backgroundColor: theme.colors.sectionBackgroundColor,
      elevation: 2,
    },
    mealSection: {
      marginBottom: 0,
      paddingVertical: scaled.paddingXL,
      backgroundColor: "transparent",
      borderRadius: scaled.borderRadiusM,
      borderColor: theme.colors.cardBorderColor,
      elevation: 2,
      borderWidth: 0,
    },
    calorieSection: {
      flex: 3,
      height: "100%",
      justifyContent: "center",
      borderRadius: scaled.borderRadiusM,
      backgroundColor: theme.colors.cardDarkGrayBackground,
      borderColor: theme.colors.sectionBorderColor,
      borderRightWidth: scaleSize(1),
      borderLeftWidth: scaleSize(1),
      elevation: 4,
    },
    fabMenuSection: {},
    sectionHeaderContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: 0,
    },
    mealSectionHeaderContainer: {
      backgroundColor: theme.colors.cardDarkGrayBackground,
      borderBottomRightRadius: scaled.borderRadiusS,
      borderBottomLeftRadius: scaled.borderRadiusS,
    },
    mealSectionFooterContainer: {
      padding: scaled.paddingXS,
      marginBottom: scaled.paddingXL,
      backgroundColor: theme.colors.surface,
      borderBottomLeftRadius: scaled.borderRadiusM,
      borderBottomRightRadius: scaled.borderRadiusM,
    },
    sectionTitle: {
      fontSize: scaled.fontSizeL,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
    },
    footerButton: {
      alignSelf: "flex-start",
      marginTop: scaled.marginXL,
      marginHorizontal: scaled.marginXL,
      borderRadius: scaled.borderRadiusM,
      padding: 0,
    },
    totalMealSectionCalories: {
      fontSize: scaled.fontSizeL,
      fontWeight: "bold",
      color: theme.colors.primary,
    },
    dateInfo: {
      alignSelf: "center",
      marginTop: scaled.marginS,
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      fontSize: scaled.fontSizeS,
    },
    sliderSectionContainer: {
      marginVertical: scaled.marginXL,
      flexDirection: "row",
      minHeight: `${scaled.sectionHeight}%`,
      maxHeight: `${scaled.carouselHeight}%`,
      gap: scaled.paddingXL,
      padding: scaled.paddingS,
    },
  });
};

export default foodDiaryScreenStyles;
