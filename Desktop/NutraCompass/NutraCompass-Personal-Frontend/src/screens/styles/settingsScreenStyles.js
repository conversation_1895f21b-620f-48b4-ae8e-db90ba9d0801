import { StyleSheet } from "react-native";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const settingsScreenStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    rowContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: scaleSize(15),
      paddingHorizontal: scaleSize(20),
      backgroundColor: theme.colors.surface,
    },
    rowLeft: {
      flexDirection: "row",
      alignItems: "center",
    },
    rowIcon: {
      marginRight: scaleSize(15),
    },
    rowText: {
      fontSize: scaleSize(16),
      fontWeight: "500",
      color: theme.colors.primaryTextColor,
    },
    rowBorder: {
      borderBottomWidth: scaleSize(1),
      borderBottomColor: theme.colors.sectionBorderColor,
    },
  });
};

export default settingsScreenStyles;
