import { StyleSheet } from "react-native";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";
const welcomeScreenStyles = () => {
  const { theme } = useThemeContext();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.screenBackground,
    },
    gradientContainer: {
      flex: 1,
    },
    contentContainer: {
      marginHorizontal: scaleSize(20),
      flex: 1,
      justifyContent: "top",

      alignItems: "center",
      flexDirection: "column",
      paddingTop: scaleSize(40),
    },
    appName: {
      fontSize: scaleSize(28), // Increased font size for app name
      fontWeight: "bold",
      color: theme.colors.primaryTextColor,
      marginVertical: scaleSize(16),
      textAlign: "center",
    },
    description: {
      color: theme.colors.primaryTextColor,
      fontSize: scaleSize(18), // Increased font size for description
      textAlign: "center",
      marginHorizontal: scaleSize(16),
      marginBottom: scaleSize(24), // Increased margin to separate from buttons
    },
  });
};

export default welcomeScreenStyles;
