import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
  memo,
} from "react";
import { View, StyleSheet, Dimensions, Animated } from "react-native";
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import { useThemeContext } from "../../context/ThemeContext.js";
import CustomTabButton from "./CustomTabButton.js";
import { useModal, MODAL_TYPES } from "../../context/ModalContext.js";
import { scaleSize, isTablet } from "../../utils/deviceUtils.js";

const CustomPersonalBottomTabBar = memo(
  ({ state, descriptors, navigation }) => {
    const { openModal } = useModal();
    const { theme } = useThemeContext();
    const [tabPositions, setTabPositions] = useState([]);
    const animation = useRef(new Animated.Value(0)).current;
    const containerRef = useRef(null);
    const tablet = isTablet();

    // Log performance
    // useEffect(() => {
    //   if (__DEV__) {
    //     console.log(
    //       `[NAV-PERF] BottomTabBar rendered for ${
    //         state.routes[state.index].name
    //       }`
    //     );
    //   }
    // }, [state]);

    // Responsive sizing
    const sizes = useMemo(
      () => ({
        tabBarHeight: scaleSize(80),
        gradientHeight: scaleSize(3),
        plusButtonSize: scaleSize(75),
        plusButtonBottom: scaleSize(20),
        tabMargin: scaleSize(30),
        paddingBottom: scaleSize(12),
      }),
      []
    );

    // Animate gradient position
    useEffect(() => {
      Animated.spring(animation, {
        toValue: state.index,
        useNativeDriver: false,
        tension: 10,
        friction: 10,
      }).start();
    }, [state.index]);

    const handlePlusPress = useCallback(() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      openModal(MODAL_TYPES.ACTION, {});
    }, [openModal]);

    // Helper function to find the active route name
    const getActiveRouteNameFromState = useCallback((navState) => {
      if (!navState?.routes) return null;
      const route = navState.routes[navState.index];
      return route.state
        ? getActiveRouteNameFromState(route.state)
        : route.name;
    }, []);

    // Check if AI screen is active
    const activeRouteName = useMemo(
      () => getActiveRouteNameFromState(state),
      [state, getActiveRouteNameFromState]
    );

    if (activeRouteName === "AI") return null;

    const styles = useMemo(() => {
      return StyleSheet.create({
        tabBar: {
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          flexDirection: "row",
          height: sizes.tabBarHeight,
          elevation: 2,
          backgroundColor: theme.colors.surface,
          paddingBottom: sizes.paddingBottom,
        },
        gradientContainer: {
          position: "absolute",
          top: 0,
          height: sizes.gradientHeight,
          borderTopLeftRadius: 2,
          borderTopRightRadius: 2,
          overflow: "hidden",
        },
        gradient: {
          flex: 1,
          width: "100%",
        },
        tabsContainer: {
          flex: 1,
          flexDirection: "row",
          justifyContent: "space-between",
        },
        plusButtonContainer: {
          position: "absolute",
          left: Dimensions.get("window").width / 2 - sizes.plusButtonSize / 2,
          bottom: sizes.plusButtonBottom,
          zIndex: 2,
        },
      });
    }, [theme, sizes]);

    const getIconName = useCallback((routeName) => {
      const iconMap = {
        Home: "home",
        Diary: "open-book",
        Pantry: "fast-food",
        Fitness: "barbell",
      };
      return iconMap[routeName] || "home";
    }, []);

    const onTabPress = useCallback(
      (route, index) => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

        const event = navigation.emit({
          type: "tabPress",
          target: route.key,
          canPreventDefault: true,
        });

        if (!event.defaultPrevented) {
          navigation.navigate(route.name);
        }
      },
      [navigation]
    );

    // Calculate gradient position based on absolute measurements
    const gradientLeft = useMemo(
      () =>
        animation.interpolate({
          inputRange: state.routes.map((_, i) => i),
          outputRange: state.routes.map((_, i) => {
            const position = tabPositions[i]?.x || 0;
            return position + (tabPositions[i]?.width * 0.1 || 0);
          }),
        }),
      [state.routes, tabPositions, animation]
    );

    const gradientWidth = useMemo(
      () =>
        animation.interpolate({
          inputRange: state.routes.map((_, i) => i),
          outputRange: state.routes.map((_, i) => {
            return tabPositions[i]?.width * 0.8 || 0;
          }),
        }),
      [state.routes, tabPositions, animation]
    );

    const handleLayout = useCallback((index, event) => {
      const { width, x } = event.nativeEvent.layout;
      setTabPositions((prev) => {
        const newPositions = [...prev];
        newPositions[index] = { x, width };
        return newPositions;
      });
    }, []);

    return (
      <View style={{ flexDirection: "row" }}>
        <View style={styles.tabBar} ref={containerRef}>
          <Animated.View
            style={[
              styles.gradientContainer,
              {
                left: gradientLeft,
                width: gradientWidth,
              },
            ]}
          >
            <LinearGradient
              colors={[theme.colors.primary, theme.colors.secondary]}
              start={{ x: 0.5, y: 0 }}
              end={{ x: 0.5, y: 1 }}
              style={styles.gradient}
            />
          </Animated.View>

          {state.routes.map((route, index) => (
            <CustomTabButton
              key={route.key}
              label={route.name}
              isFocused={state.index === index}
              onPress={() => onTabPress(route, index)}
              icon={getIconName(route.name)}
              onLayout={(event) => handleLayout(index, event)}
              style={[
                index === 1 && { marginRight: sizes.tabMargin },
                index === 2 && { marginLeft: sizes.tabMargin },
              ]}
            />
          ))}

          <View style={styles.plusButtonContainer}>
            <CustomTabButton
              label=""
              isFocused={false}
              onPress={handlePlusPress}
              icon="plus"
              isPlusButton={true}
            />
          </View>
        </View>
      </View>
    );
  }
);

export default CustomPersonalBottomTabBar;
