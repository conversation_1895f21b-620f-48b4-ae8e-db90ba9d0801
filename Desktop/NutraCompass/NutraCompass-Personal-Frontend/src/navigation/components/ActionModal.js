import React from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Text,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { useModal, MODAL_TYPES } from "../../context/ModalContext.js";
import { useThemeContext } from "../../context/ThemeContext.js";
import { useTime } from "../../context/TimeContext.js";
import { useFoodLog } from "../../features/FoodDiary/context/FoodLogContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get("window");

const ActionModal = ({ isVisible, closeModal }) => {
  const { openModal, closeAllModals, replaceModal } = useModal();
  const { theme } = useThemeContext();
  const { selectedDate } = useTime();
  const { mealSections, setActiveMealSection } = useFoodLog();

  // Get the first valid meal section with fallback protection
  const getDefaultMealSection = () => {
    const defaultSection = mealSections?.find(
      (section) => section.name?.trim() && section.id !== "Water"
    );

    // Add additional validation if needed
    if (!defaultSection) {
      console.error("No valid default meal section found");
      return null; // Or throw error depending on your error handling strategy
    }
    return defaultSection;
  };

  // Consolidated action handler with default handling
  const handleAction = (modalType, props = {}) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (modalType === MODAL_TYPES.BARCODE_SCANNER) {
      // Use replaceModal instead of closeModal
      replaceModal(MODAL_TYPES.BARCODE_SCANNER, {
        onBarcodeScanned: (scannedData) => {
          // Replace scanner with results handler
          replaceModal(MODAL_TYPES.SCAN_RESULTS, {
            scannedItem: scannedData,
            selectedDate: new Date(),
            onSaveSuccess: () => {
              closeAllModals();
            },
          });
        },
        onClose: () => {
          // Return to ActionModal when scanner is closed
          replaceModal(MODAL_TYPES.ACTION);
        },
      });
      return;
    }

    // Handle AI Digital Food Scanner
    if (modalType === MODAL_TYPES.DIGITAL_FOOD_SCANNER) {
      replaceModal(MODAL_TYPES.DIGITAL_FOOD_SCANNER, {
        onClose: () => replaceModal(MODAL_TYPES.ACTION),
      });
      return;
    }

    // Special handling for FOOD_ENTRY
    if (modalType === MODAL_TYPES.FOOD_ENTRY) {
      const activeSection = props.activeMealSection || getDefaultMealSection();

      if (!activeSection) {
        console.error("Cannot open FOOD_ENTRY - no valid meal section");
        return;
      }

      // Ensure we always have a valid section
      setActiveMealSection((prev) => {
        // Only update if section changed
        return prev?.id === activeSection.id ? prev : activeSection;
      });

      // Merge props with default values
      const mergedProps = {
        ...props,
        activeMealSection: activeSection,
      };

      closeModal();
      requestAnimationFrame(() => {
        openModal(modalType, mergedProps);
      });
      return;
    }

    // Default handling for other modals
    closeModal();
    requestAnimationFrame(() => {
      openModal(modalType, props);
    });
  };

  const primaryActions = [
    {
      label: "Log Food",
      icon: "fast-food",
      modalType: MODAL_TYPES.FOOD_ENTRY,
      props: {
        isBuildingMeal: false,
        onCancel: () => setActiveMealSection(null),
      }, // Add any required props
    },
    {
      label: "Barcode Scan",
      icon: "barcode",
      modalType: MODAL_TYPES.BARCODE_SCANNER,
    },
    {
      label: "Meal Scan",
      icon: "camera",
      modalType: MODAL_TYPES.DIGITAL_FOOD_SCANNER,
    },
    // {
    //   label: "Voice Log",
    //   icon: "mic",
    //   modalType: null, // Disabled
    //   disabled: true,
    //   comingSoon: true,
    // },
  ];

  const secondaryActions = [
    {
      label: "Customize Meal\nNames",
      icon: "list",
      modalType: MODAL_TYPES.MEAL_SECTION_CUSTOMIZATION,
      props: {}, // Add any required props
    },
    {
      label: "Customize Nutrition\nGoals",
      icon: "nutrition",
      modalType: MODAL_TYPES.NUTRITION_GOALS_CUSTOMIZATION,
      props: {}, // Add any required props
    },
    {
      label: "Calculate Nutrition\nGoals",
      icon: "calculator",
      modalType: MODAL_TYPES.NUTRITION_GOALS_CALCULATION,
      props: {}, // Add any required props
    },
  ];

  const thirdActions = [
    {
      label: "Water",
      icon: "water",
      modalType: MODAL_TYPES.WATER_LOG_ENTRY,
      props: {
        activeItem: null, // Default to new entry
        onClose: () => {
          // Cleanup if needed
          if (typeof setActiveFoodItem === "function") {
            setActiveFoodItem(null);
          }
        },
      }, // Add any required props
    },
  ];

  // Unified action button components
  const ActionButton = ({
    type,
    label,
    icon,
    modalType,
    props,
    disabled = false,
  }) => {
    const stylesConfig = {
      primary: {
        button: styles.primaryButton,
        text: styles.primaryButtonText,
        iconSize: scaleSize(24),
      },
      secondary: {
        button: styles.secondaryButton,
        text: styles.secondaryButtonText,
        iconSize: scaleSize(20),
      },
      third: {
        button: styles.thirdButton,
        text: styles.thirdButtonText,
        iconSize: scaleSize(20),
      },
    };

    const { button, text, iconSize } = stylesConfig[type];

    return (
      <TouchableOpacity
        style={[
          button,
          {
            backgroundColor: theme.colors.screenBackground,
            opacity: disabled ? 0.6 : 1,
          },
        ]}
        onPress={disabled ? undefined : () => handleAction(modalType, props)}
        activeOpacity={disabled ? 1 : type === "primary" ? 0.8 : 0.6}
        disabled={disabled}
      >
        <View style={styles.buttonContent}>
          <Ionicons
            name={icon}
            size={iconSize}
            color={theme.colors.primaryTextColor}
          />
          <View style={{ flexDirection: "column" }}>
            <Text
              style={[
                text,
                {
                  color: theme.colors.primaryTextColor,
                  textAlign: "flex-start",
                },
              ]}
            >
              {label}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.modal}>
      <View
        style={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface },
        ]}
      >
        <View
          style={{
            position: "absolute",
            alignSelf: "center",
            top: 0,
            width: "60%",
            height: scaleSize(3),
            borderRadius: scaleSize(3),
            backgroundColor: theme.colors.subTextColor,
            opacity: 0.5,
          }}
        />
        <View style={styles.scrollContainer}>
          <View style={styles.primaryActions}>
            {primaryActions.map((action) => (
              <ActionButton
                key={action.label}
                type="primary"
                label={action.label}
                icon={action.icon}
                modalType={action.modalType}
                props={action.props}
                disabled={action.disabled}
              />
            ))}
          </View>

          <View style={styles.lowerActionsContainer}>
            <View style={styles.secondaryActions}>
              {secondaryActions.map((action) => (
                <ActionButton
                  key={action.label}
                  type="secondary"
                  label={action.label}
                  icon={action.icon}
                  modalType={action.modalType}
                  props={action.props}
                />
              ))}
            </View>

            <View style={styles.thirdActions}>
              {thirdActions.map((action) => (
                <ActionButton
                  key={action.label}
                  type="third"
                  label={action.label}
                  icon={action.icon}
                  modalType={action.modalType}
                  props={action.props}
                />
              ))}
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  modal: {
    position: "absolute",
    bottom: 0,
    width: SCREEN_WIDTH,
    margin: 0,
  },
  modalContainer: {
    borderTopLeftRadius: scaleSize(20),
    borderTopRightRadius: scaleSize(20),
    padding: scaleSize(20),
    paddingBottom: scaleSize(30),
    maxHeight: SCREEN_HEIGHT * 0.7,
  },
  primaryActions: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: scaleSize(20),
  },
  lowerActionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: scaleSize(8),
  },
  buttonContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: scaleSize(12),
  },
  primaryButton: {
    width: "48%",
    borderRadius: scaleSize(10),
    padding: scaleSize(16),
    marginBottom: scaleSize(10),
  },
  primaryButtonText: {
    fontSize: scaleSize(16),
    fontWeight: "500",
  },
  secondaryActions: {
    width: "50%",
  },
  secondaryButton: {
    width: "100%",
    borderRadius: scaleSize(10),
    padding: scaleSize(14),
    marginVertical: scaleSize(6),
  },
  secondaryButtonText: {
    fontSize: scaleSize(14),
    fontWeight: "500",
    textAlign: "center",
    lineHeight: scaleSize(20),
  },
  thirdActions: {
    width: "35%",
  },
  thirdButton: {
    width: "100%",
    borderRadius: scaleSize(10),
    padding: scaleSize(14),
    marginVertical: scaleSize(6),
  },
  thirdButtonText: {
    fontSize: scaleSize(14),
    fontWeight: "500",
  },
  scrollContainer: {
    flexGrow: 1,
  },
});

export default ActionModal;
