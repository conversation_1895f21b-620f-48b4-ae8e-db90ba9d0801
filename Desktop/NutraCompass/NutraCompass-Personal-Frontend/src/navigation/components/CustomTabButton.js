// CustomTabButton.js
import React from "react";
import { View, TouchableOpacity, StyleSheet, Text } from "react-native";
import { Ionicons, Entypo, FontAwesome6 } from "@expo/vector-icons";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";
import * as Haptics from "expo-haptics";

const CustomTabButton = ({
  label,
  isFocused,
  onPress,
  icon,
  isPlusButton = false,
  onLayout,
  style,
}) => {
  const { theme, mode, toggleDarkMode } = useThemeContext();

  // Responsive sizing
  const baseIconSize = 24;
  const baseButtonSize = 45;
  const basePlusButtonSize = 75;
  const basePlusIconSize = 32;
  const baseBorderWidth = 4;
  const baseFontSize = 12;
  const basePaddingBottom = 6;

  // Apply scaling
  const iconSize = scaleSize(baseIconSize);
  const buttonSize = scaleSize(baseButtonSize);
  const plusButtonSize = scaleSize(basePlusButtonSize);
  const plusIconSize = scaleSize(basePlusIconSize);
  const borderWidth = scaleSize(baseBorderWidth);
  const fontSize = scaleSize(baseFontSize);
  const paddingBottom = scaleSize(basePaddingBottom);

  const styles = StyleSheet.create({
    tab: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingBottom: paddingBottom,
    },
    btnContainer: {
      width: isPlusButton ? plusButtonSize : buttonSize,
      height: isPlusButton ? plusButtonSize : buttonSize,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: isPlusButton ? borderWidth : 0,
      borderRadius: isPlusButton ? plusButtonSize / 2 : 0,
      borderColor: isPlusButton ? theme.colors.screenBackground : "transparent",
      backgroundColor: isPlusButton ? theme.colors.primary : "transparent",
      ...(isPlusButton && {
        elevation: 8,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 6,
      }),
    },
    icon: {
      color: isFocused
        ? theme.colors.primaryTextColor
        : theme.colors.subTextColor,
    },
    plusIcon: {
      color: "white",
    },
    text: {
      fontSize: fontSize,
      textAlign: "center",
      color: isFocused
        ? theme.colors.primaryTextColor
        : theme.colors.subTextColor,
    },
  });

  // FIX 1: Pass iconName as parameter to renderIcon
  const renderIcon = (iconName) => {
    if (isPlusButton) {
      return (
        <FontAwesome6
          name="plus"
          size={plusIconSize}
          color={theme.colors.text}
          style={styles.plusIcon}
        />
      );
    }

    // Handle specific icons that need different icon sets
    if (iconName === "open-book") {
      return <Entypo name={iconName} size={iconSize} style={styles.icon} />;
    }

    // Default to Ionicons
    return <Ionicons name={iconName} size={iconSize} style={styles.icon} />;
  };

  // Handle Fitness button differently
  const handlePress = () => {
    if (label === "Fitness") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      toggleDarkMode();
    } else {
      onPress();
    }
  };

  // FIX 2: Get the actual icon to use
  const actualIcon =
    label === "Fitness" ? (mode === "dark" ? "sunny" : "moon") : icon;

  return (
    <TouchableOpacity
      style={[styles.tab, style]}
      activeOpacity={1}
      onPress={handlePress}
      onLayout={onLayout}
    >
      <View style={styles.btnContainer}>
        {/* FIX 3: Pass the actualIcon to renderIcon */}
        {renderIcon(actualIcon)}
      </View>
      {/* Hide label for Fitness button */}
      {!isPlusButton && label !== "Fitness" && (
        <Text style={styles.text}>{label}</Text>
      )}
    </TouchableOpacity>
  );
};

export default CustomTabButton;

// import React from "react";
// import { View, TouchableOpacity, StyleSheet, Text } from "react-native";
// import { Ionicons, Entypo, FontAwesome6 } from "@expo/vector-icons";
// import { useThemeContext } from "../../context/ThemeContext.js";
// import { scaleSize } from "../../utils/deviceUtils.js";

// const CustomTabButton = ({
//   label,
//   isFocused,
//   onPress,
//   icon,
//   isPlusButton = false,
//   onLayout,
//   style,
// }) => {
//   const { theme } = useThemeContext();

//   // Responsive sizing - base values for iPhone
//   const baseIconSize = 24;
//   const baseButtonSize = 45;

//   // Increased plus button sizes
//   const basePlusButtonSize = 75; // Increased from 55
//   const basePlusIconSize = 32; // New size for plus icon

//   const baseBorderWidth = 4;
//   const baseFontSize = 12;
//   const basePaddingBottom = 6;

//   // Apply scaling
//   const iconSize = scaleSize(baseIconSize);
//   const buttonSize = scaleSize(baseButtonSize);
//   const plusButtonSize = scaleSize(basePlusButtonSize);
//   const plusIconSize = scaleSize(basePlusIconSize); // Special size for plus icon
//   const borderWidth = scaleSize(baseBorderWidth);
//   const fontSize = scaleSize(baseFontSize);
//   const paddingBottom = scaleSize(basePaddingBottom);

//   const styles = StyleSheet.create({
//     tab: {
//       flex: 1,
//       justifyContent: "center",
//       alignItems: "center",
//       paddingBottom: paddingBottom,
//     },
//     btnContainer: {
//       width: isPlusButton ? plusButtonSize : buttonSize,
//       height: isPlusButton ? plusButtonSize : buttonSize,
//       justifyContent: "center",
//       alignItems: "center",
//       borderWidth: isPlusButton ? borderWidth : 0,
//       borderRadius: isPlusButton ? plusButtonSize / 2 : 0,
//       borderColor: isPlusButton ? theme.colors.screenBackground : "transparent",
//       backgroundColor: isPlusButton ? theme.colors.primary : "transparent",
//       // Add shadow for plus button to make it stand out
//       ...(isPlusButton && {
//         elevation: 8,
//         shadowColor: "#000",
//         shadowOffset: { width: 0, height: 4 },
//         shadowOpacity: 0.3,
//         shadowRadius: 6,
//       }),
//     },
//     icon: {
//       color: isFocused
//         ? theme.colors.primaryTextColor
//         : theme.colors.subTextColor,
//     },
//     plusIcon: {
//       color: "white",
//     },
//     text: {
//       fontSize: fontSize,
//       textAlign: "center",
//       color: isFocused
//         ? theme.colors.primaryTextColor
//         : theme.colors.subTextColor,
//     },
//   });

//   // Function to render the appropriate icon component
//   const renderIcon = () => {
//     if (isPlusButton) {
//       return (
//         <FontAwesome6
//           name="plus"
//           size={plusIconSize} // Use special size for plus icon
//           color={theme.colors.text}
//           style={styles.plusIcon}
//         />
//       );
//     }

//     // Handle specific icons that need different icon sets
//     if (icon === "open-book") {
//       return <Entypo name={icon} size={iconSize} style={styles.icon} />;
//     }

//     // Default to Ionicons
//     return <Ionicons name={icon} size={iconSize} style={styles.icon} />;
//   };

//   return (
//     <TouchableOpacity
//       style={[styles.tab, style]}
//       activeOpacity={1}
//       onPress={onPress}
//       onLayout={onLayout}
//     >
//       <View style={styles.btnContainer}>{renderIcon()}</View>
//       {!isPlusButton && <Text style={styles.text}>{label}</Text>}
//     </TouchableOpacity>
//   );
// };

// export default CustomTabButton;
