import React, { useMemo, useEffect } from "react";
import { View, TouchableOpacity, Text, StyleSheet } from "react-native";
import { MaterialCommunityIcons, Feather } from "@expo/vector-icons";
import { DrawerContentScrollView } from "@react-navigation/drawer";
import { useThemeContext } from "../../context/ThemeContext.js";
import ProfilePicture from "../../features/SocialMedia/components/ProfilePicture.js";
import { useAuth } from "../../authentication/context/AuthContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

// Static drawer items configuration
const drawerItems = [
  {
    name: "Personal",
    label: "Personal",
    icon: "account-circle",
    IconComponent: MaterialCommunityIcons,
  },
  {
    name: "Themes",
    label: "Theme Palette",
    icon: "palette",
    IconComponent: MaterialCommunityIcons,
  },
  {
    name: "Feedback",
    label: "Suggestion Box",
    icon: "lightbulb-on-outline",
    IconComponent: MaterialCommunityIcons,
  },
  {
    name: "SettingsStack",
    label: "Settings",
    icon: "more-horizontal",
    IconComponent: Feather,
  },
];

export const CustomDrawerContent = (props) => {
  const { state, navigation } = props;
  const { theme } = useThemeContext();
  const { loggingOut } = useAuth();

  // Calculate sizes once
  const sizes = useMemo(
    () => ({
      profileSize: scaleSize(200),
      iconSize: scaleSize(28),
      fontSize: scaleSize(16),
      itemPaddingVertical: scaleSize(15),
      itemPaddingHorizontal: scaleSize(20),
      profilePadding: scaleSize(20),
    }),
    []
  );

  // Memoize styles to prevent recreation
  const styles = useMemo(() => {
    return StyleSheet.create({
      container: {
        flex: 1,
        backgroundColor: theme.colors.screenBackground,
      },
      profileContainer: {
        alignItems: "center",
        padding: sizes.profilePadding,
      },
      item: {
        paddingVertical: sizes.itemPaddingVertical,
        paddingHorizontal: sizes.itemPaddingHorizontal,
        backgroundColor: theme.colors.screenBackground,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
      },
      itemText: {
        fontSize: sizes.fontSize,
        color: theme.colors.primaryTextColor,
      },
      itemFocused: {
        backgroundColor: theme.colors.secondary,
      },
      scrollContent: {
        flexGrow: 1,
        paddingBottom: scaleSize(20),
      },
      logoutButton: {
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        borderTopWidth: 1,
        borderTopColor: theme.colors.divider,
        backgroundColor: theme.colors.screenBackground,
      },
      itemsContainer: {
        flex: 1,
      },
    });
  }, [theme, sizes]);

  // Get current route name efficiently
  const currentRouteName = state.routes[state.index].name;

  return (
    <View style={styles.container}>
      <DrawerContentScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.profileContainer}>
          <ProfilePicture size={sizes.profileSize} />
        </View>

        <View style={styles.itemsContainer}>
          {drawerItems.map((item) => {
            const { name, label, icon, IconComponent } = item;
            const isFocused = currentRouteName === name;

            return (
              <TouchableOpacity
                key={name}
                onPress={() => navigation.navigate(name)}
                style={[styles.item, isFocused && styles.itemFocused]}
              >
                <Text style={styles.itemText}>{label}</Text>
                <IconComponent
                  name={icon}
                  size={sizes.iconSize}
                  color={theme.colors.primaryTextColor}
                />
              </TouchableOpacity>
            );
          })}
          {/* Logout button fixed to bottom */}
          <TouchableOpacity onPress={loggingOut} style={styles.item}>
            <Text style={styles.itemText}>Logout</Text>
            <MaterialCommunityIcons
              name={"logout"}
              size={sizes.iconSize}
              color={theme.colors.primaryTextColor}
            />
          </TouchableOpacity>
        </View>
      </DrawerContentScrollView>
    </View>
  );
};
