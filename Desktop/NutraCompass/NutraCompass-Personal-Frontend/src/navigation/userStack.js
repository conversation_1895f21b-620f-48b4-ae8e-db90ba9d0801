import React, { useEffect } from "react";
import { Dimensions } from "react-native";
import { isTablet } from "../utils/deviceUtils.js";
import {
  createStackNavigator,
  CardStyleInterpolators,
} from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createDrawerNavigator } from "@react-navigation/drawer";
import {
  NavigationContainer,
  useNavigationState,
} from "@react-navigation/native";
import AppLoadingScreen from "../screens/AppLoadingScreen";
import DashboardScreen from "../screens/Dashboard";
import NutraCompassAIScreen from "../features/AIAgent/screens/NutraCompassAIScreen.js";
import FoodDiaryScreen from "../screens/FoodDiary";
import FoodsScreen from "../screens/Foods";
import WorkoutDiaryScreen from "../screens/WorkoutDiary";
import GoalsScreen from "../screens/Goals";
import ThemeScreen from "../features/ThemeChanger/screens/ThemesScreen";
import SettingsScreen from "../screens/Settings";
import CustomMealsScreen from "../features/FoodMenu/screens/CustomMealsScreen";
// Social Media Screens
import MyProfileScreen from "../features/SocialMedia/MyProfile/screens/MyProfileScreen";
import MyLibraryScreen from "../features/SocialMedia/MyLibrary/screens/MyLibraryScreen";
import MyAccomplishmentsScreen from "../features/SocialMedia/MyAccomplishments/screens/MyAccomplishmentsScreen";
import MarketPlaceScreen from "../features/SocialMedia/MarketPlace/screens/MarketPlaceScreen";
import SocialSettingsScreen from "../features/SocialMedia/SocialSettings/screens/SocialSettingsScreen";
import ChatScreen from "../features/SocialMedia/Messages/screens/ChatScreen";
import SelectFriend from "../features/SocialMedia/Messages/screens/SelectFriend";
import TextScreen from "../features/SocialMedia/Messages/screens/TextScreen";
import AddFriendScreen from "../features/SocialMedia/Messages/screens/AddFriend";
// Custom Components
import CustomTopTabBar from "./components/CustomTopTabBar";
import CustomPersonalBottomTabBar from "./components/CustomPersonalBottomTabBar";
import CustomSocialBottomTabBar from "./components/CustomSocialBottomTabBar";
import { CustomDrawerContent } from "./components/CustomDrawerContent.js";
import ProfileSettings from "../features/Settings/screens/ProfileSettings.js";
import NotificationsSettings from "../features/Settings/screens/NotificationSettings.js";
import NutritionSettings from "../features/Settings/screens/NutritionSettings.js";
import MeasurementSettings from "../features/Settings/screens/MeasurementSettings.js";
import PermissionSettings from "../features/Settings/screens/PermissionSettings.js";
import AccountSettings from "../features/Settings/screens/AccountSettings.js";
import NutritionalProgramScreen from "../features/NutritionalProgram/screens/NutritionalProgramScreen.js";
import TutorialScreen from "../screens/TutorialScreen.js";
import FeedbackForumScreen from "../screens/FeedbackForumScreen.js";
// Performance logger
// const logNavigationPerformance = (componentName, routeName) => {
//   if (__DEV__) {
//     const now = Date.now();
//     console.log(
//       `[NAV-PERF] ${componentName} rendered for ${routeName} at ${now}`
//     );
//     return now;
//   }
//   return null;
// };

const Stack = createStackNavigator();
const BottomTabNavigator = createBottomTabNavigator();
const Drawer = createDrawerNavigator();
const PersonalStack = createStackNavigator();

// Personal Section with persistent header
const PersonalSection = () => (
  <PersonalStack.Navigator
    screenOptions={{
      header: (props) => <CustomTopTabBar {...props} />,
    }}
  >
    <PersonalStack.Screen
      name="PersonalTabs"
      component={PersonalBottomTabs}
      options={{ headerTitle: "Dashboard" }}
    />
  </PersonalStack.Navigator>
);

const HomeStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="Dashboard" component={DashboardScreen} />
      <Stack.Screen name="AI" component={NutraCompassAIScreen} />
      <Stack.Screen
        name="Nutritional Program"
        component={NutritionalProgramScreen}
      />
    </Stack.Navigator>
  );
};

const FoodsStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Food Options" component={FoodsScreen} />
      <Stack.Screen name="Custom Meals" component={CustomMealsScreen} />
    </Stack.Navigator>
  );
};

const PersonalBottomTabs = () => {
  // const state = useNavigationState((state) => state);
  // useEffect(() => {
  //   logNavigationPerformance(
  //     "PersonalBottomTabs",
  //     state?.routes[state.index]?.name
  //   );
  // }, [state]);

  return (
    <BottomTabNavigator.Navigator
      initialRouteName="Home"
      tabBar={(props) => <CustomPersonalBottomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <BottomTabNavigator.Screen name="Home" component={HomeStack} />
      <BottomTabNavigator.Screen name="Pantry" component={FoodsStack} />
      <BottomTabNavigator.Screen name="Diary" component={FoodDiaryScreen} />
      <BottomTabNavigator.Screen
        name="Fitness"
        component={WorkoutDiaryScreen}
      />
    </BottomTabNavigator.Navigator>
  );
};

const SocialBottomTabs = () => {
  return (
    <BottomTabNavigator.Navigator
      tabBar={(props) => <CustomSocialBottomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <BottomTabNavigator.Screen
        name="My Profile"
        component={MyProfileScreen}
      />
    </BottomTabNavigator.Navigator>
  );
};

const MessagesStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
      }}
    >
      <Stack.Screen name="Chat" component={ChatScreen} />
      <Stack.Screen name="SelectFriend" component={SelectFriend} />
      <Stack.Screen name="TextScreen" component={TextScreen} />
    </Stack.Navigator>
  );
};

const SettingsStack = () => (
  <Stack.Navigator
    screenOptions={{
      header: (props) => <CustomTopTabBar {...props} />,
    }}
  >
    <Stack.Screen
      name="Settings"
      component={SettingsScreen}
      options={{ headerTitle: "Settings" }}
    />
    <Stack.Screen
      name="ProfileSettings"
      component={ProfileSettings}
      options={{ headerTitle: "Profile Settings" }}
    />
    <Stack.Screen
      name="NutritionSettings"
      component={NutritionSettings}
      options={{ headerTitle: "Nutrition Settings" }}
    />
    <Stack.Screen
      name="MeasurementSettings"
      component={MeasurementSettings}
      options={{ headerTitle: "Measurement Settings" }}
    />
    <Stack.Screen
      name="NotificationsSettings"
      component={NotificationsSettings}
      options={{ headerTitle: "Notification Settings" }}
    />
    <Stack.Screen
      name="PermissionSettings"
      component={PermissionSettings}
      options={{ headerTitle: "Permission Settings" }}
    />
    <Stack.Screen
      name="AccountSettings"
      component={AccountSettings}
      options={{ headerTitle: "Account Settings" }}
    />
    <Stack.Screen
      name="Tutorial"
      component={TutorialScreen}
      options={{ headerTitle: "Tutorial" }}
    />
  </Stack.Navigator>
);

const DrawerNavigator = () => {
  const drawerWidth = isTablet()
    ? Dimensions.get("window").width * 0.5
    : Dimensions.get("window").width * 0.8;

  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        drawerStyle: { width: drawerWidth },
        headerShown: false,
        // Optimize drawer animation
        drawerType: "slide",
        overlayColor: "transparent",
        gestureHandlerProps: {
          enableTrackpadTwoFingerGesture: true,
        },
      }}
    >
      <Drawer.Screen name="Personal" component={PersonalSection} />
      <Drawer.Screen
        name="Themes"
        component={ThemeScreen}
        options={{
          headerShown: true,
          header: (props) => <CustomTopTabBar {...props} />,
          headerTitle: "Theme Palette",
        }}
      />
      <Drawer.Screen
        name="Feedback"
        component={FeedbackForumScreen}
        options={{
          headerShown: true,
          header: (props) => <CustomTopTabBar {...props} />,
          headerTitle: "Suggestion Box",
        }}
      />
      <Drawer.Screen
        name="SettingsStack"
        component={SettingsStack}
        options={{ headerShown: false }}
      />
    </Drawer.Navigator>
  );
};

const RootNavigator = () => {
  // const state = useNavigationState((state) => state);

  // useEffect(() => {
  //   if (state) {
  //     const currentRoute = state.routes[state.index];
  //     logNavigationPerformance("RootNavigator", currentRoute.name);
  //   }
  // }, [state]);

  return (
    <Stack.Navigator
      initialRouteName={"AppLoadingScreen"}
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
        // Optimize stack animations
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
      }}
    >
      <Stack.Screen
        name="AppLoadingScreen"
        component={AppLoadingScreen}
        options={{ title: "AppLoadingScreen" }}
      />
      <Stack.Screen
        name="TutorialInitial"
        component={TutorialScreen}
        initialParams={{ isInitial: true }}
      />
      <Stack.Screen
        name="Main"
        component={DrawerNavigator}
        options={{ title: "Main" }}
      />
    </Stack.Navigator>
  );
};

const UserStack = () => {
  return <RootNavigator />;
};

export default UserStack;
