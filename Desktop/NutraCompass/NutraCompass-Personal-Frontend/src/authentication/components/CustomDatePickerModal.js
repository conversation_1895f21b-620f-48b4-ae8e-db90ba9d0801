import React, { useState, useEffect } from "react";
import { Modal, View, TouchableOpacity, Text, Platform } from "react-native";
import { Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useThemeContext } from "../../context/ThemeContext.js";
import { isTablet, scaleSize } from "../../utils/deviceUtils.js";

const CustomDatePickerModal = ({
  title,
  selectedDate,
  onSelect,
  visible,
  onClose,
}) => {
  const tablet = isTablet();
  const { theme } = useThemeContext();
  const [temporaryDate, setTemporaryDate] = useState(new Date());
  const [showAndroidPicker, setShowAndroidPicker] = useState(false);

  // Initialize and update date when visible or selectedDate changes
  useEffect(() => {
    if (visible) {
      const initialDate =
        selectedDate instanceof Date ? selectedDate : new Date();
      setTemporaryDate(initialDate);
      if (Platform.OS === "android") {
        setShowAndroidPicker(true);
      }
    }
  }, [visible, selectedDate]);

  const handleDateChange = (event, selectedDate) => {
    if (Platform.OS === "android") {
      // Android specific handling
      if (event.type === "set" && selectedDate) {
        setTemporaryDate(selectedDate);
        onSelect(selectedDate);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      setShowAndroidPicker(false);
      onClose();
    } else {
      // iOS handling
      if (selectedDate) {
        setTemporaryDate(selectedDate);
      }
    }
  };

  const handleSave = () => {
    if (temporaryDate instanceof Date && !isNaN(temporaryDate)) {
      onSelect(temporaryDate);
      onClose();
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      console.error("Invalid or undefined date selected.");
    }
  };

  // Render Android date picker separately
  if (Platform.OS === "android" && showAndroidPicker) {
    return (
      <DateTimePicker
        value={temporaryDate}
        mode="date"
        display="calendar"
        onChange={handleDateChange}
        themeVariant={theme.dark ? "dark" : "light"}
        accentColor={theme.colors.primary}
      />
    );
  }

  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <View
        style={{
          flex: 1,
          justifyContent: "flex-end",
          backgroundColor: "rgba(0, 0, 0, 0.7)",
        }}
      >
        <Card
          style={{
            width: "100%",
            borderTopLeftRadius: scaleSize(20),
            borderTopRightRadius: scaleSize(20),
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            paddingVertical: scaleSize(20),
          }}
        >
          <Card.Title
            title={title}
            titleStyle={{
              fontSize: scaleSize(20),
              paddingTop: scaleSize(10),
              paddingLeft: scaleSize(10),
            }}
          />
          <Card.Content
            style={{
              height: scaleSize(250),
              justifyContent: "center",
            }}
          >
            {Platform.OS === "ios" ? (
              // iOS Picker Implementation
              <DateTimePicker
                value={temporaryDate}
                mode="date"
                display="spinner"
                onChange={handleDateChange}
                textColor={theme.colors.primaryTextColor}
                themeVariant="dark"
                style={{
                  height: "100%",
                  width: "100%",
                  transform: tablet ? [{ scale: 1.3 }] : [],
                }}
              />
            ) : (
              // Android preview in our modal
              <TouchableOpacity
                onPress={() => setShowAndroidPicker(true)}
                style={{
                  padding: scaleSize(20),
                  backgroundColor: theme.colors.cardBackground,
                  borderRadius: scaleSize(8),
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{
                    fontSize: scaleSize(18),
                    color: theme.colors.primaryTextColor,
                    fontWeight: "bold",
                  }}
                >
                  {temporaryDate.toDateString()}
                </Text>
                <Text
                  style={{
                    fontSize: scaleSize(14),
                    color: theme.colors.secondaryTextColor,
                    marginTop: scaleSize(8),
                  }}
                >
                  Tap to select date
                </Text>
              </TouchableOpacity>
            )}
          </Card.Content>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-around",
              paddingBottom: scaleSize(20),
              paddingHorizontal: scaleSize(20),
            }}
          >
            <TouchableOpacity
              onPress={onClose}
              style={{
                padding: scaleSize(12),
                borderRadius: scaleSize(8),
                backgroundColor: theme.colors.cardBackground,
                minWidth: scaleSize(120),
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: theme.colors.primary,
                  fontSize: scaleSize(16),
                  fontWeight: "bold",
                }}
              >
                CANCEL
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleSave}
              style={{
                padding: scaleSize(12),
                borderRadius: scaleSize(8),
                backgroundColor: theme.colors.primary,
                minWidth: scaleSize(120),
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: "white",
                  fontSize: scaleSize(16),
                  fontWeight: "bold",
                }}
              >
                SAVE
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </View>
    </Modal>
  );
};

export default CustomDatePickerModal;
