import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { Image } from "expo-image";
import { <PERSON>, Button } from "react-native-paper";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";
export default function BodyTypeSection({ value, setValue, onNext }) {
  const { theme } = useThemeContext();
  const [selectedBodyType, setSelectedBodyType] = useState(null);

  const bodyTypes = [
    {
      id: 1,
      value: 8,
      description: "Very Lean: 5-10% body fat",
      image: require("../../../assets/BodyFatImages/1.png"), // Replace with actual image path
    },
    {
      id: 2,
      value: 13,
      description: "Lean: 11-15% body fat",
      image: require("../../../assets/BodyFatImages/2.png"), // Replace with actual image path
    },
    {
      id: 3,
      value: 18,
      description: "Moderately Lean: 16-20% body fat",
      image: require("../../../assets/BodyFatImages/3.png"), // Replace with actual image path
    },
    {
      id: 4,
      value: 23,
      description: "Average: 21-25% body fat",
      image: require("../../../assets/BodyFatImages/4.png"), // Replace with actual image path
    },
    {
      id: 5,
      value: 28,
      description: "Above Average: 26-30% body fat",
      image: require("../../../assets/BodyFatImages/5.png"), // Replace with actual image path
    },
    {
      id: 6,
      value: 33,
      description: "Overweight: 31-35% body fat",
      image: require("../../../assets/BodyFatImages/6.png"), // Replace with actual image path
    },
    {
      id: 7,
      value: 40,
      description: "Obese: 36%+ body fat",
      image: require("../../../assets/BodyFatImages/7.png"), // Replace with actual image path
    },
  ];

  useEffect(() => {
    if (
      value.bodyFatPercentageRange &&
      (!selectedBodyType ||
        selectedBodyType.value !== value.bodyFatPercentageRange)
    ) {
      const matchingBodyType = bodyTypes.find(
        (type) => type.value === value.bodyFatPercentageRange
      );
      if (matchingBodyType) {
        setSelectedBodyType(matchingBodyType);
      }
    }
  }, [value, selectedBodyType]);

  const handleBodyTypeSelect = (bodyType) => {
    setSelectedBodyType(bodyType);
    setValue({ ...value, bodyFatPercentageRange: bodyType.value });
  };

  const handleNext = () => {
    onNext();
  };

  const handleSkip = () => {
    onNext();
  };

  return (
    <View style={{ flex: 1, width: "100%" }}>
      <View
        style={{
          alignItems: "center",
          paddingTop: scaleSize(10),
          gap: scaleSize(5),
        }}
      >
        <Text
          style={{
            fontSize: scaleSize(28),
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
          }}
        >
          Body Type
        </Text>
        <Text
          style={{
            fontSize: scaleSize(18),
            color: "black",
            textAlign: "center",
          }}
        >
          To improve the accuracy in creating daily nutritional goals for you,
          select your body type below.
        </Text>

        <Card
          style={{
            width: "100%",
            marginTop: scaleSize(10),
            borderRadius: scaleSize(14),
            borderWidth: scaleSize(1),
            borderColor: theme.colors.primary,
          }}
        >
          <Card.Content
            style={{
              flexDirection: "row",
              justifyContent: "space-evenly",
            }}
          >
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {bodyTypes.map((bodyType) => (
                <TouchableOpacity
                  key={bodyType.id}
                  onPress={() => handleBodyTypeSelect(bodyType)}
                  style={{
                    borderWidth: scaleSize(1),
                    borderRadius: scaleSize(10),
                    padding: scaleSize(10),
                    borderColor:
                      selectedBodyType?.id === bodyType.id
                        ? theme.colors.primary
                        : "transparent",
                  }}
                >
                  <Image
                    source={bodyType.image}
                    style={{
                      width: scaleSize(150),
                      height: scaleSize(150),
                      alignSelf: "center",
                    }}
                    contentFit="contain"
                  />
                  <Text
                    style={{
                      textAlign: "center",
                      fontSize: scaleSize(14),
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    {bodyType.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Card.Content>
        </Card>
      </View>
      <View
        style={{
          flex: 1,
          alignItems: "center",
          justifyContent: "flex-end",
          gap: scaleSize(20),
        }}
      >
        {/* Next Button */}
        <TouchableOpacity
          onPress={handleNext}
          style={{
            backgroundColor: "white",
            borderRadius: scaleSize(8),
            width: "60%",
            paddingVertical: scaleSize(12),
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text
            style={{
              color: "black",
              fontSize: scaleSize(18),
              fontWeight: "bold",
            }}
          >
            Next
          </Text>
        </TouchableOpacity>

        {/* Skip Button */}
        <TouchableOpacity
          onPress={handleSkip}
          style={{
            width: "60%",
            paddingVertical: scaleSize(12),
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text
            style={{
              color: "white",
              fontSize: scaleSize(18),
              fontWeight: "bold",
            }}
          >
            Skip
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
