import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableWithoutFeedback,
  TouchableOpacity,
  Keyboard,
} from "react-native";
import { Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../../context/ThemeContext.js";
import { useAuth } from "../context/AuthContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const ResetPasswordModal = ({ visible, onClose }) => {
  const { theme } = useThemeContext();
  const [email, setEmail] = useState("");
  const { resetPassword } = useAuth();

  const handleForgotPassword = () => {
    if (!email) {
      alert("Please enter your email address.");
      return;
    }
    resetPassword(email, "");
    onClose();
  };

  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.modalBackground}>
          <Card style={styles.card(theme)}>
            <Card.Title
              title="Reset Password"
              titleStyle={styles.title(theme)}
            />
            <Card.Content>
              <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={styles.input(theme)}
                    keyboardType="email-address"
                    placeholder="Enter your email"
                    placeholderTextColor={theme.colors.subTextColor}
                    value={email}
                    onChangeText={setEmail}
                    autoCapitalize="none"
                  />
                </View>
              </TouchableWithoutFeedback>
            </Card.Content>
            <View style={styles.actionsContainer}>
              <TouchableOpacity style={styles.button(theme)} onPress={onClose}>
                <Text style={styles.buttonText(theme)}>CANCEL</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button(theme), styles.resetButton(theme)]}
                onPress={handleForgotPassword}
              >
                <Text
                  style={[styles.buttonText(theme), styles.resetButtonText]}
                >
                  RESET
                </Text>
              </TouchableOpacity>
            </View>
          </Card>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = {
  modalBackground: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  card: (theme) => ({
    width: "85%",
    paddingVertical: scaleSize(25),
    borderRadius: scaleSize(16),
    backgroundColor: theme.colors.surface,
  }),
  title: (theme) => ({
    paddingTop: scaleSize(4),
    fontSize: scaleSize(18),
    fontWeight: "bold",
    color: theme.colors.primaryTextColor,
    alignSelf: "center",
  }),
  inputContainer: {
    alignSelf: "center",
    width: "90%",
    marginTop: scaleSize(10),
  },
  input: (theme) => ({
    height: scaleSize(45),
    borderColor: theme.colors.primary,
    borderWidth: scaleSize(1.5),
    borderRadius: scaleSize(10),
    paddingHorizontal: scaleSize(12),
    color: theme.colors.primaryTextColor,
    fontSize: scaleSize(16),
    paddingVertical: scaleSize(10),
  }),
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    paddingHorizontal: scaleSize(15),
    paddingTop: scaleSize(15),
    paddingBottom: scaleSize(5),
  },
  button: (theme) => ({
    paddingVertical: scaleSize(8),
    paddingHorizontal: scaleSize(15),
    borderRadius: scaleSize(8),
    marginLeft: scaleSize(10),
    backgroundColor: theme.colors.surfaceVariant,
  }),
  resetButton: (theme) => ({
    backgroundColor: theme.colors.primary,
  }),
  buttonText: (theme) => ({
    fontSize: scaleSize(14),
    fontWeight: "bold",
    color: theme.colors.primaryTextColor,
  }),
  resetButtonText: {
    color: "#FFFFFF",
  },
};

export default ResetPasswordModal;
