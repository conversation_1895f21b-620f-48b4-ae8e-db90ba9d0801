import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  TouchableOpacity,
  Text,
  Platform,
  StyleSheet,
  ScrollView,
} from "react-native";
import { Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { Picker } from "@react-native-picker/picker";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const CustomHeightPickerModal = ({
  title,
  selectedHeight,
  onSelect,
  visible,
  onClose,
}) => {
  const { theme } = useThemeContext();
  const [selectedUnit, setSelectedUnit] = useState("in");
  const [selectedFeet, setSelectedFeet] = useState("5");
  const [selectedInches, setSelectedInches] = useState("7");
  const [selectedCentimeters, setSelectedCentimeters] = useState("170");

  useEffect(() => {
    if (selectedHeight) {
      setSelectedUnit(selectedHeight.unit || "in");
      if (selectedHeight.unit === "in" && selectedHeight.inches) {
        setSelectedFeet(Math.floor(selectedHeight.inches / 12).toString());
        setSelectedInches((selectedHeight.inches % 12).toString());
      } else if (selectedHeight.unit === "cm" && selectedHeight.centimeters) {
        setSelectedCentimeters(selectedHeight.centimeters.toString());
      }
    } else {
      // Default values
      setSelectedFeet("5");
      setSelectedInches("7");
      setSelectedCentimeters("170");
    }
  }, [selectedHeight]);

  const handleSave = () => {
    let height = {};
    if (selectedUnit === "cm") {
      const cm = parseFloat(selectedCentimeters);
      height = {
        inches: parseFloat((cm / 2.54).toFixed(2)),
        centimeters: cm,
        unit: "cm",
      };
    } else {
      const feet = parseInt(selectedFeet, 10);
      const inches = parseInt(selectedInches, 10);
      const totalInches = feet * 12 + inches;
      height = {
        inches: totalInches,
        centimeters: parseFloat((totalInches * 2.54).toFixed(2)),
        unit: "in",
      };
    }

    onSelect(height);
    onClose();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Android-specific UI components
  const UnitButton = ({ label, value }) => (
    <TouchableOpacity
      onPress={() => setSelectedUnit(value)}
      style={[
        styles.androidUnitButton,
        {
          backgroundColor: theme.colors.cardBackground,
          borderColor: theme.colors.primary,
        },
        selectedUnit === value && {
          backgroundColor: theme.colors.primary,
        },
      ]}
    >
      <Text
        style={[
          styles.androidUnitText,
          { color: theme.colors.primaryTextColor },
          selectedUnit === value && styles.androidUnitSelectedText,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const ValueButton = ({ label, value, isSelected, onPress }) => (
    <TouchableOpacity
      onPress={() => onPress(value)}
      style={[
        styles.androidValueButton,
        {
          backgroundColor: theme.colors.cardBackground,
          borderColor: theme.colors.primary,
        },
        isSelected && {
          backgroundColor: theme.colors.primary,
        },
      ]}
    >
      <Text
        style={[
          styles.androidValueText,
          { color: theme.colors.primaryTextColor },
          isSelected && styles.androidValueSelectedText,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  // Generate value ranges
  const feetValues = Array.from({ length: 5 }, (_, i) => (i + 4).toString());
  const inchValues = Array.from({ length: 12 }, (_, i) => i.toString());
  const cmValues = Array.from({ length: 221 }, (_, i) => (i + 30).toString());

  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <View
        style={{
          flex: 1,
          justifyContent: "flex-end",
          backgroundColor: "rgba(0, 0, 0, 0.7)",
        }}
      >
        <Card
          style={{
            width: "100%",
            borderTopLeftRadius: scaleSize(20),
            borderTopRightRadius: scaleSize(20),
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            paddingVertical: scaleSize(20),
          }}
        >
          <Card.Title
            title={title}
            titleStyle={{
              fontSize: scaleSize(20),
              paddingTop: scaleSize(10),
              paddingLeft: scaleSize(10),
            }}
          />
          <Card.Content
            style={{
              height: scaleSize(250),
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {Platform.OS === "ios" ? (
              // iOS Picker Implementation
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-around",
                  alignItems: "center",
                  width: "100%",
                }}
              >
                {selectedUnit === "in" ? (
                  <>
                    <View style={{ flex: 1 }}>
                      <Picker
                        selectedValue={selectedFeet}
                        onValueChange={setSelectedFeet}
                        style={{
                          height: "100%",
                          justifyContent: "center",
                        }}
                        itemStyle={{
                          fontSize: scaleSize(20),
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {feetValues.map((value) => (
                          <Picker.Item
                            key={value}
                            label={`${value} ft`}
                            value={value}
                          />
                        ))}
                      </Picker>
                    </View>

                    <View style={{ flex: 1 }}>
                      <Picker
                        selectedValue={selectedInches}
                        onValueChange={setSelectedInches}
                        style={{
                          height: "100%",
                          justifyContent: "center",
                        }}
                        itemStyle={{
                          fontSize: scaleSize(20),
                          color: theme.colors.primaryTextColor,
                        }}
                      >
                        {inchValues.map((value) => (
                          <Picker.Item
                            key={value}
                            label={`${value} in`}
                            value={value}
                          />
                        ))}
                      </Picker>
                    </View>
                  </>
                ) : (
                  <View style={{ flex: 1 }}>
                    <Picker
                      selectedValue={selectedCentimeters}
                      onValueChange={setSelectedCentimeters}
                      style={{
                        height: "100%",
                        justifyContent: "center",
                      }}
                      itemStyle={{
                        fontSize: scaleSize(20),
                        color: theme.colors.primaryTextColor,
                      }}
                    >
                      {cmValues.map((value) => (
                        <Picker.Item
                          key={value}
                          label={`${value} cm`}
                          value={value}
                        />
                      ))}
                    </Picker>
                  </View>
                )}
                <View style={{ flex: 0.7 }}>
                  <Picker
                    selectedValue={selectedUnit}
                    onValueChange={setSelectedUnit}
                    style={{
                      height: "100%",
                      justifyContent: "center",
                    }}
                    itemStyle={{
                      fontSize: scaleSize(18),
                      color: theme.colors.primaryTextColor,
                    }}
                  >
                    <Picker.Item label="cm" value="cm" />
                    <Picker.Item label="ft/in" value="in" />
                  </Picker>
                </View>
              </View>
            ) : (
              // Android Custom Implementation
              <View style={{ width: "100%" }}>
                {/* Unit Selection */}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "center",
                    marginBottom: scaleSize(20),
                  }}
                >
                  <UnitButton label="cm" value="cm" />
                  <View style={{ width: scaleSize(20) }} />
                  <UnitButton label="ft/in" value="in" />
                </View>

                {/* Value Selection */}
                {selectedUnit === "in" ? (
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-around",
                    }}
                  >
                    {/* Feet Selection */}
                    <View style={{ flex: 1, alignItems: "center" }}>
                      <Text
                        style={{
                          fontSize: scaleSize(16),
                          color: theme.colors.primaryTextColor,
                          marginBottom: scaleSize(10),
                        }}
                      >
                        Feet
                      </Text>
                      <ScrollView
                        style={styles.androidScrollView}
                        contentContainerStyle={styles.androidScrollContent}
                      >
                        {feetValues.map((value) => (
                          <ValueButton
                            key={`feet-${value}`}
                            label={value}
                            value={value}
                            isSelected={selectedFeet === value}
                            onPress={setSelectedFeet}
                          />
                        ))}
                      </ScrollView>
                    </View>

                    {/* Inches Selection */}
                    <View style={{ flex: 1, alignItems: "center" }}>
                      <Text
                        style={{
                          fontSize: scaleSize(16),
                          color: theme.colors.primaryTextColor,
                          marginBottom: scaleSize(10),
                        }}
                      >
                        Inches
                      </Text>
                      <ScrollView
                        style={styles.androidScrollView}
                        contentContainerStyle={styles.androidScrollContent}
                      >
                        {inchValues.map((value) => (
                          <ValueButton
                            key={`inches-${value}`}
                            label={value}
                            value={value}
                            isSelected={selectedInches === value}
                            onPress={setSelectedInches}
                          />
                        ))}
                      </ScrollView>
                    </View>
                  </View>
                ) : (
                  // Centimeters Selection
                  <View style={{ alignItems: "center" }}>
                    <Text
                      style={{
                        fontSize: scaleSize(16),
                        color: theme.colors.primaryTextColor,
                        marginBottom: scaleSize(10),
                      }}
                    >
                      Centimeters
                    </Text>
                    <ScrollView
                      style={styles.androidScrollView}
                      contentContainerStyle={styles.androidScrollContent}
                      horizontal={true}
                    >
                      {cmValues.map((value) => (
                        <ValueButton
                          key={`cm-${value}`}
                          label={value}
                          value={value}
                          isSelected={selectedCentimeters === value}
                          onPress={setSelectedCentimeters}
                        />
                      ))}
                    </ScrollView>
                  </View>
                )}
              </View>
            )}
          </Card.Content>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-around",
              paddingBottom: scaleSize(20),
              paddingHorizontal: scaleSize(20),
            }}
          >
            <TouchableOpacity
              onPress={onClose}
              style={{
                padding: scaleSize(12),
                borderRadius: scaleSize(8),
                backgroundColor: theme.colors.cardBackground,
                minWidth: scaleSize(120),
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: theme.colors.primary,
                  fontSize: scaleSize(16),
                  fontWeight: "bold",
                }}
              >
                CANCEL
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleSave}
              style={{
                padding: scaleSize(12),
                borderRadius: scaleSize(8),
                backgroundColor: theme.colors.primary,
                minWidth: scaleSize(120),
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: "white",
                  fontSize: scaleSize(16),
                  fontWeight: "bold",
                }}
              >
                SAVE
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </View>
    </Modal>
  );
};

// Android-specific styles
const styles = StyleSheet.create({
  androidUnitButton: {
    paddingVertical: scaleSize(10),
    paddingHorizontal: scaleSize(20),
    borderRadius: scaleSize(8),
    borderWidth: 1,
  },
  androidUnitText: {
    fontSize: scaleSize(16),
    fontWeight: "500",
  },
  androidUnitSelectedText: {
    color: "white",
  },
  androidValueButton: {
    padding: scaleSize(12),
    borderRadius: scaleSize(8),
    margin: scaleSize(4),
    borderWidth: 1,
    minWidth: scaleSize(60),
    alignItems: "center",
    justifyContent: "center",
  },
  androidValueText: {
    fontSize: scaleSize(16),
  },
  androidValueSelectedText: {
    color: "white",
    fontWeight: "bold",
  },
  androidScrollView: {
    maxHeight: scaleSize(150),
  },
  androidScrollContent: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
  },
});

export default CustomHeightPickerModal;
