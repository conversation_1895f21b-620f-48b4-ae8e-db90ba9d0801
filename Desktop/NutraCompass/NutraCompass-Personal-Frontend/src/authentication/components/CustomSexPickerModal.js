import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  TouchableOpacity,
  Text,
  Platform,
  StyleSheet,
} from "react-native";
import { Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { Picker } from "@react-native-picker/picker";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const CustomSexPickerModal = ({ title, visible, onClose, onSelect }) => {
  const { theme } = useThemeContext();
  const [selectedValue, setSelectedValue] = useState(null);
  const defaultValue = "Male";

  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);

  const handleSave = () => {
    if (selectedValue !== null) {
      onSelect(selectedValue);
      onClose();
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const isSaveDisabled = selectedValue === null;

  // Android-specific option button component
  const OptionButton = ({ label, value }) => (
    <TouchableOpacity
      onPress={() => setSelectedValue(value)}
      style={[
        styles.androidOption,
        selectedValue === value && {
          backgroundColor: theme.colors.primary,
        },
      ]}
    >
      <Text
        style={[
          styles.androidOptionText,
          selectedValue === value && styles.androidOptionSelectedText,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <View
        style={{
          flex: 1,
          justifyContent: "flex-end",
          backgroundColor: "rgba(0, 0, 0, 0.7)",
        }}
      >
        <Card
          style={{
            width: "100%",
            borderTopLeftRadius: scaleSize(20),
            borderTopRightRadius: scaleSize(20),
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            paddingVertical: scaleSize(20),
          }}
        >
          <Card.Title
            title={title}
            titleStyle={{
              fontSize: scaleSize(20),
              paddingTop: scaleSize(10),
              paddingLeft: scaleSize(10),
            }}
          />
          <Card.Content
            style={{
              height: scaleSize(200),
              justifyContent: "center",
            }}
          >
            {Platform.OS === "ios" ? (
              // iOS Picker Implementation
              <Picker
                style={{
                  height: "80%",
                  justifyContent: "center",
                }}
                itemStyle={{
                  fontSize: scaleSize(24),
                  height: scaleSize(200),
                  color: theme.colors.primaryTextColor,
                }}
                dropdownIconColor={theme.colors.primaryTextColor}
                selectedValue={selectedValue}
                onValueChange={setSelectedValue}
              >
                <Picker.Item label="Male" value="Male" />
                <Picker.Item label="Female" value="Female" />
              </Picker>
            ) : (
              // Android Custom Implementation
              <View style={styles.androidContainer}>
                <OptionButton label="Male" value="Male" />
                <OptionButton label="Female" value="Female" />
              </View>
            )}
          </Card.Content>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-around",
              paddingBottom: scaleSize(20),
              paddingHorizontal: scaleSize(20),
            }}
          >
            <TouchableOpacity
              onPress={onClose}
              style={{
                padding: scaleSize(12),
                borderRadius: scaleSize(8),
                backgroundColor: theme.colors.cardBackground,
                minWidth: scaleSize(120),
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: theme.colors.primary,
                  fontSize: scaleSize(16),
                  fontWeight: "bold",
                }}
              >
                CANCEL
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleSave}
              disabled={isSaveDisabled}
              style={{
                padding: scaleSize(12),
                borderRadius: scaleSize(8),
                backgroundColor: isSaveDisabled
                  ? theme.colors.disabled
                  : theme.colors.primary,
                minWidth: scaleSize(120),
                alignItems: "center",
                opacity: isSaveDisabled ? 0.6 : 1,
              }}
            >
              <Text
                style={{
                  color: "white",
                  fontSize: scaleSize(16),
                  fontWeight: "bold",
                }}
              >
                SAVE
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </View>
    </Modal>
  );
};

// Android-specific styles
const styles = StyleSheet.create({
  androidContainer: {
    width: "100%",
    paddingHorizontal: scaleSize(20),
  },
  androidOption: {
    padding: scaleSize(16),
    marginVertical: scaleSize(8),
    borderRadius: scaleSize(8),
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  androidOptionText: {
    fontSize: scaleSize(18),
    fontWeight: "500",
  },
  androidOptionSelectedText: {
    color: "white",
  },
});

export default CustomSexPickerModal;
