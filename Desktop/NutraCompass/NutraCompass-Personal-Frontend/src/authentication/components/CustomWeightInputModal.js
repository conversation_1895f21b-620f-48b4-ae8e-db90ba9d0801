import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
} from "react-native";
import { Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../../context/ThemeContext.js";
import { useUserSettings } from "../../features/Settings/context/UserSettingsContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const CustomWeightInputModal = ({
  title,
  selectedWeight,
  onSelect,
  visible,
  onClose,
}) => {
  const { theme } = useThemeContext();
  const { getUserMeasurementSettings } = useUserSettings();
  const measurementSettings = getUserMeasurementSettings();
  const [selectedUnit, setSelectedUnit] = useState("lbs");
  const [inputValue, setInputValue] = useState("");
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  // Initialize with user's current weight
  useEffect(() => {
    if (visible) {
      const defaultUnit = measurementSettings?.bodyWeightUnit || "lbs";
      let initialValue = "";
      let initialUnit = defaultUnit;

      if (selectedWeight) {
        const parts = selectedWeight.split(" ");
        if (parts.length >= 2) {
          initialValue = parts[0];
          initialUnit = parts[1];
        } else if (parts.length === 1) {
          initialValue = parts[0];
        }
      } else {
        initialValue = defaultUnit === "lbs" ? "150" : "68";
      }

      if (initialUnit !== "kg" && initialUnit !== "lbs") {
        initialUnit = defaultUnit;
      }

      setInputValue(initialValue);
      setSelectedUnit(initialUnit);
    }
  }, [visible, selectedWeight]);

  // Use keyboardDidShow/DidHide instead of keyboardWillShow/WillHide
  useEffect(() => {
    const showSubscription = Keyboard.addListener("keyboardDidShow", (e) => {
      setKeyboardHeight(e.endCoordinates.height);
    });
    const hideSubscription = Keyboard.addListener("keyboardDidHide", () => {
      setKeyboardHeight(0);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  const handleSave = () => {
    const weight = `${inputValue} ${selectedUnit}`;
    onSelect(weight);
    onClose();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const convertWeight = (value, fromUnit, toUnit) => {
    if (!value) return "";
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) return "";

    if (fromUnit === "kg" && toUnit === "lbs") {
      return (numericValue * 2.20462).toFixed(1);
    } else if (fromUnit === "lbs" && toUnit === "kg") {
      return (numericValue / 2.20462).toFixed(1);
    }
    return value;
  };

  const handleUnitButtonClick = (unit) => {
    const convertedValue = convertWeight(inputValue, selectedUnit, unit);
    setSelectedUnit(unit);
    setInputValue(convertedValue);
  };

  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <TouchableWithoutFeedback onPress={onClose}>
        <View
          style={{
            flex: 1,
            justifyContent: "flex-end",
            backgroundColor: "rgba(0, 0, 0, 0.7)",
          }}
        >
          {/* Positioned above keyboard with dynamic bottom spacing */}
          <View
            style={{
              width: "100%",
              position: "absolute",
              bottom: keyboardHeight, // Positions directly above keyboard
            }}
          >
            <TouchableWithoutFeedback>
              <Card
                style={{
                  borderTopLeftRadius: scaleSize(20),
                  borderTopRightRadius: scaleSize(20),
                  paddingVertical: scaleSize(20),
                }}
              >
                <Card.Title
                  title={title}
                  titleStyle={{
                    fontSize: scaleSize(20),
                    paddingTop: scaleSize(10),
                    paddingLeft: scaleSize(10),
                  }}
                />
                <Card.Content
                  style={{
                    alignItems: "center",
                    paddingVertical: scaleSize(20),
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "center",
                      marginBottom: scaleSize(20),
                    }}
                  >
                    <TouchableOpacity
                      onPress={() => handleUnitButtonClick("kg")}
                      style={{
                        padding: scaleSize(12),
                        borderTopLeftRadius: scaleSize(8),
                        borderBottomLeftRadius: scaleSize(8),
                        backgroundColor:
                          selectedUnit === "kg"
                            ? theme.colors.primary
                            : theme.colors.cardBackgroundColor,
                        minWidth: scaleSize(80),
                        alignItems: "center",
                        borderWidth: scaleSize(1),
                        borderColor: theme.colors.primary,
                      }}
                    >
                      <Text
                        style={{
                          color:
                            selectedUnit === "kg"
                              ? theme.colors.primaryTextColor
                              : theme.colors.primary,
                          fontSize: scaleSize(16),
                          fontWeight: "bold",
                        }}
                      >
                        kg
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      onPress={() => handleUnitButtonClick("lbs")}
                      style={{
                        padding: scaleSize(12),
                        borderTopRightRadius: scaleSize(8),
                        borderBottomRightRadius: scaleSize(8),
                        backgroundColor:
                          selectedUnit === "lbs"
                            ? theme.colors.primary
                            : theme.colors.cardBackgroundColor,
                        minWidth: scaleSize(80),
                        alignItems: "center",
                        borderWidth: scaleSize(1),
                        borderColor: theme.colors.primary,
                        borderLeftWidth: 0,
                      }}
                    >
                      <Text
                        style={{
                          color:
                            selectedUnit === "lbs"
                              ? theme.colors.primaryTextColor
                              : theme.colors.primary,
                          fontSize: scaleSize(16),
                          fontWeight: "bold",
                        }}
                      >
                        lbs
                      </Text>
                    </TouchableOpacity>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      borderColor: theme.colors.primary,
                      borderWidth: scaleSize(1),
                      borderRadius: scaleSize(8),
                      width: scaleSize(200),
                      height: scaleSize(50),
                      paddingHorizontal: scaleSize(10),
                    }}
                  >
                    <TextInput
                      style={{
                        flex: 1,
                        textAlign: "center",
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(24),
                        fontWeight: "bold",
                        padding: 0,
                        margin: 0,
                      }}
                      keyboardType="numeric"
                      value={inputValue}
                      onChangeText={(text) => setInputValue(text)}
                      autoFocus={true}
                      placeholder="0"
                      placeholderTextColor={theme.colors.subTextColor}
                    />
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(20),
                        marginLeft: scaleSize(5),
                      }}
                    >
                      {selectedUnit}
                    </Text>
                  </View>
                </Card.Content>

                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-around",
                    paddingBottom: scaleSize(20),
                    paddingHorizontal: scaleSize(20),
                  }}
                >
                  <TouchableOpacity
                    onPress={onClose}
                    style={{
                      padding: scaleSize(12),
                      borderRadius: scaleSize(8),
                      backgroundColor: theme.colors.cardBackground,
                      minWidth: scaleSize(120),
                      alignItems: "center",
                    }}
                  >
                    <Text
                      style={{
                        color: theme.colors.primary,
                        fontSize: scaleSize(16),
                        fontWeight: "bold",
                      }}
                    >
                      CANCEL
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={handleSave}
                    disabled={!inputValue || isNaN(inputValue)}
                    style={{
                      padding: scaleSize(12),
                      borderRadius: scaleSize(8),
                      backgroundColor:
                        !inputValue || isNaN(inputValue)
                          ? theme.colors.subTextColor
                          : theme.colors.primary,
                      minWidth: scaleSize(120),
                      alignItems: "center",
                      opacity: !inputValue || isNaN(inputValue) ? 0.6 : 1,
                    }}
                  >
                    <Text
                      style={{
                        color: theme.colors.primaryTextColor,
                        fontSize: scaleSize(16),
                        fontWeight: "bold",
                      }}
                    >
                      SAVE
                    </Text>
                  </TouchableOpacity>
                </View>
              </Card>
            </TouchableWithoutFeedback>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default CustomWeightInputModal;
