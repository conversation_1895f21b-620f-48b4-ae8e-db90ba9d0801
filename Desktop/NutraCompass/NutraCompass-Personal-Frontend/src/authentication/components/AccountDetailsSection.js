import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  ScrollView,
  Text,
  View,
  TouchableOpacity,
  Alert,
  Modal,
} from "react-native";
import { TextIn<PERSON>, <PERSON><PERSON>, Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../../context/ThemeContext.js";
import { MaterialIcons } from "@expo/vector-icons";
import { scaleSize } from "../../utils/deviceUtils.js";
import { debounce } from "lodash";

// Validation utilities
const validateEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
const validatePassword = (password) => {
  if (!password) return "Password is required";
  if (password.length < 8) return "Password must be at least 8 characters";
  if (!/[A-Z]/.test(password))
    return "Password must contain an uppercase letter";
  if (!/[a-z]/.test(password))
    return "Password must contain a lowercase letter";
  if (!/[0-9]/.test(password)) return "Password must contain a number";
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return "Password must contain a special character";
  }
  return null;
};
const validateName = (name) => (name ? name.trim().length > 0 : false);

export default function AccountDetailsSection({
  captureRegistrationData,
  registrationDataRef,
  value,
  setValue,
  handleSignUp,
  checkEmailExists,
  handleVerifyCode,
  handleResendCode,
}) {
  const { theme } = useThemeContext();

  // State management for input fields and modal
  const [isSecureTextEntry, setIsSecureTextEntry] = useState(true); // Toggles password visibility
  const [errors, setErrors] = useState({}); // Stores validation errors
  const [touched, setTouched] = useState({}); // Tracks which fields the user has interacted with
  const [isFormValid, setIsFormValid] = useState(false); // Determines if the form is valid
  const [isEmailCheckComplete, setIsEmailCheckComplete] = useState(false); // Tracks email validation status
  const [verificationModalVisible, setVerificationModalVisible] =
    useState(false); // Controls modal visibility
  const [verificationCode, setVerificationCode] = useState(""); // Stores the verification code input
  const [isVerifying, setIsVerifying] = useState(false);
  const [visited, setVisited] = useState(false); // Tracks if the section has been visited
  const debounceTimeoutRef = useRef(null);

  useEffect(() => {
    if (!visited) {
      setVisited(true);
    }
  }, []);

  useEffect(() => {
    return () => {
      // Clear email debounce timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Cleanup verification debounces
      const verifyDebounce = debouncedVerifyRef.current;
      const resendDebounce = debouncedResendRef.current;
      verifyDebounce.cancel();
      resendDebounce.cancel();
    };
  }, []);

  /**
   * Validates the form in real-time whenever the value or error states change.
   * Ensures that all fields meet validation criteria before enabling the Sign-Up button.
   */
  useEffect(() => {
    const isFirstNameValid = validateName(value.firstName);
    const isLastNameValid = validateName(value.lastName);
    const isUserNameValid = validateName(value.userName);
    const isEmailValid = validateEmail(value.email);
    const isPasswordValid = validatePassword(value.password) === null;
    const passwordsMatch = value.password === value.confirmPassword;

    // Special handling for email completion status
    const isEmailReady = !isEmailValid || isEmailCheckComplete;

    const isValid =
      isFirstNameValid &&
      isLastNameValid &&
      isUserNameValid &&
      isEmailValid &&
      isPasswordValid &&
      passwordsMatch &&
      isEmailReady &&
      (errors.email === undefined || errors.email === "");

    // console.log("Form Validation Details:", {
    //   isFirstNameValid,
    //   isLastNameValid,
    //   isUserNameValid,
    //   isEmailValid,
    //   isPasswordValid,
    //   passwordsMatch,
    //   isEmailReady,
    //   emailError: errors.email,
    //   allValid: isValid,
    // });

    setIsFormValid(isValid);
  }, [value, errors, isEmailCheckComplete]);

  /**
   * Handles text input changes and validates the input.
   * Updates the error state dynamically based on validation results.
   */
  const handleInputChange = (field, text) => {
    // Update the value immediately
    setValue((prev) => ({ ...prev, [field]: text }));

    // Create a copy of current errors
    const newErrors = { ...errors };

    // Clear the error for this field initially
    delete newErrors[field];

    const fieldLabels = {
      firstName: "First name",
      lastName: "Last name",
      userName: "Username",
      email: "Email",
      password: "Password",
      confirmPassword: "Confirm password",
    };

    // Handle validation based on field type
    if (field === "firstName" || field === "lastName" || field === "userName") {
      if (!validateName(text)) {
        newErrors[field] = `${fieldLabels[field]} cannot be empty.`;
      }
    } else if (field === "email") {
      // Always reset email check status
      setIsEmailCheckComplete(false);

      if (!validateEmail(text)) {
        newErrors.email = "Invalid email address.";
        setIsEmailCheckComplete(true); // Mark as complete since we don't need async check
      } else {
        // Clear any existing timeout
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current);
        }

        // Schedule email availability check
        debounceTimeoutRef.current = setTimeout(async () => {
          try {
            const emailExists = await checkEmailExists(text);
            setErrors((prev) => ({
              ...prev,
              email: emailExists ? "Email is already in use." : "",
            }));
          } catch (error) {
            setErrors((prev) => ({
              ...prev,
              email: "Error checking email availability",
            }));
          } finally {
            setIsEmailCheckComplete(true);
          }
        }, 500);
      }
    } else if (field === "password") {
      const passwordError = validatePassword(text);
      if (passwordError) {
        newErrors.password = passwordError;
      }
    } else if (field === "confirmPassword") {
      if (text !== value.password) {
        newErrors.confirmPassword = "Passwords do not match.";
      }
    }

    // Update errors immediately
    setErrors(newErrors);
  };

  /**
   * Tracks when a user interacts with an input field.
   * Used to control when error messages should be shown.
   */
  const handleBlur = (field) => {
    setTouched((prevTouched) => ({ ...prevTouched, [field]: true }));
  };

  /**
   * Handles the Sign-Up button click.
   */
  const onSignUpPress = async () => {
    if (isFormValid) {
      // Capture registration data BEFORE any state changes
      captureRegistrationData(value);

      try {
        const isSignUpSuccessful = await handleSignUp();

        if (isSignUpSuccessful) {
          setVerificationModalVisible(true);
        }
      } catch (error) {
        console.error("Error during sign-up:", error);
        Alert.alert("Error", "An error occurred during sign-up.");
      }
    } else {
      Alert.alert("Error", "Please fix the errors before proceeding.");
    }
  };

  // Create stable refs for debounced functions
  const debouncedVerifyRef = useRef(
    debounce(async (email, code) => {
      if (isVerifying) return;
      setIsVerifying(true);

      try {
        if (!email || !code) {
          throw new Error("Email and code are required for verification");
        }

        const success = await handleVerifyCode(email, code);
        if (success) {
          setVerificationModalVisible(false);
          // Optional: Navigate to home screen here
        }
      } catch (error) {
        Alert.alert("Error", error.message || "Verification failed");
      } finally {
        setIsVerifying(false);
      }
    }, 500)
  );

  const debouncedResendRef = useRef(
    debounce(async (email) => {
      try {
        console.log("Resending code to:", email);
        if (!email) {
          throw new Error("Email is required to resend code");
        }

        await handleResendCode(email);
      } catch (error) {
        Alert.alert(
          "Error",
          error.message || "Failed to resend the verification code."
        );
      }
    }, 1000)
  );

  /**
   * Handles verification code submission with debounce.
   * Verifies the user's email address using the entered code.
   */
  const onVerifyPress = () => {
    // Ensure we're using the current email value
    debouncedVerifyRef.current(value.email, verificationCode);
  };

  /**
   * Resends the verification code with debounce.
   */
  const onResendPress = () => {
    // Use the email from form state
    debouncedResendRef.current(value.email);
  };

  return (
    <>
      <ScrollView
        style={{ flex: 1, width: "100%" }}
        contentContainerStyle={{
          paddingBottom: scaleSize(200),
        }}
      >
        <TouchableOpacity
          activeOpacity={1}
          style={{
            alignItems: "center",
            paddingTop: scaleSize(10),
            gap: scaleSize(5),
          }}
        >
          <Text
            style={{
              fontSize: scaleSize(28),
              fontWeight: "bold",
              color: "black",
              textAlign: "center",
            }}
          >
            Account Details
          </Text>
          <Text
            style={{
              fontSize: scaleSize(18),
              color: "black",
              textAlign: "center",
            }}
          >
            Enter your account details to complete the signup process.
          </Text>

          <Card style={{ width: "100%", marginTop: scaleSize(10) }}>
            <Card.Content>
              <View
                style={{
                  justifyContent: "center",
                  width: "100%",
                  gap: scaleSize(10),
                }}
              >
                {/* First Name Input */}
                <TextInput
                  label="First Name"
                  style={{
                    backgroundColor: "transparent", // Grayish background for text inputs
                    paddingLeft: scaleSize(0),
                    color: theme.colors.primaryTextColor, // Text color from the theme
                    height: scaleSize(50),
                    width: "100%",
                    fontSize: scaleSize(16),
                  }}
                  value={value?.firstName || ""}
                  onChangeText={(text) => handleInputChange("firstName", text)}
                  onBlur={() => handleBlur("firstName")}
                  mode="outlined"
                  autoCapitalize="none"
                  error={
                    (!!errors.firstName && touched.firstName) ||
                    (!!errors.firstName && visited)
                  }
                />
                {((errors.firstName && touched.firstName) ||
                  (errors.firstName && visited)) && (
                  <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                    {errors.firstName}
                  </Text>
                )}

                {/* Last Name Input */}
                <TextInput
                  label="Last Name"
                  style={{
                    backgroundColor: "transparent", // Grayish background for text inputs
                    paddingLeft: scaleSize(0),
                    color: theme.colors.primaryTextColor, // Text color from the theme
                    height: scaleSize(50),
                    width: "100%",
                    fontSize: scaleSize(16),
                  }}
                  value={value?.lastName || ""}
                  onChangeText={(text) => handleInputChange("lastName", text)}
                  onBlur={() => handleBlur("lastName")}
                  mode="outlined"
                  autoCapitalize="none"
                  error={!!errors.lastName && (touched.lastName || visited)}
                />
                {errors.lastName && (touched.lastName || visited) && (
                  <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                    {errors.lastName}
                  </Text>
                )}

                {/* User Name Input */}
                <TextInput
                  label="User Name"
                  style={{
                    backgroundColor: "transparent", // Grayish background for text inputs
                    paddingLeft: scaleSize(0),
                    color: theme.colors.primaryTextColor, // Text color from the theme
                    height: scaleSize(50),
                    width: "100%",
                    fontSize: scaleSize(16),
                  }}
                  value={value?.userName || ""}
                  onChangeText={(text) => handleInputChange("userName", text)}
                  onBlur={() => handleBlur("userName")}
                  mode="outlined"
                  autoCapitalize="none"
                  error={!!errors.userName && (touched.userName || visited)}
                />
                {errors.userName && (touched.userName || visited) && (
                  <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                    {errors.userName}
                  </Text>
                )}

                {/* Email Input */}
                <TextInput
                  label="Email"
                  style={{
                    backgroundColor: "transparent", // Grayish background for text inputs
                    paddingLeft: scaleSize(0),
                    color: theme.colors.primaryTextColor, // Text color from the theme
                    height: scaleSize(50),
                    width: "100%",
                    fontSize: scaleSize(16),
                  }}
                  value={value?.email || ""}
                  onChangeText={(text) => handleInputChange("email", text)}
                  onBlur={() => handleBlur("email")}
                  mode="outlined"
                  autoCapitalize="none"
                  keyboardType="email-address"
                  error={!!errors.email && (touched.email || visited)}
                />
                {errors.email && (touched.email || visited) && (
                  <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                    {errors.email}
                  </Text>
                )}

                {/* Password Input */}
                <TextInput
                  label="Password"
                  style={{
                    backgroundColor: "transparent", // Grayish background for text inputs
                    paddingLeft: scaleSize(0),
                    color: theme.colors.primaryTextColor, // Text color from the theme
                    height: scaleSize(50),
                    width: "100%",
                    fontSize: scaleSize(16),
                  }}
                  value={value?.password || ""}
                  onChangeText={(text) => handleInputChange("password", text)}
                  onBlur={() => handleBlur("password")}
                  secureTextEntry={isSecureTextEntry}
                  mode="outlined"
                  autoCapitalize="none"
                  error={!!errors.password && (touched.password || visited)}
                  right={
                    <TextInput.Icon
                      icon="eye"
                      size={scaleSize(20)} // Scaled icon size
                      onPress={() => setIsSecureTextEntry(!isSecureTextEntry)}
                    />
                  }
                />
                {errors.password && (touched.password || visited) && (
                  <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                    {errors.password}
                  </Text>
                )}

                {/* Confirm Password Input */}
                <TextInput
                  label="Confirm Password"
                  style={{
                    backgroundColor: "transparent", // Grayish background for text inputs
                    paddingLeft: scaleSize(0),
                    color: theme.colors.primaryTextColor, // Text color from the theme
                    height: scaleSize(50),
                    width: "100%",
                    fontSize: scaleSize(16),
                  }}
                  value={value?.confirmPassword || ""}
                  onChangeText={(text) =>
                    handleInputChange("confirmPassword", text)
                  }
                  onBlur={() => handleBlur("confirmPassword")}
                  secureTextEntry={isSecureTextEntry}
                  mode="outlined"
                  autoCapitalize="none"
                  error={
                    !!errors.confirmPassword &&
                    (touched.confirmPassword || visited)
                  }
                  right={
                    <TextInput.Icon
                      icon="eye"
                      size={scaleSize(20)} // Scaled icon size
                      onPress={() => setIsSecureTextEntry(!isSecureTextEntry)}
                    />
                  }
                />
                {errors.confirmPassword &&
                  (touched.confirmPassword || visited) && (
                    <Text style={{ color: "red", fontSize: scaleSize(12) }}>
                      {errors.confirmPassword}
                    </Text>
                  )}
              </View>
            </Card.Content>
          </Card>
        </TouchableOpacity>

        <View
          style={{
            marginTop: scaleSize(10),
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
            gap: scaleSize(20),
          }}
        >
          {/* Sign Up Button */}
          <TouchableOpacity
            onPress={onSignUpPress}
            disabled={!isFormValid}
            style={{
              backgroundColor: isFormValid ? theme.colors.primary : "gray",
              borderRadius: scaleSize(8),
              width: "60%",
              paddingVertical: scaleSize(12), // Added for proper button height
              justifyContent: "center",
              alignItems: "center",
              opacity: !isFormValid ? 0.6 : 1, // Visual indicator for disabled state
            }}
          >
            <Text
              style={{
                color: theme.colors.primaryTextColor,
                fontSize: scaleSize(18),
                fontWeight: "bold",
              }}
            >
              Sign Up
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Verification Code Modal */}
      <Modal
        visible={verificationModalVisible}
        transparent={true}
        animationType="slide"
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0, 0, 0, 0.9)",
          }}
        >
          <Card
            style={{
              width: "80%",
              padding: scaleSize(20),
              position: "relative",
            }}
          >
            {/* Close Button */}
            <TouchableOpacity
              onPress={() => {
                if (verificationModalVisible === true) {
                  setVerificationModalVisible(false);
                }
              }}
              style={{
                position: "absolute",
                top: 0,
                left: scaleSize(10),
                zIndex: 2,
              }}
            >
              <MaterialIcons
                name="close"
                size={scaleSize(24)}
                color={theme.colors.primaryTextColor}
              />
            </TouchableOpacity>

            {/* Modal Content */}
            <Text
              style={{
                fontSize: scaleSize(20),
                textAlign: "center",
                marginBottom: scaleSize(10),
                color: theme.colors.primaryTextColor,
              }}
            >
              Verify Your Email
            </Text>
            <TextInput
              label="Verification Code"
              value={verificationCode}
              onChangeText={(text) => setVerificationCode(text)}
              style={{
                marginBottom: scaleSize(20),
                height: scaleSize(50),
                fontSize: scaleSize(16),
              }}
            />
            {/* Verify Button */}
            <TouchableOpacity
              onPress={onVerifyPress}
              disabled={isVerifying}
              style={{
                backgroundColor: theme.colors.primary, // Assuming contained button
                borderRadius: scaleSize(8), // Standard border radius
                paddingVertical: scaleSize(12),
                paddingHorizontal: scaleSize(24),
                justifyContent: "center",
                alignItems: "center",
                opacity: isVerifying ? 0.6 : 1,
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(18), // Standard font size
                  fontWeight: "bold", // Default for contained button
                }}
              >
                Verify
              </Text>
            </TouchableOpacity>

            {/* Resend Code Button */}
            <TouchableOpacity
              onPress={onResendPress}
              style={{ marginTop: scaleSize(10) }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor, // Same as text button
                  fontSize: scaleSize(16), // Standard text size
                  textDecorationLine: "underline", // Typical for text buttons
                }}
              >
                Resend Code
              </Text>
            </TouchableOpacity>
          </Card>
        </View>
      </Modal>
    </>
  );
}
