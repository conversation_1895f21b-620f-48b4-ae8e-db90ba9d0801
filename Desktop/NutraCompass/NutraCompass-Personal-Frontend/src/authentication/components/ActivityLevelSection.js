import React, { useState, useEffect } from "react";
import {
  Text,
  View,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { TextInput, Card, Icon } from "react-native-paper";
import Slider from "@react-native-community/slider";
import * as Haptics from "expo-haptics";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const ActivityLevelSection = ({ value, setValue, onNext }) => {
  const { theme } = useThemeContext();
  const [sliderValue, setSliderValue] = useState(40);
  const [customKcal, setCustomKcal] = useState("");

  useEffect(() => {
    const levelToSliderValue = {
      None: 0,
      Sedentary: 20,
      "Lightly Active": 40,
      "Moderately Active": 60,
      "Very Active": 80,
    };

    if (
      value.activityLevel &&
      sliderValue !== levelToSliderValue[value.activityLevel]
    ) {
      setSliderValue(levelToSliderValue[value.activityLevel]);
    }

    const newCustomKcal = value.maintenanceCalories
      ? String(value.maintenanceCalories)
      : "";
    if (customKcal !== newCustomKcal && sliderValue === 100) {
      setCustomKcal(newCustomKcal);
    }
  }, [value]);

  const getActivityLevel = (sliderValue) => {
    if (sliderValue === 0) return "None";
    else if (sliderValue === 20) return "Sedentary";
    else if (sliderValue === 40) return "Lightly Active";
    else if (sliderValue === 60) return "Moderately Active";
    else if (sliderValue === 80) return "Very Active";
    return "";
  };

  const handleSliderChange = (sliderValue) => {
    setSliderValue(sliderValue);
    if (sliderValue !== 100) {
      setCustomKcal("");
      setValue((prevValue) => ({
        ...prevValue,
        activityLevel: getActivityLevel(sliderValue),
        maintenanceCalories: null,
      }));
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleCustomKcalChange = (text) => {
    setCustomKcal(text);
    if (sliderValue === 100) {
      setValue((prevValue) => ({
        ...prevValue,
        maintenanceCalories: parseFloat(text) || null,
        activityLevel: "",
      }));
    }
  };

  const handleNext = () => {
    Keyboard.dismiss();
    onNext();
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    onNext();
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <KeyboardAvoidingView
        style={{ flex: 1, width: "100%" }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : scaleSize(60)}
      >
        <View style={{ flex: 1, width: "100%" }}>
          <View
            style={{
              alignItems: "center",
              paddingTop: scaleSize(10),
              gap: scaleSize(5),
            }}
          >
            <Text
              style={{
                fontSize: scaleSize(28),
                fontWeight: "bold",
                color: "black",
                textAlign: "center",
              }}
            >
              Activity Level
            </Text>
            <Text
              style={{
                paddingHorizontal: scaleSize(10),
                fontSize: scaleSize(16),
                color: "black",
                textAlign: "center",
              }}
            >
              Based on your activity level, NutraCompass can estimate the amount
              of energy you burn each day.
            </Text>

            <Card
              style={{
                width: "100%",
                marginTop: scaleSize(10),
                borderRadius: scaleSize(14),
                borderWidth: scaleSize(1),
                borderColor: theme.colors.primary,
                backgroundColor: theme.colors.surface,
              }}
            >
              <Card.Content>
                <View
                  style={{
                    justifyContent: "center",
                    width: "100%",
                    gap: scaleSize(10),
                  }}
                >
                  <View style={{ alignSelf: "center" }}>
                    <Icon
                      source="run-fast"
                      color={theme.colors.primaryTextColor}
                      size={scaleSize(80)}
                    />
                  </View>

                  {sliderValue !== 100 && (
                    <Text
                      style={{
                        fontSize: scaleSize(18),
                        color: theme.colors.primaryTextColor,
                        textAlign: "center",
                        fontWeight: "500",
                      }}
                    >
                      {getActivityLevel(sliderValue)}
                    </Text>
                  )}

                  {sliderValue === 100 && (
                    <View
                      style={{
                        flexDirection: "row",
                        gap: scaleSize(10),
                        alignItems: "center",
                        justifyContent: "center",
                        alignSelf: "center",
                      }}
                    >
                      <Text
                        style={{
                          fontSize: scaleSize(18),
                          color: theme.colors.primaryTextColor,
                          textAlign: "center",
                          fontWeight: "500",
                        }}
                      >
                        Custom
                      </Text>
                      <TextInput
                        style={{
                          alignSelf: "center",
                          height: scaleSize(40),
                          borderColor: theme.colors.primary,
                          backgroundColor: "transparent",
                          borderWidth: scaleSize(1),
                          borderRadius: scaleSize(8),
                          textAlign: "center",
                          color: theme.colors.primaryTextColor,
                          width: scaleSize(100),
                          fontSize: scaleSize(16),
                        }}
                        underlineColor="transparent"
                        activeOutlineColor="red"
                        activeUnderlineColor={theme.colors.primary}
                        keyboardType="number-pad"
                        value={customKcal}
                        onChangeText={handleCustomKcalChange}
                        placeholder="2000"
                        placeholderTextColor={theme.colors.subTextColor}
                      />
                      <Text
                        style={{
                          fontSize: scaleSize(18),
                          color: theme.colors.primaryTextColor,
                          textAlign: "center",
                          fontWeight: "400",
                        }}
                      >
                        kcal
                      </Text>
                    </View>
                  )}
                  <Slider
                    style={{ width: "100%", height: scaleSize(40) }}
                    step={20}
                    minimumValue={0}
                    maximumValue={100}
                    minimumTrackTintColor={theme.colors.primary}
                    maximumTrackTintColor={theme.colors.subTextColor}
                    value={sliderValue}
                    onValueChange={handleSliderChange}
                  />
                  <Text
                    style={{
                      fontSize: scaleSize(14),
                      color: theme.colors.primaryTextColor,
                      textAlign: "center",
                      paddingHorizontal: scaleSize(10),
                    }}
                  >
                    {sliderValue === 0 && "No activity."}
                    {sliderValue === 20 &&
                      "Little or no exercise or daily activity."}
                    {sliderValue === 40 &&
                      "Basic daily living and/or light exercise."}
                    {sliderValue === 60 && "Moderate exercise 3-5 days/week."}
                    {sliderValue === 80 &&
                      "Intense activity throughout the day."}
                    {sliderValue === 100 && "Set your own fixed daily value."}
                  </Text>
                </View>
              </Card.Content>
            </Card>
          </View>
          <View
            style={{
              flex: 1,
              alignItems: "center",
              justifyContent: "flex-end",
              gap: scaleSize(20),
              paddingBottom: scaleSize(20),
            }}
          >
            {/* Next Button */}
            <TouchableOpacity
              onPress={handleNext}
              style={{
                backgroundColor: theme.colors.surface,
                borderRadius: scaleSize(8),
                width: "60%",
                paddingVertical: scaleSize(12),
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(18),
                  fontWeight: "bold",
                }}
              >
                Next
              </Text>
            </TouchableOpacity>

            {/* Skip Button */}
            <TouchableOpacity
              onPress={handleSkip}
              style={{
                borderRadius: scaleSize(8),
                width: "60%",
                paddingVertical: scaleSize(12),
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "transparent",
              }}
            >
              <Text
                style={{
                  color: "white",
                  fontSize: scaleSize(18),
                  fontWeight: "bold",
                }}
              >
                Skip
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
};

export default ActivityLevelSection;
