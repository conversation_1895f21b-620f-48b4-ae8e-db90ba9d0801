// LoginModal.js
import React from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Dimensions,
  Image,
  Platform,
} from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import { useThemeContext } from "../../context/ThemeContext.js";
import * as AppleAuthentication from "expo-apple-authentication";
import { scaleSize } from "../../utils/deviceUtils.js";

const screenHeight = Dimensions.get("window").height;

const LoginModal = ({
  isVisible,
  onClose,
  handleAppleSignIn,
  handleGoogleSignIn,
  handleEmailLogin,
}) => {
  const { theme, mode } = useThemeContext();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={{ flex: 1 }}
        activeOpacity={1}
        onPressOut={onClose}
      >
        <View
          style={{
            position: "absolute",
            bottom: 0,
            height: screenHeight * 0.4,
            width: "100%",
            backgroundColor: theme.colors.surface,
            borderTopLeftRadius: scaleSize(20),
            borderTopRightRadius: scaleSize(20),
            padding: scaleSize(20),
            alignItems: "center",
          }}
        >
          {/* Drag Indicator */}
          <View
            style={{
              height: scaleSize(4),
              width: scaleSize(40),
              backgroundColor: "gray",
              borderRadius: scaleSize(2),
              marginBottom: scaleSize(20),
              position: "absolute",
            }}
          />

          {/* Title and Subtitle */}
          <Text
            style={{
              paddingTop: scaleSize(12),
              fontSize: scaleSize(24),
              fontWeight: "bold",
              textAlign: "center",
              color: theme.colors.primaryTextColor,
            }}
          >
            Welcome back
          </Text>
          <Text
            style={{
              fontSize: scaleSize(16),
              color: "gray",
              textAlign: "center",
              marginTop: scaleSize(8),
              marginBottom: scaleSize(12),
              color: theme.colors.subTextColor,
            }}
          >
            Select method to log in
          </Text>

          <View
            style={{
              paddingTop: scaleSize(10),
              gap: scaleSize(10),
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {/* Apple Sign-In Button */}
            {Platform.OS === "ios" && (
              <AppleAuthentication.AppleAuthenticationButton
                buttonType={
                  AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN
                }
                buttonStyle={
                  mode === "dark"
                    ? AppleAuthentication.AppleAuthenticationButtonStyle.WHITE
                    : AppleAuthentication.AppleAuthenticationButtonStyle
                        .WHITE_OUTLINE
                }
                cornerRadius={scaleSize(10)}
                style={{
                  width: "90%",
                  height: scaleSize(44),
                  marginVertical: scaleSize(8),
                }}
                onPress={handleAppleSignIn}
              />
            )}

            {/* Google Sign-In Button */}
            <TouchableOpacity
              style={{
                flexDirection: "row",
                backgroundColor: theme.colors.surface,
                padding: scaleSize(12),
                borderRadius: scaleSize(10),
                width: "90%",
                alignItems: "center",
                justifyContent: "center",
                borderWidth: scaleSize(1),
                borderColor: theme.colors.primaryTextColor,
              }}
              onPress={handleGoogleSignIn}
            >
              <Image
                source={require("../../../assets/google.png")}
                style={{
                  width: scaleSize(20),
                  height: scaleSize(20),
                  marginRight: scaleSize(10),
                }}
              />
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(16),
                }}
              >
                Continue with Google
              </Text>
            </TouchableOpacity>

            {/* Email Sign-In Button */}
            <TouchableOpacity
              style={{
                flexDirection: "row",
                backgroundColor: theme.colors.surface,
                padding: scaleSize(12),
                borderRadius: scaleSize(10),
                width: "90%",
                alignItems: "center",
                justifyContent: "center",
                borderWidth: scaleSize(1),
                borderColor: theme.colors.primaryTextColor,
              }}
              onPress={handleEmailLogin}
            >
              <FontAwesome
                name="envelope"
                size={scaleSize(20)}
                color={theme.colors.primaryTextColor}
                style={{ marginRight: scaleSize(10) }}
              />
              <Text
                style={{
                  color: theme.colors.primaryTextColor,
                  fontSize: scaleSize(16),
                }}
              >
                Continue with Email
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

export default LoginModal;
