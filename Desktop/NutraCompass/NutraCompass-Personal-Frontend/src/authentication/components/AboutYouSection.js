import React, { useState, useEffect } from "react";
import {
  TouchableWithoutFeedback,
  Text,
  View,
  TouchableOpacity,
} from "react-native";
import { Card } from "react-native-paper";
import * as Haptics from "expo-haptics";
import Feather from "react-native-vector-icons/Feather";
import signupScreenStyles from "../../screens/styles/signupScreenStyles.js";
import { useThemeContext } from "../../context/ThemeContext.js";
import CustomSexPickerModal from "./CustomSexPickerModal.js";
import CustomDatePickerModal from "./CustomDatePickerModal.js";
import CustomHeightPickerModal from "./CustomHeightPickerModal.js";
import CustomWeightInputModal from "./CustomWeightInputModal.js";
import { scaleSize } from "../../utils/deviceUtils.js";

const SelectInput = ({ onPress, selectedValue, placeholder }) => {
  const { theme } = useThemeContext();
  const styles = signupScreenStyles();

  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <View style={styles.selectInput}>
        <View style={{ flexDirection: "row", gap: scaleSize(20) }}>
          <Feather
            name="arrow-right"
            color={
              selectedValue ? theme.colors.primary : "rgba(169, 169, 169, 0.7)"
            }
            size={scaleSize(18)}
          />
          <Text
            style={{
              fontSize: scaleSize(18),
              color: selectedValue
                ? theme.colors.primary
                : "rgba(169, 169, 169, 0.7)",
            }}
          >
            {selectedValue || placeholder}
          </Text>
        </View>
        <Feather
          name="arrow-right"
          color={theme.colors.primaryTextColor}
          size={scaleSize(18)}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default function AboutYouSection({ value, setValue, onNext }) {
  const styles = signupScreenStyles();
  const { theme } = useThemeContext();

  const [isSexModalVisible, setSexModalVisible] = useState(false);
  const [isBirthdayModalVisible, setBirthdayModalVisible] = useState(false);
  const [isHeightModalVisible, setHeightModalVisible] = useState(false);
  const [isWeightModalVisible, setWeightModalVisible] = useState(false);

  const formatHeight = (height) => {
    if (height?.unit === "in" && height?.inches) {
      const feet = Math.floor(height.inches / 12);
      const inches = Math.round(height.inches % 12);
      return `${feet}'${inches}"`;
    }
    if (height?.unit === "cm" && height?.centimeters) {
      return `${height.centimeters} cm`;
    }
    return "";
  };

  const [localState, setLocalState] = useState({
    sex: value.sex || "",
    birthday: value.birthday || null,
    height: formatHeight(value.height),
    weight: value.weight || "",
  });

  useEffect(() => {
    setLocalState({
      sex: value.sex || "",
      birthday: value.birthday || null,
      height: formatHeight(value.height),
      weight: value.weight || "",
    });
  }, [value]);

  const calculateAge = (birthday) => {
    const currentDate = new Date();
    const birthDate = new Date(birthday);
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    const monthDifference = currentDate.getMonth() - birthDate.getMonth();
    if (
      monthDifference < 0 ||
      (monthDifference === 0 && currentDate.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  const handleSelect = (field, inputValue) => {
    switch (field) {
      case "sex":
        setLocalState((prev) => ({ ...prev, sex: inputValue }));
        setValue((prev) => ({ ...prev, sex: inputValue }));
        break;
      case "birthday":
        const formattedDate = inputValue.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
        const age = calculateAge(inputValue);
        setLocalState((prev) => ({ ...prev, birthday: formattedDate }));
        setValue((prev) => ({ ...prev, birthday: formattedDate, age }));
        break;
      case "height":
        const formattedHeight = formatHeight(inputValue);
        setLocalState((prev) => ({ ...prev, height: formattedHeight }));
        setValue((prev) => ({ ...prev, height: inputValue }));
        break;
      case "weight":
        setLocalState((prev) => ({ ...prev, weight: inputValue }));
        setValue((prev) => ({ ...prev, weight: inputValue }));
        break;
      default:
        break;
    }
  };

  const handleNext = () => {
    if (
      localState.sex &&
      localState.birthday &&
      localState.height &&
      localState.weight
    ) {
      onNext();
    } else {
      console.log("Please fill out all profile details.");
    }
  };

  return (
    <View style={{ flex: 1, width: "100%" }}>
      <View
        style={{
          alignItems: "center",
          paddingTop: scaleSize(10),
          gap: scaleSize(5),
        }}
      >
        <Text
          style={{
            fontSize: scaleSize(28),
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
          }}
        >
          About You
        </Text>
        <Text
          style={{
            paddingHorizontal: scaleSize(10),
            fontSize: scaleSize(16),
            color: "black",
            textAlign: "center",
          }}
        >
          Enter your details so NutraCompass can customize your targets.
        </Text>
        <Card
          style={{
            width: "100%",
            marginTop: scaleSize(10),
            borderWidth: scaleSize(1),
            borderColor:
              localState.sex &&
              localState.birthday &&
              localState.height &&
              localState.weight
                ? theme.colors.primary
                : theme.colors.cardBorderColor,
          }}
        >
          <Card.Content>
            <View
              style={{
                justifyContent: "center",
                width: "100%",
                gap: scaleSize(10),
              }}
            >
              <SelectInput
                onPress={() => setSexModalVisible(true)}
                selectedValue={localState.sex}
                placeholder="Your sex"
              />
              <SelectInput
                onPress={() => setBirthdayModalVisible(true)}
                selectedValue={localState.birthday}
                placeholder="Your birthday"
              />
              <SelectInput
                onPress={() => setHeightModalVisible(true)}
                selectedValue={localState.height}
                placeholder="Your height"
              />
              <SelectInput
                onPress={() => setWeightModalVisible(true)}
                selectedValue={localState.weight}
                placeholder="Your weight"
              />
            </View>
          </Card.Content>
        </Card>
      </View>
      <CustomSexPickerModal
        title="Select Sex"
        visible={isSexModalVisible}
        onClose={() => setSexModalVisible(false)}
        onSelect={(sex) => handleSelect("sex", sex)}
      />
      <CustomDatePickerModal
        title="Select Birthday"
        selectedDate={localState.birthday}
        onSelect={(date) => handleSelect("birthday", date)}
        visible={isBirthdayModalVisible}
        onClose={() => setBirthdayModalVisible(false)}
      />
      <CustomHeightPickerModal
        title="Select Height"
        selectedHeight={value.height}
        onSelect={(height) => handleSelect("height", height)}
        visible={isHeightModalVisible}
        onClose={() => setHeightModalVisible(false)}
      />
      <CustomWeightInputModal
        title="Enter Weight"
        selectedWeight={localState.weight}
        onSelect={(weight) => handleSelect("weight", weight)}
        visible={isWeightModalVisible}
        onClose={() => setWeightModalVisible(false)}
      />
      <View
        style={{
          flex: 1,
          alignItems: "center",
          justifyContent: "flex-end",
          paddingBottom: scaleSize(20),
          gap: scaleSize(20),
        }}
      >
        <Text
          style={{
            fontSize: scaleSize(14),
            color: "white",
            textAlign: "center",
          }}
        >
          We use this information to calculate and provide you with daily
          personalized recommendations.
        </Text>
        <TouchableOpacity
          onPress={handleNext}
          disabled={
            !(
              localState.sex &&
              localState.birthday &&
              localState.height &&
              localState.weight
            )
          }
          style={{
            backgroundColor:
              localState.sex &&
              localState.birthday &&
              localState.height &&
              localState.weight
                ? "white"
                : "gray",
            borderRadius: scaleSize(8),
            width: "60%",
            justifyContent: "center",
            alignItems: "center",
            paddingVertical: scaleSize(12),
            opacity: !(
              localState.sex &&
              localState.birthday &&
              localState.height &&
              localState.weight
            )
              ? 0.7
              : 1,
          }}
        >
          <Text
            style={{
              color: "black",
              fontSize: scaleSize(18),
              fontWeight: "bold",
            }}
          >
            Next
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
