import React, { useState, useEffect } from "react";
import { Text, View, TouchableOpacity } from "react-native";
import { TextInput, Card, RadioButton } from "react-native-paper";
import Slider from "@react-native-community/slider";
import * as Haptics from "expo-haptics";
import signupScreenStyles from "../../screens/styles/signupScreenStyles.js";
import { useThemeContext } from "../../context/ThemeContext.js";
import { scaleSize } from "../../utils/deviceUtils.js";
export default function SetAGoalSection({ value, setValue, onNext }) {
  const styles = signupScreenStyles();
  const { theme } = useThemeContext();

  const [goal, setGoal] = useState(
    value.customEnergyTarget ? "Custom Energy Target" : "Weight Goal"
  );
  const [customKcalTarget, setCustomKcalTarget] = useState(
    value.customEnergyTarget ? String(value.customEnergyTarget) : "2000"
  );
  const [weightTrendGoalSliderValue, setWeightTrendGoalSliderValue] = useState(
    value.weightTrendGoal ?? 0
  );

  // Sync local changes with the `setValue` prop
  const handleGoalChange = (newGoal) => {
    setGoal(newGoal);

    if (newGoal === "Weight Goal") {
      setValue((prev) => ({
        ...prev,
        weightTrendGoal: weightTrendGoalSliderValue,
        customEnergyTarget: null,
      }));
    } else if (newGoal === "Custom Energy Target") {
      setValue((prev) => ({
        ...prev,
        customEnergyTarget: Number(customKcalTarget),
        weightTrendGoal: null,
      }));
    }
  };

  const handleWeightTrendChange = (sliderValue) => {
    setWeightTrendGoalSliderValue(sliderValue);

    if (goal === "Weight Goal") {
      setValue((prev) => ({
        ...prev,
        weightTrendGoal: sliderValue,
      }));
    }
  };

  const handleCustomKcalChange = (text) => {
    setCustomKcalTarget(text);

    if (goal === "Custom Energy Target") {
      setValue((prev) => ({
        ...prev,
        customEnergyTarget: Number(text),
      }));
    }
  };

  const handleNext = () => {
    onNext();
  };

  const handleSkip = () => {
    onNext();
  };

  return (
    <View style={{ flex: 1, width: "100%" }}>
      <View
        style={{
          alignItems: "center",
          paddingTop: scaleSize(10),
          gap: scaleSize(5),
        }}
      >
        <Text
          style={{
            fontSize: scaleSize(28),
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
          }}
        >
          Set A Goal
        </Text>
        <Text
          style={{
            fontSize: scaleSize(18),
            color: "black",
            textAlign: "center",
          }}
        >
          NutraCompass can dynamically calculate your daily calorie target. Do
          you want to lose, maintain, or gain weight? Or set a custom daily
          calorie target.
        </Text>

        <Card
          style={{
            width: "100%",
            marginTop: scaleSize(10),
            borderRadius: scaleSize(14),
            borderWidth: scaleSize(1),
            borderColor: theme.colors.primary,
          }}
        >
          <Card.Content>
            <RadioButton.Group
              onValueChange={(newValue) => {
                handleGoalChange(newValue);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              value={goal}
            >
              <View style={{ gap: scaleSize(20) }}>
                <View style={{ flexDirection: "row", gap: scaleSize(10) }}>
                  <RadioButton.Android
                    value="Weight Goal"
                    color={theme.colors.primary}
                    uncheckedColor={theme.colors.primary}
                  />
                  <View style={{ flex: 1, gap: scaleSize(10) }}>
                    <Text
                      style={{
                        fontSize: scaleSize(18),
                        color:
                          goal === "Weight Goal"
                            ? theme.colors.primaryTextColor
                            : theme.colors.subTextColor,
                      }}
                    >
                      Weight Goal
                    </Text>
                    <Text
                      style={{
                        fontSize: scaleSize(16),
                        color:
                          goal === "Weight Goal"
                            ? theme.colors.primaryTextColor
                            : theme.colors.subTextColor,
                      }}
                    >
                      {(() => {
                        switch (true) {
                          case weightTrendGoalSliderValue < 0:
                            return "Lose";
                          case weightTrendGoalSliderValue === 0:
                            return "Maintain";
                          case weightTrendGoalSliderValue > 0:
                            return "Gain";
                          default:
                            return "";
                        }
                      })()}{" "}
                      ({Math.abs(weightTrendGoalSliderValue)} lbs/week)
                    </Text>

                    <Slider
                      style={{ width: "100%" }}
                      step={0.25}
                      minimumValue={-2}
                      maximumValue={2}
                      minimumTrackTintColor={
                        goal === "Weight Goal"
                          ? theme.colors.primary
                          : theme.colors.subTextColor
                      }
                      maximumTrackTintColor="rgba(169, 169, 169, 0.7)"
                      thumbTintColor={
                        goal === "Weight Goal"
                          ? theme.colors.primary
                          : theme.colors.subTextColor
                      }
                      value={weightTrendGoalSliderValue}
                      onValueChange={(sliderValue) => {
                        handleWeightTrendChange(sliderValue);
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      }}
                      disabled={!(goal === "Weight Goal")}
                    />
                  </View>
                </View>
                <View style={{ flexDirection: "row" }}>
                  <RadioButton.Android
                    value="Custom Energy Target"
                    color={theme.colors.primary}
                    uncheckedColor={theme.colors.primary}
                  />
                  <View style={{ flex: 2 / 3, gap: scaleSize(10) }}>
                    <Text
                      style={{
                        fontSize: scaleSize(18),
                        color:
                          goal === "Custom Energy Target"
                            ? theme.colors.primaryTextColor
                            : theme.colors.subTextColor,
                        textAlign: "center",
                      }}
                    >
                      Custom Calorie Target
                    </Text>
                    <View
                      style={{
                        flexDirection: "row",
                        gap: scaleSize(10),
                        alignItems: "center",
                        justifyContent: "center",
                        alignSelf: "center",
                      }}
                    >
                      <TextInput
                        style={{
                          width: "70%",
                          alignSelf: "center",
                          height: scaleSize(40),
                          fontSize: scaleSize(16),
                          color: theme.colors.primaryTextColor,
                          borderColor:
                            goal === "Custom Energy Target"
                              ? theme.colors.primaryTextColor
                              : theme.colors.subTextColor,
                          backgroundColor: "transparent",
                          borderWidth: scaleSize(1),
                          borderRadius: scaleSize(6),
                          textAlign: "center",
                        }}
                        underlineColor="transparent"
                        activeUnderlineColor={theme.colors.primary}
                        keyboardType="number-pad"
                        value={customKcalTarget}
                        onChangeText={(text) => {
                          handleCustomKcalChange(text);
                        }}
                        disabled={!(goal === "Custom Energy Target")}
                      />
                      <Text
                        style={{
                          fontSize: scaleSize(18),
                          color:
                            goal === "Custom Energy Target"
                              ? theme.colors.primaryTextColor
                              : theme.colors.subTextColor,
                          textAlign: "center",
                          fontWeight: "400",
                        }}
                      >
                        kcal
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </RadioButton.Group>
          </Card.Content>
        </Card>
      </View>
      <View
        style={{
          flex: 1,
          alignItems: "center",
          justifyContent: "flex-end",
          gap: scaleSize(20),
          paddingBottom: scaleSize(20),
        }}
      >
        {/* Next Button - TouchableOpacity */}
        <TouchableOpacity
          onPress={handleNext}
          style={{
            backgroundColor: theme.colors.surface,
            borderRadius: scaleSize(8),
            width: "60%",
            paddingVertical: scaleSize(12),
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text
            style={{
              color: theme.colors.primaryTextColor,
              fontSize: scaleSize(18),
              fontWeight: "bold",
            }}
          >
            Next
          </Text>
        </TouchableOpacity>

        {/* Skip Button - TouchableOpacity */}
        <TouchableOpacity
          onPress={handleSkip}
          style={{
            borderRadius: scaleSize(8),
            width: "60%",
            paddingVertical: scaleSize(12),
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text
            style={{
              color: "white",
              fontSize: scaleSize(18),
              fontWeight: "bold",
            }}
          >
            Skip
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
