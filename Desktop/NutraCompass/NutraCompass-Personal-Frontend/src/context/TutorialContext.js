// contexts/TutorialContext.js
import { createContext, useContext, useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";

const TutorialContext = createContext();

export const TutorialProvider = ({ children }) => {
  const [isCompleted, setIsCompleted] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const value = await AsyncStorage.getItem("@tutorial_completed");
        setIsCompleted(value === "true");
      } catch (error) {
        console.error("Tutorial check failed:", error);
      } finally {
        setIsChecking(false);
      }
    };

    checkStatus();
  }, []);

  const markCompleted = async () => {
    try {
      await AsyncStorage.setItem("@tutorial_completed", "true");
      setIsCompleted(true);
    } catch (error) {
      console.error("Failed to mark tutorial:", error);
    }
  };

  return (
    <TutorialContext.Provider
      value={{ isCompleted, isChecking, markCompleted }}
    >
      {children}
    </TutorialContext.Provider>
  );
};

export const useTutorial = () => useContext(TutorialContext);
