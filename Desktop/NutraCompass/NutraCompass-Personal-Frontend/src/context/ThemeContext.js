import React, { createContext, useContext, useState, useEffect } from "react";
import { StatusBar, Platform } from "react-native";
import {
  Default,
  BlossomPink,
  LavenderDream,
  SereneAqua,
  SunsetGlow,
  UrbanMidnight,
  VerdantFields,
  CrimsonTide,
  NeonBlush,
  CitrusSunset,
  TropicalLagoon,
  SilkenGray,
  RoyalViolet,
} from "../../themes";
import { useAuth } from "../authentication/context/AuthContext.js";
import { useUserSettings } from "../features/Settings/context/UserSettingsContext.js";

const themes = [
  Default,
  BlossomPink,
  LavenderDream,
  SereneAqua,
  SunsetGlow,
  UrbanMidnight,
  VerdantFields,
  CrimsonTide,
  NeonBlush,
  CitrusSunset,
  TropicalLagoon,
  SilkenGray,
  RoyalViolet,
];

// Create a theme context
export const ThemeContext = createContext();

export function useThemeContext() {
  return useContext(ThemeContext);
}

export function ThemeProvider({ children }) {
  const { user } = useAuth();
  const { getAppAppearance, setAppAppearance } = useUserSettings();
  const { isDark, theme: themeName } = getAppAppearance();
  const [darkMode, setDarkMode] = useState(isDark || true); // Set default dark mode
  const mode = darkMode ? "dark" : "light";
  const [selectedTheme, setSelectedTheme] = useState(Default); // Use Theme1 as the default
  const [theme, setTheme] = useState(
    mode === "dark"
      ? { name: selectedTheme.name, ...selectedTheme.dark }
      : { name: selectedTheme.name, ...selectedTheme.light }
  );

  useEffect(() => {
    if (user) {
      const foundTheme = themes.find((theme) => theme.name === themeName);
      if (foundTheme) {
        setDarkMode(isDark);
        setSelectedTheme(foundTheme);
      }
    } else {
      setSelectedTheme(Default); // Set default theme when user is undefined
    }
  }, [user, themeName]);

  useEffect(() => {
    if (selectedTheme) {
      const newTheme =
        mode === "dark"
          ? { name: selectedTheme.name, ...selectedTheme.dark }
          : { name: selectedTheme.name, ...selectedTheme.light };

      setTheme(newTheme);

      // Update StatusBar for both platforms
      StatusBar.setBarStyle(darkMode ? "light-content" : "dark-content");

      // Android-specific StatusBar background color with fallbacks
      if (Platform.OS === "android") {
        // First try: theme-defined statusBar color
        // Second try: surfaceVariant color
        // Third try: primary background color
        // Final fallback: black for dark mode, white for light mode
        const statusBarColor =
          newTheme.colors.screenBackground ||
          (darkMode ? "#000000" : "#FFFFFF");

        StatusBar.setBackgroundColor(statusBarColor);
        StatusBar.setTranslucent(false);
      }
    }
  }, [darkMode, selectedTheme]);

  // Function to toggle theme
  const toggleTheme = (selectedTheme) => {
    setSelectedTheme(selectedTheme);
    // Update theme in Firestore
    setAppAppearance({ isDark: darkMode, theme: selectedTheme.name });
  };

  // Function to toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode((prevDarkMode) => !prevDarkMode);
    // Update theme in Firestore
    setAppAppearance({ isDark: !darkMode, theme: selectedTheme.name });
  };

  return (
    <ThemeContext.Provider
      value={{
        theme,
        toggleTheme,
        toggleDarkMode,
        darkMode,
        mode,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}
