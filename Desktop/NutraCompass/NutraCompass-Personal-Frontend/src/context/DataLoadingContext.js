// contexts/DataLoadingContext.js
import React, { createContext, useContext, useState } from "react";

const MAX_RETRIES = 2; // Defined here but not exported
const DataLoadingContext = createContext();

export const DataLoadingProvider = ({ children }) => {
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [loadingStates, setLoadingStates] = useState({
    userSettings: false,
    mealSections: false,
    nutritionProgram: false,
    inactivePrograms: false,
    foodEntries: false,
    foodHistory: false,
    customMeals: false,
  });

  const retryableLoad = async (loader, key) => {
    console.log(`[DataLoading] Starting retryable load for ${key}`);

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        console.log(`[DataLoading] ${key} attempt ${attempt}/${MAX_RETRIES}`);
        setLoadingStates((prev) => ({ ...prev, [key]: true }));

        const result = await loader();

        console.log(`[DataLoading] ${key} succeeded on attempt ${attempt}`);
        setLoadingStates((prev) => ({ ...prev, [key]: false }));
        return result;
      } catch (error) {
        console.error(`[DataLoading] ${key} failed attempt ${attempt}:`, error);

        if (attempt === MAX_RETRIES) {
          console.error(
            `[DataLoading] ${key} failed all ${MAX_RETRIES} attempts`
          );
          setLoadingStates((prev) => ({ ...prev, [key]: false }));
          throw error;
        }

        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    }
  };

  return (
    <DataLoadingContext.Provider
      value={{
        initialLoadComplete,
        setInitialLoadComplete,
        loadingStates,
        setLoadingStates,
        retryableLoad,
      }}
    >
      {children}
    </DataLoadingContext.Provider>
  );
};

export const useDataLoading = () => {
  const context = useContext(DataLoadingContext);
  if (!context) {
    throw new Error("useDataLoading must be used within DataLoadingProvider");
  }
  return context;
};
