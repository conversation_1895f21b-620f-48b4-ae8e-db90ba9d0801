// contexts/ModalContext.js
import {
  useContext,
  createContext,
  useReducer,
  useCallback,
  useRef,
  useEffect,
} from "react";
import ActionModal from "../navigation/components/ActionModal.js";
import MealSectionCustomizationModal from "../features/FoodDiary/components/MealSectionCustomizationModal.js";
import DailyNutritionGoalsCustomizationModal from "../features/FoodDiary/components/DailyNutritionGoalsCustomizationModal.js";
import DailyNutritionGoalsCalculationModal from "../features/FoodDiary/components/DailyNutritionGoalsCalculationModal.js";
import CreateCustomMealModal from "../features/FoodMenu/components/CreateCustomMealModal.js";
import FoodEntryModal from "../features/FoodDiary/components/FoodEntryModal.js";
import BarcodeScanner from "../features/FoodDiary/components/BarcodeScanner.js";
import DigitalFoodScanner from "../features/FoodDiary/components/DigitalFoodScanner.js";
import ScanResultsHandler from "../features/FoodDiary/components/ScanResultsHandler.js";
import FoodNutrientModal from "../features/FoodDiary/components/FoodNutrientModal.js";
import WaterLogEntryModal from "../features/FoodDiary/components/WaterLogEntryModal.js";
import QuickAddModal from "../features/FoodDiary/components/MealSectionMenuOptions/QuickAddModal.js";
import ModalManager from "../components/ModalManager.js";

/**
 * @typedef {Object} ModalType
 * @property {string} FOOD_ENTRY - Food entry modal
 * @property {string} FOOD_NUTRIENT - Food nutrient details modal
 */

/**
 * Modal type definitions
 * @type {ModalType}
 */
export const MODAL_TYPES = {
  ACTION: "ACTION",
  MEAL_SECTION_CUSTOMIZATION: "MEAL_SECTION_CUSTOMIZATION",
  NUTRITION_GOALS_CUSTOMIZATION: "NUTRITION_GOALS_CUSTOMIZATION",
  NUTRITION_GOALS_CALCULATION: "NUTRITION_GOALS_CALCULATION",
  CREATE_CUSTOM_MEAL: "CREATE_CUSTOM_MEAL",
  FOOD_ENTRY: "FOOD_ENTRY",
  BARCODE_SCANNER: "BARCODE_SCANNER",
  DIGITAL_FOOD_SCANNER: "DIGITAL_FOOD_SCANNER",
  SCAN_RESULTS: "SCAN_RESULTS",
  FOOD_NUTRIENT: "FOOD_NUTRIENT",
  WATER_LOG_ENTRY: "WATER_LOG_ENTRY",
  QUICK_ADD: "QUICK_ADD",
};

/**
 * @typedef {Object} ModalConfig
 * @property {React.Component} component - Modal component
 * @property {Object} animationConfig - Animation settings
 * @property {number} backdropOpacity - Backdrop opacity (0-1)
 * @property {Object} style - Base modal style
 */

/**
 * Modal configuration registry
 * @type {Object.<string, ModalConfig>}
 */
export const modalConfig = {
  [MODAL_TYPES.ACTION]: {
    component: ActionModal,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "50%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.MEAL_SECTION_CUSTOMIZATION]: {
    component: MealSectionCustomizationModal,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.NUTRITION_GOALS_CUSTOMIZATION]: {
    component: DailyNutritionGoalsCustomizationModal,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.NUTRITION_GOALS_CALCULATION]: {
    component: DailyNutritionGoalsCalculationModal,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.CREATE_CUSTOM_MEAL]: {
    component: CreateCustomMealModal,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.FOOD_ENTRY]: {
    component: FoodEntryModal,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.BARCODE_SCANNER]: {
    component: BarcodeScanner,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.DIGITAL_FOOD_SCANNER]: {
    component: DigitalFoodScanner,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.SCAN_RESULTS]: {
    component: ScanResultsHandler,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.FOOD_NUTRIENT]: {
    component: FoodNutrientModal,
    animationConfig: {
      enter: {
        animation: "slideInRight",
        duration: 300,
      },
      exit: {
        animation: "slideOutRight",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
    },
    contentStyle: {
      height: "100%", // Custom height for this modal type
    },
  },
  [MODAL_TYPES.WATER_LOG_ENTRY]: {
    component: WaterLogEntryModal,
    animationConfig: {
      enter: {
        animation: "slideInUp", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutDown",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
      justifyContent: "center",
      alignItems: "center",
    },
    contentStyle: {
      width: "90%",
      borderRadius: 20,
      padding: 20,
    },
  },
  [MODAL_TYPES.QUICK_ADD]: {
    component: QuickAddModal,
    animationConfig: {
      enter: {
        animation: "slideInDown", // Must use react-native-modal animation names
        duration: 300,
      },
      exit: {
        animation: "slideOutUp",
        duration: 250,
      },
    },
    backdropOpacity: 0.5,
    style: {
      margin: 0,
      justifyContent: "flex-start",
      alignItems: "center",
    },
    contentStyle: {
      width: "100%",
      borderBottomLeftRadius: 20,
      borderBottomRightRadius: 20,
      padding: 20,
    },
  },
};

// State management
const initialState = { modalStack: [] };

function modalReducer(state, action) {
  let newState;
  switch (action.type) {
    case "OPEN_MODAL":
      if (!modalConfig[action.payload.modalType]) {
        console.error("Invalid modal type:", action.payload.modalType);
        return state;
      }

      newState = {
        ...state,
        modalStack: [
          ...state.modalStack,
          {
            id: `${action.payload.modalType}-${Date.now()}`,
            type: action.payload.modalType,
            props: action.payload.modalProps,
            zIndex: 1000 + state.modalStack.length,
          },
        ],
      };
      break;

    case "CLOSE_MODAL":
      newState = {
        ...state,
        modalStack: state.modalStack.slice(0, -1),
      };
      break;

    case "CLOSE_ALL_MODALS":
      newState = initialState;
      break;

    case "REPLACE_MODAL":
      if (state.modalStack.length === 0) {
        // If stack is empty, just open the modal normally
        newState = {
          ...state,
          modalStack: [
            {
              id: action.payload.id,
              type: action.payload.modalType,
              props: action.payload.modalProps,
              zIndex: 1000,
            },
          ],
        };
      } else {
        // Calculate new zIndex: maintain current top modal's zIndex
        const topModal = state.modalStack[state.modalStack.length - 1];
        const newZIndex = topModal.zIndex;

        // Replace the top modal while maintaining others
        newState = {
          ...state,
          modalStack: [
            ...state.modalStack.slice(0, -1),
            {
              id: action.payload.id,
              type: action.payload.modalType,
              props: action.payload.modalProps,
              zIndex: newZIndex, // Maintain same zIndex
            },
          ],
        };
      }
      break;

    default:
      newState = state;
  }

  console.log(`[Reducer] Action: ${action.type}`, {
    prevStack: state.modalStack.map((m) => m.type),
    newStack: newState.modalStack.map((m) => m.type),
  });
  return newState;
}

// Stable callback hook (only for functions)
const useStableCallback = (callback) => {
  const ref = useRef();
  ref.current = callback;
  return useCallback((...args) => ref.current?.(...args), []);
};

const ModalContext = createContext();

export const ModalProvider = ({ children }) => {
  const [state, dispatch] = useReducer(modalReducer, initialState);

  const openModal = useCallback((modalType, modalProps = {}) => {
    console.groupCollapsed(`[ModalProvider] Opening ${modalType}`);
    console.log("Props:", modalProps);

    if (!MODAL_TYPES[modalType]) {
      console.error(`Unknown modal type: ${modalType}`);
      console.groupEnd();
      return;
    }

    // Create a stable ID for the modal
    const id = `${modalType}-${Date.now()}`;

    // Stabilize callback props using function wrappers
    const stabilizedProps = Object.entries(modalProps).reduce(
      (acc, [key, value]) => {
        if (typeof value === "function") {
          // Create a stable callback wrapper
          let currentValue = value;
          const stableCallback = (...args) => currentValue(...args);

          // Update function to allow callback changes
          stableCallback.update = (newFn) => {
            currentValue = newFn;
          };

          acc[key] = stableCallback;
        } else {
          acc[key] = value;
        }
        return acc;
      },
      {}
    );

    dispatch({
      type: "OPEN_MODAL",
      payload: {
        modalType,
        id,
        modalProps: stabilizedProps,
      },
    });

    console.groupEnd();
    return id;
  }, []);

  const closeModal = useCallback(() => {
    console.log("[ModalProvider] Closing top modal");
    dispatch({ type: "CLOSE_MODAL" });
  }, []);

  const closeAllModals = useCallback(() => {
    console.log("[ModalProvider] Closing all modals");
    dispatch({ type: "CLOSE_ALL_MODALS" });
  }, []);

  const getCurrentModal = useCallback(() => {
    return state.modalStack.length > 0
      ? state.modalStack[state.modalStack.length - 1]
      : null;
  }, [state.modalStack]);

  const replaceModal = useCallback((modalType, modalProps = {}) => {
    console.groupCollapsed(
      `[ModalProvider] Replacing top modal with ${modalType}`
    );
    console.log("Props:", modalProps);

    if (!MODAL_TYPES[modalType]) {
      console.error(`Unknown modal type: ${modalType}`);
      console.groupEnd();
      return;
    }

    // Create stable ID
    const id = `${modalType}-${Date.now()}`;

    // Stabilize props (same as openModal)
    const stabilizedProps = Object.entries(modalProps).reduce(
      (acc, [key, value]) => {
        if (typeof value === "function") {
          let currentValue = value;
          const stableCallback = (...args) => currentValue(...args);
          stableCallback.update = (newFn) => {
            currentValue = newFn;
          };
          acc[key] = stableCallback;
        } else {
          acc[key] = value;
        }
        return acc;
      },
      {}
    );

    dispatch({
      type: "REPLACE_MODAL",
      payload: {
        modalType,
        id,
        modalProps: stabilizedProps,
      },
    });

    console.groupEnd();
    return id;
  }, []);

  return (
    <ModalContext.Provider
      value={{
        modalStack: state.modalStack,
        openModal,
        closeModal,
        closeAllModals,
        replaceModal,
        getCurrentModal,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error("useModal must be used within a ModalProvider");
  }
  return context;
};
