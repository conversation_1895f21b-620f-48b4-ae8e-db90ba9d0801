import { Platform, StyleSheet } from "react-native";

const shadowStyle = (elevation = 4) => {
  // iOS uses shadow properties
  if (Platform.OS === "ios") {
    return {
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 0.5 * elevation },
      shadowOpacity: 0.3,
      shadowRadius: 0.8 * elevation,
    };
  }

  // Android uses elevation + background color
  return {
    elevation,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0.5 * elevation },
    shadowOpacity: 0.3,
    shadowRadius: 0.8 * elevation,
  };
};

export default shadowStyle;
