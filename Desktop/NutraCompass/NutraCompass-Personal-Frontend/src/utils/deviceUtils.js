import { Dimensions } from "react-native";

// Base dimensions for reference
const BASE_WIDTH = 428; // iPhone 15 Pro Max width
const BASE_HEIGHT = 926; // iPhone 15 Pro Max height
const SIZE_SCALING_FACTOR = 0.85;

// Device detection
export const isTablet = () => {
  const { width, height } = Dimensions.get("window");
  const aspectRatio = height / width;

  // Tablet detection logic:
  // 1. Width >= 768 (standard iPad width)
  // 2. Aspect ratio < 1.6 (typical phone ratios are taller)
  return width >= 768 || aspectRatio < 1.6;
};

// Responsive scaling function
export const scaleSize = (size) => {
  const { width } = Dimensions.get("window");
  const scaleFactor = width / BASE_WIDTH;

  // Handle edge cases
  if (scaleFactor <= 0.5) return Math.max(size * 0.5, 12); // Minimum size
  if (scaleFactor > 4) return size * 1.8; // Maximum size

  if (scaleFactor <= 1) {
    return Math.round(size * scaleFactor);
  } else {
    // Logarithmic scaling with factor
    return Math.round(size * (1 + SIZE_SCALING_FACTOR * Math.log(scaleFactor)));
  }
};

export const scalePercentage = (percentage) => {
  const { width } = Dimensions.get("window");

  // Convert percentage string to number
  const percentageValue = parseFloat(percentage);
  if (isNaN(percentageValue)) return percentage;

  // Calculate scaling factor based on screen width
  const scaleFactor = width / BASE_WIDTH;

  // Define scaling thresholds
  const SCALE_START = 1.0; // Start scaling at base width
  const MAX_SCALE = 1.45; // Maximum scaling factor (55% → 80%)

  // Scale percentage for larger screens
  if (scaleFactor > SCALE_START) {
    // Logarithmic scaling that accelerates as screen size increases
    const scaledValue = Math.min(
      100, // Never exceed 100%
      percentageValue * (1 + 0.5 * Math.log(scaleFactor))
    );

    return `${scaledValue.toFixed(2)}%`;
  }

  // Return original value for smaller screens
  return `${percentageValue}%`;
};

// Get screen dimensions
export const screenDimensions = () => Dimensions.get("window");
