// utils/nutrientUtils.js
export const NUTRIENT_KEYS = {
  CORE: [
    "ENERC_KCAL",
    "CHOCDF",
    "PROCNT",
    "FAT",
    "FASAT",
    "FATRN",
    "FAPU",
    "FAMS",
    "CHOLE",
    "FIBTG",
    "SUGAR",
  ],
  VITAMINS: [
    "VITA_RAE",
    "VITC",
    "VITD",
    "TOCPHA",
    "VITK1",
    "THIA",
    "RIBF",
    "NIA",
    "VITB6A",
    "FOLDFE",
    "VITB12",
  ],
  MINERALS: ["CA", "FE", "MG", "P", "K", "NA", "ZN"],
};

export const NUTRIENT_LABELS = {
  ENERC_KCAL: "Calories",
  CHOCDF: "Total Carbohydrate",
  PROCNT: "Protein",
  FAT: "Total Fat",
  FASAT: "Saturated Fat",
  FATRN: "Trans Fat",
  FAPU: "Polyunsaturated Fat",
  FAMS: "Monounsaturated Fat",
  CHOLE: "Cholesterol",
  FIBTG: "Dietary Fiber",
  SUGAR: "Total Sugar",
  VITA_RAE: "Vitamin A",
  VITC: "Vitamin C",
  VITD: "Vitamin D",
  TOCPHA: "Vitamin E",
  VITK1: "Vitamin K1",
  THIA: "Thiamin (Vitamin B1)",
  RIBF: "Riboflavin (Vitamin B2)",
  NIA: "Niacin (Vitamin B3)",
  VITB6A: "Vitamin B6",
  FOLDFE: "Folate (Vitamin B9)",
  VITB12: "Vitamin B12",
  CA: "Calcium",
  FE: "Iron",
  MG: "Magnesium",
  P: "Phosphorus",
  K: "Potassium",
  NA: "Sodium",
  ZN: "Zinc",
};

export const NUTRIENT_UNITS = {
  ENERC_KCAL: "kcal",
  CHOCDF: "g",
  PROCNT: "g",
  FAT: "g",
  FASAT: "g",
  FATRN: "g",
  FAPU: "g",
  FAMS: "g",
  CHOLE: "mg",
  FIBTG: "g",
  SUGAR: "g",
  VITA_RAE: "mcg",
  VITC: "mg",
  VITD: "mcg",
  TOCPHA: "mg",
  VITK1: "mcg",
  THIA: "mg",
  RIBF: "mg",
  NIA: "mg",
  VITB6A: "mg",
  FOLDFE: "mcg",
  VITB12: "mcg",
  CA: "mg",
  FE: "mg",
  MG: "mg",
  P: "mg",
  K: "mg",
  NA: "mg",
  ZN: "mg",
};

// Generate full nutrient structure with default values
export const generateNutrientStructure = (userValues = {}) => {
  const structure = {
    core: {},
    vitamins: {},
    minerals: {},
  };

  // Helper to create nutrient object
  const createNutrient = (key, value = 0) => ({
    label: NUTRIENT_LABELS[key] || key,
    quantity: parseFloat(value) || 0,
    unit: NUTRIENT_UNITS[key] || "g",
    totalDaily: {
      quantity: null,
      unit: "%",
    },
  });

  // Process core nutrients
  NUTRIENT_KEYS.CORE.forEach((key) => {
    structure.core[key] = createNutrient(key, userValues[key]);
  });

  // Process vitamins
  NUTRIENT_KEYS.VITAMINS.forEach((key) => {
    structure.vitamins[key] = createNutrient(key);
  });

  // Process minerals
  NUTRIENT_KEYS.MINERALS.forEach((key) => {
    structure.minerals[key] = createNutrient(key);
  });

  return structure;
};

// Apply user-provided values to nutrient structure
export const applyUserNutrientValues = (nutrients, userValues) => {
  const updated = { ...nutrients };

  // Apply core values
  if (userValues.calories) {
    updated.core.ENERC_KCAL.quantity = parseFloat(userValues.calories) || 0;
  }
  if (userValues.protein) {
    updated.core.PROCNT.quantity = parseFloat(userValues.protein) || 0;
  }
  if (userValues.carbs) {
    updated.core.CHOCDF.quantity = parseFloat(userValues.carbs) || 0;
  }
  if (userValues.fat) {
    updated.core.FAT.quantity = parseFloat(userValues.fat) || 0;
  }

  return updated;
};
