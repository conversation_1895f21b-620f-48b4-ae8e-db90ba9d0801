import * as ImageManipulator from "expo-image-manipulator";
import RNFS from "react-native-fs";
import { storage } from "../config/firebase.js";
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
} from "firebase/storage";

const ImageTypes = {
  PROFILE: {
    path: (userId) => `profilePictures/${userId}`,
    fileName: () => `profilePic.jpg`, // Always overwrite same file
    resize: { width: 800, height: 800, quality: 70 },
  },
  PROGRESS: {
    path: (userId, { programId, checkpointId }) =>
      `progressPictures/${userId}/programs/${programId}/checkpoints/${checkpointId}`,
    fileName: () => `progress_${Date.now()}.jpg`,
    resize: { width: 1000, height: 1000, quality: 75 },
  },
  MEAL: {
    path: (userId, { mealId }) => `customMeals/${userId}/${mealId}`,
    fileName: ({ mealId }, imageType = "main") => `${mealId}_${imageType}.jpg`,
    resize: { width: 1200, height: 1200, quality: 80 },
  },
};

export const ImageUploader = {
  upload: async ({ userId, uri, type, context }) => {
    const config = ImageTypes[type];
    let processedUri = uri;
    let resizedImage = null;

    try {
      if (uri.startsWith("content://")) {
        const tempPath = `${RNFS.CachesDirectoryPath}/${Date.now()}.jpg`;
        await RNFS.copyFile(uri, tempPath);
        processedUri = tempPath;
      }

      resizedImage = await ImageManipulator.manipulateAsync(
        processedUri,
        [
          {
            resize: {
              width: config.resize.width,
              height: config.resize.height,
            },
          },
        ],
        {
          compress: config.resize.quality / 100,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      // Use fetch to get Blob
      const response = await fetch(resizedImage.uri);
      const blob = await response.blob();

      const storagePath = `${config.path(userId, context)}/${config.fileName(
        context
      )}`;
      const storageRef = ref(storage, storagePath);

      await uploadBytes(storageRef, blob, { contentType: "image/jpeg" });
      return await getDownloadURL(storageRef);
    } finally {
      // Clean temporary files
      try {
        if (processedUri && processedUri !== uri) {
          await RNFS.unlink(processedUri);
        }
        if (resizedImage?.uri) {
          await RNFS.unlink(resizedImage.uri);
        }
      } catch (cleanupError) {
        console.warn("Cleanup error:", cleanupError);
      }
    }
  },

  remove: async ({ userId, type, context }) => {
    const config = ImageTypes[type];
    const storagePath = `${config.path(userId, context)}/${config.fileName(
      context
    )}`;

    try {
      await deleteObject(ref(storage, storagePath));
    } catch (error) {
      if (error.code !== "storage/object-not-found") {
        throw error;
      }
    }
  },
};
