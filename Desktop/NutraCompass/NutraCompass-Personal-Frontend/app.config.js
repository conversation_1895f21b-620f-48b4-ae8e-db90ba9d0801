const path = require("path");

module.exports = ({ config }) => {
  // Critical workaround for build errors
  if (!process.env.EXPO_DEBUG) {
    process.env.EXPO_NO_TEAM_PLUGIN = "1";
    process.env.EXPO_NO_PREBUILD = "1";
  }

  // Use CONFIG_ENV to decide which config files to use
  const isProd = process.env.CONFIG_ENV === "production";

  // Get current directory path
  const currentDir = __dirname;

  // Set absolute paths to Firebase config files
  const iosGoogleServicesFile = isProd
    ? path.join(currentDir, "src/config/GoogleService-Info-Prod.plist")
    : path.join(currentDir, "src/config/GoogleService-Info-Dev.plist");

  const androidGoogleServicesFile = isProd
    ? path.join(currentDir, "src/config/google-services-prod.json")
    : path.join(currentDir, "src/config/google-services-dev.json");

  const extraConfig = {
    eas: {
      projectId:
        process.env.EXPO_PUBLIC_EAS_PROJECT_ID ||
        "268ed217-15ee-43ed-877a-cb5b01b2a22c",
    },
    NutraCompass_API_URL:
      process.env.NUTRACOMPASS_API_URL ||
      process.env.EXPO_PUBLIC_NUTRACOMPASS_API_URL,
    FIREBASE_API_KEY:
      process.env.FIREBASE_API_KEY || process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
    FIREBASE_AUTH_DOMAIN:
      process.env.FIREBASE_AUTH_DOMAIN ||
      process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
    FIREBASE_PROJECT_ID:
      process.env.FIREBASE_PROJECT_ID ||
      process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
    FIREBASE_STORAGE_BUCKET:
      process.env.FIREBASE_STORAGE_BUCKET ||
      process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
    FIREBASE_MESSAGING_SENDER_ID:
      process.env.FIREBASE_MESSAGING_SENDER_ID ||
      process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    FIREBASE_APP_ID:
      process.env.FIREBASE_APP_ID || process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
    FIREBASE_MEASUREMENT_ID:
      process.env.FIREBASE_MEASUREMENT_ID ||
      process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID,
    GOOGLE_AUTH_WEB_CLIENT_ID:
      process.env.GOOGLE_AUTH_WEB_CLIENT_ID ||
      process.env.EXPO_PUBLIC_GOOGLE_AUTH_WEB_CLIENT_ID,
    GOOGLE_AUTH_IOS_CLIENT_ID:
      process.env.GOOGLE_AUTH_IOS_CLIENT_ID ||
      process.env.EXPO_PUBLIC_GOOGLE_AUTH_IOS_CLIENT_ID,
    EDAMAM_APP_ID:
      process.env.EDAMAM_APP_ID || process.env.EXPO_PUBLIC_EDAMAM_APP_ID,
    EDAMAM_APP_KEY:
      process.env.EDAMAM_APP_KEY || process.env.EXPO_PUBLIC_EDAMAM_APP_KEY,
    EDAMAM_PARSER_BASE_URL:
      process.env.EDAMAM_PARSER_BASE_URL ||
      process.env.EXPO_PUBLIC_EDAMAM_PARSER_BASE_URL,
    EDAMAM_NUTRIENTS_BASE_URL:
      process.env.EDAMAM_NUTRIENTS_BASE_URL ||
      process.env.EXPO_PUBLIC_EDAMAM_NUTRIENTS_BASE_URL,
    RAPIDAPI_KEY:
      process.env.RAPIDAPI_KEY || process.env.EXPO_PUBLIC_RAPIDAPI_KEY,
    RAPIDAPI_HOST:
      process.env.RAPIDAPI_HOST || process.env.EXPO_PUBLIC_RAPIDAPI_HOST,
    OPENROUTER_API_KEY:
      process.env.OPENROUTER_API_KEY || process.env.EXPO_PUBLIC_OPENROUTER_API_KEY,
  };

  return {
    ...config,
    name: "NutraCompass",
    slug: "nutracompass",
    version: "1.0.2",
    runtimeVersion: "1.0.2",
    orientation: "portrait",
    icon: "./assets/NutraCompass.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/brandmark-design.png",
      contentFit: "contain",
      backgroundColor: "#1E1E1E",
      resizeMode: "contain",
    },
    assetBundlePatterns: ["./assets/**/*"],
    ios: {
      supportsTablet: true,
      googleServicesFile: iosGoogleServicesFile,
      bundleIdentifier: "com.nutracompass.personal",
      buildNumber: "1",
      deploymentTarget: "18.0",
      usesAppleSignIn: true,
      associatedDomains: [
        "applinks:nutracompass-personal-prod.firebaseapp.com",
      ],
      requireFullScreen: true,
      infoPlist: {
        CFBundleURLTypes: [
          {
            CFBundleURLSchemes: [
              "com.googleusercontent.apps.356020700499-ljocfsi93litfhpamtoof3v7t3l8ilqi",
              "com.googleusercontent.apps.356020700499-aippfvrmg3s9i8ltcm8psia0ei2lc11c",
            ],
          },
        ],
        ITSAppUsesNonExemptEncryption: false,
        UIRequiredDeviceCapabilities: ["arm64"],
        MinimumOSVersion: "18.0",
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
      ],
      googleServicesFile: androidGoogleServicesFile,
      package: "com.nutracompass.personal",
      versionCode: 3,
    },
    web: {
      favicon: "./assets/favicon.png",
    },
    plugins: [
      "expo-localization",
      [
        "expo-camera",
        {
          cameraPermission:
            "Allow $(PRODUCT_NAME) to access your camera to scan food barcodes and capture profile pictures, nutritional progress photos, and custom meal images.",
        },
      ],
      [
        "expo-av",
        {
          microphonePermission:
            "Allow $(PRODUCT_NAME) to access your microphone to play videos with sound.",
        },
      ],
      [
        "expo-contacts",
        {
          contactsPermission:
            "Allow $(PRODUCT_NAME) to access your contacts to connect with friends in the health community.",
        },
      ],
      "expo-apple-authentication",
      "@react-native-firebase/app",
      "@react-native-firebase/crashlytics",
      "expo-notifications",
      [
        "expo-build-properties",
        {
          ios: {
            deploymentTarget: "18.0",
            useFrameworks: "static",
          },
        },
      ],
      [
        "expo-image-picker",
        {
          photosPermission:
            "Allow $(PRODUCT_NAME) to access your photo library to select images for your profile, progress tracking, and custom meal creations.",
        },
      ],
      [
        "expo-sensors",
        {
          motionPermission:
            "Allow $(PRODUCT_NAME) to access motion & fitness data to track your workouts, steps, and activity levels for personalized health insights.",
        },
      ],
      "expo-task-manager",
      "expo-updates",
    ],
    extra: extraConfig,
    updates: {
      enabled: true,
      checkAutomatically: "ON_ERROR_RECOVERY",
      fallbackToCacheTimeout: 0,
      url: "https://u.expo.dev/268ed217-15ee-43ed-877a-cb5b01b2a22c",
    },
  };
};
