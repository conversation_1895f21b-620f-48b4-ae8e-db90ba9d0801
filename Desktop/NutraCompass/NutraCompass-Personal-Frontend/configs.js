// configs.js
import Constants from "expo-constants";

const Configs = {
  EXPO_PROJECT_ID: Constants.expoConfig.extra.eas.projectId,
  NutraCompass_API_URL:
    "https://cc9bce3bb480.ngrok-free.app" ||
    Constants.expoConfig.extra.NutraCompass_API_URL.trim(),
  firebaseConfig: {
    apiKey: Constants.expoConfig.extra.FIREBASE_API_KEY,
    authDomain: Constants.expoConfig.extra.FIREBASE_AUTH_DOMAIN,
    projectId: Constants.expoConfig.extra.FIREBASE_PROJECT_ID,
    storageBucket: Constants.expoConfig.extra.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: Constants.expoConfig.extra.FIREBASE_MESSAGING_SENDER_ID,
    appId: Constants.expoConfig.extra.FIREBASE_APP_ID,
    measurementId: Constants.expoConfig.extra.FIREBASE_MEASUREMENT_ID,
  },
  GOOGLE_AUTH_WEB_CLIENT_ID:
    Constants.expoConfig.extra.GOOGLE_AUTH_WEB_CLIENT_ID,
  GOOGLE_AUTH_IOS_CLIENT_ID:
    Constants.expoConfig.extra.GOOGLE_AUTH_IOS_CLIENT_ID,
  edamamConfig: {
    APP_ID: Constants.expoConfig.extra.EDAMAM_APP_ID,
    APP_KEY: Constants.expoConfig.extra.EDAMAM_APP_KEY,
    PARSER_BASE_URL: Constants.expoConfig.extra.EDAMAM_PARSER_BASE_URL,
    NUTRIENTS_BASE_URL: Constants.expoConfig.extra.EDAMAM_NUTRIENTS_BASE_URL,
  },
  exerciseDBConfig: {
    rapidApiKey: Constants.expoConfig.extra.RAPIDAPI_KEY,
    rapidApiHost: Constants.expoConfig.extra.RAPIDAPI_HOST,
  },
  openRouterConfig: {
    API_KEY: Constants.expoConfig.extra.OPENROUTER_API_KEY,
    BASE_URL: "https://openrouter.ai/api/v1",
    MODEL: "openai/gpt-4.1-nano-2025-04-14",
  },
  NO_FIREBASE_AUTH: true,
};

module.exports = Configs;
