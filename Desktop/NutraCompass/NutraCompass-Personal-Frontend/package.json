{"name": "frontend", "version": "1.0.2", "main": "node_modules/expo/AppEntry.js", "type": "commonjs", "scripts": {"start": "react-native start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "prebuild": "export EXPO_NO_TEAM_PLUGIN=1"}, "dependencies": {"@gorhom/bottom-sheet": "^4.6.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-native-community/slider": "4.5.2", "@react-native-firebase/analytics": "^21.11.0", "@react-native-firebase/app": "^21.10.1", "@react-native-firebase/crashlytics": "^21.10.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-native-picker/picker": "2.7.5", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/material-top-tabs": "^6.6.14", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "axios": "^1.7.7", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "expo": "~51.0.39", "expo-apple-authentication": "~6.4.2", "expo-application": "~5.9.1", "expo-asset": "10.0.10", "expo-auth-session": "~5.5.2", "expo-av": "~14.0.7", "expo-build-properties": "0.12.5", "expo-camera": "^15.0.16", "expo-checkbox": "~3.0.0", "expo-constants": "16.0.2", "expo-contacts": "13.0.5", "expo-dev-client": "~4.0.29", "expo-device": "~6.0.2", "expo-haptics": "13.0.1", "expo-image": "^1.13.0", "expo-image-manipulator": "~12.0.5", "expo-image-picker": "~15.1.0", "expo-linear-gradient": "13.0.2", "expo-localization": "~15.0.3", "expo-notifications": "~0.28.19", "expo-random": "~14.0.1", "expo-sensors": "13.0.9", "expo-splash-screen": "~0.27.7", "expo-status-bar": "1.12.1", "expo-system-ui": "~3.0.7", "expo-task-manager": "~11.8.2", "expo-updates": "~0.25.28", "firebase": "^10.14.1", "fix": "^0.0.6", "intl": "^1.2.5", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "nativewind": "^2.0.11", "pretty-format": "^29.7.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-animatable": "^1.4.0", "react-native-calendars": "^1.1307.0", "react-native-chart-kit": "^6.12.0", "react-native-circular-progress": "^1.4.0", "react-native-dropdown-picker": "^5.4.6", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.16.1", "react-native-intersection-observer": "^0.2.0", "react-native-iphone-x-helper": "^1.3.1", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^17.1.0", "react-native-onboarding-swiper": "^1.3.0", "react-native-pager-view": "6.3.0", "react-native-paper": "^5.12.5", "react-native-picker-select": "^9.3.1", "react-native-popup-menu": "^0.16.1", "react-native-reanimated": "~3.10.1", "react-native-reanimated-carousel": "^3.5.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-svg": "15.2.0", "react-native-svg-transformer": "^1.5.0", "react-native-tab-view": "^3.5.2", "react-native-uuid": "^2.0.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "react-router-dom": "^6.27.0", "tinycolor2": "^1.6.0"}, "devDependencies": {"@babel/core": "^7.25.8", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "autoprefixer": "^10.4.20", "babel-plugin-transform-css-import-to-string": "^0.0.2", "metro": "0.80.8", "metro-config": "0.80.8", "metro-react-native-babel-transformer": "^0.77.0", "metro-resolver": "0.80.8", "postcss": "^8.4.47", "react-native-css-transformer": "^2.0.0", "tailwindcss": "3.3.2"}, "private": true}